2015-10-17 21:24:09,319 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445087491445_0004_000001
2015-10-17 21:24:09,991 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 21:24:09,991 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 4 cluster_timestamp: 1445087491445 } attemptId: 1 } keyId: -1547346236)
2015-10-17 21:24:10,272 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 21:24:11,054 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 21:24:11,132 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 21:24:11,179 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 21:24:11,179 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 21:24:11,179 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 21:24:11,179 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 21:24:11,179 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 21:24:11,179 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 21:24:11,179 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 21:24:11,179 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 21:24:11,225 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 21:24:11,257 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 21:24:11,288 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 21:24:11,304 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 21:24:11,366 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 21:24:11,679 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 21:24:11,741 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 21:24:11,741 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 21:24:11,741 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445087491445_0004 to jobTokenSecretManager
2015-10-17 21:24:11,960 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445087491445_0004 because: not enabled; too many maps; too much input;
2015-10-17 21:24:11,976 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445087491445_0004 = 1751822336. Number of splits = 13
2015-10-17 21:24:11,976 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445087491445_0004 = 1
2015-10-17 21:24:11,976 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0004Job Transitioned from NEW to INITED
2015-10-17 21:24:11,991 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445087491445_0004.
2015-10-17 21:24:12,038 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 21:24:12,054 INFO [Socket Reader #1 for port 56784] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 56784
2015-10-17 21:24:12,132 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 21:24:12,132 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 21:24:12,132 INFO [IPC Server listener on 56784] org.apache.hadoop.ipc.Server: IPC Server listener on 56784: starting
2015-10-17 21:24:12,147 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at 04DN8IQ.fareast.corp.microsoft.com/***********:56784
2015-10-17 21:24:12,241 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 21:24:12,241 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 21:24:12,257 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 21:24:12,257 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 21:24:12,257 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 21:24:12,272 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 21:24:12,272 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 21:24:12,288 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 56791
2015-10-17 21:24:12,288 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 21:24:12,335 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\Jetty_0_0_0_0_56791_mapreduce____.wz611x\webapp
2015-10-17 21:24:12,554 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:56791
2015-10-17 21:24:12,554 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 56791
2015-10-17 21:24:12,991 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 21:24:12,991 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445087491445_0004
2015-10-17 21:24:12,991 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 21:24:13,007 INFO [Socket Reader #1 for port 56794] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 56794
2015-10-17 21:24:13,007 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 21:24:13,007 INFO [IPC Server listener on 56794] org.apache.hadoop.ipc.Server: IPC Server listener on 56794: starting
2015-10-17 21:24:13,038 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 21:24:13,038 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 21:24:13,038 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 21:24:13,069 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-17 21:24:13,194 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 21:24:13,194 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 21:24:13,194 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 21:24:13,210 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 21:24:13,210 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0004Job Transitioned from INITED to SETUP
2015-10-17 21:24:13,225 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 21:24:13,225 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0004Job Transitioned from SETUP to RUNNING
2015-10-17 21:24:13,257 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,272 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,272 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000010 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000011 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000012 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000010_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000011_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000012_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:24:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:24:13,304 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 21:24:13,304 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 21:24:13,335 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445087491445_0004, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0004/job_1445087491445_0004_1.jhist
2015-10-17 21:24:14,194 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:13 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 21:24:14,257 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=7 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:19456, vCores:-16> knownNMs=5
2015-10-17 21:24:14,257 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:19456, vCores:-16>
2015-10-17 21:24:14,257 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:24:15,319 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 10
2015-10-17 21:24:15,319 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_01_000002 to attempt_1445087491445_0004_m_000001_0
2015-10-17 21:24:15,319 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_01_000003 to attempt_1445087491445_0004_m_000010_0
2015-10-17 21:24:15,319 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_01_000004 to attempt_1445087491445_0004_m_000000_0
2015-10-17 21:24:15,319 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_01_000005 to attempt_1445087491445_0004_m_000005_0
2015-10-17 21:24:15,319 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_01_000006 to attempt_1445087491445_0004_m_000002_0
2015-10-17 21:24:15,319 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_01_000007 to attempt_1445087491445_0004_m_000004_0
2015-10-17 21:24:15,319 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_01_000008 to attempt_1445087491445_0004_m_000009_0
2015-10-17 21:24:15,319 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_01_000009 to attempt_1445087491445_0004_m_000003_0
2015-10-17 21:24:15,319 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_01_000010 to attempt_1445087491445_0004_m_000006_0
2015-10-17 21:24:15,319 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_01_000011 to attempt_1445087491445_0004_m_000007_0
2015-10-17 21:24:15,319 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-26>
2015-10-17 21:24:15,319 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:24:15,319 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 21:24:15,429 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:15,460 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0004/job.jar
2015-10-17 21:24:15,475 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0004/job.xml
2015-10-17 21:24:15,475 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 21:24:15,475 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 21:24:15,475 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 21:24:15,585 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:24:15,585 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:15,585 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000010_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:24:15,585 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:15,585 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:24:15,585 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:15,585 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:24:15,585 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:15,600 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:24:15,600 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:15,600 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:24:15,600 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:15,600 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:24:15,600 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:15,600 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:24:15,600 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:15,600 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:24:15,600 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:15,600 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:24:15,616 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_01_000002 taskAttempt attempt_1445087491445_0004_m_000001_0
2015-10-17 21:24:15,616 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_01_000003 taskAttempt attempt_1445087491445_0004_m_000010_0
2015-10-17 21:24:15,616 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000010_0
2015-10-17 21:24:15,616 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000001_0
2015-10-17 21:24:15,632 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_01_000009 taskAttempt attempt_1445087491445_0004_m_000003_0
2015-10-17 21:24:15,632 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_01_000010 taskAttempt attempt_1445087491445_0004_m_000006_0
2015-10-17 21:24:15,632 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000003_0
2015-10-17 21:24:15,632 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000006_0
2015-10-17 21:24:15,632 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_01_000011 taskAttempt attempt_1445087491445_0004_m_000007_0
2015-10-17 21:24:15,632 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_01_000008 taskAttempt attempt_1445087491445_0004_m_000009_0
2015-10-17 21:24:15,632 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000007_0
2015-10-17 21:24:15,632 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000009_0
2015-10-17 21:24:15,632 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_01_000007 taskAttempt attempt_1445087491445_0004_m_000004_0
2015-10-17 21:24:15,632 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000004_0
2015-10-17 21:24:15,632 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_01_000006 taskAttempt attempt_1445087491445_0004_m_000002_0
2015-10-17 21:24:15,632 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_01_000005 taskAttempt attempt_1445087491445_0004_m_000005_0
2015-10-17 21:24:15,632 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000002_0
2015-10-17 21:24:15,632 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000005_0
2015-10-17 21:24:15,632 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_01_000004 taskAttempt attempt_1445087491445_0004_m_000000_0
2015-10-17 21:24:15,632 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000000_0
2015-10-17 21:24:15,632 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:24:15,679 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55629
2015-10-17 21:24:15,679 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55629
2015-10-17 21:24:15,679 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:24:15,679 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:24:15,694 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:24:15,694 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 21:24:15,694 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 21:24:15,694 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 21:24:15,694 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:24:15,819 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000001_0 : 13562
2015-10-17 21:24:15,819 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000010_0 : 13562
2015-10-17 21:24:15,819 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000004_0 : 13562
2015-10-17 21:24:15,819 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000001_0] using containerId: [container_1445087491445_0004_01_000002 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:24:15,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:24:15,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000010_0] using containerId: [container_1445087491445_0004_01_000003 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:24:15,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000010_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:24:15,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000004_0] using containerId: [container_1445087491445_0004_01_000007 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 21:24:15,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:24:15,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000001
2015-10-17 21:24:15,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:24:15,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000010
2015-10-17 21:24:15,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000010 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:24:15,835 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000002_0 : 13562
2015-10-17 21:24:15,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000004
2015-10-17 21:24:15,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:24:15,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000002_0] using containerId: [container_1445087491445_0004_01_000006 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 21:24:15,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:24:15,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000002
2015-10-17 21:24:15,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:24:15,835 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000009_0 : 13562
2015-10-17 21:24:15,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000009_0] using containerId: [container_1445087491445_0004_01_000008 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 21:24:15,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:24:15,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000009
2015-10-17 21:24:15,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:24:15,866 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000005_0 : 13562
2015-10-17 21:24:15,866 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000005_0] using containerId: [container_1445087491445_0004_01_000005 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:55629]
2015-10-17 21:24:15,866 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:24:15,866 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000005
2015-10-17 21:24:15,866 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:24:15,913 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000000_0 : 13562
2015-10-17 21:24:15,913 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000000_0] using containerId: [container_1445087491445_0004_01_000004 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:55629]
2015-10-17 21:24:15,913 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:24:15,913 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000000
2015-10-17 21:24:15,913 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:24:16,132 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000003_0 : 13562
2015-10-17 21:24:16,132 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000007_0 : 13562
2015-10-17 21:24:16,132 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000006_0 : 13562
2015-10-17 21:24:16,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000003_0] using containerId: [container_1445087491445_0004_01_000009 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:57365]
2015-10-17 21:24:16,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:24:16,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000007_0] using containerId: [container_1445087491445_0004_01_000011 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:57365]
2015-10-17 21:24:16,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:24:16,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000006_0] using containerId: [container_1445087491445_0004_01_000010 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:57365]
2015-10-17 21:24:16,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:24:16,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000003
2015-10-17 21:24:16,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:24:16,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000007
2015-10-17 21:24:16,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:24:16,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000006
2015-10-17 21:24:16,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:24:16,366 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=7 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:8192, vCores:-27> knownNMs=5
2015-10-17 21:24:16,366 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:24:16,366 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:16,366 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_01_000012 to attempt_1445087491445_0004_m_000008_0
2015-10-17 21:24:16,366 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-27>
2015-10-17 21:24:16,366 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:24:16,366 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:11 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:10 RackLocal:1
2015-10-17 21:24:16,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:16,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:24:16,366 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_01_000012 taskAttempt attempt_1445087491445_0004_m_000008_0
2015-10-17 21:24:16,366 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000008_0
2015-10-17 21:24:16,366 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 21:24:16,475 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000008_0 : 13562
2015-10-17 21:24:16,475 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000008_0] using containerId: [container_1445087491445_0004_01_000012 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:57365]
2015-10-17 21:24:16,475 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:24:16,475 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000008
2015-10-17 21:24:16,475 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:24:17,397 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=4 release= 0 newContainers=2 finishedContainers=0 resourcelimit=<memory:6144, vCores:-29> knownNMs=5
2015-10-17 21:24:17,397 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 21:24:17,397 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:17,397 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_01_000013 to attempt_1445087491445_0004_m_000011_0
2015-10-17 21:24:17,397 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:17,397 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_01_000014 to attempt_1445087491445_0004_m_000012_0
2015-10-17 21:24:17,397 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:17,397 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-29>
2015-10-17 21:24:17,397 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:24:17,397 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:3
2015-10-17 21:24:17,397 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000011_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:24:17,397 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:24:17,397 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000012_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:24:17,397 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_01_000014 taskAttempt attempt_1445087491445_0004_m_000012_0
2015-10-17 21:24:17,397 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_01_000013 taskAttempt attempt_1445087491445_0004_m_000011_0
2015-10-17 21:24:17,397 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000012_0
2015-10-17 21:24:17,397 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000011_0
2015-10-17 21:24:17,397 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 21:24:17,397 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:24:17,757 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000011_0 : 13562
2015-10-17 21:24:17,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000011_0] using containerId: [container_1445087491445_0004_01_000013 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:24:17,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000011_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:24:17,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000011
2015-10-17 21:24:17,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000011 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:24:17,772 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000012_0 : 13562
2015-10-17 21:24:17,772 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000012_0] using containerId: [container_1445087491445_0004_01_000014 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:57365]
2015-10-17 21:24:17,772 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000012_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:24:17,772 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000012
2015-10-17 21:24:17,772 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000012 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:24:18,429 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=5 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:2048, vCores:-33> knownNMs=5
2015-10-17 21:24:18,429 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:24:18,429 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Cannot assign container Container: [ContainerId: container_1445087491445_0004_01_000015, NodeId: MSRA-SA-39.fareast.corp.microsoft.com:49130, NodeHttpAddress: MSRA-SA-39.fareast.corp.microsoft.com:8042, Resource: <memory:1024, vCores:1>, Priority: 20, Token: Token { kind: ContainerToken, service: **************:49130 }, ] for a map as either  container memory less than required <memory:1024, vCores:1> or no pending map tasks - maps.isEmpty=true
2015-10-17 21:24:18,429 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-33>
2015-10-17 21:24:18,429 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:24:18,429 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:14 ContRel:1 HostLocal:10 RackLocal:3
2015-10-17 21:24:19,507 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=0 release= 1 newContainers=0 finishedContainers=1 resourcelimit=<memory:1024, vCores:-34> knownNMs=5
2015-10-17 21:24:19,507 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_01_000015
2015-10-17 21:24:19,507 ERROR [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Container complete event for unknown container id container_1445087491445_0004_01_000015
2015-10-17 21:24:19,507 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 21:24:19,507 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:24:19,616 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:24:19,663 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000002 asked for a task
2015-10-17 21:24:19,663 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000002 given task: attempt_1445087491445_0004_m_000001_0
2015-10-17 21:24:19,679 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:24:19,710 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000003 asked for a task
2015-10-17 21:24:19,710 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000003 given task: attempt_1445087491445_0004_m_000010_0
2015-10-17 21:24:20,679 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 21:24:20,679 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:24:21,069 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:24:21,101 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000013 asked for a task
2015-10-17 21:24:21,101 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000013 given task: attempt_1445087491445_0004_m_000011_0
2015-10-17 21:24:25,335 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:24:25,554 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:24:25,585 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000006 asked for a task
2015-10-17 21:24:25,585 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000006 given task: attempt_1445087491445_0004_m_000002_0
2015-10-17 21:24:26,866 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000008 asked for a task
2015-10-17 21:24:26,866 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000008 given task: attempt_1445087491445_0004_m_000009_0
2015-10-17 21:24:27,241 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.13104755
2015-10-17 21:24:27,304 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.131026
2015-10-17 21:24:28,663 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:24:28,741 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.13101514
2015-10-17 21:24:29,272 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000007 asked for a task
2015-10-17 21:24:29,272 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000007 given task: attempt_1445087491445_0004_m_000004_0
2015-10-17 21:24:30,366 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.13104755
2015-10-17 21:24:30,366 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.131026
2015-10-17 21:24:31,741 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.13101514
2015-10-17 21:24:33,382 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.131026
2015-10-17 21:24:33,382 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.13104755
2015-10-17 21:24:34,757 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.13101514
2015-10-17 21:24:35,288 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:24:35,429 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:24:35,960 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000004 asked for a task
2015-10-17 21:24:35,960 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000004 given task: attempt_1445087491445_0004_m_000000_0
2015-10-17 21:24:36,179 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:24:36,179 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:24:36,210 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:24:36,210 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:24:36,210 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:24:36,241 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000010 asked for a task
2015-10-17 21:24:36,241 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000010 given task: attempt_1445087491445_0004_m_000006_0
2015-10-17 21:24:36,241 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000014 asked for a task
2015-10-17 21:24:36,241 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000014 given task: attempt_1445087491445_0004_m_000012_0
2015-10-17 21:24:36,272 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000012 asked for a task
2015-10-17 21:24:36,272 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000012 given task: attempt_1445087491445_0004_m_000008_0
2015-10-17 21:24:36,288 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000009 asked for a task
2015-10-17 21:24:36,288 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000009 given task: attempt_1445087491445_0004_m_000003_0
2015-10-17 21:24:36,288 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000011 asked for a task
2015-10-17 21:24:36,288 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000011 given task: attempt_1445087491445_0004_m_000007_0
2015-10-17 21:24:36,413 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.14427365
2015-10-17 21:24:36,413 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.1669167
2015-10-17 21:24:37,788 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.13101514
2015-10-17 21:24:37,851 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:24:38,022 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000005 asked for a task
2015-10-17 21:24:38,022 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000005 given task: attempt_1445087491445_0004_m_000005_0
2015-10-17 21:24:38,929 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.07034665
2015-10-17 21:24:39,647 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.23926127
2015-10-17 21:24:39,647 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.23922792
2015-10-17 21:24:40,882 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.16902618
2015-10-17 21:24:42,335 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.08485426
2015-10-17 21:24:42,444 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.12360149
2015-10-17 21:24:42,976 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.23922792
2015-10-17 21:24:42,976 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.23926127
2015-10-17 21:24:42,991 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.07436101
2015-10-17 21:24:43,897 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.2299585
2015-10-17 21:24:45,991 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.13101996
2015-10-17 21:24:45,991 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.23922792
2015-10-17 21:24:45,991 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.23926127
2015-10-17 21:24:46,038 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.13101342
2015-10-17 21:24:46,663 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.13098624
2015-10-17 21:24:46,929 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.2392493
2015-10-17 21:24:47,866 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:24:47,882 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:24:48,022 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:24:48,022 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:24:48,163 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:24:49,054 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.3139637
2015-10-17 21:24:49,054 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.29022932
2015-10-17 21:24:49,522 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.13101342
2015-10-17 21:24:49,616 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.13101996
2015-10-17 21:24:49,960 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.2392493
2015-10-17 21:24:50,194 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.13098624
2015-10-17 21:24:50,273 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.037808258
2015-10-17 21:24:51,819 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.07741392
2015-10-17 21:24:51,819 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.056052946
2015-10-17 21:24:52,085 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.34745386
2015-10-17 21:24:52,085 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.34742332
2015-10-17 21:24:52,476 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.043262914
2015-10-17 21:24:52,898 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.13101342
2015-10-17 21:24:52,991 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.2392493
2015-10-17 21:24:53,132 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.13101996
2015-10-17 21:24:53,663 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.13098624
2015-10-17 21:24:54,273 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.0799252
2015-10-17 21:24:55,148 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.34742332
2015-10-17 21:24:55,148 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.34745386
2015-10-17 21:24:55,804 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.11117945
2015-10-17 21:24:55,944 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.12721783
2015-10-17 21:24:56,023 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.32372594
2015-10-17 21:24:56,335 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.097188905
2015-10-17 21:24:56,507 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.13101342
2015-10-17 21:24:56,538 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.13101996
2015-10-17 21:24:57,210 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.13098624
2015-10-17 21:24:58,163 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.05261888
2015-10-17 21:24:58,163 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.34742332
2015-10-17 21:24:58,163 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.34745386
2015-10-17 21:24:58,210 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.12453667
2015-10-17 21:24:59,038 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.34746152
2015-10-17 21:24:59,460 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.13102981
2015-10-17 21:24:59,773 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.13105518
2015-10-17 21:25:00,007 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.13101853
2015-10-17 21:25:00,148 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.13101996
2015-10-17 21:25:00,163 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.13101342
2015-10-17 21:25:00,632 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.13098624
2015-10-17 21:25:01,194 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.45562646
2015-10-17 21:25:01,194 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.41700616
2015-10-17 21:25:01,679 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.1213387
2015-10-17 21:25:01,882 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.12453667
2015-10-17 21:25:02,085 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.34746152
2015-10-17 21:25:02,976 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.13102981
2015-10-17 21:25:03,241 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.13105518
2015-10-17 21:25:03,694 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.13101996
2015-10-17 21:25:03,804 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.13101342
2015-10-17 21:25:03,866 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.13101853
2015-10-17 21:25:04,194 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.13098624
2015-10-17 21:25:04,210 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.45562646
2015-10-17 21:25:04,210 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.45563197
2015-10-17 21:25:05,085 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.34746152
2015-10-17 21:25:05,335 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.13104554
2015-10-17 21:25:05,663 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.12453667
2015-10-17 21:25:06,632 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.13102981
2015-10-17 21:25:06,929 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.13105518
2015-10-17 21:25:07,226 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.45562646
2015-10-17 21:25:07,273 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.45563197
2015-10-17 21:25:07,366 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.13101996
2015-10-17 21:25:07,476 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.13101342
2015-10-17 21:25:07,679 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.13101853
2015-10-17 21:25:07,866 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.13098624
2015-10-17 21:25:08,148 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.39780453
2015-10-17 21:25:09,101 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.13104554
2015-10-17 21:25:09,444 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.12453667
2015-10-17 21:25:10,069 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.07131715
2015-10-17 21:25:10,132 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.056470875
2015-10-17 21:25:10,288 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.5638287
2015-10-17 21:25:10,304 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.45563197
2015-10-17 21:25:10,491 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.13102981
2015-10-17 21:25:10,710 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.13105518
2015-10-17 21:25:10,991 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.13101996
2015-10-17 21:25:11,163 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.13101342
2015-10-17 21:25:11,194 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.44618592
2015-10-17 21:25:11,444 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.13101853
2015-10-17 21:25:11,819 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.13098624
2015-10-17 21:25:12,976 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.13104554
2015-10-17 21:25:13,163 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.12453667
2015-10-17 21:25:13,319 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.5638287
2015-10-17 21:25:13,398 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.5638331
2015-10-17 21:25:13,741 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.121724054
2015-10-17 21:25:14,148 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.107119605
2015-10-17 21:25:14,210 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.4556214
2015-10-17 21:25:14,601 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.15761124
2015-10-17 21:25:14,601 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.13102981
2015-10-17 21:25:14,601 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.13101342
2015-10-17 21:25:14,913 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.13105518
2015-10-17 21:25:15,179 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.13100065
2015-10-17 21:25:15,226 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.13101853
2015-10-17 21:25:16,319 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.5638287
2015-10-17 21:25:16,398 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.5638331
2015-10-17 21:25:16,773 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.13104554
2015-10-17 21:25:16,976 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.12453667
2015-10-17 21:25:17,257 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.4556214
2015-10-17 21:25:17,944 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.13101453
2015-10-17 21:25:18,023 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.13101445
2015-10-17 21:25:18,413 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.18658623
2015-10-17 21:25:18,429 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.2309809
2015-10-17 21:25:18,554 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.13102981
2015-10-17 21:25:18,866 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.13105518
2015-10-17 21:25:18,929 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.19278893
2015-10-17 21:25:18,991 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.13101853
2015-10-17 21:25:19,351 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.62322587
2015-10-17 21:25:19,444 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.5638331
2015-10-17 21:25:20,273 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.45790613
2015-10-17 21:25:20,429 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.62322587
2015-10-17 21:25:20,679 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.13104554
2015-10-17 21:25:21,382 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.12453667
2015-10-17 21:25:21,773 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.13101445
2015-10-17 21:25:21,866 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.13101453
2015-10-17 21:25:22,038 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.23920888
2015-10-17 21:25:22,210 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.23923585
2015-10-17 21:25:22,429 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.667
2015-10-17 21:25:22,460 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.6614041
2015-10-17 21:25:22,460 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.23919825
2015-10-17 21:25:22,913 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.13101853
2015-10-17 21:25:23,007 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.13105518
2015-10-17 21:25:23,007 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.6614041
2015-10-17 21:25:23,054 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.13102981
2015-10-17 21:25:23,366 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.52695435
2015-10-17 21:25:24,335 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.13104554
2015-10-17 21:25:25,148 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.12453667
2015-10-17 21:25:25,398 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.23920888
2015-10-17 21:25:25,445 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.667
2015-10-17 21:25:25,476 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.667
2015-10-17 21:25:25,507 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.13101445
2015-10-17 21:25:25,570 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.13101453
2015-10-17 21:25:25,695 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.23923585
2015-10-17 21:25:25,898 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.23919825
2015-10-17 21:25:26,398 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.56380975
2015-10-17 21:25:26,601 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.13101853
2015-10-17 21:25:26,741 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.13105518
2015-10-17 21:25:26,866 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.13102981
2015-10-17 21:25:28,148 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.13104554
2015-10-17 21:25:28,460 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.667
2015-10-17 21:25:28,476 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.667
2015-10-17 21:25:28,820 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.12453667
2015-10-17 21:25:28,929 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.23920888
2015-10-17 21:25:29,132 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.23923585
2015-10-17 21:25:29,273 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.13101445
2015-10-17 21:25:29,398 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.23919825
2015-10-17 21:25:29,398 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.56380975
2015-10-17 21:25:29,491 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.13101453
2015-10-17 21:25:30,335 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.13101853
2015-10-17 21:25:30,523 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.13105518
2015-10-17 21:25:30,679 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.13102981
2015-10-17 21:25:31,460 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.69072497
2015-10-17 21:25:31,476 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.667
2015-10-17 21:25:31,851 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.13104554
2015-10-17 21:25:32,398 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.23920888
2015-10-17 21:25:32,398 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.12453667
2015-10-17 21:25:32,429 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.56380975
2015-10-17 21:25:32,538 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.23923585
2015-10-17 21:25:32,929 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.23919825
2015-10-17 21:25:33,179 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.13101445
2015-10-17 21:25:33,413 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.13101453
2015-10-17 21:25:33,851 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.13101853
2015-10-17 21:25:34,413 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.15078714
2015-10-17 21:25:34,413 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.13102981
2015-10-17 21:25:34,507 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.7340344
2015-10-17 21:25:34,507 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.69883287
2015-10-17 21:25:35,429 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.13104554
2015-10-17 21:25:35,445 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.5904621
2015-10-17 21:25:35,945 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.23920888
2015-10-17 21:25:36,038 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.14025456
2015-10-17 21:25:36,054 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.23923585
2015-10-17 21:25:36,476 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.23919825
2015-10-17 21:25:36,757 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.13101445
2015-10-17 21:25:37,163 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.13101453
2015-10-17 21:25:37,538 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.771824
2015-10-17 21:25:37,538 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.7448171
2015-10-17 21:25:38,132 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.13450164
2015-10-17 21:25:38,179 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.20583366
2015-10-17 21:25:38,476 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.6669992
2015-10-17 21:25:38,601 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.17839614
2015-10-17 21:25:38,726 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.6669992
2015-10-17 21:25:39,070 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.13104554
2015-10-17 21:25:39,585 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.23923585
2015-10-17 21:25:39,726 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.23920888
2015-10-17 21:25:40,085 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.23919825
2015-10-17 21:25:40,476 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.19408882
2015-10-17 21:25:40,538 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.7914544
2015-10-17 21:25:40,554 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.8176293
2015-10-17 21:25:40,913 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.13101445
2015-10-17 21:25:41,491 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.667
2015-10-17 21:25:41,585 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.13101453
2015-10-17 21:25:42,257 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.18010491
2015-10-17 21:25:42,429 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.23924033
2015-10-17 21:25:42,616 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.22634618
2015-10-17 21:25:43,038 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.1585364
2015-10-17 21:25:43,195 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.23923585
2015-10-17 21:25:43,241 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.23920888
2015-10-17 21:25:43,554 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.87246823
2015-10-17 21:25:43,570 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.8368262
2015-10-17 21:25:43,648 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.23919825
2015-10-17 21:25:44,413 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.22738875
2015-10-17 21:25:44,507 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.667
2015-10-17 21:25:44,851 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.13101445
2015-10-17 21:25:45,570 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.13101453
2015-10-17 21:25:46,023 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.2392399
2015-10-17 21:25:46,507 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.23923585
2015-10-17 21:25:46,601 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.23924033
2015-10-17 21:25:46,601 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.2392158
2015-10-17 21:25:46,601 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.92715746
2015-10-17 21:25:46,601 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.87821054
2015-10-17 21:25:46,663 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.23920888
2015-10-17 21:25:46,929 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.2051757
2015-10-17 21:25:47,132 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.23919825
2015-10-17 21:25:47,570 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.667
2015-10-17 21:25:48,554 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.22738875
2015-10-17 21:25:49,007 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.13101445
2015-10-17 21:25:49,413 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.13101453
2015-10-17 21:25:49,632 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 0.96886706
2015-10-17 21:25:49,632 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.917969
2015-10-17 21:25:49,741 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.2392399
2015-10-17 21:25:49,929 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.29259926
2015-10-17 21:25:50,054 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.23920888
2015-10-17 21:25:50,445 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.2392158
2015-10-17 21:25:50,445 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.26837766
2015-10-17 21:25:50,648 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.23924033
2015-10-17 21:25:50,726 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.23922384
2015-10-17 21:25:50,866 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.7103213
2015-10-17 21:25:52,163 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.22738875
2015-10-17 21:25:52,679 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 1.0
2015-10-17 21:25:52,679 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.95058656
2015-10-17 21:25:52,851 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.13101445
2015-10-17 21:25:53,492 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.13101453
2015-10-17 21:25:53,523 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.2392399
2015-10-17 21:25:53,726 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.34744066
2015-10-17 21:25:53,820 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.3041833
2015-10-17 21:25:53,898 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.7414953
2015-10-17 21:25:54,257 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.331929
2015-10-17 21:25:54,523 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.23924033
2015-10-17 21:25:54,570 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.2392158
2015-10-17 21:25:54,648 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.23922384
2015-10-17 21:25:55,710 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 1.0
2015-10-17 21:25:55,710 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 0.99116516
2015-10-17 21:25:55,976 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.22738875
2015-10-17 21:25:56,460 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.13243385
2015-10-17 21:25:56,945 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.779586
2015-10-17 21:25:57,117 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.34744066
2015-10-17 21:25:57,257 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.17363973
2015-10-17 21:25:57,335 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.2392399
2015-10-17 21:25:57,351 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.34739637
2015-10-17 21:25:57,726 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.3473995
2015-10-17 21:25:58,554 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.23922384
2015-10-17 21:25:58,585 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.2392158
2015-10-17 21:25:58,757 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 1.0
2015-10-17 21:25:59,413 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.23924033
2015-10-17 21:25:59,663 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.22738875
2015-10-17 21:25:59,976 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.806944
2015-10-17 21:26:00,101 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.17423794
2015-10-17 21:26:00,413 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.34744066
2015-10-17 21:26:00,820 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.1980125
2015-10-17 21:26:00,882 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.34739637
2015-10-17 21:26:01,007 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.2392399
2015-10-17 21:26:01,117 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.3473995
2015-10-17 21:26:01,804 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 1.0
2015-10-17 21:26:02,460 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.2392158
2015-10-17 21:26:02,460 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.23922384
2015-10-17 21:26:02,992 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.8371967
2015-10-17 21:26:03,085 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.23924033
2015-10-17 21:26:03,398 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.22738875
2015-10-17 21:26:03,710 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.34744066
2015-10-17 21:26:03,788 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.19508053
2015-10-17 21:26:04,335 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.34739637
2015-10-17 21:26:04,570 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.3473995
2015-10-17 21:26:04,851 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.23922452
2015-10-17 21:26:04,945 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.2392399
2015-10-17 21:26:06,054 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.8766434
2015-10-17 21:26:06,163 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.23922384
2015-10-17 21:26:06,492 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.2392158
2015-10-17 21:26:07,007 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.23924033
2015-10-17 21:26:07,226 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.22738875
2015-10-17 21:26:07,398 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.34744066
2015-10-17 21:26:07,585 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.23696303
2015-10-17 21:26:07,867 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.34739637
2015-10-17 21:26:08,132 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.3473995
2015-10-17 21:26:08,570 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.2392399
2015-10-17 21:26:08,632 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.23922452
2015-10-17 21:26:09,070 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.9029369
2015-10-17 21:26:09,445 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000010_0 is : 1.0
2015-10-17 21:26:09,445 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0004_m_000010_0
2015-10-17 21:26:09,460 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000010_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:26:09,460 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_01_000003 taskAttempt attempt_1445087491445_0004_m_000010_0
2015-10-17 21:26:09,460 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000010_0
2015-10-17 21:26:09,460 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:26:09,648 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000010_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:26:09,648 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000010_0
2015-10-17 21:26:09,648 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000010 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:26:09,663 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-17 21:26:10,148 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:1 HostLocal:10 RackLocal:3
2015-10-17 21:26:10,148 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 21:26:10,148 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-17 21:26:10,148 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-17 21:26:10,148 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:13 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:1 HostLocal:10 RackLocal:3
2015-10-17 21:26:10,195 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.23922384
2015-10-17 21:26:10,382 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.2392158
2015-10-17 21:26:10,757 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0004_m_000000
2015-10-17 21:26:10,757 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 21:26:10,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445087491445_0004_m_000000
2015-10-17 21:26:10,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:26:10,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:26:10,773 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000000_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:26:10,804 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.23924033
2015-10-17 21:26:10,960 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.22738875
2015-10-17 21:26:11,101 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.34744066
2015-10-17 21:26:11,195 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:13 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:1 HostLocal:10 RackLocal:3
2015-10-17 21:26:11,195 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=5 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 21:26:11,195 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_01_000003
2015-10-17 21:26:11,195 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:12 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:1 HostLocal:10 RackLocal:3
2015-10-17 21:26:11,195 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000010_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:26:11,335 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.34739637
2015-10-17 21:26:11,632 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.2391939
2015-10-17 21:26:11,726 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.3473995
2015-10-17 21:26:12,085 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.9406189
2015-10-17 21:26:12,367 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.2392399
2015-10-17 21:26:12,523 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.23922452
2015-10-17 21:26:14,054 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.23922384
2015-10-17 21:26:14,367 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.2392158
2015-10-17 21:26:14,367 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.23924033
2015-10-17 21:26:14,523 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.34744066
2015-10-17 21:26:14,523 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.22738875
2015-10-17 21:26:14,773 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.34739637
2015-10-17 21:26:15,101 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 0.98581827
2015-10-17 21:26:15,101 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.3473995
2015-10-17 21:26:15,320 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.2391939
2015-10-17 21:26:16,085 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.2392399
2015-10-17 21:26:16,288 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.23922452
2015-10-17 21:26:17,367 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:26:17,367 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 21:26:17,367 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_01_000016 to attempt_1445087491445_0004_r_000000_0
2015-10-17 21:26:17,367 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-17 21:26:17,382 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:26:17,382 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:26:17,382 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_01_000016 taskAttempt attempt_1445087491445_0004_r_000000_0
2015-10-17 21:26:17,382 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_r_000000_0
2015-10-17 21:26:17,382 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:26:17,507 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_r_000000_0 : 13562
2015-10-17 21:26:17,507 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_r_000000_0] using containerId: [container_1445087491445_0004_01_000016 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:26:17,507 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:26:17,507 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_r_000000
2015-10-17 21:26:17,507 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:26:17,882 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.23922384
2015-10-17 21:26:18,085 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.34744066
2015-10-17 21:26:18,101 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 1.0
2015-10-17 21:26:18,132 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.22738875
2015-10-17 21:26:18,367 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.34739637
2015-10-17 21:26:18,367 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.2392158
2015-10-17 21:26:18,382 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.25068563
2015-10-17 21:26:18,413 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 21:26:18,679 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.3473995
2015-10-17 21:26:19,038 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.2391939
2015-10-17 21:26:19,820 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.2392399
2015-10-17 21:26:20,007 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.23922452
2015-10-17 21:26:20,163 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:26:20,195 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_r_000016 asked for a task
2015-10-17 21:26:20,195 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_r_000016 given task: attempt_1445087491445_0004_r_000000_0
2015-10-17 21:26:21,101 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 0 maxEvents 10000
2015-10-17 21:26:21,117 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 1.0
2015-10-17 21:26:21,320 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.23922384
2015-10-17 21:26:21,585 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.39685515
2015-10-17 21:26:21,804 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.34739637
2015-10-17 21:26:21,945 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.23970197
2015-10-17 21:26:22,148 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.3133507
2015-10-17 21:26:22,148 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.25695947
2015-10-17 21:26:22,164 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.37324238
2015-10-17 21:26:22,210 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 21:26:23,195 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.2391939
2015-10-17 21:26:23,210 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 21:26:23,726 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.2392399
2015-10-17 21:26:23,773 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.23922452
2015-10-17 21:26:23,773 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000001_0 is : 1.0
2015-10-17 21:26:23,773 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0004_m_000001_0
2015-10-17 21:26:23,773 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:26:23,773 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_01_000002 taskAttempt attempt_1445087491445_0004_m_000001_0
2015-10-17 21:26:23,773 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000001_0
2015-10-17 21:26:23,773 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:26:23,976 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:26:23,976 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000001_0
2015-10-17 21:26:23,976 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:26:23,976 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-17 21:26:24,273 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000011_0 is : 1.0
2015-10-17 21:26:24,273 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 21:26:24,273 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0004_m_000011_0
2015-10-17 21:26:24,273 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000011_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:26:24,273 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_01_000013 taskAttempt attempt_1445087491445_0004_m_000011_0
2015-10-17 21:26:24,273 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000011_0
2015-10-17 21:26:24,273 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:26:24,460 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000011_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:26:24,460 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000011_0
2015-10-17 21:26:24,460 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000011 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:26:24,460 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-17 21:26:24,554 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-17 21:26:24,554 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_01_000002
2015-10-17 21:26:24,554 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:26:24,554 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-17 21:26:25,085 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.23922384
2015-10-17 21:26:25,273 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 21:26:25,367 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.45558137
2015-10-17 21:26:25,460 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.38714933
2015-10-17 21:26:25,601 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_01_000013
2015-10-17 21:26:25,601 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:26:25,601 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000011_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:26:25,601 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:26:25,601 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_01_000017 to attempt_1445087491445_0004_m_000000_1
2015-10-17 21:26:25,601 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:16 ContRel:1 HostLocal:10 RackLocal:4
2015-10-17 21:26:25,601 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:26:25,601 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000000_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:26:25,601 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_01_000017 taskAttempt attempt_1445087491445_0004_m_000000_1
2015-10-17 21:26:25,601 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000000_1
2015-10-17 21:26:25,601 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:26:25,789 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0004_m_000007
2015-10-17 21:26:25,789 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445087491445_0004_m_000007
2015-10-17 21:26:25,789 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000000_1 : 13562
2015-10-17 21:26:25,789 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 21:26:25,789 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:26:25,804 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:26:25,804 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000000_1] using containerId: [container_1445087491445_0004_01_000017 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:26:25,804 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000000_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:26:25,804 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000007_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:26:25,804 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000000
2015-10-17 21:26:25,804 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.34744897
2015-10-17 21:26:25,835 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.30133432
2015-10-17 21:26:25,851 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.43563133
2015-10-17 21:26:26,132 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.3151685
2015-10-17 21:26:26,304 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:26,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:16 ContRel:1 HostLocal:10 RackLocal:4
2015-10-17 21:26:26,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=5 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 21:26:26,867 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.2391939
2015-10-17 21:26:27,054 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.051282056
2015-10-17 21:26:27,335 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:27,757 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.2594035
2015-10-17 21:26:28,117 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:26:28,148 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000017 asked for a task
2015-10-17 21:26:28,148 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000017 given task: attempt_1445087491445_0004_m_000000_1
2015-10-17 21:26:28,148 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.23922452
2015-10-17 21:26:28,367 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:28,851 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.45562834
2015-10-17 21:26:28,945 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.2655477
2015-10-17 21:26:29,101 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.45204538
2015-10-17 21:26:29,414 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:29,507 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.45560816
2015-10-17 21:26:29,632 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.3302339
2015-10-17 21:26:29,632 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.34744897
2015-10-17 21:26:30,023 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.34742883
2015-10-17 21:26:30,070 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.051282056
2015-10-17 21:26:30,460 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:30,726 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.2391939
2015-10-17 21:26:31,445 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:31,570 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.3264105
2015-10-17 21:26:31,867 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.23922452
2015-10-17 21:26:32,351 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.45562834
2015-10-17 21:26:32,445 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:32,570 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.45559853
2015-10-17 21:26:32,773 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.31804174
2015-10-17 21:26:33,070 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.45560816
2015-10-17 21:26:33,085 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.051282056
2015-10-17 21:26:33,492 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:33,554 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.3302339
2015-10-17 21:26:33,820 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.34744897
2015-10-17 21:26:34,382 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.34742883
2015-10-17 21:26:34,476 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:34,664 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.2391939
2015-10-17 21:26:35,132 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.12453667
2015-10-17 21:26:35,226 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.34744292
2015-10-17 21:26:35,492 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:35,648 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.23922452
2015-10-17 21:26:35,867 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.45562834
2015-10-17 21:26:35,945 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.45559853
2015-10-17 21:26:36,085 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.051282056
2015-10-17 21:26:36,351 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.45560816
2015-10-17 21:26:36,460 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.3474171
2015-10-17 21:26:36,476 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:37,289 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.3302339
2015-10-17 21:26:37,492 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:37,757 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.34744897
2015-10-17 21:26:38,132 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.12453667
2015-10-17 21:26:38,507 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:38,742 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.34742883
2015-10-17 21:26:38,820 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.2391939
2015-10-17 21:26:39,007 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.34744292
2015-10-17 21:26:39,117 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.051282056
2015-10-17 21:26:39,382 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.45562834
2015-10-17 21:26:39,382 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.45559853
2015-10-17 21:26:39,554 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:39,789 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.23922452
2015-10-17 21:26:39,976 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.45560816
2015-10-17 21:26:40,148 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.3474171
2015-10-17 21:26:40,585 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:41,023 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.3302339
2015-10-17 21:26:41,164 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.12453667
2015-10-17 21:26:41,554 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.34744897
2015-10-17 21:26:41,601 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:42,148 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.051282056
2015-10-17 21:26:42,601 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:42,757 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.34744292
2015-10-17 21:26:42,757 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.34742883
2015-10-17 21:26:42,851 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.2391939
2015-10-17 21:26:42,882 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.45562834
2015-10-17 21:26:42,976 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.45559853
2015-10-17 21:26:43,570 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.27635333
2015-10-17 21:26:43,585 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:43,585 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.45560816
2015-10-17 21:26:43,882 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.3474171
2015-10-17 21:26:44,210 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.12453667
2015-10-17 21:26:44,617 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:44,726 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.3302339
2015-10-17 21:26:45,210 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.051282056
2015-10-17 21:26:45,257 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.34744897
2015-10-17 21:26:45,601 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:46,398 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.45562834
2015-10-17 21:26:46,414 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.45559853
2015-10-17 21:26:46,460 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.34744292
2015-10-17 21:26:46,601 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:46,617 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.34742883
2015-10-17 21:26:46,664 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.28883514
2015-10-17 21:26:46,992 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.45560816
2015-10-17 21:26:47,429 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.3391816
2015-10-17 21:26:47,617 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:47,773 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.3474171
2015-10-17 21:26:48,242 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.051282056
2015-10-17 21:26:48,414 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.3302339
2015-10-17 21:26:48,648 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:48,914 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.34744897
2015-10-17 21:26:49,695 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:49,789 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.45562834
2015-10-17 21:26:49,929 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.45559853
2015-10-17 21:26:50,023 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.34744292
2015-10-17 21:26:50,210 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.22738875
2015-10-17 21:26:50,539 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.45560816
2015-10-17 21:26:50,632 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.34526908
2015-10-17 21:26:50,679 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:50,851 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.34742883
2015-10-17 21:26:51,242 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.051282056
2015-10-17 21:26:51,367 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.34740257
2015-10-17 21:26:51,710 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:52,007 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.3474171
2015-10-17 21:26:52,148 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.3302339
2015-10-17 21:26:52,961 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:52,961 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.34744897
2015-10-17 21:26:53,257 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.46873796
2015-10-17 21:26:53,273 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.22738875
2015-10-17 21:26:53,398 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.45559853
2015-10-17 21:26:53,882 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.34744292
2015-10-17 21:26:53,992 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.45560816
2015-10-17 21:26:54,195 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:54,789 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.34742883
2015-10-17 21:26:54,789 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.34738368
2015-10-17 21:26:55,226 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:55,632 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.34740257
2015-10-17 21:26:55,836 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.3302339
2015-10-17 21:26:55,898 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.3474171
2015-10-17 21:26:56,226 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:56,273 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.22738875
2015-10-17 21:26:56,507 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.34744897
2015-10-17 21:26:56,757 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.53919196
2015-10-17 21:26:56,867 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.45559853
2015-10-17 21:26:57,257 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:57,586 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.52283955
2015-10-17 21:26:57,601 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.34744292
2015-10-17 21:26:58,304 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:58,492 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.34742883
2015-10-17 21:26:58,679 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.34738368
2015-10-17 21:26:59,289 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.3302339
2015-10-17 21:26:59,367 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:26:59,539 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.3302339
2015-10-17 21:26:59,648 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.3474171
2015-10-17 21:26:59,789 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.34740257
2015-10-17 21:27:00,242 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.5638227
2015-10-17 21:27:00,398 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:27:00,398 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.34744897
2015-10-17 21:27:00,570 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.52532715
2015-10-17 21:27:01,023 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.5638118
2015-10-17 21:27:01,367 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.34744292
2015-10-17 21:27:01,429 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:27:02,070 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.34742883
2015-10-17 21:27:02,336 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.3302339
2015-10-17 21:27:02,414 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:27:02,476 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.34738368
2015-10-17 21:27:03,195 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.3302339
2015-10-17 21:27:03,304 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.3474171
2015-10-17 21:27:03,445 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:27:03,695 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.34740257
2015-10-17 21:27:03,695 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.5638227
2015-10-17 21:27:03,867 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.38723508
2015-10-17 21:27:04,039 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.5638114
2015-10-17 21:27:04,054 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:27:04,429 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:27:04,554 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.5638118
2015-10-17 21:27:04,914 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.34744292
2015-10-17 21:27:05,367 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.3302339
2015-10-17 21:27:05,429 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:27:05,664 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.34742883
2015-10-17 21:27:06,257 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.34738368
2015-10-17 21:27:06,461 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:27:06,507 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.34762955
2015-10-17 21:27:07,070 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.3474171
2015-10-17 21:27:07,070 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:27:07,242 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.5638227
2015-10-17 21:27:07,445 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.5638114
2015-10-17 21:27:07,445 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:27:07,523 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.34740257
2015-10-17 21:27:07,867 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.44799423
2015-10-17 21:27:08,132 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.5638118
2015-10-17 21:27:08,382 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.40342373
2015-10-17 21:27:08,492 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:27:08,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0004_m_000003_0 because it is running on unusable node:MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 21:27:08,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0004_m_000006_0 because it is running on unusable node:MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 21:27:08,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0004_m_000007_0 because it is running on unusable node:MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 21:27:08,726 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000003_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:27:08,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0004_m_000008_0 because it is running on unusable node:MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 21:27:08,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0004_m_000012_0 because it is running on unusable node:MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 21:27:08,726 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000006_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:27:08,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_01_000011
2015-10-17 21:27:08,726 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000007_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:27:08,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_01_000014
2015-10-17 21:27:08,726 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_01_000009 taskAttempt attempt_1445087491445_0004_m_000003_0
2015-10-17 21:27:08,726 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000008_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:27:08,726 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000003_0
2015-10-17 21:27:08,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_01_000012
2015-10-17 21:27:08,726 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_01_000010 taskAttempt attempt_1445087491445_0004_m_000006_0
2015-10-17 21:27:08,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_01_000010
2015-10-17 21:27:08,726 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000006_0
2015-10-17 21:27:08,726 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000012_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:27:08,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_01_000009
2015-10-17 21:27:08,726 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000007_0: Container released on a *lost* node
2015-10-17 21:27:08,726 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_01_000011 taskAttempt attempt_1445087491445_0004_m_000007_0
2015-10-17 21:27:08,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:16 ContRel:1 HostLocal:10 RackLocal:4
2015-10-17 21:27:08,726 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000007_0
2015-10-17 21:27:08,726 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 21:27:08,726 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000012_0: Container released on a *lost* node
2015-10-17 21:27:08,726 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_01_000012 taskAttempt attempt_1445087491445_0004_m_000008_0
2015-10-17 21:27:08,726 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_01_000014 taskAttempt attempt_1445087491445_0004_m_000012_0
2015-10-17 21:27:08,726 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000008_0
2015-10-17 21:27:08,726 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000008_0: Container released on a *lost* node
2015-10-17 21:27:08,726 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000012_0
2015-10-17 21:27:08,726 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000006_0: Container released on a *lost* node
2015-10-17 21:27:08,726 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000003_0: Container released on a *lost* node
2015-10-17 21:27:08,726 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 21:27:08,726 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 21:27:08,726 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 21:27:08,726 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 21:27:08,976 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.36066097
2015-10-17 21:27:09,570 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:27:09,961 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.40304247
2015-10-17 21:27:10,211 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:27:10,211 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.34738368
2015-10-17 21:27:10,351 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.39653787
2015-10-17 21:27:10,554 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:27:10,679 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.5638227
2015-10-17 21:27:10,882 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.5638114
2015-10-17 21:27:10,992 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.3474171
2015-10-17 21:27:11,148 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000003_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:27:11,273 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:27:11,507 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.43306357
2015-10-17 21:27:11,507 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.34740257
2015-10-17 21:27:11,539 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000007_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:27:11,539 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000012_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:27:11,539 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000006_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:27:11,539 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000008_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:27:11,539 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:27:11,554 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out1/_temporary/1/_temporary/attempt_1445087491445_0004_m_000003_0
2015-10-17 21:27:11,851 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:27:11,851 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:27:11,851 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:27:11,851 WARN [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out1/_temporary/1/_temporary/attempt_1445087491445_0004_m_000008_0
2015-10-17 21:27:11,851 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000003_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:27:11,851 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000008_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:27:11,851 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:27:11,851 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:27:11,851 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:27:11,851 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:27:11,851 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000003_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:27:11,851 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000008_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:27:11,867 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out1/_temporary/1/_temporary/attempt_1445087491445_0004_m_000007_0
2015-10-17 21:27:11,867 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000007_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:27:11,867 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:27:11,867 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:27:11,867 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000007_2 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:27:11,867 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.455643
2015-10-17 21:27:11,867 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.5638118
2015-10-17 21:27:11,882 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:27:11,882 WARN [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out1/_temporary/1/_temporary/attempt_1445087491445_0004_m_000006_0
2015-10-17 21:27:11,882 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000006_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:27:11,882 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:27:11,882 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:27:11,882 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000006_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:27:11,882 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out1/_temporary/1/_temporary/attempt_1445087491445_0004_m_000012_0
2015-10-17 21:27:11,882 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000012_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:27:11,882 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:27:11,882 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:27:11,882 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000012_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:27:12,148 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:6 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:16 ContRel:1 HostLocal:10 RackLocal:4
2015-10-17 21:27:12,164 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=5 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 21:27:12,914 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 21:27:13,195 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.4216792
2015-10-17 21:27:13,226 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:27:13,945 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:13,945 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.4556358
2015-10-17 21:27:14,023 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.34738368
2015-10-17 21:27:14,132 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.5638227
2015-10-17 21:27:14,351 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.5638114
2015-10-17 21:27:14,539 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.43306357
2015-10-17 21:27:14,539 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.43306357
2015-10-17 21:27:14,601 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_0 is : 0.41106838
2015-10-17 21:27:14,976 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:15,132 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_0 is : 0.34740257
2015-10-17 21:27:15,351 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.5638118
2015-10-17 21:27:15,570 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_0 is : 0.455643
2015-10-17 21:27:15,961 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:16,273 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:27:16,695 INFO [Socket Reader #1 for port 56794] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 56794: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:27:16,851 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.45563385
2015-10-17 21:27:16,976 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:17,273 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_0 is : 0.4556358
2015-10-17 21:27:17,304 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_0 is : 0.34738368
2015-10-17 21:27:17,476 INFO [Socket Reader #1 for port 56794] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 56794: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:27:17,539 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.43837354
2015-10-17 21:27:17,679 INFO [Socket Reader #1 for port 56794] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 56794: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:27:17,742 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.5638227
2015-10-17 21:27:17,851 INFO [Socket Reader #1 for port 56794] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 56794: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:27:17,929 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.5638114
2015-10-17 21:27:17,976 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:17,992 INFO [Socket Reader #1 for port 56794] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 56794: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:27:18,148 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.43306357
2015-10-17 21:27:19,023 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:19,023 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.5638118
2015-10-17 21:27:19,320 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:27:20,023 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:20,398 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.45563385
2015-10-17 21:27:20,539 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.53591895
2015-10-17 21:27:21,007 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:21,148 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.5638227
2015-10-17 21:27:21,320 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.5638114
2015-10-17 21:27:21,882 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.43306357
2015-10-17 21:27:22,039 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:22,351 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:27:22,492 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.5638118
2015-10-17 21:27:23,023 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:23,586 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.53591895
2015-10-17 21:27:24,008 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.45563385
2015-10-17 21:27:24,023 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:24,664 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.5638227
2015-10-17 21:27:24,804 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.5638114
2015-10-17 21:27:25,039 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:25,351 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:27:25,726 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.43306357
2015-10-17 21:27:25,992 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.5638118
2015-10-17 21:27:26,023 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:26,586 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.5366308
2015-10-17 21:27:27,054 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:27,758 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.45563385
2015-10-17 21:27:27,976 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.62687725
2015-10-17 21:27:28,039 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:28,179 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.5638114
2015-10-17 21:27:28,367 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:27:29,086 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:29,398 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.43306357
2015-10-17 21:27:29,492 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.6012072
2015-10-17 21:27:29,601 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.6387274
2015-10-17 21:27:30,133 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:31,117 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:31,320 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.667
2015-10-17 21:27:31,320 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.667
2015-10-17 21:27:31,383 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:27:31,570 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.5796258
2015-10-17 21:27:31,617 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.45563385
2015-10-17 21:27:32,164 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:32,617 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.6387274
2015-10-17 21:27:33,148 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.43306357
2015-10-17 21:27:33,148 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:33,179 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.6664595
2015-10-17 21:27:34,164 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:34,226 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.6664595
2015-10-17 21:27:34,398 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:27:34,789 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.667
2015-10-17 21:27:35,179 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:35,226 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.6587075
2015-10-17 21:27:35,414 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.45563385
2015-10-17 21:27:35,617 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.6387274
2015-10-17 21:27:35,867 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.6587075
2015-10-17 21:27:36,164 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:36,554 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.667
2015-10-17 21:27:36,820 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.43306357
2015-10-17 21:27:37,195 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:37,351 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.6387274
2015-10-17 21:27:37,414 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:27:38,211 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:38,242 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.667
2015-10-17 21:27:38,648 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.667
2015-10-17 21:27:38,664 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.667
2015-10-17 21:27:39,195 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:39,226 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.45563385
2015-10-17 21:27:40,086 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.667
2015-10-17 21:27:40,226 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:40,398 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.43306357
2015-10-17 21:27:40,461 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:27:41,258 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:41,711 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.667
2015-10-17 21:27:41,804 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.667
2015-10-17 21:27:42,133 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.667
2015-10-17 21:27:42,242 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:42,851 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.45563385
2015-10-17 21:27:43,258 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:43,492 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:27:43,679 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.667
2015-10-17 21:27:44,023 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.43306357
2015-10-17 21:27:44,242 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:44,883 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.6906613
2015-10-17 21:27:45,226 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.667
2015-10-17 21:27:45,258 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:45,539 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.667
2015-10-17 21:27:46,273 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:46,461 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.45563385
2015-10-17 21:27:46,554 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:27:47,195 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.667
2015-10-17 21:27:47,320 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:47,586 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.43306357
2015-10-17 21:27:47,898 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.72835857
2015-10-17 21:27:48,304 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:48,586 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.667
2015-10-17 21:27:49,070 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.667
2015-10-17 21:27:49,351 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:49,570 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:27:50,101 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.45563385
2015-10-17 21:27:50,336 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:50,758 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.667
2015-10-17 21:27:50,914 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.75801975
2015-10-17 21:27:51,367 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:51,601 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.4939198
2015-10-17 21:27:52,086 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.667
2015-10-17 21:27:52,351 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:52,539 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.667
2015-10-17 21:27:52,586 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:27:53,367 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:53,930 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.8039247
2015-10-17 21:27:54,148 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.667
2015-10-17 21:27:54,195 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.50234413
2015-10-17 21:27:54,351 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:55,367 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:55,648 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:27:55,695 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.667
2015-10-17 21:27:55,726 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.5339195
2015-10-17 21:27:56,086 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.667
2015-10-17 21:27:56,414 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:56,945 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.84510666
2015-10-17 21:27:57,398 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:57,586 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.667
2015-10-17 21:27:58,414 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:58,430 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.5582355
2015-10-17 21:27:58,664 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:27:59,336 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.667
2015-10-17 21:27:59,648 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.53591895
2015-10-17 21:27:59,648 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:27:59,742 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.667
2015-10-17 21:28:00,851 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:28:00,867 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.8808453
2015-10-17 21:28:00,992 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.667
2015-10-17 21:28:01,680 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:28:01,836 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:28:02,273 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.563847
2015-10-17 21:28:02,851 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:28:02,976 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.6683186
2015-10-17 21:28:03,148 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.53591895
2015-10-17 21:28:03,226 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.667
2015-10-17 21:28:03,836 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:28:03,914 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.9315181
2015-10-17 21:28:04,367 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.667
2015-10-17 21:28:04,695 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:28:04,867 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:28:05,852 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:28:05,961 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.563847
2015-10-17 21:28:06,648 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.6814574
2015-10-17 21:28:06,648 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.667
2015-10-17 21:28:06,727 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.53591895
2015-10-17 21:28:06,867 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:28:06,914 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 0.9836266
2015-10-17 21:28:07,711 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:28:07,836 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.673403
2015-10-17 21:28:07,852 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:28:08,883 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:28:09,570 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.563847
2015-10-17 21:28:09,899 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:28:09,930 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 1.0
2015-10-17 21:28:10,164 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.6700839
2015-10-17 21:28:10,164 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.53591895
2015-10-17 21:28:10,211 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.6943915
2015-10-17 21:28:10,727 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:28:10,883 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:28:11,274 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.68626297
2015-10-17 21:28:11,914 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:28:12,945 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:28:12,945 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 1.0
2015-10-17 21:28:13,195 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.563847
2015-10-17 21:28:13,242 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_1 is : 1.0
2015-10-17 21:28:13,242 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0004_m_000000_1
2015-10-17 21:28:13,242 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000000_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:28:13,242 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_01_000017 taskAttempt attempt_1445087491445_0004_m_000000_1
2015-10-17 21:28:13,242 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000000_1
2015-10-17 21:28:13,242 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:28:13,399 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000000_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:28:13,399 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000000_1
2015-10-17 21:28:13,399 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445087491445_0004_m_000000_0
2015-10-17 21:28:13,399 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:28:13,399 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-17 21:28:13,399 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000000_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:28:13,399 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_01_000004 taskAttempt attempt_1445087491445_0004_m_000000_0
2015-10-17 21:28:13,399 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000000_0
2015-10-17 21:28:13,399 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55629
2015-10-17 21:28:13,570 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:6 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:16 ContRel:1 HostLocal:10 RackLocal:4
2015-10-17 21:28:13,570 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:28:13,570 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:28:13,570 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_01_000018 to attempt_1445087491445_0004_m_000007_1
2015-10-17 21:28:13,570 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:10 RackLocal:5
2015-10-17 21:28:13,570 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:28:13,570 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000007_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:28:13,570 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_01_000018 taskAttempt attempt_1445087491445_0004_m_000007_1
2015-10-17 21:28:13,570 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000007_1
2015-10-17 21:28:13,570 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:28:13,586 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000000_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:28:13,586 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:28:13,836 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out1/_temporary/1/_temporary/attempt_1445087491445_0004_m_000000_0
2015-10-17 21:28:13,836 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.6820196
2015-10-17 21:28:13,836 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000000_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:28:13,836 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.07692308
2015-10-17 21:28:13,836 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.70648354
2015-10-17 21:28:13,836 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000007_1 : 13562
2015-10-17 21:28:13,836 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000007_1] using containerId: [container_1445087491445_0004_01_000018 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:28:13,836 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000007_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:28:13,836 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000007
2015-10-17 21:28:13,899 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000000_0 is : 0.53591895
2015-10-17 21:28:13,977 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 21:28:14,602 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=4 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 21:28:14,602 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_01_000017
2015-10-17 21:28:14,602 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000000_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:28:14,602 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:10 RackLocal:5
2015-10-17 21:28:14,742 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.69830835
2015-10-17 21:28:15,008 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:16,055 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:16,211 INFO [Socket Reader #1 for port 56794] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 56794: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:28:16,852 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:28:16,930 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:28:16,930 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.563847
2015-10-17 21:28:16,977 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000018 asked for a task
2015-10-17 21:28:16,977 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000018 given task: attempt_1445087491445_0004_m_000007_1
2015-10-17 21:28:17,102 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:17,289 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.7189156
2015-10-17 21:28:17,383 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.6957632
2015-10-17 21:28:17,696 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_01_000004
2015-10-17 21:28:17,696 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:28:17,696 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:10 RackLocal:5
2015-10-17 21:28:18,117 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:18,227 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.7114613
2015-10-17 21:28:19,133 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:19,868 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:28:20,149 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:20,243 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.563847
2015-10-17 21:28:20,743 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.7309332
2015-10-17 21:28:20,868 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.70840067
2015-10-17 21:28:21,149 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:21,633 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.7236889
2015-10-17 21:28:22,180 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:22,883 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:28:23,164 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:23,696 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.563847
2015-10-17 21:28:24,180 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.74262947
2015-10-17 21:28:24,180 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:24,180 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.71996474
2015-10-17 21:28:24,430 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.13104554
2015-10-17 21:28:25,024 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.7351719
2015-10-17 21:28:25,165 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:25,899 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:28:26,180 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:27,227 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:27,461 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.13104554
2015-10-17 21:28:27,540 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.7312756
2015-10-17 21:28:27,665 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.7546223
2015-10-17 21:28:27,915 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.563847
2015-10-17 21:28:28,211 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:28,571 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.74773204
2015-10-17 21:28:28,899 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:28:29,243 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:30,274 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:30,462 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.13104554
2015-10-17 21:28:30,993 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.74334353
2015-10-17 21:28:31,087 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.7663288
2015-10-17 21:28:31,258 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:31,868 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.563847
2015-10-17 21:28:31,899 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:28:31,899 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.75940764
2015-10-17 21:28:32,274 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:33,258 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:33,462 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.19117247
2015-10-17 21:28:34,274 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:34,415 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.777919
2015-10-17 21:28:34,508 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.7565067
2015-10-17 21:28:34,915 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:28:35,274 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:35,368 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.7712001
2015-10-17 21:28:35,415 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.60335505
2015-10-17 21:28:36,305 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:36,477 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.23922384
2015-10-17 21:28:37,337 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:37,837 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.7896546
2015-10-17 21:28:37,915 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.7692852
2015-10-17 21:28:37,930 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:28:38,321 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:38,805 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.78318167
2015-10-17 21:28:39,055 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.6657436
2015-10-17 21:28:39,352 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:39,477 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.23922384
2015-10-17 21:28:40,337 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:40,946 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:28:41,274 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.8012674
2015-10-17 21:28:41,337 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:41,477 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.7821103
2015-10-17 21:28:42,321 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.79570544
2015-10-17 21:28:42,337 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:42,493 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.23922384
2015-10-17 21:28:42,727 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.6669986
2015-10-17 21:28:43,368 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:43,977 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:28:44,352 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:44,712 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.8135723
2015-10-17 21:28:45,009 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.79491836
2015-10-17 21:28:45,384 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:45,493 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.32583493
2015-10-17 21:28:45,649 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.8073782
2015-10-17 21:28:46,431 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:46,571 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.6669986
2015-10-17 21:28:46,978 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:28:47,415 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:48,212 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.825567
2015-10-17 21:28:48,353 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.6669986
2015-10-17 21:28:48,415 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:48,524 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.3474171
2015-10-17 21:28:48,540 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.80852884
2015-10-17 21:28:49,149 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.8198626
2015-10-17 21:28:49,462 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:49,821 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.667
2015-10-17 21:28:50,071 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:28:50,446 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:51,446 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:51,524 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.3474171
2015-10-17 21:28:51,665 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.8380135
2015-10-17 21:28:52,118 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.8217732
2015-10-17 21:28:52,462 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:52,556 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.8315618
2015-10-17 21:28:53,087 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:28:53,400 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.667
2015-10-17 21:28:53,446 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:54,462 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:54,571 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.36072543
2015-10-17 21:28:55,150 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.849832
2015-10-17 21:28:55,446 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:55,556 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.8342367
2015-10-17 21:28:55,853 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.8437427
2015-10-17 21:28:56,087 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:28:56,462 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:57,150 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.667
2015-10-17 21:28:57,447 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:57,587 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.4556043
2015-10-17 21:28:58,493 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:28:58,493 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.8614369
2015-10-17 21:28:59,087 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.8468827
2015-10-17 21:28:59,103 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:28:59,368 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.8561118
2015-10-17 21:28:59,478 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:00,525 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:00,587 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.4556043
2015-10-17 21:29:01,197 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.667
2015-10-17 21:29:01,509 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:01,962 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.8734157
2015-10-17 21:29:02,103 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:29:02,540 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:02,540 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.8586643
2015-10-17 21:29:02,900 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.8689107
2015-10-17 21:29:03,525 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:03,603 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.4556043
2015-10-17 21:29:04,556 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:05,119 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:29:05,228 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.667
2015-10-17 21:29:05,369 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.8863718
2015-10-17 21:29:05,540 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:05,790 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.8720117
2015-10-17 21:29:06,165 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.88222635
2015-10-17 21:29:06,556 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:06,619 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.55597186
2015-10-17 21:29:07,556 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:08,134 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:29:08,572 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:08,837 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.89921534
2015-10-17 21:29:08,869 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.667
2015-10-17 21:29:09,212 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.884827
2015-10-17 21:29:09,462 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.8943036
2015-10-17 21:29:09,572 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:09,634 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.56379145
2015-10-17 21:29:10,587 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:11,134 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:29:11,572 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:12,119 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.91181767
2015-10-17 21:29:12,525 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.89752567
2015-10-17 21:29:12,619 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:12,619 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.667
2015-10-17 21:29:12,634 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.56379145
2015-10-17 21:29:12,759 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.90714276
2015-10-17 21:29:13,603 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:14,150 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:29:14,619 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:15,588 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.92492867
2015-10-17 21:29:15,634 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.61657166
2015-10-17 21:29:15,634 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:15,884 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.91187924
2015-10-17 21:29:16,166 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.9209154
2015-10-17 21:29:16,369 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.667
2015-10-17 21:29:16,650 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:17,197 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:29:17,213 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.61657166
2015-10-17 21:29:17,650 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:18,666 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:18,666 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.667
2015-10-17 21:29:18,838 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.93881917
2015-10-17 21:29:19,103 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.9245489
2015-10-17 21:29:19,431 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.9344026
2015-10-17 21:29:19,650 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:20,197 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.667
2015-10-17 21:29:20,197 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:29:20,681 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:21,666 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.667
2015-10-17 21:29:21,666 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:22,353 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.95236146
2015-10-17 21:29:22,556 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.9384893
2015-10-17 21:29:22,681 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:23,010 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.94752204
2015-10-17 21:29:23,213 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:29:23,697 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:23,807 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.667
2015-10-17 21:29:24,682 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.667
2015-10-17 21:29:24,682 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:25,713 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:25,885 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.9627921
2015-10-17 21:29:26,072 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.9490626
2015-10-17 21:29:26,213 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:29:26,322 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.9583137
2015-10-17 21:29:26,697 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:27,416 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.67625725
2015-10-17 21:29:27,728 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.66761017
2015-10-17 21:29:27,728 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:28,760 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:29,229 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:29:29,791 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:30,166 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.97563374
2015-10-17 21:29:30,244 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.96951115
2015-10-17 21:29:30,244 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.9619988
2015-10-17 21:29:30,729 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.6986642
2015-10-17 21:29:30,775 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:31,072 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.68836033
2015-10-17 21:29:31,791 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:32,244 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:29:32,791 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:33,713 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 0.98896337
2015-10-17 21:29:33,744 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.73323375
2015-10-17 21:29:33,744 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.9830029
2015-10-17 21:29:33,775 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.975335
2015-10-17 21:29:33,791 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:34,635 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.6970777
2015-10-17 21:29:34,807 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:35,260 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:29:35,838 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:36,776 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.7857281
2015-10-17 21:29:36,869 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:36,994 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 1.0
2015-10-17 21:29:36,994 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 0.99658346
2015-10-17 21:29:37,088 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 0.9887618
2015-10-17 21:29:37,838 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_0 is : 1.0
2015-10-17 21:29:37,838 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0004_m_000002_0
2015-10-17 21:29:37,838 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:29:37,838 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_01_000006 taskAttempt attempt_1445087491445_0004_m_000002_0
2015-10-17 21:29:37,838 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000002_0
2015-10-17 21:29:37,838 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:29:37,901 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:37,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:29:37,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000002_0
2015-10-17 21:29:37,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:29:37,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-17 21:29:38,010 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:10 RackLocal:5
2015-10-17 21:29:38,291 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:29:38,447 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_0 is : 1.0
2015-10-17 21:29:38,463 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0004_m_000004_0
2015-10-17 21:29:38,463 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:29:38,463 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_01_000007 taskAttempt attempt_1445087491445_0004_m_000004_0
2015-10-17 21:29:38,463 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000004_0
2015-10-17 21:29:38,463 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:29:38,526 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.7091984
2015-10-17 21:29:38,572 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:29:38,572 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000004_0
2015-10-17 21:29:38,572 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:29:38,572 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-17 21:29:38,979 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:29:39,041 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:10 RackLocal:5
2015-10-17 21:29:39,072 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_01_000006
2015-10-17 21:29:39,072 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:10 RackLocal:5
2015-10-17 21:29:39,072 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:29:39,869 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.8355365
2015-10-17 21:29:40,057 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 21:29:40,323 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 1.0
2015-10-17 21:29:41,073 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_0 is : 1.0
2015-10-17 21:29:41,073 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0004_m_000009_0
2015-10-17 21:29:41,073 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:29:41,073 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_01_000008 taskAttempt attempt_1445087491445_0004_m_000009_0
2015-10-17 21:29:41,073 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000009_0
2015-10-17 21:29:41,073 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:29:41,198 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 21:29:41,291 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_01_000007
2015-10-17 21:29:41,291 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:10 RackLocal:5
2015-10-17 21:29:41,291 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:29:41,385 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:29:41,479 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:29:41,479 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000009_0
2015-10-17 21:29:41,479 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:29:41,479 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-17 21:29:42,276 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 21:29:42,291 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:10 RackLocal:5
2015-10-17 21:29:42,369 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.7187327
2015-10-17 21:29:43,057 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.8766666
2015-10-17 21:29:43,323 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 21:29:43,526 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_01_000008
2015-10-17 21:29:43,526 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:10 RackLocal:5
2015-10-17 21:29:43,526 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:29:44,385 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 21:29:44,448 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:29:45,479 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 21:29:46,213 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.9143292
2015-10-17 21:29:46,213 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.72855705
2015-10-17 21:29:46,604 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 21:29:47,557 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:29:47,713 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 21:29:48,776 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 21:29:49,463 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.94876814
2015-10-17 21:29:49,885 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 21:29:50,104 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.7387874
2015-10-17 21:29:50,292 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:29:50,292 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:29:50,292 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_01_000019 to attempt_1445087491445_0004_m_000003_1
2015-10-17 21:29:50,292 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:18 ContRel:1 HostLocal:10 RackLocal:6
2015-10-17 21:29:50,292 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:29:50,292 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000003_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:29:50,292 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_01_000019 taskAttempt attempt_1445087491445_0004_m_000003_1
2015-10-17 21:29:50,292 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000003_1
2015-10-17 21:29:50,292 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:29:50,651 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:29:51,151 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 21:29:51,604 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 21:29:51,667 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000003_1 : 13562
2015-10-17 21:29:51,667 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000003_1] using containerId: [container_1445087491445_0004_01_000019 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 21:29:51,667 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000003_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:29:51,667 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000003
2015-10-17 21:29:52,260 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 21:29:52,651 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 0.98268235
2015-10-17 21:29:53,354 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 21:29:53,776 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:29:53,979 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000007_1 is : 1.0
2015-10-17 21:29:54,088 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0004_m_000007_1
2015-10-17 21:29:54,088 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000007_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:29:54,088 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_01_000018 taskAttempt attempt_1445087491445_0004_m_000007_1
2015-10-17 21:29:54,088 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000007_1
2015-10-17 21:29:54,088 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:29:54,088 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.751647
2015-10-17 21:29:54,448 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 21:29:54,526 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000007_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:29:54,526 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000007_1
2015-10-17 21:29:54,526 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445087491445_0004_m_000007_2
2015-10-17 21:29:54,526 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:29:54,542 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-17 21:29:54,542 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000007_2 TaskAttempt Transitioned from UNASSIGNED to KILLED
2015-10-17 21:29:54,542 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Processing the event EventType: CONTAINER_DEALLOCATE
2015-10-17 21:29:55,198 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:18 ContRel:1 HostLocal:10 RackLocal:6
2015-10-17 21:29:55,260 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 21:29:55,573 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 21:29:56,495 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_01_000018
2015-10-17 21:29:56,495 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000007_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:29:56,495 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:18 ContRel:1 HostLocal:10 RackLocal:6
2015-10-17 21:29:56,698 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:29:56,885 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:29:57,698 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:29:57,698 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:29:57,698 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_01_000020 to attempt_1445087491445_0004_m_000008_1
2015-10-17 21:29:57,698 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:19 ContRel:1 HostLocal:10 RackLocal:7
2015-10-17 21:29:57,698 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:29:57,698 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000008_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:29:57,698 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_01_000020 taskAttempt attempt_1445087491445_0004_m_000008_1
2015-10-17 21:29:57,698 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000008_1
2015-10-17 21:29:57,698 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:29:57,807 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:29:57,823 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.76441026
2015-10-17 21:29:58,417 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000008_1 : 13562
2015-10-17 21:29:58,417 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000008_1] using containerId: [container_1445087491445_0004_01_000020 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:29:58,417 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000008_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:29:58,417 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000008
2015-10-17 21:29:58,807 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 21:29:58,807 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:29:58,807 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:29:58,807 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_01_000021 to attempt_1445087491445_0004_m_000006_1
2015-10-17 21:29:58,807 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:20 ContRel:1 HostLocal:10 RackLocal:8
2015-10-17 21:29:58,807 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:29:58,823 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000006_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:29:58,823 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_01_000021 taskAttempt attempt_1445087491445_0004_m_000006_1
2015-10-17 21:29:58,823 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000006_1
2015-10-17 21:29:58,823 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:29:58,823 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:29:59,573 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000006_1 : 13562
2015-10-17 21:29:59,573 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000006_1] using containerId: [container_1445087491445_0004_01_000021 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:29:59,573 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000006_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:29:59,573 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000006
2015-10-17 21:29:59,948 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:29:59,964 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 21:30:00,010 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:30:01,089 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:01,745 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.7766362
2015-10-17 21:30:02,245 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:30:02,245 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:02,386 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000020 asked for a task
2015-10-17 21:30:02,386 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000020 given task: attempt_1445087491445_0004_m_000008_1
2015-10-17 21:30:03,214 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:30:03,323 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:03,479 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:30:03,761 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000021 asked for a task
2015-10-17 21:30:03,761 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000021 given task: attempt_1445087491445_0004_m_000006_1
2015-10-17 21:30:04,448 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:05,557 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:05,589 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.7889184
2015-10-17 21:30:06,323 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:30:06,401 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:30:06,573 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000019 asked for a task
2015-10-17 21:30:06,573 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000019 given task: attempt_1445087491445_0004_m_000003_1
2015-10-17 21:30:06,620 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:07,729 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:08,761 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:09,401 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:30:09,511 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.800385
2015-10-17 21:30:09,823 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:10,401 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.09085927
2015-10-17 21:30:10,964 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:11,526 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.13102981
2015-10-17 21:30:12,089 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:12,464 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:30:13,136 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:13,323 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.81070185
2015-10-17 21:30:13,526 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.13101445
2015-10-17 21:30:14,230 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:14,589 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.13102981
2015-10-17 21:30:15,433 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:15,620 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:30:16,495 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:16,605 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.13101445
2015-10-17 21:30:17,183 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.82260543
2015-10-17 21:30:17,589 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:17,714 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.13102981
2015-10-17 21:30:18,698 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:18,698 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:30:19,823 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.13101445
2015-10-17 21:30:19,839 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:20,792 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.17338015
2015-10-17 21:30:20,902 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.8352115
2015-10-17 21:30:20,948 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:21,870 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:30:22,105 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:22,902 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.13101445
2015-10-17 21:30:23,214 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:23,949 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.2392158
2015-10-17 21:30:24,574 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:24,870 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.84579355
2015-10-17 21:30:25,058 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.10256411
2015-10-17 21:30:25,745 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:26,105 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.13101445
2015-10-17 21:30:26,917 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:27,042 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.2392158
2015-10-17 21:30:27,980 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:28,089 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.12820514
2015-10-17 21:30:28,808 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.8557553
2015-10-17 21:30:29,011 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:30,105 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:30,214 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.2392158
2015-10-17 21:30:31,261 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.15384616
2015-10-17 21:30:31,261 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:32,324 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.19898842
2015-10-17 21:30:32,652 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:33,089 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.8650853
2015-10-17 21:30:33,371 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.2392158
2015-10-17 21:30:33,918 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:34,418 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.15384616
2015-10-17 21:30:34,871 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_1 is : 9.742042E-4
2015-10-17 21:30:35,011 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:36,027 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.21103846
2015-10-17 21:30:36,371 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:36,589 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.2392158
2015-10-17 21:30:37,480 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.8786771
2015-10-17 21:30:37,527 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.15384616
2015-10-17 21:30:37,668 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:38,261 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_1 is : 9.742042E-4
2015-10-17 21:30:39,043 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:39,340 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.2391939
2015-10-17 21:30:40,980 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:40,980 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.15384616
2015-10-17 21:30:41,402 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.8901117
2015-10-17 21:30:42,293 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:42,511 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.2391939
2015-10-17 21:30:43,652 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:43,871 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.34742883
2015-10-17 21:30:44,371 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.15384616
2015-10-17 21:30:45,496 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.904614
2015-10-17 21:30:45,496 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:45,762 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.2391939
2015-10-17 21:30:46,480 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:30:46,480 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_01_000022 to attempt_1445087491445_0004_m_000012_1
2015-10-17 21:30:46,480 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:21 ContRel:1 HostLocal:11 RackLocal:8
2015-10-17 21:30:46,480 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:30:46,480 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000012_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:30:46,480 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_01_000022 taskAttempt attempt_1445087491445_0004_m_000012_1
2015-10-17 21:30:46,480 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000012_1
2015-10-17 21:30:46,480 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:30:46,840 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:46,949 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0004_m_000003
2015-10-17 21:30:46,949 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 21:30:46,949 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445087491445_0004_m_000003
2015-10-17 21:30:46,949 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:30:46,949 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:30:46,949 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000003_2 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:30:47,262 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.34742883
2015-10-17 21:30:47,496 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:21 ContRel:1 HostLocal:11 RackLocal:8
2015-10-17 21:30:47,527 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_1 is : 0.0022783366
2015-10-17 21:30:47,715 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=5 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 21:30:47,746 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.15384616
2015-10-17 21:30:48,090 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:48,652 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000012_1 : 13562
2015-10-17 21:30:48,652 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000012_1] using containerId: [container_1445087491445_0004_01_000022 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 21:30:48,652 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000012_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:30:48,652 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000012
2015-10-17 21:30:49,043 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.25142497
2015-10-17 21:30:49,340 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:49,559 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.9176986
2015-10-17 21:30:49,684 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:30:49,684 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_01_000023 to attempt_1445087491445_0004_m_000003_2
2015-10-17 21:30:49,684 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:22 ContRel:1 HostLocal:12 RackLocal:8
2015-10-17 21:30:49,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:30:49,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000003_2 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:30:49,684 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_01_000023 taskAttempt attempt_1445087491445_0004_m_000003_2
2015-10-17 21:30:49,684 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000003_2
2015-10-17 21:30:49,684 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:30:50,574 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:50,574 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.4556358
2015-10-17 21:30:50,777 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_1 is : 0.0022783366
2015-10-17 21:30:50,840 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000003_2 : 13562
2015-10-17 21:30:50,840 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000003_2] using containerId: [container_1445087491445_0004_01_000023 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 21:30:50,840 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000003_2 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:30:50,840 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000003
2015-10-17 21:30:50,887 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 21:30:50,887 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.15384616
2015-10-17 21:30:51,840 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:52,152 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:30:52,246 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.31767103
2015-10-17 21:30:52,387 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000022 asked for a task
2015-10-17 21:30:52,387 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000022 given task: attempt_1445087491445_0004_m_000012_1
2015-10-17 21:30:53,481 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.92932343
2015-10-17 21:30:53,699 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:53,856 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.4556358
2015-10-17 21:30:54,277 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.15384616
2015-10-17 21:30:55,090 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:55,262 INFO [Socket Reader #1 for port 56794] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:30:55,543 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000023 asked for a task
2015-10-17 21:30:55,543 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000023 given task: attempt_1445087491445_0004_m_000003_2
2015-10-17 21:30:55,621 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.34738368
2015-10-17 21:30:56,340 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:57,027 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_1 is : 0.0035799937
2015-10-17 21:30:57,199 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.4556358
2015-10-17 21:30:57,465 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.94229656
2015-10-17 21:30:57,465 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.15384616
2015-10-17 21:30:57,684 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:58,793 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:30:58,871 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.34738368
2015-10-17 21:31:00,028 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:31:00,356 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_1 is : 0.0035799937
2015-10-17 21:31:00,465 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.5493558
2015-10-17 21:31:00,699 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.15384616
2015-10-17 21:31:01,246 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.9556999
2015-10-17 21:31:01,293 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:31:01,668 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.13101453
2015-10-17 21:31:02,184 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.34738368
2015-10-17 21:31:02,606 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:31:03,699 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.13105518
2015-10-17 21:31:03,746 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:31:04,043 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.15384616
2015-10-17 21:31:04,856 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.13101453
2015-10-17 21:31:04,950 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:31:05,168 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.96703637
2015-10-17 21:31:05,387 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.41719562
2015-10-17 21:31:05,403 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.56383866
2015-10-17 21:31:06,621 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_1 is : 0.0039069
2015-10-17 21:31:06,637 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:31:06,887 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.13105518
2015-10-17 21:31:07,215 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.1794872
2015-10-17 21:31:07,840 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:31:08,528 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.23922452
2015-10-17 21:31:08,621 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.4556043
2015-10-17 21:31:08,700 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.56383866
2015-10-17 21:31:09,090 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.9755573
2015-10-17 21:31:09,090 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:31:10,231 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.23924033
2015-10-17 21:31:10,325 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:31:10,981 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.1794872
2015-10-17 21:31:11,481 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:31:11,590 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.56383866
2015-10-17 21:31:11,747 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.23922452
2015-10-17 21:31:11,825 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.4556043
2015-10-17 21:31:11,856 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.667
2015-10-17 21:31:12,637 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:31:12,731 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 0.9874868
2015-10-17 21:31:12,934 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_1 is : 0.0048841164
2015-10-17 21:31:13,387 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.23924033
2015-10-17 21:31:14,372 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:31:14,372 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.1794872
2015-10-17 21:31:15,137 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.4556043
2015-10-17 21:31:15,590 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.23922452
2015-10-17 21:31:16,169 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_1 is : 0.0052083293
2015-10-17 21:31:16,325 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 1.0
2015-10-17 21:31:16,622 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.23924033
2015-10-17 21:31:17,028 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:31:17,325 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_0 is : 1.0
2015-10-17 21:31:17,559 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0004_m_000005_0
2015-10-17 21:31:17,559 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:31:17,559 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_01_000005 taskAttempt attempt_1445087491445_0004_m_000005_0
2015-10-17 21:31:17,559 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000005_0
2015-10-17 21:31:17,559 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55629
2015-10-17 21:31:18,278 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.48721883
2015-10-17 21:31:18,528 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.667
2015-10-17 21:31:19,403 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_1 is : 0.0055328356
2015-10-17 21:31:20,231 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.34740257
2015-10-17 21:31:20,512 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.34744897
2015-10-17 21:31:20,637 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:31:20,637 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000005_0
2015-10-17 21:31:20,637 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:31:20,637 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-17 21:31:20,981 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_01_000005
2015-10-17 21:31:20,981 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:22 ContRel:1 HostLocal:12 RackLocal:8
2015-10-17 21:31:20,981 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000005_0: 
2015-10-17 21:31:21,591 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.5621732
2015-10-17 21:31:21,809 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.6684927
2015-10-17 21:31:21,872 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 21:31:21,872 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.1794872
2015-10-17 21:31:22,731 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_1 is : 0.005861153
2015-10-17 21:31:23,075 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 14 maxEvents 10000
2015-10-17 21:31:23,403 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.34740257
2015-10-17 21:31:23,622 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.34744897
2015-10-17 21:31:24,184 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 14 maxEvents 10000
2015-10-17 21:31:24,700 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.56380266
2015-10-17 21:31:25,028 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.72607696
2015-10-17 21:31:25,028 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.1794872
2015-10-17 21:31:25,278 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 14 maxEvents 10000
2015-10-17 21:31:25,981 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_1 is : 0.006834552
2015-10-17 21:31:26,466 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 14 maxEvents 10000
2015-10-17 21:31:26,528 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.4556311
2015-10-17 21:31:26,825 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.35418767
2015-10-17 21:31:27,669 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 14 maxEvents 10000
2015-10-17 21:31:27,903 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.56380266
2015-10-17 21:31:28,200 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.78375995
2015-10-17 21:31:28,247 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.1794872
2015-10-17 21:31:28,731 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 14 maxEvents 10000
2015-10-17 21:31:29,200 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_1 is : 0.007812498
2015-10-17 21:31:29,794 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.4556311
2015-10-17 21:31:29,997 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 14 maxEvents 10000
2015-10-17 21:31:30,044 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.455643
2015-10-17 21:31:31,044 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.5793878
2015-10-17 21:31:31,044 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 14 maxEvents 10000
2015-10-17 21:31:31,841 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.1794872
2015-10-17 21:31:31,872 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.82568425
2015-10-17 21:31:32,341 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 14 maxEvents 10000
2015-10-17 21:31:32,419 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_1 is : 0.008462391
2015-10-17 21:31:33,263 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.455643
2015-10-17 21:31:33,466 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 14 maxEvents 10000
2015-10-17 21:31:33,466 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.4556311
2015-10-17 21:31:34,294 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.6182528
2015-10-17 21:31:34,685 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 14 maxEvents 10000
2015-10-17 21:31:34,935 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.1794872
2015-10-17 21:31:34,981 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.86276466
2015-10-17 21:31:35,700 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_1 is : 0.009117958
2015-10-17 21:31:35,825 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 14 maxEvents 10000
2015-10-17 21:31:36,575 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.48603684
2015-10-17 21:31:36,685 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.5638554
2015-10-17 21:31:37,497 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.667
2015-10-17 21:31:37,497 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.667
2015-10-17 21:31:37,966 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 14 maxEvents 10000
2015-10-17 21:31:38,200 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.1794872
2015-10-17 21:31:38,294 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.903229
2015-10-17 21:31:38,966 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_1 is : 0.009117958
2015-10-17 21:31:39,169 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 14 maxEvents 10000
2015-10-17 21:31:39,622 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.5638264
2015-10-17 21:31:39,857 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.5638554
2015-10-17 21:31:40,403 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 14 maxEvents 10000
2015-10-17 21:31:40,716 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.667
2015-10-17 21:31:41,450 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.1794872
2015-10-17 21:31:41,450 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 0.95278
2015-10-17 21:31:41,654 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 14 maxEvents 10000
2015-10-17 21:31:42,747 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 14 maxEvents 10000
2015-10-17 21:31:43,013 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.5638264
2015-10-17 21:31:43,060 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.5638554
2015-10-17 21:31:43,857 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.667
2015-10-17 21:31:43,888 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 14 maxEvents 10000
2015-10-17 21:31:44,122 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000006_1 is : 1.0
2015-10-17 21:31:44,216 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0004_m_000006_1
2015-10-17 21:31:44,216 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000006_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:31:44,216 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_01_000021 taskAttempt attempt_1445087491445_0004_m_000006_1
2015-10-17 21:31:44,232 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000006_1
2015-10-17 21:31:44,232 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:31:44,638 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.1794872
2015-10-17 21:31:45,044 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.5638554
2015-10-17 21:31:45,044 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000006_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:31:45,044 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000006_1
2015-10-17 21:31:45,044 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:31:45,044 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-17 21:31:45,482 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 14 maxEvents 10000
2015-10-17 21:31:45,904 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:22 ContRel:1 HostLocal:12 RackLocal:8
2015-10-17 21:31:46,044 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_01_000021
2015-10-17 21:31:46,044 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:22 ContRel:1 HostLocal:12 RackLocal:8
2015-10-17 21:31:46,044 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000006_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:31:46,169 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.6518088
2015-10-17 21:31:46,247 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.667
2015-10-17 21:31:46,497 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.6518088
2015-10-17 21:31:46,622 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:31:47,044 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.667
2015-10-17 21:31:47,810 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:31:48,185 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_1 is : 0.009767835
2015-10-17 21:31:48,263 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.20512822
2015-10-17 21:31:48,904 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:31:49,357 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.667
2015-10-17 21:31:49,404 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.667
2015-10-17 21:31:49,951 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:31:50,232 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.70554376
2015-10-17 21:31:51,029 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:31:51,341 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.23076925
2015-10-17 21:31:52,107 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:31:52,435 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.667
2015-10-17 21:31:52,560 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.667
2015-10-17 21:31:53,326 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.74371
2015-10-17 21:31:53,779 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:31:54,341 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_1 is : 0.01693332
2015-10-17 21:31:54,419 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.23076925
2015-10-17 21:31:55,216 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:31:55,701 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.66865015
2015-10-17 21:31:55,841 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.6852816
2015-10-17 21:31:56,341 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:31:56,748 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.77179754
2015-10-17 21:31:57,451 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_1 is : 0.01693332
2015-10-17 21:31:57,779 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.23076925
2015-10-17 21:31:57,904 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:31:59,248 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.7290363
2015-10-17 21:31:59,482 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:31:59,998 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.71003807
2015-10-17 21:32:01,060 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:32:01,498 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.25641027
2015-10-17 21:32:02,263 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:32:03,576 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.76063967
2015-10-17 21:32:03,701 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:32:04,685 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.8206778
2015-10-17 21:32:04,748 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.25641027
2015-10-17 21:32:04,998 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:32:05,435 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.7747685
2015-10-17 21:32:06,295 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:32:06,920 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.8022096
2015-10-17 21:32:07,592 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:32:08,092 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.9508305
2015-10-17 21:32:08,732 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.25641027
2015-10-17 21:32:08,842 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.849774
2015-10-17 21:32:08,904 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:32:10,201 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:32:10,482 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.8436016
2015-10-17 21:32:11,310 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 0.9982238
2015-10-17 21:32:11,654 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000008_1 is : 1.0
2015-10-17 21:32:11,670 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:32:11,904 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0004_m_000008_1
2015-10-17 21:32:11,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000008_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:32:11,904 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_01_000020 taskAttempt attempt_1445087491445_0004_m_000008_1
2015-10-17 21:32:11,904 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000008_1
2015-10-17 21:32:11,904 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:32:12,029 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.25641027
2015-10-17 21:32:12,232 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.8915297
2015-10-17 21:32:12,982 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:32:13,998 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.88656986
2015-10-17 21:32:15,623 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.9329901
2015-10-17 21:32:15,701 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_01_000020
2015-10-17 21:32:15,701 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:22 ContRel:1 HostLocal:12 RackLocal:8
2015-10-17 21:32:15,701 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000008_1: 
2015-10-17 21:32:15,936 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:32:15,951 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.25641027
2015-10-17 21:32:17,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000008_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:32:17,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000008_1
2015-10-17 21:32:17,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:32:17,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-17 21:32:17,373 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.9303516
2015-10-17 21:32:17,592 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 15 maxEvents 10000
2015-10-17 21:32:18,076 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:11 CompletedReds:0 ContAlloc:22 ContRel:1 HostLocal:12 RackLocal:8
2015-10-17 21:32:19,186 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 16 maxEvents 10000
2015-10-17 21:32:19,201 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 0.97739506
2015-10-17 21:32:19,623 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.25641027
2015-10-17 21:32:20,936 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 16 maxEvents 10000
2015-10-17 21:32:21,045 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.96667546
2015-10-17 21:32:22,248 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 16 maxEvents 10000
2015-10-17 21:32:22,279 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000012_1 is : 1.0
2015-10-17 21:32:22,592 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0004_m_000012_1
2015-10-17 21:32:22,592 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000012_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:32:22,592 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_01_000022 taskAttempt attempt_1445087491445_0004_m_000012_1
2015-10-17 21:32:22,592 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000012_1
2015-10-17 21:32:22,592 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:32:23,139 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.2820513
2015-10-17 21:32:23,670 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 16 maxEvents 10000
2015-10-17 21:32:24,123 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000012_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:32:24,123 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000012_1
2015-10-17 21:32:24,123 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000012 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:32:24,123 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 12
2015-10-17 21:32:24,498 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_01_000022
2015-10-17 21:32:24,498 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:12 CompletedReds:0 ContAlloc:22 ContRel:1 HostLocal:12 RackLocal:8
2015-10-17 21:32:24,498 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000012_1: 
2015-10-17 21:32:24,498 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 0.98483133
2015-10-17 21:32:25,045 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 16 maxEvents 10000
2015-10-17 21:32:26,561 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 17 maxEvents 10000
2015-10-17 21:32:26,780 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_2 is : 1.0
2015-10-17 21:32:27,061 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.2820513
2015-10-17 21:32:27,670 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0004_m_000003_2
2015-10-17 21:32:27,670 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000003_2 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:32:27,670 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_01_000023 taskAttempt attempt_1445087491445_0004_m_000003_2
2015-10-17 21:32:27,670 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000003_2
2015-10-17 21:32:27,670 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:32:27,780 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 17 maxEvents 10000
2015-10-17 21:32:29,295 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 17 maxEvents 10000
2015-10-17 21:32:30,233 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.30769232
2015-10-17 21:32:30,530 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_01_000023
2015-10-17 21:32:30,530 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:12 CompletedReds:0 ContAlloc:22 ContRel:1 HostLocal:12 RackLocal:8
2015-10-17 21:32:30,530 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000003_2: 
2015-10-17 21:32:30,530 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000003_1 is : 0.017256951
2015-10-17 21:32:30,748 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 17 maxEvents 10000
2015-10-17 21:32:32,295 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 17 maxEvents 10000
2015-10-17 21:32:33,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000003_2 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:32:33,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000003_2
2015-10-17 21:32:33,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445087491445_0004_m_000003_1
2015-10-17 21:32:33,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:32:33,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 13
2015-10-17 21:32:33,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000003_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:32:33,311 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_01_000019 taskAttempt attempt_1445087491445_0004_m_000003_1
2015-10-17 21:32:33,311 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000003_1
2015-10-17 21:32:33,311 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:32:33,358 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000003_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:32:33,358 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:32:33,530 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0004_m_000003
2015-10-17 21:32:33,530 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 21:32:33,545 INFO [Socket Reader #1 for port 56794] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 56794: readAndProcess from client *********** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:32:33,608 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:13 CompletedReds:0 ContAlloc:22 ContRel:1 HostLocal:12 RackLocal:8
2015-10-17 21:32:33,795 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.30769232
2015-10-17 21:32:33,936 WARN [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out1/_temporary/1/_temporary/attempt_1445087491445_0004_m_000003_1
2015-10-17 21:32:33,936 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000003_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:32:33,999 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 17 maxEvents 10000
2015-10-17 21:32:36,327 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_01_000019
2015-10-17 21:32:36,327 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:13 CompletedReds:0 ContAlloc:22 ContRel:1 HostLocal:12 RackLocal:8
2015-10-17 21:32:36,327 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000003_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:32:36,702 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_0. startIndex 18 maxEvents 10000
2015-10-17 21:32:36,733 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.30769232
2015-10-17 21:32:37,374 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.33579627
2015-10-17 21:32:40,405 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.3558988
2015-10-17 21:32:43,421 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.37634596
2015-10-17 21:32:46,639 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.39082953
2015-10-17 21:32:49,749 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.4081533
2015-10-17 21:32:52,780 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.42870522
2015-10-17 21:32:55,796 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.4491903
2015-10-17 21:32:58,905 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.46558475
2015-10-17 21:33:02,077 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.48744652
2015-10-17 21:33:05,124 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.5082811
2015-10-17 21:33:08,187 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.5288032
2015-10-17 21:33:11,437 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.55082166
2015-10-17 21:33:14,453 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.57089376
2015-10-17 21:33:17,500 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.59120286
2015-10-17 21:33:20,546 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.6087557
2015-10-17 21:33:23,578 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.62903666
2015-10-17 21:33:26,625 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.64141786
2015-10-17 21:33:29,625 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.659475
2015-10-17 21:33:30,812 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.659475
2015-10-17 21:33:32,672 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.66807884
2015-10-17 21:33:35,687 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.6703507
2015-10-17 21:33:38,703 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.67361414
2015-10-17 21:33:41,719 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.6771682
2015-10-17 21:33:44,766 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.6801183
2015-10-17 21:33:47,782 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.6824919
2015-10-17 21:33:50,829 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.6839482
2015-10-17 21:33:53,844 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.68455416
2015-10-17 21:33:57,000 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.6850751
2015-10-17 21:34:00,110 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.68561274
2015-10-17 21:34:03,251 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.68628603
2015-10-17 21:34:06,329 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.68712837
2015-10-17 21:34:09,423 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.6880488
2015-10-17 21:34:12,563 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.6887217
2015-10-17 21:34:15,626 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.68959534
2015-10-17 21:34:18,720 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.6906095
2015-10-17 21:34:21,814 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.6915748
2015-10-17 21:34:24,876 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.6926844
2015-10-17 21:34:27,986 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.6962807
2015-10-17 21:34:31,033 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.699306
2015-10-17 21:34:34,048 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.70253706
2015-10-17 21:34:37,064 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.70526683
2015-10-17 21:34:40,080 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.70753527
2015-10-17 21:34:43,095 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7104126
2015-10-17 21:34:46,111 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7131186
2015-10-17 21:34:49,127 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.71532357
2015-10-17 21:34:52,142 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7171758
2015-10-17 21:34:55,158 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.71755785
2015-10-17 21:34:58,158 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.71784496
2015-10-17 21:35:01,174 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7179564
2015-10-17 21:35:04,189 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7180722
2015-10-17 21:35:07,205 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7181442
2015-10-17 21:35:10,220 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.71822786
2015-10-17 21:35:13,236 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7182516
2015-10-17 21:35:16,252 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7185858
2015-10-17 21:35:19,267 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7205403
2015-10-17 21:35:22,283 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7226591
2015-10-17 21:35:25,298 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7249907
2015-10-17 21:35:28,314 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.72745687
2015-10-17 21:35:31,517 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7296042
2015-10-17 21:35:34,533 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7319125
2015-10-17 21:35:37,564 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.73408204
2015-10-17 21:35:40,580 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7371899
2015-10-17 21:35:43,579 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7392711
2015-10-17 21:35:46,595 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.74118185
2015-10-17 21:35:49,642 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7436406
2015-10-17 21:35:52,673 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7458198
2015-10-17 21:35:55,689 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.74806494
2015-10-17 21:35:58,720 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.750754
2015-10-17 21:36:01,736 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.75271595
2015-10-17 21:36:04,751 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7564068
2015-10-17 21:36:07,782 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7588203
2015-10-17 21:36:10,798 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.76111466
2015-10-17 21:36:13,829 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7635728
2015-10-17 21:36:16,845 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7653848
2015-10-17 21:36:19,892 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7671646
2015-10-17 21:36:22,907 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7694721
2015-10-17 21:36:25,954 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7717091
2015-10-17 21:36:28,985 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7737799
2015-10-17 21:36:32,017 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7763662
2015-10-17 21:36:35,063 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7783966
2015-10-17 21:36:38,095 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.78070873
2015-10-17 21:36:41,110 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.783635
2015-10-17 21:36:44,126 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7860668
2015-10-17 21:36:47,141 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.78817266
2015-10-17 21:36:50,141 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7903971
2015-10-17 21:36:53,173 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7929214
2015-10-17 21:36:56,173 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7953206
2015-10-17 21:36:59,204 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.7975743
2015-10-17 21:37:02,219 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8000138
2015-10-17 21:37:05,235 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.802128
2015-10-17 21:37:08,235 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8048661
2015-10-17 21:37:11,251 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.80800396
2015-10-17 21:37:14,282 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.81018716
2015-10-17 21:37:17,297 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.81236506
2015-10-17 21:37:20,313 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8144649
2015-10-17 21:37:23,344 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8168746
2015-10-17 21:37:26,360 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.81890595
2015-10-17 21:37:29,375 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.82117414
2015-10-17 21:37:32,422 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.82327056
2015-10-17 21:37:35,485 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.82537276
2015-10-17 21:37:38,532 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8275181
2015-10-17 21:37:41,563 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8290725
2015-10-17 21:37:44,625 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8305162
2015-10-17 21:37:47,703 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.83184165
2015-10-17 21:37:50,766 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.83283365
2015-10-17 21:37:53,844 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.83393884
2015-10-17 21:37:56,906 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8345533
2015-10-17 21:37:59,969 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.83550876
2015-10-17 21:38:03,031 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8364949
2015-10-17 21:38:06,125 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.83752346
2015-10-17 21:38:09,188 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8385197
2015-10-17 21:38:12,250 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8395598
2015-10-17 21:38:15,313 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8405282
2015-10-17 21:38:18,406 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.84165514
2015-10-17 21:38:21,469 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.84281003
2015-10-17 21:38:24,531 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.84391165
2015-10-17 21:38:27,609 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8450279
2015-10-17 21:38:30,656 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8460909
2015-10-17 21:38:33,719 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.84725904
2015-10-17 21:38:37,672 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8483874
2015-10-17 21:38:40,734 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8493665
2015-10-17 21:38:43,812 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.85004187
2015-10-17 21:38:46,875 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.85077596
2015-10-17 21:38:49,906 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.85143256
2015-10-17 21:38:53,031 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.85194653
2015-10-17 21:38:56,109 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.85265195
2015-10-17 21:38:59,156 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8532986
2015-10-17 21:39:02,203 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.853608
2015-10-17 21:39:05,265 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8541695
2015-10-17 21:39:08,359 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8548145
2015-10-17 21:39:11,437 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.85543334
2015-10-17 21:39:14,531 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8562454
2015-10-17 21:39:17,593 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.85715055
2015-10-17 21:39:20,640 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.85809845
2015-10-17 21:39:23,734 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8590367
2015-10-17 21:39:26,796 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.85967577
2015-10-17 21:39:29,890 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8603089
2015-10-17 21:39:32,968 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8610121
2015-10-17 21:39:36,062 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8617649
2015-10-17 21:39:39,124 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8622085
2015-10-17 21:39:42,171 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.86287415
2015-10-17 21:39:45,249 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8636643
2015-10-17 21:39:48,296 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8641684
2015-10-17 21:39:51,374 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8647374
2015-10-17 21:39:54,421 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.86558783
2015-10-17 21:39:57,484 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8666308
2015-10-17 21:40:00,531 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8671803
2015-10-17 21:40:04,031 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.86783713
2015-10-17 21:40:07,078 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8686581
2015-10-17 21:40:10,125 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8697607
2015-10-17 21:40:13,171 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8709296
2015-10-17 21:40:16,234 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.87247
2015-10-17 21:40:19,281 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8738524
2015-10-17 21:40:22,297 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8753027
2015-10-17 21:40:25,359 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8768567
2015-10-17 21:40:28,390 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8782831
2015-10-17 21:40:31,453 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.87968683
2015-10-17 21:40:34,516 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8811362
2015-10-17 21:40:37,547 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.88260156
2015-10-17 21:40:40,594 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8841677
2015-10-17 21:40:43,656 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.88584185
2015-10-17 21:40:46,703 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8869263
2015-10-17 21:40:49,797 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.887906
2015-10-17 21:40:52,860 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.88840777
2015-10-17 21:40:55,922 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8891992
2015-10-17 21:40:58,985 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8899869
2015-10-17 21:41:02,063 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8910257
2015-10-17 21:41:05,125 INFO [IPC Server handler 6 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8920652
2015-10-17 21:41:08,188 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.893117
2015-10-17 21:41:11,219 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8938916
2015-10-17 21:41:14,282 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8950277
2015-10-17 21:41:17,329 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.89551806
2015-10-17 21:41:20,407 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.89619887
2015-10-17 21:41:23,516 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.89696246
2015-10-17 21:41:26,610 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.89759177
2015-10-17 21:41:29,938 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8981339
2015-10-17 21:41:33,001 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.89840496
2015-10-17 21:41:36,063 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8991016
2015-10-17 21:41:39,110 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.8999733
2015-10-17 21:41:39,751 WARN [ResponseProcessor for block BP-1347369012-**************-1444972147527:blk_1073742862_2058] org.apache.hadoop.hdfs.DFSClient: Slow ReadProcessor read fields took 65036ms (threshold=30000ms); ack: seqno: -2 status: SUCCESS status: ERROR downstreamAckTimeNanos: 0, targets: [***********:50010, *************:50010]
2015-10-17 21:41:39,751 WARN [ResponseProcessor for block BP-1347369012-**************-1444972147527:blk_1073742862_2058] org.apache.hadoop.hdfs.DFSClient: DFSOutputStream ResponseProcessor exception  for block BP-1347369012-**************-1444972147527:blk_1073742862_2058
java.io.IOException: Bad response ERROR for block BP-1347369012-**************-1444972147527:blk_1073742862_2058 from datanode *************:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer$ResponseProcessor.run(DFSOutputStream.java:898)
2015-10-17 21:41:39,767 WARN [DataStreamer for file /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0004/job_1445087491445_0004_1.jhist block BP-1347369012-**************-1444972147527:blk_1073742862_2058] org.apache.hadoop.hdfs.DFSClient: Error Recovery for block BP-1347369012-**************-1444972147527:blk_1073742862_2058 in pipeline ***********:50010, *************:50010: bad datanode *************:50010
2015-10-17 21:41:42,189 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9008695
2015-10-17 21:41:45,267 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9016627
2015-10-17 21:41:48,329 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9024454
2015-10-17 21:41:51,376 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.90310365
2015-10-17 21:41:54,454 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9039397
2015-10-17 21:41:57,517 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9046691
2015-10-17 21:42:00,579 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.90543354
2015-10-17 21:42:03,673 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9061385
2015-10-17 21:42:06,736 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.90678406
2015-10-17 21:42:09,798 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.90741765
2015-10-17 21:42:12,892 INFO [IPC Server handler 19 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9081584
2015-10-17 21:42:15,955 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9089078
2015-10-17 21:42:19,017 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.90959144
2015-10-17 21:42:22,080 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9103466
2015-10-17 21:42:25,158 INFO [IPC Server handler 11 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9112804
2015-10-17 21:42:28,221 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.912243
2015-10-17 21:42:31,314 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9130924
2015-10-17 21:42:34,393 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.91392815
2015-10-17 21:42:37,424 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.91483605
2015-10-17 21:42:40,486 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.91584015
2015-10-17 21:42:43,549 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.91707015
2015-10-17 21:42:46,643 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9181691
2015-10-17 21:42:49,690 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.91923815
2015-10-17 21:42:52,737 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.92008036
2015-10-17 21:42:55,799 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.92121935
2015-10-17 21:42:58,862 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.92225415
2015-10-17 21:43:01,940 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9233663
2015-10-17 21:43:05,034 INFO [IPC Server handler 17 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9245127
2015-10-17 21:43:08,128 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.92548037
2015-10-17 21:43:11,206 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9263114
2015-10-17 21:43:14,299 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9273248
2015-10-17 21:43:17,362 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9283364
2015-10-17 21:43:20,471 INFO [IPC Server handler 15 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9292902
2015-10-17 21:43:23,565 INFO [IPC Server handler 22 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9302155
2015-10-17 21:43:26,612 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.93167996
2015-10-17 21:43:29,690 INFO [IPC Server handler 12 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.93323386
2015-10-17 21:43:32,753 INFO [IPC Server handler 27 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9342084
2015-10-17 21:43:35,815 INFO [IPC Server handler 9 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.935289
2015-10-17 21:43:38,862 INFO [IPC Server handler 14 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9363284
2015-10-17 21:43:41,894 INFO [IPC Server handler 1 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9373441
2015-10-17 21:43:45,003 INFO [IPC Server handler 25 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9383257
2015-10-17 21:43:48,066 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9399576
2015-10-17 21:43:51,113 INFO [IPC Server handler 3 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.94217277
2015-10-17 21:43:54,144 INFO [IPC Server handler 10 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9444305
2015-10-17 21:43:57,206 INFO [IPC Server handler 16 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9467523
2015-10-17 21:44:00,253 INFO [IPC Server handler 20 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9494393
2015-10-17 21:44:03,363 INFO [IPC Server handler 21 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.95205915
2015-10-17 21:44:06,425 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.95457166
2015-10-17 21:44:09,472 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9567042
2015-10-17 21:44:12,488 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9590725
2015-10-17 21:44:15,504 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9613522
2015-10-17 21:44:18,535 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.96346897
2015-10-17 21:44:21,535 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.96577626
2015-10-17 21:44:24,566 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.96815014
2015-10-17 21:44:27,566 INFO [IPC Server handler 4 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.97119284
2015-10-17 21:44:30,582 INFO [IPC Server handler 28 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.97467494
2015-10-17 21:44:33,613 INFO [IPC Server handler 0 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9772471
2015-10-17 21:44:36,645 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.98016334
2015-10-17 21:44:39,676 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9822384
2015-10-17 21:44:42,692 INFO [IPC Server handler 2 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.98483425
2015-10-17 21:44:45,723 INFO [IPC Server handler 13 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9874805
2015-10-17 21:44:48,770 INFO [IPC Server handler 7 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.98998725
2015-10-17 21:44:51,817 INFO [IPC Server handler 24 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.99166185
2015-10-17 21:44:54,864 INFO [IPC Server handler 23 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.99329275
2015-10-17 21:44:57,942 INFO [IPC Server handler 5 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9949335
2015-10-17 21:45:01,036 INFO [IPC Server handler 29 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.99650395
2015-10-17 21:45:04,114 INFO [IPC Server handler 26 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.9976426
2015-10-17 21:45:07,192 INFO [IPC Server handler 8 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.99875355
2015-10-17 21:45:10,255 INFO [IPC Server handler 18 on 56794] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_0 is : 0.99970984
2015-10-17 21:45:11,098 ERROR [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Error communicating with RM: Resource Manager doesn't recognize AttemptId: application_1445087491445_0004
org.apache.hadoop.yarn.exceptions.YarnRuntimeException: Resource Manager doesn't recognize AttemptId: application_1445087491445_0004
	at org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator.getResources(RMContainerAllocator.java:675)
	at org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator.heartbeat(RMContainerAllocator.java:244)
	at org.apache.hadoop.mapreduce.v2.app.rm.RMCommunicator$1.run(RMCommunicator.java:282)
	at java.lang.Thread.run(Thread.java:724)
Caused by: org.apache.hadoop.yarn.exceptions.ApplicationAttemptNotFoundException: Application attempt appattempt_1445087491445_0004_000001 doesn't exist in ApplicationMasterService cache.
	at org.apache.hadoop.yarn.server.resourcemanager.ApplicationMasterService.allocate(ApplicationMasterService.java:436)
	at org.apache.hadoop.yarn.api.impl.pb.service.ApplicationMasterProtocolPBServiceImpl.allocate(ApplicationMasterProtocolPBServiceImpl.java:60)
	at org.apache.hadoop.yarn.proto.ApplicationMasterProtocol$ApplicationMasterProtocolService$2.callBlockingMethod(ApplicationMasterProtocol.java:99)
	at org.apache.hadoop.ipc.ProtobufRpcEngine$Server$ProtoBufRpcInvoker.call(ProtobufRpcEngine.java:619)
	at org.apache.hadoop.ipc.RPC$Server.call(RPC.java:962)
	at org.apache.hadoop.ipc.Server$Handler$1.run(Server.java:2039)
	at org.apache.hadoop.ipc.Server$Handler$1.run(Server.java:2035)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.ipc.Server$Handler.run(Server.java:2033)

	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:57)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:526)
	at org.apache.hadoop.yarn.ipc.RPCUtil.instantiateException(RPCUtil.java:53)
	at org.apache.hadoop.yarn.ipc.RPCUtil.unwrapAndThrowException(RPCUtil.java:101)
	at org.apache.hadoop.yarn.api.impl.pb.client.ApplicationMasterProtocolPBClientImpl.allocate(ApplicationMasterProtocolPBClientImpl.java:79)
	at sun.reflect.GeneratedMethodAccessor3.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:606)
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:187)
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:102)
	at com.sun.proxy.$Proxy36.allocate(Unknown Source)
	at org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor.makeRemoteRequest(RMContainerRequestor.java:188)
	at org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator.getResources(RMContainerAllocator.java:667)
	... 3 more
Caused by: org.apache.hadoop.ipc.RemoteException(org.apache.hadoop.yarn.exceptions.ApplicationAttemptNotFoundException): Application attempt appattempt_1445087491445_0004_000001 doesn't exist in ApplicationMasterService cache.
	at org.apache.hadoop.yarn.server.resourcemanager.ApplicationMasterService.allocate(ApplicationMasterService.java:436)
	at org.apache.hadoop.yarn.api.impl.pb.service.ApplicationMasterProtocolPBServiceImpl.allocate(ApplicationMasterProtocolPBServiceImpl.java:60)
	at org.apache.hadoop.yarn.proto.ApplicationMasterProtocol$ApplicationMasterProtocolService$2.callBlockingMethod(ApplicationMasterProtocol.java:99)
	at org.apache.hadoop.ipc.ProtobufRpcEngine$Server$ProtoBufRpcInvoker.call(ProtobufRpcEngine.java:619)
	at org.apache.hadoop.ipc.RPC$Server.call(RPC.java:962)
	at org.apache.hadoop.ipc.Server$Handler$1.run(Server.java:2039)
	at org.apache.hadoop.ipc.Server$Handler$1.run(Server.java:2035)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.ipc.Server$Handler.run(Server.java:2033)

	at org.apache.hadoop.ipc.Client.call(Client.java:1468)
	at org.apache.hadoop.ipc.Client.call(Client.java:1399)
	at org.apache.hadoop.ipc.ProtobufRpcEngine$Invoker.invoke(ProtobufRpcEngine.java:232)
	at com.sun.proxy.$Proxy35.allocate(Unknown Source)
	at org.apache.hadoop.yarn.api.impl.pb.client.ApplicationMasterProtocolPBClientImpl.allocate(ApplicationMasterProtocolPBClientImpl.java:77)
	... 11 more
2015-10-17 21:45:11,098 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0004Job Transitioned from RUNNING to REBOOT
2015-10-17 21:45:11,098 INFO [Thread-143] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: false
2015-10-17 21:45:11,098 INFO [Thread-143] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: false
2015-10-17 21:45:11,098 INFO [Thread-143] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: false
2015-10-17 21:45:11,098 INFO [Thread-143] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is false
2015-10-17 21:45:11,098 INFO [Thread-143] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-17 21:45:11,114 INFO [Thread-143] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
