2015-10-17 23:35:24,092 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 23:35:24,263 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 23:35:24,263 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: ReduceTask metrics system started
2015-10-17 23:35:24,388 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 23:35:24,388 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445094324383_0002, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@3acd9e86)
2015-10-17 23:35:24,639 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 23:35:25,467 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445094324383_0002
2015-10-17 23:35:26,842 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 23:35:27,967 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 23:35:28,014 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@3c28dd37
2015-10-17 23:35:28,092 INFO [main] org.apache.hadoop.mapred.ReduceTask: Using ShuffleConsumerPlugin: org.apache.hadoop.mapreduce.task.reduce.Shuffle@7400e044
2015-10-17 23:35:28,154 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: MergerManager: memoryLimit=130652568, maxSingleShuffleLimit=32663142, mergeThreshold=86230696, ioSortFactor=10, memToMemMergeOutputsThreshold=10
2015-10-17 23:35:28,186 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445094324383_0002_r_000000_1 Thread started: EventFetcher for fetching Map Completion Events
2015-10-17 23:35:28,186 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445094324383_0002_r_000000_1: Got 10 new map-outputs
2015-10-17 23:35:28,201 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 6 to fetcher#2
2015-10-17 23:35:28,201 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 4 to fetcher#1
2015-10-17 23:35:28,201 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 4 of 4 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-17 23:35:28,201 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 6 of 6 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#2
2015-10-17 23:35:28,748 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445094324383_0002&reduce=0&map=attempt_1445094324383_0002_m_000009_0,attempt_1445094324383_0002_m_000005_0,attempt_1445094324383_0002_m_000008_0,attempt_1445094324383_0002_m_000001_0 sent hash and received reply
2015-10-17 23:35:28,764 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445094324383_0002_m_000009_0: Shuffling to disk since 172334804 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 23:35:28,764 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445094324383_0002&reduce=0&map=attempt_1445094324383_0002_m_000000_0,attempt_1445094324383_0002_m_000004_0,attempt_1445094324383_0002_m_000003_0,attempt_1445094324383_0002_m_000006_0,attempt_1445094324383_0002_m_000002_0,attempt_1445094324383_0002_m_000007_0 sent hash and received reply
2015-10-17 23:35:28,764 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445094324383_0002_m_000000_0: Shuffling to disk since 216988123 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 23:35:28,764 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445094324383_0002_m_000009_0 decomp: 172334804 len: 172334808 to DISK
2015-10-17 23:35:28,779 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#2 about to shuffle output of map attempt_1445094324383_0002_m_000000_0 decomp: 216988123 len: 216988127 to DISK
2015-10-17 23:36:51,626 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 172334808 bytes from map-output for attempt_1445094324383_0002_m_000009_0
2015-10-17 23:36:51,782 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445094324383_0002_m_000005_0: Shuffling to disk since 216990140 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 23:36:51,798 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445094324383_0002_m_000005_0 decomp: 216990140 len: 216990144 to DISK
2015-10-17 23:37:20,799 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216988127 bytes from map-output for attempt_1445094324383_0002_m_000000_0
2015-10-17 23:37:20,815 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445094324383_0002_m_000004_0: Shuffling to disk since 216999709 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 23:37:20,815 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#2 about to shuffle output of map attempt_1445094324383_0002_m_000004_0 decomp: 216999709 len: 216999713 to DISK
2015-10-17 23:39:40,649 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216999713 bytes from map-output for attempt_1445094324383_0002_m_000004_0
2015-10-17 23:39:40,665 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445094324383_0002_m_000003_0: Shuffling to disk since 216972750 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 23:39:40,665 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#2 about to shuffle output of map attempt_1445094324383_0002_m_000003_0 decomp: 216972750 len: 216972754 to DISK
2015-10-17 23:40:07,651 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216990144 bytes from map-output for attempt_1445094324383_0002_m_000005_0
2015-10-17 23:40:07,666 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445094324383_0002_m_000008_0: Shuffling to disk since 217015228 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 23:40:07,666 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445094324383_0002_m_000008_0 decomp: 217015228 len: 217015232 to DISK
2015-10-17 23:41:34,155 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216972754 bytes from map-output for attempt_1445094324383_0002_m_000003_0
2015-10-17 23:41:34,170 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445094324383_0002_m_000006_0: Shuffling to disk since 217011663 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 23:41:34,170 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#2 about to shuffle output of map attempt_1445094324383_0002_m_000006_0 decomp: 217011663 len: 217011667 to DISK
2015-10-17 23:41:53,687 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217015232 bytes from map-output for attempt_1445094324383_0002_m_000008_0
2015-10-17 23:41:53,703 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445094324383_0002_m_000001_0: Shuffling to disk since 217009502 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 23:41:53,703 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445094324383_0002_m_000001_0 decomp: 217009502 len: 217009506 to DISK
2015-10-17 23:43:07,112 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217011667 bytes from map-output for attempt_1445094324383_0002_m_000006_0
2015-10-17 23:43:07,112 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445094324383_0002_m_000002_0: Shuffling to disk since 216991624 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 23:43:07,112 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#2 about to shuffle output of map attempt_1445094324383_0002_m_000002_0 decomp: 216991624 len: 216991628 to DISK
