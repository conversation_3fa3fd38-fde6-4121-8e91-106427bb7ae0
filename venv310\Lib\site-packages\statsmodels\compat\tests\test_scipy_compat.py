from numpy.testing import assert_equal
from statsmodels.compat.scipy import _next_regular


def test_next_regular():
    hams = {
        1: 1,
        2: 2,
        3: 3,
        4: 4,
        5: 5,
        6: 6,
        7: 8,
        8: 8,
        14: 15,
        15: 15,
        16: 16,
        17: 18,
        1021: 1024,
        1536: 1536,
        51200000: 51200000,
        510183360: 510183360,
        510183360 + 1: 512000000,
        511000000: 512000000,
        854296875: 854296875,
        854296875 + 1: 859963392,
        196608000000: 196608000000,
        196608000000 + 1: 196830000000,
        8789062500000: 8789062500000,
        8789062500000 + 1: 8796093022208,
        206391214080000: 206391214080000,
        206391214080000 + 1: 206624260800000,
        470184984576000: 470184984576000,
        470184984576000 + 1: 470715894135000,
        7222041363087360: 7222041363087360,
        7222041363087360 + 1: 7230196133913600,
        # power of 5    5**23
        11920928955078125: 11920928955078125,
        11920928955078125 - 1: 11920928955078125,
        # power of 3    3**34
        16677181699666569: 16677181699666569,
        16677181699666569 - 1: 16677181699666569,
        # power of 2   2**54
        18014398509481984: 18014398509481984,
        18014398509481984 - 1: 18014398509481984,
        # above this, int(ceil(n)) == int(ceil(n+1))
        19200000000000000: 19200000000000000,
        19200000000000000 + 1: 19221679687500000,
        288230376151711744: 288230376151711744,
        288230376151711744 + 1: 288325195312500000,
        288325195312500000 - 1: 288325195312500000,
        288325195312500000: 288325195312500000,
        288325195312500000 + 1: 288555831593533440,
        # power of 3    3**83
        3 ** 83 - 1: 3 ** 83,
        3 ** 83: 3 ** 83,
        # power of 2     2**135
        2 ** 135 - 1: 2 ** 135,
        2 ** 135: 2 ** 135,
        # power of 5      5**57
        5 ** 57 - 1: 5 ** 57,
        5 ** 57: 5 ** 57,
        # http://www.drdobbs.com/228700538
        # 2**96 * 3**1 * 5**13
        2 ** 96 * 3 ** 1 * 5 ** 13 - 1: 2 ** 96 * 3 ** 1 * 5 ** 13,
        2 ** 96 * 3 ** 1 * 5 ** 13: 2 ** 96 * 3 ** 1 * 5 ** 13,
        2 ** 96 * 3 ** 1 * 5 ** 13 + 1: 2 ** 43 * 3 ** 11 * 5 ** 29,
        # 2**36 * 3**69 * 5**7
        2 ** 36 * 3 ** 69 * 5 ** 7 - 1: 2 ** 36 * 3 ** 69 * 5 ** 7,
        2 ** 36 * 3 ** 69 * 5 ** 7: 2 ** 36 * 3 ** 69 * 5 ** 7,
        2 ** 36 * 3 ** 69 * 5 ** 7 + 1: 2 ** 90 * 3 ** 32 * 5 ** 9,
        # 2**37 * 3**44 * 5**42
        2 ** 37 * 3 ** 44 * 5 ** 42 - 1: 2 ** 37 * 3 ** 44 * 5 ** 42,
        2 ** 37 * 3 ** 44 * 5 ** 42: 2 ** 37 * 3 ** 44 * 5 ** 42,
        2 ** 37 * 3 ** 44 * 5 ** 42 + 1: 2 ** 20 * 3 ** 106 * 5 ** 7,
    }

    for x, y in hams.items():
        assert_equal(_next_regular(x), y)
