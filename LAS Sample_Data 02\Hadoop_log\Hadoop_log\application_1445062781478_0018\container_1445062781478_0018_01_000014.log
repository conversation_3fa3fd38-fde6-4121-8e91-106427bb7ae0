2015-10-17 16:49:56,288 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 16:49:56,356 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 16:49:56,356 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: ReduceTask metrics system started
2015-10-17 16:49:56,371 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 16:49:56,371 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445062781478_0018, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@7253580c)
2015-10-17 16:49:56,473 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 16:49:56,922 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445062781478_0018
2015-10-17 16:49:57,416 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 16:49:57,845 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 16:49:57,875 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@2eedd32f
2015-10-17 16:49:57,905 INFO [main] org.apache.hadoop.mapred.ReduceTask: Using ShuffleConsumerPlugin: org.apache.hadoop.mapreduce.task.reduce.Shuffle@5b904247
2015-10-17 16:49:57,924 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: MergerManager: memoryLimit=140928608, maxSingleShuffleLimit=35232152, mergeThreshold=93012888, ioSortFactor=10, memToMemMergeOutputsThreshold=10
2015-10-17 16:49:57,925 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445062781478_0018_r_000000_0 Thread started: EventFetcher for fetching Map Completion Events
2015-10-17 16:49:57,979 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-17 16:49:57,979 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-17 16:49:57,979 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445062781478_0018_r_000000_0: Got 2 new map-outputs
2015-10-17 16:49:58,002 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445062781478_0018&reduce=0&map=attempt_1445062781478_0018_m_000000_0 sent hash and received reply
2015-10-17 16:49:58,005 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445062781478_0018_m_000000_0: Shuffling to disk since 60515385 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 16:49:58,009 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445062781478_0018_m_000000_0 decomp: 60515385 len: 60515389 to DISK
2015-10-17 16:49:58,467 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515389 bytes from map-output for attempt_1445062781478_0018_m_000000_0
2015-10-17 16:49:58,521 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 542ms
2015-10-17 16:49:58,521 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#1
2015-10-17 16:49:58,521 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-17 16:49:58,527 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445062781478_0018&reduce=0&map=attempt_1445062781478_0018_m_000001_0 sent hash and received reply
2015-10-17 16:49:58,527 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445062781478_0018_m_000001_0: Shuffling to disk since 60515836 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 16:49:58,534 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445062781478_0018_m_000001_0 decomp: 60515836 len: 60515840 to DISK
2015-10-17 16:49:58,912 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515840 bytes from map-output for attempt_1445062781478_0018_m_000001_0
2015-10-17 16:49:58,923 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 402ms
2015-10-17 16:52:01,598 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445062781478_0018_r_000000_0: Got 1 new map-outputs
2015-10-17 16:52:01,598 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#1
2015-10-17 16:52:01,599 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-17 16:52:01,649 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445062781478_0018&reduce=0&map=attempt_1445062781478_0018_m_000006_1 sent hash and received reply
2015-10-17 16:52:01,650 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445062781478_0018_m_000006_1: Shuffling to disk since 60515100 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 16:52:01,658 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445062781478_0018_m_000006_1 decomp: 60515100 len: 60515104 to DISK
2015-10-17 16:52:02,590 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515104 bytes from map-output for attempt_1445062781478_0018_m_000006_1
2015-10-17 16:52:02,603 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 1004ms
2015-10-17 16:52:18,248 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445062781478_0018_r_000000_0: Got 1 new map-outputs
2015-10-17 16:52:18,248 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#1
2015-10-17 16:52:18,248 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-17 16:52:18,257 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445062781478_0018&reduce=0&map=attempt_1445062781478_0018_m_000003_1 sent hash and received reply
2015-10-17 16:52:18,259 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445062781478_0018_m_000003_1: Shuffling to disk since 60515787 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 16:52:18,262 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445062781478_0018_m_000003_1 decomp: 60515787 len: 60515791 to DISK
2015-10-17 16:52:18,792 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515791 bytes from map-output for attempt_1445062781478_0018_m_000003_1
2015-10-17 16:52:18,798 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 550ms
2015-10-17 16:52:25,887 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445062781478_0018_r_000000_0: Got 1 new map-outputs
2015-10-17 16:52:25,888 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning 04DN8IQ.fareast.corp.microsoft.com:13562 with 1 to fetcher#1
2015-10-17 16:52:25,888 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to 04DN8IQ.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-17 16:52:26,689 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445062781478_0018&reduce=0&map=attempt_1445062781478_0018_m_000009_0 sent hash and received reply
2015-10-17 16:52:26,763 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445062781478_0018_m_000009_0: Shuffling to disk since 56695786 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 16:52:26,766 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445062781478_0018_m_000009_0 decomp: 56695786 len: 56695790 to DISK
2015-10-17 16:52:27,971 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445062781478_0018_r_000000_0: Got 1 new map-outputs
2015-10-17 16:52:27,971 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#2
2015-10-17 16:52:27,972 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#2
2015-10-17 16:52:27,982 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445062781478_0018&reduce=0&map=attempt_1445062781478_0018_m_000004_1 sent hash and received reply
2015-10-17 16:52:27,984 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445062781478_0018_m_000004_1: Shuffling to disk since 60513765 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 16:52:27,991 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#2 about to shuffle output of map attempt_1445062781478_0018_m_000004_1 decomp: 60513765 len: 60513769 to DISK
2015-10-17 16:52:28,572 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60513769 bytes from map-output for attempt_1445062781478_0018_m_000004_1
2015-10-17 16:52:28,607 INFO [fetcher#2] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#2 in 635ms
2015-10-17 16:52:35,047 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 56695790 bytes from map-output for attempt_1445062781478_0018_m_000009_0
2015-10-17 16:52:35,059 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: 04DN8IQ.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 9171ms
2015-10-17 16:52:57,274 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445062781478_0018_r_000000_0: Got 1 new map-outputs
2015-10-17 16:52:57,275 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#1
2015-10-17 16:52:57,275 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-17 16:52:57,284 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445062781478_0018&reduce=0&map=attempt_1445062781478_0018_m_000008_1 sent hash and received reply
2015-10-17 16:52:57,285 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445062781478_0018_m_000008_1: Shuffling to disk since 60516677 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 16:52:57,292 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445062781478_0018_m_000008_1 decomp: 60516677 len: 60516681 to DISK
2015-10-17 16:52:57,928 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60516681 bytes from map-output for attempt_1445062781478_0018_m_000008_1
2015-10-17 16:52:57,940 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 666ms
2015-10-17 16:53:16,302 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445062781478_0018_r_000000_0: Got 1 new map-outputs
2015-10-17 16:53:16,302 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#1
2015-10-17 16:53:16,302 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-17 16:53:16,313 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445062781478_0018&reduce=0&map=attempt_1445062781478_0018_m_000002_1 sent hash and received reply
2015-10-17 16:53:16,313 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445062781478_0018_m_000002_1: Shuffling to disk since 60514392 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 16:53:16,320 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445062781478_0018_m_000002_1 decomp: 60514392 len: 60514396 to DISK
2015-10-17 16:53:17,093 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60514396 bytes from map-output for attempt_1445062781478_0018_m_000002_1
2015-10-17 16:53:17,146 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 844ms
2015-10-17 16:53:44,296 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445062781478_0018_r_000000_0: Got 1 new map-outputs
2015-10-17 16:53:44,296 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#1
2015-10-17 16:53:44,296 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-17 16:53:44,306 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445062781478_0018&reduce=0&map=attempt_1445062781478_0018_m_000005_1 sent hash and received reply
2015-10-17 16:53:44,306 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445062781478_0018_m_000005_1: Shuffling to disk since 60514806 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 16:53:44,313 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445062781478_0018_m_000005_1 decomp: 60514806 len: 60514810 to DISK
2015-10-17 16:53:45,122 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60514810 bytes from map-output for attempt_1445062781478_0018_m_000005_1
2015-10-17 16:53:45,130 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 834ms
2015-10-17 16:54:06,898 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: Exception in getting events
java.io.IOException: Failed on local exception: java.io.IOException: An existing connection was forcibly closed by the remote host; Host Details : local host is: "MSRA-SA-41/**************"; destination host is: "minint-75dgdam1.fareast.corp.microsoft.com":53419; 
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:772)
	at org.apache.hadoop.ipc.Client.call(Client.java:1472)
	at org.apache.hadoop.ipc.Client.call(Client.java:1399)
	at org.apache.hadoop.ipc.WritableRpcEngine$Invoker.invoke(WritableRpcEngine.java:244)
	at com.sun.proxy.$Proxy9.getMapCompletionEvents(Unknown Source)
	at org.apache.hadoop.mapreduce.task.reduce.EventFetcher.getMapCompletionEvents(EventFetcher.java:120)
	at org.apache.hadoop.mapreduce.task.reduce.EventFetcher.run(EventFetcher.java:66)
Caused by: java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.net.SocketInputStream$Reader.performIO(SocketInputStream.java:57)
	at org.apache.hadoop.net.SocketIOWithTimeout.doIO(SocketIOWithTimeout.java:142)
	at org.apache.hadoop.net.SocketInputStream.read(SocketInputStream.java:161)
	at org.apache.hadoop.net.SocketInputStream.read(SocketInputStream.java:131)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at org.apache.hadoop.ipc.Client$Connection$PingInputStream.read(Client.java:513)
	at java.io.BufferedInputStream.fill(BufferedInputStream.java:235)
	at java.io.BufferedInputStream.read(BufferedInputStream.java:254)
	at java.io.DataInputStream.readInt(DataInputStream.java:387)
	at org.apache.hadoop.ipc.Client$Connection.receiveRpcResponse(Client.java:1071)
	at org.apache.hadoop.ipc.Client$Connection.run(Client.java:966)
2015-10-17 16:54:06,898 INFO [communication thread] org.apache.hadoop.mapred.Task: Communication exception: java.io.IOException: Failed on local exception: java.io.IOException: An existing connection was forcibly closed by the remote host; Host Details : local host is: "MSRA-SA-41/**************"; destination host is: "minint-75dgdam1.fareast.corp.microsoft.com":53419; 
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:772)
	at org.apache.hadoop.ipc.Client.call(Client.java:1472)
	at org.apache.hadoop.ipc.Client.call(Client.java:1399)
	at org.apache.hadoop.ipc.WritableRpcEngine$Invoker.invoke(WritableRpcEngine.java:244)
	at com.sun.proxy.$Proxy9.statusUpdate(Unknown Source)
	at org.apache.hadoop.mapred.Task$TaskReporter.run(Task.java:737)
	at java.lang.Thread.run(Thread.java:724)
Caused by: java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.net.SocketInputStream$Reader.performIO(SocketInputStream.java:57)
	at org.apache.hadoop.net.SocketIOWithTimeout.doIO(SocketIOWithTimeout.java:142)
	at org.apache.hadoop.net.SocketInputStream.read(SocketInputStream.java:161)
	at org.apache.hadoop.net.SocketInputStream.read(SocketInputStream.java:131)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at org.apache.hadoop.ipc.Client$Connection$PingInputStream.read(Client.java:513)
	at java.io.BufferedInputStream.fill(BufferedInputStream.java:235)
	at java.io.BufferedInputStream.read(BufferedInputStream.java:254)
	at java.io.DataInputStream.readInt(DataInputStream.java:387)
	at org.apache.hadoop.ipc.Client$Connection.receiveRpcResponse(Client.java:1071)
	at org.apache.hadoop.ipc.Client$Connection.run(Client.java:966)

2015-10-17 16:54:29,447 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 0 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 16:54:49,450 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 0 time(s); maxRetries=45
2015-10-17 16:55:09,452 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 1 time(s); maxRetries=45
2015-10-17 16:55:29,454 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 2 time(s); maxRetries=45
2015-10-17 16:55:49,455 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 3 time(s); maxRetries=45
2015-10-17 16:56:09,459 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 4 time(s); maxRetries=45
2015-10-17 16:56:29,460 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 5 time(s); maxRetries=45
2015-10-17 16:56:49,464 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 6 time(s); maxRetries=45
2015-10-17 16:57:09,465 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 7 time(s); maxRetries=45
2015-10-17 16:57:29,469 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 8 time(s); maxRetries=45
2015-10-17 16:57:49,471 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 9 time(s); maxRetries=45
2015-10-17 16:58:09,475 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 10 time(s); maxRetries=45
2015-10-17 16:58:29,476 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 11 time(s); maxRetries=45
2015-10-17 16:58:49,480 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 12 time(s); maxRetries=45
2015-10-17 16:59:09,482 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 13 time(s); maxRetries=45
2015-10-17 16:59:29,486 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 14 time(s); maxRetries=45
2015-10-17 16:59:49,487 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 15 time(s); maxRetries=45
2015-10-17 17:00:09,491 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 16 time(s); maxRetries=45
2015-10-17 17:00:29,493 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 17 time(s); maxRetries=45
2015-10-17 17:00:49,497 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 18 time(s); maxRetries=45
2015-10-17 17:01:09,499 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 19 time(s); maxRetries=45
2015-10-17 17:01:29,503 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 20 time(s); maxRetries=45
2015-10-17 17:01:49,505 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 21 time(s); maxRetries=45
2015-10-17 17:02:09,508 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 22 time(s); maxRetries=45
2015-10-17 17:02:29,509 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 23 time(s); maxRetries=45
2015-10-17 17:02:49,512 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 24 time(s); maxRetries=45
2015-10-17 17:03:09,514 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 25 time(s); maxRetries=45
2015-10-17 17:03:29,518 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 26 time(s); maxRetries=45
2015-10-17 17:03:49,519 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 27 time(s); maxRetries=45
2015-10-17 17:04:09,523 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 28 time(s); maxRetries=45
2015-10-17 17:04:29,524 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 29 time(s); maxRetries=45
2015-10-17 17:04:49,528 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 30 time(s); maxRetries=45
2015-10-17 17:05:09,530 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 31 time(s); maxRetries=45
2015-10-17 17:05:29,533 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 32 time(s); maxRetries=45
2015-10-17 17:05:49,534 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 33 time(s); maxRetries=45
2015-10-17 17:06:09,538 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:53419. Already tried 34 time(s); maxRetries=45
