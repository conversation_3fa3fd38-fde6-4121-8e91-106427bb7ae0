{"cells": [{"cell_type": "code", "execution_count": null, "id": "67b952d8", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import missingno as mno\n", "import numpy as np\n", "import lasio as ls"]}, {"cell_type": "code", "execution_count": null, "id": "e37a73d0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fd65bff4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "86b4f09e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d92b2e56", "metadata": {}, "outputs": [], "source": []}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}