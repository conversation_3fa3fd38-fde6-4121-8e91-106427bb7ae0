<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <link rel="icon" href="data:image/x-icon;base64,AAABAAEAEBAAAAEACABoBQAAFgAAACgAAAAQAAAAIAAAAAEACAAAAAAAAAEAAAAAAAAAAAAAAAEAAAAAAAAAAAAAPD4/AC0vMADd3+AAWFpbAFlaWwC8vb0AOjw9AERHRwCSlJUAk5SVAJ6fnwD39/cAW1xeAGVnaAC0t7YAgYOEAI2OjgDk5uYA9fX1AFhaXABzdHUA5ubmAG5wcAA6PD4Ax8jIAEVHSAA2ODkAqaqqAPLz8wB7fX0AQ0VGADQ2NwB6e3sAs7W1AJqbnAAjJSYAi4yNAP7+/gDu7+8A7+/vAHZ5eQDf4OAAtbe4ANHR0QBPUFEAwsLCAKioqQCjpKQA/Pz8AO3t7QBrbG0AvcDAAExOTwDJy8oAlpeYAC4wMQDe4OEAkZOTAPr6+gBoamsAWVtcANzc3ABkZmYAKy4vAK6vrwAsLi8A/fz9AJCRkQDe3t8AZmhpANna2gDa2toAYmRkAJGTlADr6+wA9vb2AH6AgABlZmcAYGJiAMjJyQBGSEkAr6+wAIGCgwC3uLgAqKmpANTU1ABSU1QAqqusAEJERQC1trYAlpiYAC8xMQD///8AfX5/AH5+fwBtb3AA8PDwAODh4QB6enoAuLi5ALO0tAA8Pj4A/f39AHt8fQDu7u4Ad3h4AExPUADFxcYA0NDQAMDBwQBOT1AAPkBBAPn7+wDq7OwAeXp7ANvd3QDc3d0AdHZ2AEtNTgBMTU4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAS1gjNy91XV0MDUItMl1dXSFwGxsbHy5dZEBuPGpdXV0sUx5oNRsbFngiXTkkMl1dXV1dXTtZGzBrXV1dT0RdXV1dXV0dcBsFC11yXTRcQ11dXV1dThsBBzFdFANdGG1dXV1dJRsbeBBdNgBbXQlXXV1dWhsbUzh0XTxjBF1xAkhdKAEbDlVgXSp3XV4PXSlfXVIbHydoK11GVF1QCF0SIF0wGz1dGRdNURxhXWZKU0ldVhsHPl1Fc2UbP10TClJdXV0zG3dHXV1MGhtsXV1dXV1daW8bGzpiJjMbEV1dXV1dXV0nFRsbGwEbG0FdXV1dXV1dXV0GdgcbWTpnXV1dXQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=" type="image/x-icon" />
    {% if not dataframe.test_mode: %}
        <script> {% include 'js/jquery-3.7.1.min.js' %} </script>
        {% if dataframe.page_layout == 'widescreen': %}
            <script> {% include 'js/sweetviz.js' %}  </script>
        {% else %}
            <script> {% include 'js/sweetviz_vertical.js' %}  </script>
        {% endif %}
        <style> {% include 'sv_assets.css' %} </style>
    {% else %}
        <!-- TEST MODE -->
        <script src="./sweetviz/templates/js/jquery-3.7.1.min.js"></script>
        {% if dataframe.page_layout == 'widescreen': %}
            <script src="./sweetviz/templates/js/sweetviz.js"></script>
        {% else %}
            <script src="./sweetviz/templates/js/sweetviz_vertical.js"></script>
        {% endif %}
        <link href="./sweetviz/templates/sv_assets.css" rel="stylesheet">
    {% endif %}
        <style> {% include 'sweetviz.css' %} </style>

    <style>
        {% if dataframe._target is not none: %}
            span.minigraph-target::before { content: url(data:image/png;base64,{{ dataframe._target.minigraph.graph_base64.decode('ascii') }});}
            {% if "detail_graphs" in dataframe._target.keys(): %}
                {% for detail_graph in dataframe._target.detail_graphs %}
                    span.detail_graph-f{{ dataframe._target.order_index }}-{{ detail_graph.index_for_css }}::before { content: url(data:image/png;base64,{{ detail_graph.graph_base64.decode('ascii') }});}
                {% endfor %}
            {% endif %}
        {% endif %}

        {% for item in dataframe._features.values() %}
            {% if "minigraph" in item.keys(): %}
                span.minigraph-f{{ item.order_index }}::before { content: url(data:image/png;base64,{{ item.minigraph.graph_base64.decode('ascii') }});}
            {% endif %}
            {% if "detail_graphs" in item.keys(): %}
                {% for detail_graph in item.detail_graphs %}
                    span.detail_graph-f{{ item.order_index }}-{{ detail_graph.index_for_css }}::before { content: url(data:image/png;base64,{{ detail_graph.graph_base64.decode('ascii') }});}
                {% endfor %}
            {% endif %}
        {% endfor %}
        {% if dataframe._associations is not none: %}
            span.association-graph-source::before { content: url(data:image/png;base64,{{ dataframe._association_graphs.all.graph_base64.decode('ascii') }});}
        {% endif %}
        {% if dataframe._associations_compare is not none: %}
            span.association-graph-compare::before { content: url(data:image/png;base64,{{ dataframe._association_graphs_compare.all.graph_base64.decode('ascii') }});}
        {% endif %}
        span.graph-legend { content: url(data:image/png;base64,{{ dataframe.graph_legend.graph_base64.decode('ascii') }});}
    </style>
<script>
    g_height = {{ dataframe.page_height }};
    g_scale = {{ dataframe.scale }};
</script>
</head>

<body>
<div class="{{ 'page-root' if dataframe.page_layout == "widescreen" else 'page-root-vertical'}}">
    <!-- TOP HEADER -->
    <!-- ALL SUMMARY TABS -->
    <div class="page-column-main" id="col1" style="transform: scale({{ dataframe.scale }});transform-origin: 0 0;">
        <!-- DF Summary -->

        <!-- ASSOCIATIONS (VERTICAL ONLY) -->
        {% if dataframe.page_layout == 'vertical': %}
            {% if dataframe.associations_html_source is not none: %}
                {{ dataframe.associations_html_source }}
            {% endif %}
            {% if dataframe.associations_html_compare is not none: %}
                {{ dataframe.associations_html_compare }}
            {% endif %}
        {% endif %}

        <!-- ALL SUMMARIES -->
       {{ dataframe.dataframe_summary_html }}
        {% if layout.show_logo %}
            <div class="pos-logo-group">
                <div class="pos-logo im-logo"></div>
                <div class="pos-credits text-credits">
                    <span class="text-version">{{ version }}<br>
                    <a href="https://github.com/fbdesignpro/sweetviz">Get updates, docs & report issues here</a></span><br>
                    Created & maintained by <a href="https://www.fbdesignpro.com">Francois Bertrand</a> <br>
                    Graphic design by <a href="https://www.fbdesignpro.com" data-reach="Jeff is working on his contact info :)">Jean-Francois Hains</a>
                </div>
            </div>
        {% endif %}
        <div class="page-all-summaries">
            <span class="graph-legend" style="left: 391px;position: absolute;top: 5px;"></span>
            <!-- TARGET Summary -->
            {% if dataframe._target is not none: %}
                <div class="pos-feature-summary" style="top: {{ dataframe._target.summary_pos }}px">
                    {{ dataframe._target.html_summary }}
                    <!-- This DETAIL WINDOW (VERTICAL ONLY) -->
                    {% if dataframe.page_layout == 'vertical': %}
                        {{ dataframe._target.html_detail }}
                    {% endif %}
                </div>
            {% endif %}
            <!-- FEATURE Summaries -->
            {% for item in dataframe._features.values() %}
                <div class="pos-feature-summary" style="top: {{ item.summary_pos }}px">
                    {{ item.html_summary }}
                    <!-- This DETAIL WINDOW (VERTICAL ONLY) -->
                    {% if dataframe.page_layout == 'vertical': %}
                        {{ item.html_detail }}
                    {% endif %}
                </div>
           {% endfor %}
        </div>
    </div>

    <!-- ALL DETAIL WINDOWS (WIDESCREEN ONLY) -->
    {% if dataframe.page_layout == 'widescreen': %}
        <div class="page-column-detail" id="col2">
            {% if dataframe._target is not none: %}
                {{ dataframe._target.html_detail }}
            {% endif %}
            {% if dataframe.associations_html_source is not none: %}
                {{ dataframe.associations_html_source }}
            {% endif %}
            {% if dataframe.associations_html_compare is not none: %}
                {{ dataframe.associations_html_compare }}
            {% endif %}
            <!-- TARGET Detail -->
            {% if dataframe._target is not none: %}
                {{ dataframe._target.html_detail }}
            {% endif %}
            <!-- FEATURE Details -->
            {% for item in dataframe._features.values() %}
                {{ item.html_detail }}
            {% endfor %}
        </div>
    {% endif %}
</div>
</body>
</html>
