2015-10-19 14:26:48,930 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 14:26:49,040 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 14:26:49,040 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: ReduceTask metrics system started
2015-10-19 14:26:49,071 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-19 14:26:49,071 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445182159119_0003, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@7f0eb4b4)
2015-10-19 14:26:49,196 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-19 14:26:49,649 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445182159119_0003
2015-10-19 14:26:49,899 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-19 14:26:50,368 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-19 14:26:50,368 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@6ad3381f
2015-10-19 14:26:50,384 INFO [main] org.apache.hadoop.mapred.ReduceTask: Using ShuffleConsumerPlugin: org.apache.hadoop.mapreduce.task.reduce.Shuffle@4fad9bb2
2015-10-19 14:26:50,415 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: MergerManager: memoryLimit=130652568, maxSingleShuffleLimit=32663142, mergeThreshold=86230696, ioSortFactor=10, memToMemMergeOutputsThreshold=10
2015-10-19 14:26:50,415 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445182159119_0003_r_000000_1 Thread started: EventFetcher for fetching Map Completion Events
2015-10-19 14:26:50,430 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-19 14:26:50,430 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-19 14:26:50,430 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#1
2015-10-19 14:26:50,430 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-19 14:26:50,430 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Ignoring obsolete output of FAILED map-task: 'attempt_1445182159119_0003_m_000000_0'
2015-10-19 14:26:50,430 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445182159119_0003_r_000000_1: Got 8 new map-outputs
2015-10-19 14:26:50,524 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445182159119_0003&reduce=0&map=attempt_1445182159119_0003_m_000009_0 sent hash and received reply
2015-10-19 14:26:50,524 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445182159119_0003_m_000009_0: Shuffling to disk since 172334804 is greater than maxSingleShuffleLimit (32663142)
2015-10-19 14:26:50,524 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445182159119_0003&reduce=0&map=attempt_1445182159119_0003_m_000003_0 sent hash and received reply
2015-10-19 14:26:50,540 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445182159119_0003_m_000009_0 decomp: 172334804 len: 172334808 to DISK
2015-10-19 14:26:50,540 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445182159119_0003_m_000003_0: Shuffling to disk since 216972750 is greater than maxSingleShuffleLimit (32663142)
2015-10-19 14:26:50,540 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445182159119_0003_m_000003_0 decomp: 216972750 len: 216972754 to DISK
2015-10-19 14:26:51,712 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 172334808 bytes from map-output for attempt_1445182159119_0003_m_000009_0
2015-10-19 14:26:51,712 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 1291ms
2015-10-19 14:26:51,712 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 4 to fetcher#1
2015-10-19 14:26:51,712 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 4 of 4 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-19 14:26:51,727 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445182159119_0003&reduce=0&map=attempt_1445182159119_0003_m_000006_0,attempt_1445182159119_0003_m_000007_0,attempt_1445182159119_0003_m_000008_0,attempt_1445182159119_0003_m_000001_1 sent hash and received reply
2015-10-19 14:26:51,727 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445182159119_0003_m_000006_0: Shuffling to disk since 217011663 is greater than maxSingleShuffleLimit (32663142)
2015-10-19 14:26:51,743 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445182159119_0003_m_000006_0 decomp: 217011663 len: 217011667 to DISK
2015-10-19 14:26:52,712 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216972754 bytes from map-output for attempt_1445182159119_0003_m_000003_0
2015-10-19 14:26:52,712 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 2289ms
2015-10-19 14:26:52,712 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 2 to fetcher#5
2015-10-19 14:26:52,712 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 2 of 2 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-19 14:26:52,727 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445182159119_0003&reduce=0&map=attempt_1445182159119_0003_m_000004_0,attempt_1445182159119_0003_m_000005_0 sent hash and received reply
2015-10-19 14:26:52,727 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445182159119_0003_m_000004_0: Shuffling to disk since 216999709 is greater than maxSingleShuffleLimit (32663142)
2015-10-19 14:26:52,727 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445182159119_0003_m_000004_0 decomp: 216999709 len: 216999713 to DISK
2015-10-19 14:26:53,556 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217011667 bytes from map-output for attempt_1445182159119_0003_m_000006_0
2015-10-19 14:26:53,556 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445182159119_0003_m_000007_0: Shuffling to disk since 216976206 is greater than maxSingleShuffleLimit (32663142)
2015-10-19 14:26:56,337 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445182159119_0003_m_000007_0 decomp: 216976206 len: 216976210 to DISK
2015-10-19 14:26:56,431 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Ignoring obsolete output of KILLED map-task: 'attempt_1445182159119_0003_m_000000_2'
2015-10-19 14:26:57,462 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216999713 bytes from map-output for attempt_1445182159119_0003_m_000004_0
2015-10-19 14:26:58,009 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216976210 bytes from map-output for attempt_1445182159119_0003_m_000007_0
2015-10-19 14:26:58,009 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445182159119_0003_m_000008_0: Shuffling to disk since 217015228 is greater than maxSingleShuffleLimit (32663142)
2015-10-19 14:26:58,025 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445182159119_0003_m_000008_0 decomp: 217015228 len: 217015232 to DISK
2015-10-19 14:26:59,931 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217015232 bytes from map-output for attempt_1445182159119_0003_m_000008_0
2015-10-19 14:26:59,931 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445182159119_0003_m_000001_1: Shuffling to disk since 217009502 is greater than maxSingleShuffleLimit (32663142)
2015-10-19 14:26:59,931 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445182159119_0003_m_000001_1 decomp: 217009502 len: 217009506 to DISK
2015-10-19 14:27:02,197 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217009506 bytes from map-output for attempt_1445182159119_0003_m_000001_1
2015-10-19 14:27:02,197 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 10482ms
2015-10-19 14:27:49,921 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445182159119_0003_r_000000_1: Got 1 new map-outputs
2015-10-19 14:27:49,921 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445182159119_0003_m_000005_0: Shuffling to disk since 216990140 is greater than maxSingleShuffleLimit (32663142)
2015-10-19 14:27:49,921 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#1
2015-10-19 14:27:49,921 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-19 14:27:49,921 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445182159119_0003_m_000005_0 decomp: 216990140 len: 216990144 to DISK
2015-10-19 14:27:49,921 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445182159119_0003&reduce=0&map=attempt_1445182159119_0003_m_000002_1 sent hash and received reply
2015-10-19 14:27:49,921 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445182159119_0003_m_000002_1: Shuffling to disk since 216991624 is greater than maxSingleShuffleLimit (32663142)
2015-10-19 14:27:49,937 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445182159119_0003_m_000002_1 decomp: 216991624 len: 216991628 to DISK
2015-10-19 14:27:52,234 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216990144 bytes from map-output for attempt_1445182159119_0003_m_000005_0
2015-10-19 14:27:52,250 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 59534ms
2015-10-19 14:27:52,500 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216991628 bytes from map-output for attempt_1445182159119_0003_m_000002_1
2015-10-19 14:27:52,515 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 2594ms
2015-10-19 14:32:40,058 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445182159119_0003_r_000000_1: Got 1 new map-outputs
2015-10-19 14:32:40,058 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MININT-FNANLI5.fareast.corp.microsoft.com:13562 with 1 to fetcher#1
2015-10-19 14:32:40,058 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MININT-FNANLI5.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-19 14:32:40,074 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445182159119_0003&reduce=0&map=attempt_1445182159119_0003_m_000000_1 sent hash and received reply
2015-10-19 14:32:40,105 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445182159119_0003_m_000000_1: Shuffling to disk since 216988123 is greater than maxSingleShuffleLimit (32663142)
2015-10-19 14:32:40,120 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445182159119_0003_m_000000_1 decomp: 216988123 len: 216988127 to DISK
2015-10-19 14:35:04,634 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216988127 bytes from map-output for attempt_1445182159119_0003_m_000000_1
2015-10-19 14:35:04,634 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MININT-FNANLI5.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 144586ms
2015-10-19 14:35:04,634 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: EventFetcher is interrupted.. Returning
2015-10-19 14:35:04,650 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: finalMerge called with 0 in-memory map-outputs and 10 on-disk map-outputs
2015-10-19 14:35:04,650 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 10 files, 2125289789 bytes from disk
2015-10-19 14:35:04,650 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 0 segments, 0 bytes from memory into reduce
2015-10-19 14:35:04,665 INFO [main] org.apache.hadoop.mapred.Merger: Merging 10 sorted segments
2015-10-19 14:35:04,665 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 10 segments left of total size: 2125289689 bytes
2015-10-19 14:35:04,822 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: mapred.skip.on is deprecated. Instead, use mapreduce.job.skiprecords
2015-10-19 14:42:54,538 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445182159119_0003_r_000000_1 is done. And is in the process of committing
2015-10-19 14:42:54,554 INFO [main] org.apache.hadoop.mapred.Task: Task attempt_1445182159119_0003_r_000000_1 is allowed to commit now
2015-10-19 14:42:54,569 INFO [main] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Saved output of task 'attempt_1445182159119_0003_r_000000_1' to hdfs://msra-sa-41:9000/out/out3/_temporary/1/task_1445182159119_0003_r_000000
2015-10-19 14:42:54,569 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445182159119_0003_r_000000_1' done.
