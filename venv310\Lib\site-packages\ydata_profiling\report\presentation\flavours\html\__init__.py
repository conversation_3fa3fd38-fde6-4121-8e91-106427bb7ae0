from ydata_profiling.report.presentation.flavours.html.alerts import HTMLAlerts
from ydata_profiling.report.presentation.flavours.html.collapse import HTMLCollapse
from ydata_profiling.report.presentation.flavours.html.container import HTMLContainer
from ydata_profiling.report.presentation.flavours.html.correlation_table import (
    HTMLCorrelationTable,
)
from ydata_profiling.report.presentation.flavours.html.dropdown import HTMLDropdown
from ydata_profiling.report.presentation.flavours.html.duplicate import HTMLDuplicate
from ydata_profiling.report.presentation.flavours.html.frequency_table import (
    HTMLFrequencyTable,
)
from ydata_profiling.report.presentation.flavours.html.frequency_table_small import (
    HTMLFrequencyTableSmall,
)
from ydata_profiling.report.presentation.flavours.html.html import HTMLHTML
from ydata_profiling.report.presentation.flavours.html.image import HTMLImage
from ydata_profiling.report.presentation.flavours.html.root import HTMLRoot
from ydata_profiling.report.presentation.flavours.html.sample import HTMLSample
from ydata_profiling.report.presentation.flavours.html.scores import HTMLScores
from ydata_profiling.report.presentation.flavours.html.table import HTMLTable
from ydata_profiling.report.presentation.flavours.html.toggle_button import (
    HTMLToggleButton,
)
from ydata_profiling.report.presentation.flavours.html.variable import HTMLVariable
from ydata_profiling.report.presentation.flavours.html.variable_info import (
    HTMLVariableInfo,
)

__all__ = [
    "HTMLCollapse",
    "HTMLContainer",
    "HTMLDuplicate",
    "HTMLDropdown",
    "HTMLFrequencyTable",
    "HTMLFrequencyTableSmall",
    "HTMLHTML",
    "HTMLImage",
    "HTMLRoot",
    "HTMLSample",
    "HTMLTable",
    "HTMLToggleButton",
    "HTMLVariable",
    "HTMLVariableInfo",
    "HTMLAlerts",
    "HTMLCorrelationTable",
    "HTMLScores",
]
