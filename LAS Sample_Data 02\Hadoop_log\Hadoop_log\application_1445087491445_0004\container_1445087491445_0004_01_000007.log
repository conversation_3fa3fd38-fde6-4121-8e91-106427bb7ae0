2015-10-17 21:24:25,147 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 21:24:25,429 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 21:24:25,429 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-17 21:24:25,538 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 21:24:25,538 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445087491445_0004, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@6ace4625)
2015-10-17 21:24:27,319 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 21:24:29,757 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445087491445_0004
2015-10-17 21:24:31,569 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 21:24:33,038 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 21:24:33,132 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@16059e71
2015-10-17 21:24:36,835 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/wordcount2.txt:402653184+134217728
2015-10-17 21:24:37,069 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-17 21:24:37,069 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-17 21:24:37,069 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-17 21:24:37,069 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-17 21:24:37,069 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-17 21:24:37,116 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-17 21:24:44,632 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:24:44,632 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 34171787; bufvoid = 104857600
2015-10-17 21:24:44,632 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 13785828(55143312); length = 12428569/6553600
2015-10-17 21:24:44,632 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 44657541 kvi 11164380(44657520)
2015-10-17 21:25:15,069 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-17 21:25:15,179 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 44657541 kv 11164380(44657520) kvi 8542952(34171808)
2015-10-17 21:25:20,132 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:25:20,132 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 44657541; bufend = 78831461; bufvoid = 104857600
2015-10-17 21:25:20,132 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 11164380(44657520); kvend = 24950744(99802976); length = 12428037/6553600
2015-10-17 21:25:20,132 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 89317210 kvi 22329296(89317184)
2015-10-17 21:25:48,991 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-17 21:25:49,038 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 89317210 kv 22329296(89317184) kvi 19707872(78831488)
2015-10-17 21:25:53,695 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:25:53,695 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 89317210; bufend = 18635516; bufvoid = 104857600
2015-10-17 21:25:53,695 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 22329296(89317184); kvend = 9901760(39607040); length = 12427537/6553600
2015-10-17 21:25:53,695 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29121270 kvi 7280312(29121248)
2015-10-17 21:26:20,882 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-17 21:26:20,929 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29121270 kv 7280312(29121248) kvi 4658884(18635536)
2015-10-17 21:26:25,601 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:26:25,601 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29121270; bufend = 63298571; bufvoid = 104857600
2015-10-17 21:26:25,601 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7280312(29121248); kvend = 21067524(84270096); length = 12427189/6553600
2015-10-17 21:26:25,601 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 73784325 kvi 18446076(73784304)
2015-10-17 21:26:53,820 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-17 21:26:54,039 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 73784325 kv 18446076(73784304) kvi 15824648(63298592)
2015-10-17 21:26:58,601 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:26:58,601 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 73784325; bufend = 3101826; bufvoid = 104857600
2015-10-17 21:26:58,601 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 18446076(73784304); kvend = 6018336(24073344); length = 12427741/6553600
2015-10-17 21:26:58,601 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 13587577 kvi 3396888(13587552)
2015-10-17 21:27:27,414 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-17 21:27:27,586 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 13587577 kv 3396888(13587552) kvi 775464(3101856)
2015-10-17 21:27:31,929 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:27:31,929 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 13587577; bufend = 47763869; bufvoid = 104857600
2015-10-17 21:27:31,929 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 3396888(13587552); kvend = 17183848(68735392); length = 12427441/6553600
2015-10-17 21:27:31,929 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 58249622 kvi 14562400(58249600)
2015-10-17 21:27:34,226 INFO [main] org.apache.hadoop.mapred.MapTask: Starting flush of map output
2015-10-17 21:27:59,851 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 5
2015-10-17 21:28:00,773 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 58249622 kv 14562400(58249600) kvi 12515964(50063856)
2015-10-17 21:28:00,773 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:28:00,773 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 58249622; bufend = 63876500; bufvoid = 104857600
2015-10-17 21:28:00,773 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14562400(58249600); kvend = 12515968(50063872); length = 2046433/6553600
2015-10-17 21:28:04,617 INFO [main] org.apache.hadoop.mapred.MapTask: Finished spill 6
2015-10-17 21:28:04,836 INFO [main] org.apache.hadoop.mapred.Merger: Merging 7 sorted segments
2015-10-17 21:28:05,398 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 7 segments left of total size: 228411640 bytes
2015-10-17 21:29:37,916 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445087491445_0004_m_000004_0 is done. And is in the process of committing
2015-10-17 21:29:38,463 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445087491445_0004_m_000004_0' done.
2015-10-17 21:29:38,572 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Stopping MapTask metrics system...
2015-10-17 21:29:38,572 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system stopped.
2015-10-17 21:29:38,572 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system shutdown complete.
