# 🪨 Lithology Classification ML Pipeline

A comprehensive machine learning system for predicting lithology classes from well log data, designed for petrophysical analysis in the oil & gas industry.

## 🎯 **Project Overview**

This project provides a complete supervised machine learning pipeline that:

- **Predicts lithology classes** (sandstone, shale, limestone, dolomite, coal, etc.)
- **Uses standard well log features**: GR, RHOB, NPHI, RDEP, DTC, PEF
- **Implements advanced ML models**: Random Forest & XGBoost with hyperparameter tuning
- **Provides comprehensive evaluation**: Classification metrics, confusion matrices, feature importance
- **Offers multiple interfaces**: Python script, CLI tool, and Streamlit web app
- **Generates professional visualizations** suitable for academic/industry presentations

## 📁 **Project Structure**

```
Project1/
├── lithology_ml_pipeline.py      # Main ML pipeline script
├── lithology_streamlit_app.py    # Interactive web application
├── lithology_cli.py              # Command-line interface
├── requirements.txt              # Python dependencies
├── litho_data/                   # Input CSV files with well log data
├── model_results/                # Trained models and results
│   ├── visualizations/           # Generated plots and charts
│   ├── *_model_*.joblib         # Saved ML models
│   ├── preprocessing_*.joblib    # Preprocessing objects
│   └── evaluation_*.json        # Performance metrics
└── LITHOLOGY_ML_README.md       # This documentation
```

## 🚀 **Quick Start**

### 1. **Install Dependencies**

```bash
pip install -r requirements.txt
```

### 2. **Prepare Your Data**

Place your CSV files with well log data in the `litho_data/` directory. Required columns:
- `DEPTH_MD` (optional, for depth plots)
- `GR` (Gamma Ray)
- `RHOB` (Bulk Density)
- `NPHI` (Neutron Porosity)
- `RDEP` (Deep Resistivity)
- `DTC` (Delta Time Compressional)
- `PEF` (Photoelectric Factor)
- `FORCE_2020_LITHOFACIES_LITHOLOGY` (target variable)

### 3. **Run the Complete Pipeline**

```bash
python lithology_ml_pipeline.py
```

This will:
- Load and combine all CSV files
- Preprocess the data (handle missing values, scaling, encoding)
- Train Random Forest and XGBoost models with hyperparameter tuning
- Evaluate models with comprehensive metrics
- Generate professional visualizations
- Save trained models for future use

## 🖥️ **Usage Options**

### **Option 1: Python Script (Recommended for Training)**

```bash
python lithology_ml_pipeline.py
```

**Features:**
- Complete automated pipeline
- Model training with hyperparameter tuning
- Comprehensive evaluation and visualization
- Model persistence for future use

### **Option 2: Streamlit Web App (Interactive UI)**

```bash
streamlit run lithology_streamlit_app.py
```

**Features:**
- User-friendly web interface
- Upload CSV files for prediction
- Interactive visualizations with Plotly
- Real-time model performance metrics
- Export prediction results

### **Option 3: Command Line Interface (Batch Processing)**

```bash
# Basic usage
python lithology_cli.py --input your_data.csv --output predictions.csv

# Advanced usage with confidence threshold
python lithology_cli.py --input data.csv --model xgboost --confidence 0.7

# List available models
python lithology_cli.py --input data.csv --list-models
```

**Features:**
- Batch processing of large datasets
- Model selection and confidence thresholding
- Summary statistics and distribution analysis
- Automated output file generation

## 📊 **Model Performance**

The pipeline implements two state-of-the-art machine learning algorithms:

### **Random Forest Classifier**
- **Advantages**: Robust to overfitting, handles missing values well, provides feature importance
- **Hyperparameters**: n_estimators, max_depth, min_samples_split, min_samples_leaf
- **Typical Performance**: 85-95% accuracy on well log data

### **XGBoost Classifier**
- **Advantages**: High performance, gradient boosting, handles imbalanced classes
- **Hyperparameters**: n_estimators, max_depth, learning_rate, subsample
- **Typical Performance**: 87-96% accuracy on well log data

### **Evaluation Metrics**
- **Accuracy**: Overall prediction correctness
- **Precision**: True positives / (True positives + False positives)
- **Recall**: True positives / (True positives + False negatives)
- **F1-Score**: Harmonic mean of precision and recall
- **Confusion Matrix**: Detailed class-wise performance
- **Feature Importance**: Contribution of each well log parameter

## 🎨 **Visualizations Generated**

1. **Confusion Matrices**: Model performance by lithology class
2. **Feature Importance**: Which well log parameters are most predictive
3. **Lithology vs Depth**: Spatial distribution of predictions
4. **Interactive Plotly Charts**: Web-based exploration tools
5. **Cross-plots**: Feature relationships colored by lithology

## 📈 **Data Requirements**

### **Minimum Requirements**
- At least 3 well log features from: GR, RHOB, NPHI, RDEP, DTC, PEF
- Target lithology labels in `FORCE_2020_LITHOFACIES_LITHOLOGY` column
- Minimum 1000 samples for reliable training

### **Recommended Data Quality**
- Less than 10% missing values per feature
- Balanced lithology distribution (avoid extreme class imbalance)
- Multiple wells for better generalization
- Quality-controlled log data (outlier removal, environmental corrections)

### **Supported Lithology Classes**
The system can handle any lithology classification scheme, commonly:
- Sandstone
- Shale/Mudstone
- Limestone
- Dolomite
- Coal
- Anhydrite
- Salt
- And more...

## 🔧 **Advanced Configuration**

### **Custom Feature Selection**
Modify the `feature_columns` list in the pipeline scripts:

```python
feature_columns = ['GR', 'RHOB', 'NPHI', 'RDEP', 'DTC', 'PEF', 'SP', 'CALI']
```

### **Hyperparameter Tuning**
Adjust the parameter grids in `lithology_ml_pipeline.py`:

```python
rf_param_grid = {
    'n_estimators': [100, 200, 300, 500],
    'max_depth': [10, 20, 30, None],
    'min_samples_split': [2, 5, 10],
    'min_samples_leaf': [1, 2, 4, 8]
}
```

### **Missing Value Handling**
The pipeline uses median imputation by default. Modify in the preprocessing section:

```python
imputer = SimpleImputer(strategy='median')  # or 'mean', 'most_frequent'
```

## 🎓 **Academic/Professional Presentation**

This pipeline is designed for professional presentations and academic demonstrations:

### **Key Selling Points**
1. **Industry-Standard Features**: Uses conventional well log measurements
2. **Robust Methodology**: Cross-validation, hyperparameter tuning, multiple metrics
3. **Professional Visualizations**: Publication-ready plots and interactive charts
4. **Practical Application**: Real-world petrophysical analysis workflow
5. **Scalable Architecture**: Handles datasets from hundreds to millions of samples

### **Presentation Structure**
1. **Problem Statement**: Automated lithology classification from well logs
2. **Data Overview**: Features, target classes, data quality analysis
3. **Methodology**: ML algorithms, preprocessing, evaluation strategy
4. **Results**: Model performance, feature importance, geological insights
5. **Applications**: Formation evaluation, reservoir characterization, drilling optimization

## 🚨 **Troubleshooting**

### **Common Issues**

**"No CSV files found"**
- Ensure CSV files are in the `litho_data/` directory
- Check file extensions (.csv)

**"Insufficient features"**
- Verify column names match expected features
- Ensure at least 3 features are available

**"Target column not found"**
- Check that `FORCE_2020_LITHOFACIES_LITHOLOGY` column exists
- Modify `target_column` variable if using different naming

**"Models not found"**
- Run the training pipeline first: `python lithology_ml_pipeline.py`
- Check that `model_results/` directory contains .joblib files

### **Performance Optimization**
- Use `n_jobs=-1` for parallel processing
- Reduce hyperparameter grid size for faster training
- Sample large datasets for initial testing

## 📞 **Support & Contact**

For technical support or questions about the lithology classification pipeline:

- **Project Team**: ONGC Petrophysical Analysis Team
- **Documentation**: This README and inline code comments
- **Issues**: Check error messages and troubleshooting section

## 📄 **License & Citation**

This project is developed for educational and professional use in petrophysical analysis. When using this pipeline in academic work or publications, please cite appropriately.

---

**🎉 Ready to showcase your machine learning expertise in petrophysical analysis!**
