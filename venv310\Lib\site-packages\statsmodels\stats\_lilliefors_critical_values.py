"""
This file is automatically generated by littlefors_critical_values.py.
Do not directly modifythis file.

Value based on 10,000,000 simulations."""

from numpy import array

PERCENTILES = [1, 5, 10, 25, 50, 75, 90, 92.5, 95, 97.5, 99, 99.5, 99.7, 99.9]

SAMPLE_SIZES = [
    3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 25, 30,
    40, 50, 100, 200, 400, 800, 1600
]
normal_crit_vals = {
    4:
    array([
        0.14467854, 0.16876575, 0.18664724, 0.22120362, 0.25828924, 0.29341032,
        0.34532673, 0.35917374, 0.37521968, 0.39563998, 0.41307904, 0.42157653,
        0.4261507, 0.43265213
    ]),
    5:
    array([
        0.13587046, 0.16098893, 0.17638354, 0.20235666, 0.2333944, 0.27766941,
        0.31900772, 0.32936832, 0.34309223, 0.36727643, 0.39671728, 0.41322814,
        0.42293504, 0.4386304
    ]),
    6:
    array([
        0.12919635, 0.15139467, 0.16384021, 0.18597849, 0.2186713, 0.2585473,
        0.29713753, 0.30829444, 0.32338252, 0.3456671, 0.37038945, 0.38760943,
        0.40001813, 0.42304439
    ]),
    7:
    array([
        0.12263812, 0.14163065, 0.15238656, 0.17435948, 0.20617949, 0.24243592,
        0.28031415, 0.29068512, 0.3042307, 0.32532967, 0.35070348, 0.3678189,
        0.37908881, 0.40078798
    ]),
    8:
    array([
        0.11633728, 0.13297288, 0.14353311, 0.16537078, 0.19477376, 0.22936164,
        0.2652001, 0.27501697, 0.28804474, 0.30862157, 0.33279908, 0.34911188,
        0.3603517, 0.38252055
    ]),
    9:
    array([
        0.11029593, 0.126086, 0.1365291, 0.15748048, 0.18510669, 0.21822055,
        0.25223085, 0.26161129, 0.27415243, 0.29383667, 0.31708299, 0.33312043,
        0.34406772, 0.36524182
    ]),
    10:
    array([
        0.10487398, 0.12044377, 0.13065174, 0.15042835, 0.17679904, 0.20848607,
        0.24098605, 0.25001629, 0.26202351, 0.2809226, 0.30341763, 0.31888089,
        0.32960742, 0.35061378
    ]),
    11:
    array([
        0.10036835, 0.11563925, 0.12540948, 0.14421024, 0.1695621, 0.19993088,
        0.23119563, 0.23987246, 0.25142048, 0.26961902, 0.29148756, 0.30646087,
        0.31678589, 0.33743951
    ]),
    12:
    array([
        0.09649147, 0.11137479, 0.12069511, 0.13877133, 0.16313672, 0.19233706,
        0.22244871, 0.23082937, 0.24194831, 0.25959488, 0.28077229, 0.29526801,
        0.30538827, 0.32558601
    ]),
    13:
    array([
        0.09318309, 0.10753337, 0.11647389, 0.13389325, 0.15739248, 0.18555597,
        0.21463087, 0.22267463, 0.23346007, 0.25054975, 0.27100552, 0.28519234,
        0.29504428, 0.31445229
    ]),
    14:
    array([
        0.09024138, 0.1040431, 0.11266368, 0.12948657, 0.15221479, 0.17944478,
        0.2075745, 0.21536969, 0.22585144, 0.2423819, 0.26231339, 0.2760657,
        0.28555592, 0.30466148
    ]),
    15:
    array([
        0.08750578, 0.10085666, 0.1092263, 0.12552356, 0.14751778, 0.17390304,
        0.2011939, 0.20879449, 0.21891697, 0.23499116, 0.25427802, 0.26772298,
        0.27706522, 0.29551017
    ]),
    16:
    array([
        0.08501529, 0.09795236, 0.10607182, 0.1219047, 0.14327014, 0.16885124,
        0.19537444, 0.20274039, 0.21257643, 0.22822187, 0.24703504, 0.26003621,
        0.26913735, 0.28745211
    ]),
    17:
    array([
        0.0827393, 0.09529635, 0.10320903, 0.11859867, 0.13936135, 0.16422592,
        0.1900207, 0.19722541, 0.20683438, 0.22204864, 0.2402864, 0.25306469,
        0.26200104, 0.2798316
    ]),
    18:
    array([
        0.08063198, 0.09285546, 0.10055901, 0.11554003, 0.13575222, 0.15996707,
        0.18509495, 0.1921063, 0.20145911, 0.2163409, 0.23420221, 0.24665892,
        0.25527246, 0.27285099
    ]),
    19:
    array([
        0.0786934, 0.09059543, 0.09810264, 0.11271608, 0.1324079, 0.15602767,
        0.18052002, 0.18736281, 0.19652196, 0.21105313, 0.22854301, 0.24068403,
        0.24912401, 0.26624557
    ]),
    20:
    array([
        0.07686372, 0.08852309, 0.09582876, 0.11008398, 0.12930804, 0.15236687,
        0.1762829, 0.18297149, 0.19190794, 0.20610189, 0.22327259, 0.23513834,
        0.24349604, 0.2602868
    ]),
    25:
    array([
        0.06933943, 0.07985746, 0.08645091, 0.09927555, 0.11656692, 0.1373056,
        0.15889286, 0.16493761, 0.17300926, 0.18582499, 0.2013265, 0.21221932,
        0.21979198, 0.2350391
    ]),
    30:
    array([
        0.06380332, 0.07339662, 0.07942847, 0.09118039, 0.10701841, 0.12603791,
        0.14586121, 0.15138566, 0.15878307, 0.17058849, 0.18492078, 0.19492393,
        0.20198353, 0.21612086
    ]),
    40:
    array([
        0.055784, 0.06414642, 0.06940064, 0.07961554, 0.09339439, 0.10994266,
        0.12719016, 0.13202416, 0.13850018, 0.14877502, 0.16131916, 0.17009968,
        0.17633078, 0.18870328
    ]),
    50:
    array([
        0.05022994, 0.05773701, 0.06243507, 0.07160481, 0.08395875, 0.09882109,
        0.11431303, 0.11863768, 0.12444044, 0.1337458, 0.14504504, 0.15299605,
        0.15854061, 0.16985345
    ]),
    100:
    array([
        0.0361047, 0.04146075, 0.0448043, 0.05131964, 0.06011456, 0.07068396,
        0.08173283, 0.08483991, 0.08900091, 0.09564506, 0.10373761, 0.10937278,
        0.11338394, 0.12161442
    ]),
    200:
    array([
        0.02584151, 0.02963511, 0.03200533, 0.0366259, 0.04286406, 0.05036816,
        0.05820564, 0.06041126, 0.06336215, 0.06809025, 0.0738379, 0.07788879,
        0.08074047, 0.08663425
    ]),
    400:
    array([
        0.01844162, 0.02112065, 0.02280475, 0.02607295, 0.03049198, 0.03580284,
        0.04135097, 0.04290933, 0.04500619, 0.04834792, 0.0524239, 0.05530289,
        0.05733374, 0.06145404
    ]),
    800:
    array([
        0.0131231, 0.01501723, 0.01620598, 0.01852257, 0.02164634, 0.02540595,
        0.02933599, 0.03043873, 0.03192094, 0.03429131, 0.03718247, 0.03922491,
        0.0406419, 0.04361741
    ]),
    1600:
    array([
        0.00932049, 0.01066126, 0.01150366, 0.01314135, 0.0153528, 0.0180115,
        0.02079512, 0.02157469, 0.02262168, 0.02429563, 0.02634302, 0.02777611,
        0.02879721, 0.03088286
    ])
}

# Coefficients are model log(cv) = b[0] + b[1] log(n) + b[2] log(n)**2
normal_asymp_crit_vals = {
    1.0: array([-1.17114969, -0.45068579, -0.00356741]),
    5.0: array([-1.03298277, -0.45068579, -0.00356741]),
    10.0: array([-0.95518114, -0.45068579, -0.00356741]),
    25.0: array([-0.81912169, -0.45068579, -0.00356741]),
    50.0: array([-0.6607348, -0.45068579, -0.00356741]),
    75.0: array([-0.49861004, -0.45068579, -0.00356741]),
    90.0: array([-0.35446139, -0.45068579, -0.00356741]),
    92.5: array([-0.31737193, -0.45068579, -0.00356741]),
    95.0: array([-0.26969888, -0.45068579, -0.00356741]),
    97.5: array([-0.1979977, -0.45068579, -0.00356741]),
    99.0: array([-0.11709649, -0.45068579, -0.00356741]),
    99.5: array([-0.06398777, -0.45068579, -0.00356741]),
    99.7: array([-0.0281863, -0.45068579, -0.00356741]),
    99.9: array([0.04129756, -0.45068579, -0.00356741])
}


exp_crit_vals = {
    3:
    array([
        0.2109761, 0.22988438, 0.25444542, 0.30397879, 0.36446945, 0.42814169,
        0.51106702, 0.52962262, 0.55088872, 0.57754526, 0.60036174, 0.61179874,
        0.61823126, 0.6294291
    ]),
    4:
    array([
        0.17384601, 0.20723235, 0.22806298, 0.26607407, 0.31624998, 0.38384024,
        0.44420279, 0.46088139, 0.4843941, 0.521064, 0.55751191, 0.57850145,
        0.59207384, 0.62098085
    ]),
    5:
    array([
        0.15948619, 0.18836122, 0.20519471, 0.2387884, 0.28771415, 0.34487558,
        0.40440534, 0.42088543, 0.44200346, 0.47455921, 0.51269224, 0.53654197,
        0.55188279, 0.58123527
    ]),
    6:
    array([
        0.1480075, 0.17258589, 0.18825927, 0.21978293, 0.2645031, 0.31713484,
        0.37308973, 0.38833891, 0.4084249, 0.43908986, 0.47469271, 0.49984099,
        0.5170463, 0.54989819
    ]),
    7:
    array([
        0.13783797, 0.16039265, 0.17499898, 0.20510973, 0.24578726, 0.29584306,
        0.34806914, 0.36231862, 0.38111852, 0.41060597, 0.44579783, 0.46945914,
        0.48562857, 0.5179461
    ]),
    8:
    array([
        0.12914489, 0.15056654, 0.16462426, 0.19280967, 0.23086956, 0.27829277,
        0.3273405, 0.34086546, 0.35894819, 0.3874127, 0.42078514, 0.44361638,
        0.45939843, 0.4910784
    ]),
    9:
    array([
        0.12209612, 0.1426062, 0.15604163, 0.1823721, 0.21856178, 0.26352862,
        0.3101114, 0.32311637, 0.3404356, 0.36744906, 0.39952102, 0.42164587,
        0.43719767, 0.46827333
    ]),
    10:
    array([
        0.11622346, 0.13594053, 0.14864516, 0.17351161, 0.20820576, 0.25083243,
        0.29544984, 0.3078714, 0.32438433, 0.35032272, 0.38135762, 0.40282213,
        0.41758788, 0.44762269
    ]),
    11:
    array([
        0.11116996, 0.13008811, 0.14214361, 0.16587204, 0.19916, 0.23987837,
        0.28264075, 0.29456614, 0.31039145, 0.3353923, 0.36531224, 0.38597334,
        0.40039914, 0.42945227
    ]),
    12:
    array([
        0.10682852, 0.12494111, 0.13642386, 0.15924117, 0.19119135, 0.23024225,
        0.27140493, 0.28285565, 0.29813962, 0.32235591, 0.35118835, 0.37131437,
        0.38541362, 0.41355258
    ]),
    13:
    array([
        0.10299158, 0.12035679, 0.13139418, 0.15340996, 0.18411715, 0.22174274,
        0.26139337, 0.27247472, 0.28721874, 0.31061368, 0.3386004, 0.3579752,
        0.37154656, 0.399054
    ]),
    14:
    array([
        0.09952544, 0.11620577, 0.12685351, 0.14816724, 0.17777806, 0.21413923,
        0.25247245, 0.26312392, 0.27746948, 0.30001199, 0.32724359, 0.34615172,
        0.3591787, 0.38578716
    ]),
    15:
    array([
        0.09635825, 0.11247223, 0.12279761, 0.1434653, 0.17203838, 0.20728334,
        0.2443769, 0.25476058, 0.26865606, 0.29062696, 0.31686937, 0.33520734,
        0.34795825, 0.37374779
    ]),
    16:
    array([
        0.093446, 0.10911215, 0.11910394, 0.1391716, 0.16686968, 0.20103119,
        0.23706762, 0.24716908, 0.26064721, 0.28192033, 0.30753741, 0.32530837,
        0.33776015, 0.36287832
    ]),
    17:
    array([
        0.09080542, 0.10601186, 0.11575975, 0.13523043, 0.16215573, 0.19534591,
        0.2303959, 0.24019386, 0.25330648, 0.27400789, 0.29895143, 0.31623639,
        0.32845602, 0.35308388
    ]),
    18:
    array([
        0.08844468, 0.10320066, 0.11271261, 0.13163588, 0.15780097, 0.19009349,
        0.22420159, 0.2337161, 0.24646825, 0.26673429, 0.29105285, 0.30803457,
        0.31984228, 0.34387696
    ]),
    19:
    array([
        0.08623885, 0.1006206, 0.10988138, 0.12831526, 0.15380297, 0.18527462,
        0.21845938, 0.22776537, 0.2402398, 0.2600306, 0.28378696, 0.30035609,
        0.31189906, 0.33544625
    ]),
    20:
    array([
        0.08417538, 0.09821659, 0.10727612, 0.12520467, 0.15011026, 0.18077566,
        0.21317237, 0.2222683, 0.23446664, 0.25382491, 0.27704397, 0.29319327,
        0.30456436, 0.32759304
    ]),
    25:
    array([
        0.07575606, 0.08842104, 0.09651937, 0.1126094, 0.13493277, 0.1624784,
        0.19169294, 0.19987007, 0.2108557, 0.22828626, 0.24930174, 0.26403731,
        0.2743157, 0.29520476
    ]),
    30:
    array([
        0.06952264, 0.08110063, 0.08850223, 0.10324683, 0.12365394, 0.14885572,
        0.1756019, 0.18308805, 0.19314473, 0.20920585, 0.2285569, 0.24218995,
        0.25167821, 0.270999
    ]),
    40:
    array([
        0.06066843, 0.07071874, 0.07713823, 0.0899287, 0.10765412, 0.12954118,
        0.15278494, 0.15934334, 0.16813288, 0.18214059, 0.19908466, 0.21086902,
        0.2192688, 0.23619981
    ]),
    50:
    array([
        0.05453287, 0.06354216, 0.06931727, 0.0807706, 0.09664734, 0.11625969,
        0.13711892, 0.14300016, 0.15087554, 0.16345379, 0.17865445, 0.18939212,
        0.19681564, 0.21177526
    ]),
    100:
    array([
        0.03911314, 0.04550435, 0.04958972, 0.05770944, 0.06896805, 0.08288491,
        0.09771459, 0.10189766, 0.10752233, 0.1164863, 0.12735376, 0.13497319,
        0.14038704, 0.15150129
    ]),
    200:
    array([
        0.02793837, 0.03246737, 0.03536757, 0.04111863, 0.04910105, 0.0589757,
        0.06949443, 0.0724547, 0.07645096, 0.08280575, 0.09054264, 0.09600477,
        0.09980637, 0.10760793
    ]),
    400:
    array([
        0.01991454, 0.023116, 0.02516769, 0.0292415, 0.03488679, 0.0418774,
        0.04931093, 0.05141711, 0.054236, 0.05874581, 0.06421502, 0.06808278,
        0.07079796, 0.07628466
    ]),
    800:
    array([
        0.01415514, 0.01642927, 0.0178805, 0.02075938, 0.02475783, 0.02970526,
        0.03496731, 0.03645351, 0.03845017, 0.04164885, 0.04552405, 0.0482418,
        0.05017098, 0.05406992
    ]),
    1600:
    array([
        0.01005851, 0.01165883, 0.0126869, 0.01472426, 0.01754881, 0.02104396,
        0.02476615, 0.02581898, 0.02723521, 0.02950141, 0.03224859, 0.03418578,
        0.03553487, 0.03829879
    ])
}

# Coefficients are model log(cv) = b[0] + b[1] log(n) + b[2] log(n)**2
exp_asymp_crit_vals = {
    1.0: array([-1.04988875, -0.46444021, -0.00252528]),
    5.0: array([-0.89825066, -0.46444021, -0.00252528]),
    10.0: array([-0.81197037, -0.46444021, -0.00252528]),
    25.0: array([-0.66000008, -0.46444021, -0.00252528]),
    50.0: array([-0.4814209, -0.46444021, -0.00252528]),
    75.0: array([-0.29737272, -0.46444021, -0.00252528]),
    90.0: array([-0.13397474, -0.46444021, -0.00252528]),
    92.5: array([-0.09220104, -0.46444021, -0.00252528]),
    95.0: array([-0.03868789, -0.46444021, -0.00252528]),
    97.5: array([0.0411943, -0.46444021, -0.00252528]),
    99.0: array([0.130122, -0.46444021, -0.00252528]),
    99.5: array([0.1882404, -0.46444021, -0.00252528]),
    99.7: array([0.22712671, -0.46444021, -0.00252528]),
    99.9: array([0.30189677, -0.46444021, -0.00252528])
}


# Critical Value
critical_values = {'normal': normal_crit_vals, 'exp': exp_crit_vals}
asymp_critical_values = {
    'normal': normal_asymp_crit_vals,
    'exp': exp_asymp_crit_vals
}
