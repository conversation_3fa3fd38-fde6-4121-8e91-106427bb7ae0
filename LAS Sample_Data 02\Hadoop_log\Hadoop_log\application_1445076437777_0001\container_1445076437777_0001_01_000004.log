2015-10-17 18:09:38,318 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 18:09:38,490 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 18:09:38,490 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-17 18:09:38,537 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 18:09:38,537 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445076437777_0001, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@c5f5deb)
2015-10-17 18:09:38,974 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 18:09:40,130 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445076437777_0001
2015-10-17 18:09:41,912 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 18:09:43,458 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 18:09:43,662 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@17a80e30
2015-10-17 18:09:46,068 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/pageinput2.txt:268435456+134217728
2015-10-17 18:09:46,224 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-17 18:09:46,224 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-17 18:09:46,224 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-17 18:09:46,224 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-17 18:09:46,224 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-17 18:09:46,240 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-17 18:10:14,911 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 18:10:14,911 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 48249276; bufvoid = 104857600
2015-10-17 18:10:14,911 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 17305200(69220800); length = 8909197/6553600
2015-10-17 18:10:14,911 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 57318028 kvi 14329500(57318000)
2015-10-17 18:10:46,972 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-17 18:10:46,988 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 57318028 kv 14329500(57318000) kvi 12129788(48519152)
2015-10-17 18:10:56,035 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 18:10:56,035 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 57318028; bufend = 686843; bufvoid = 104857600
2015-10-17 18:10:56,035 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14329500(57318000); kvend = 5414592(21658368); length = 8914909/6553600
2015-10-17 18:10:56,035 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 9755595 kvi 2438892(9755568)
2015-10-17 18:11:28,252 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-17 18:11:28,252 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 9755595 kv 2438892(9755568) kvi 240952(963808)
2015-10-17 18:11:39,877 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 18:11:39,877 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 9755595; bufend = 58006021; bufvoid = 104857600
2015-10-17 18:11:39,877 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 2438892(9755568); kvend = 19744380(78977520); length = 8908913/6553600
2015-10-17 18:11:39,877 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 67074757 kvi 16768684(67074736)
2015-10-17 18:11:59,377 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-17 18:11:59,439 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 67074757 kv 16768684(67074736) kvi 14558340(58233360)
2015-10-17 18:12:08,986 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 18:12:08,986 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 67074757; bufend = 10447270; bufvoid = 104857600
2015-10-17 18:12:08,986 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 16768684(67074736); kvend = 7854692(31418768); length = 8913993/6553600
2015-10-17 18:12:08,986 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 19516006 kvi 4878996(19515984)
2015-10-17 18:12:31,876 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-17 18:12:31,923 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 19516006 kv 4878996(19515984) kvi 2677056(10708224)
2015-10-17 18:12:41,407 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 18:12:41,407 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 19516006; bufend = 67756598; bufvoid = 104857600
2015-10-17 18:12:41,407 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 4878996(19515984); kvend = 22182024(88728096); length = 8911373/6553600
2015-10-17 18:12:41,407 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 76825334 kvi 19206328(76825312)
2015-10-17 18:13:02,734 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-17 18:13:02,734 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 76825334 kv 19206328(76825312) kvi 17012764(68051056)
2015-10-17 18:13:31,827 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 18:13:31,827 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 76825334; bufend = 20217188; bufvoid = 104857598
2015-10-17 18:13:31,827 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 19206328(76825312); kvend = 10297172(41188688); length = 8909157/6553600
2015-10-17 18:13:31,827 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29285924 kvi 7321476(29285904)
2015-10-17 18:14:00,873 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 5
2015-10-17 18:14:00,967 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29285924 kv 7321476(29285904) kvi 5114320(20457280)
2015-10-17 18:14:12,420 INFO [communication thread] org.apache.hadoop.mapred.Task: Communication exception: java.io.IOException: Failed on local exception: java.io.IOException: An existing connection was forcibly closed by the remote host; Host Details : local host is: "MININT-75DGDAM1/************"; destination host is: "minint-fnanli5.fareast.corp.microsoft.com":52839; 
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:772)
	at org.apache.hadoop.ipc.Client.call(Client.java:1472)
	at org.apache.hadoop.ipc.Client.call(Client.java:1399)
	at org.apache.hadoop.ipc.WritableRpcEngine$Invoker.invoke(WritableRpcEngine.java:244)
	at com.sun.proxy.$Proxy9.statusUpdate(Unknown Source)
	at org.apache.hadoop.mapred.Task$TaskReporter.run(Task.java:737)
	at java.lang.Thread.run(Thread.java:724)
Caused by: java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.net.SocketInputStream$Reader.performIO(SocketInputStream.java:57)
	at org.apache.hadoop.net.SocketIOWithTimeout.doIO(SocketIOWithTimeout.java:142)
	at org.apache.hadoop.net.SocketInputStream.read(SocketInputStream.java:161)
	at org.apache.hadoop.net.SocketInputStream.read(SocketInputStream.java:131)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at org.apache.hadoop.ipc.Client$Connection$PingInputStream.read(Client.java:513)
	at java.io.BufferedInputStream.fill(BufferedInputStream.java:235)
	at java.io.BufferedInputStream.read(BufferedInputStream.java:254)
	at java.io.DataInputStream.readInt(DataInputStream.java:387)
	at org.apache.hadoop.ipc.Client$Connection.receiveRpcResponse(Client.java:1071)
	at org.apache.hadoop.ipc.Client$Connection.run(Client.java:966)

2015-10-17 18:14:18,280 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 18:14:18,280 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29285924; bufend = 77503154; bufvoid = 104857600
2015-10-17 18:14:18,280 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7321476(29285904); kvend = 24618672(98474688); length = 8917205/6553600
2015-10-17 18:14:18,280 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 86571906 kvi 21642972(86571888)
2015-10-17 18:14:34,530 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 6
2015-10-17 18:14:34,655 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 86571906 kv 21642972(86571888) kvi 19445800(77783200)
2015-10-17 18:14:35,452 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 0 time(s); maxRetries=45
2015-10-17 18:14:55,452 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 1 time(s); maxRetries=45
2015-10-17 18:15:15,452 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 2 time(s); maxRetries=45
2015-10-17 18:15:35,452 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 3 time(s); maxRetries=45
2015-10-17 18:15:55,453 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 4 time(s); maxRetries=45
2015-10-17 18:16:15,453 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 5 time(s); maxRetries=45
2015-10-17 18:16:35,453 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 6 time(s); maxRetries=45
2015-10-17 18:16:55,453 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 7 time(s); maxRetries=45
2015-10-17 18:17:15,454 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 8 time(s); maxRetries=45
2015-10-17 18:17:35,454 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 9 time(s); maxRetries=45
2015-10-17 18:17:55,454 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 10 time(s); maxRetries=45
2015-10-17 18:18:15,454 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 11 time(s); maxRetries=45
2015-10-17 18:18:35,454 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 12 time(s); maxRetries=45
2015-10-17 18:18:55,455 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 13 time(s); maxRetries=45
2015-10-17 18:19:15,455 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 14 time(s); maxRetries=45
2015-10-17 18:19:35,456 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 15 time(s); maxRetries=45
2015-10-17 18:19:55,456 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 16 time(s); maxRetries=45
2015-10-17 18:20:15,457 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 17 time(s); maxRetries=45
2015-10-17 18:20:35,458 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 18 time(s); maxRetries=45
2015-10-17 18:20:55,458 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 19 time(s); maxRetries=45
2015-10-17 18:21:15,459 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 20 time(s); maxRetries=45
2015-10-17 18:21:35,459 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 21 time(s); maxRetries=45
2015-10-17 18:21:55,460 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 22 time(s); maxRetries=45
2015-10-17 18:22:15,461 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 23 time(s); maxRetries=45
2015-10-17 18:22:35,461 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 24 time(s); maxRetries=45
2015-10-17 18:22:55,462 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 25 time(s); maxRetries=45
2015-10-17 18:23:15,462 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 26 time(s); maxRetries=45
2015-10-17 18:23:35,463 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 27 time(s); maxRetries=45
2015-10-17 18:23:55,464 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 28 time(s); maxRetries=45
2015-10-17 18:24:15,464 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 29 time(s); maxRetries=45
2015-10-17 18:24:35,465 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 30 time(s); maxRetries=45
2015-10-17 18:24:55,465 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 31 time(s); maxRetries=45
2015-10-17 18:25:15,466 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 32 time(s); maxRetries=45
2015-10-17 18:25:35,467 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 33 time(s); maxRetries=45
2015-10-17 18:25:55,467 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 34 time(s); maxRetries=45
2015-10-17 18:26:15,467 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 35 time(s); maxRetries=45
2015-10-17 18:26:35,467 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 36 time(s); maxRetries=45
2015-10-17 18:26:55,467 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 37 time(s); maxRetries=45
2015-10-17 18:27:15,482 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 38 time(s); maxRetries=45
