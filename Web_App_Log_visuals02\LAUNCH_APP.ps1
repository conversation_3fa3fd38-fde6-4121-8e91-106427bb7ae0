# Enhanced Well Log Analyzer Launcher (PowerShell)

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Enhanced Well Log Analyzer Launcher" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if we're in the right directory
if (-not (Test-Path "Core Application\well_log_app.py")) {
    Write-Host "ERROR: well_log_app.py not found in Core Application folder" -ForegroundColor Red
    Write-Host "Please run this script from the 'Web App Log visuals' directory" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if Python is installed
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ ERROR: Python is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Python 3.8+ and try again" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if Streamlit is installed
try {
    python -c "import streamlit" 2>$null
    Write-Host "✅ Streamlit is installed" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Installing required dependencies..." -ForegroundColor Yellow
    try {
        pip install -r "Configuration\requirements.txt"
        Write-Host "✅ Dependencies installed successfully" -ForegroundColor Green
    } catch {
        Write-Host "❌ ERROR: Failed to install dependencies" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host ""
Write-Host "🚀 Starting Enhanced Well Log Analyzer..." -ForegroundColor Green
Write-Host ""
Write-Host "📱 The application will open in your default web browser at:" -ForegroundColor Cyan
Write-Host "   http://localhost:8501" -ForegroundColor White
Write-Host ""
Write-Host "💡 Features available:" -ForegroundColor Yellow
Write-Host "   • Universal CSV support (any separator)" -ForegroundColor White
Write-Host "   • Intelligent column mapping (50+ variations)" -ForegroundColor White
Write-Host "   • Multi-well analysis" -ForegroundColor White
Write-Host "   • Lithology visualization" -ForegroundColor White
Write-Host "   • Professional well log plots" -ForegroundColor White
Write-Host ""
Write-Host "⚠️ Press Ctrl+C to stop the application" -ForegroundColor Yellow
Write-Host ""

# Change to Core Application directory and launch
Set-Location "Core Application"
streamlit run well_log_app.py
