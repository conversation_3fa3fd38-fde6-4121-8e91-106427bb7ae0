{"cells": [{"cell_type": "code", "execution_count": 11, "id": "6a60f385", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 12, "id": "b1fe3cdd", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(\"C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/CSV Data/volve_wells.csv\", usecols=['WELL', 'DEPTH', 'RHOB', 'GR', 'NPHI', 'PEF', 'DT'])"]}, {"cell_type": "code", "execution_count": 13, "id": "7b26884c", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['15/9-F-11 B', '15/9-F-11 A', '15/9-F-1 B', '15/9-F-1 A'],\n", "      dtype=object)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["df['WELL'].unique()"]}, {"cell_type": "code", "execution_count": 14, "id": "659bdb8c", "metadata": {}, "outputs": [], "source": ["# Training Wells\n", "training_wells = ['15/9-F-11 B', '15/9-F-11 A', '15/9-F-1 A']\n", "\n", "# Test Well\n", "test_well = ['15/9-F-1 B']"]}, {"cell_type": "code", "execution_count": 15, "id": "9e8ec356", "metadata": {}, "outputs": [], "source": ["train_val_df = df[df['WELL'].isin(training_wells)].copy()\n", "test_df = df[df['WELL'].isin(test_well)].copy()"]}, {"cell_type": "code", "execution_count": 16, "id": "5793b69a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DEPTH</th>\n", "      <th>DT</th>\n", "      <th>GR</th>\n", "      <th>NPHI</th>\n", "      <th>PEF</th>\n", "      <th>RHOB</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>116914.000000</td>\n", "      <td>21699.000000</td>\n", "      <td>115933.000000</td>\n", "      <td>37587.000000</td>\n", "      <td>37668.000000</td>\n", "      <td>37668.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>2154.233438</td>\n", "      <td>77.252247</td>\n", "      <td>51.823119</td>\n", "      <td>0.174302</td>\n", "      <td>6.450603</td>\n", "      <td>2.443072</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>1180.976133</td>\n", "      <td>14.350893</td>\n", "      <td>37.606884</td>\n", "      <td>0.085660</td>\n", "      <td>1.478121</td>\n", "      <td>0.166466</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>145.900000</td>\n", "      <td>53.165000</td>\n", "      <td>0.149100</td>\n", "      <td>0.010000</td>\n", "      <td>3.647000</td>\n", "      <td>1.627000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1148.525000</td>\n", "      <td>66.854450</td>\n", "      <td>22.126100</td>\n", "      <td>0.115000</td>\n", "      <td>5.078850</td>\n", "      <td>2.276000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>2122.800000</td>\n", "      <td>72.724000</td>\n", "      <td>52.217000</td>\n", "      <td>0.163000</td>\n", "      <td>6.548700</td>\n", "      <td>2.501000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>3097.100000</td>\n", "      <td>86.132300</td>\n", "      <td>74.201000</td>\n", "      <td>0.212100</td>\n", "      <td>7.728625</td>\n", "      <td>2.577000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>4770.200000</td>\n", "      <td>126.827000</td>\n", "      <td>1124.403000</td>\n", "      <td>0.593200</td>\n", "      <td>13.841000</td>\n", "      <td>3.090000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               DEPTH            DT             GR          NPHI           PEF  \\\n", "count  116914.000000  21699.000000  115933.000000  37587.000000  37668.000000   \n", "mean     2154.233438     77.252247      51.823119      0.174302      6.450603   \n", "std      1180.976133     14.350893      37.606884      0.085660      1.478121   \n", "min       145.900000     53.165000       0.149100      0.010000      3.647000   \n", "25%      1148.525000     66.854450      22.126100      0.115000      5.078850   \n", "50%      2122.800000     72.724000      52.217000      0.163000      6.548700   \n", "75%      3097.100000     86.132300      74.201000      0.212100      7.728625   \n", "max      4770.200000    126.827000    1124.403000      0.593200     13.841000   \n", "\n", "               RHOB  \n", "count  37668.000000  \n", "mean       2.443072  \n", "std        0.166466  \n", "min        1.627000  \n", "25%        2.276000  \n", "50%        2.501000  \n", "75%        2.577000  \n", "max        3.090000  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["train_val_df.describe()"]}, {"cell_type": "code", "execution_count": 17, "id": "47430556", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DEPTH</th>\n", "      <th>DT</th>\n", "      <th>GR</th>\n", "      <th>NPHI</th>\n", "      <th>PEF</th>\n", "      <th>RHOB</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>33191.000000</td>\n", "      <td>4262.000000</td>\n", "      <td>32498.000000</td>\n", "      <td>3413.000000</td>\n", "      <td>3441.000000</td>\n", "      <td>3441.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1805.400000</td>\n", "      <td>80.380006</td>\n", "      <td>56.864115</td>\n", "      <td>0.203836</td>\n", "      <td>6.445014</td>\n", "      <td>2.453695</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>958.156073</td>\n", "      <td>14.698333</td>\n", "      <td>35.935409</td>\n", "      <td>0.095991</td>\n", "      <td>0.811407</td>\n", "      <td>0.129990</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>145.900000</td>\n", "      <td>54.928300</td>\n", "      <td>0.149300</td>\n", "      <td>0.059500</td>\n", "      <td>4.729900</td>\n", "      <td>2.111800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>975.650000</td>\n", "      <td>71.513225</td>\n", "      <td>29.481175</td>\n", "      <td>0.146900</td>\n", "      <td>5.938300</td>\n", "      <td>2.379200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>1805.400000</td>\n", "      <td>76.295350</td>\n", "      <td>58.005700</td>\n", "      <td>0.172700</td>\n", "      <td>6.352900</td>\n", "      <td>2.482100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>2635.150000</td>\n", "      <td>85.365475</td>\n", "      <td>79.247200</td>\n", "      <td>0.222300</td>\n", "      <td>6.872600</td>\n", "      <td>2.536700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>3464.900000</td>\n", "      <td>125.982700</td>\n", "      <td>297.767300</td>\n", "      <td>0.557600</td>\n", "      <td>10.987600</td>\n", "      <td>3.051700</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              DEPTH           DT            GR         NPHI          PEF  \\\n", "count  33191.000000  4262.000000  32498.000000  3413.000000  3441.000000   \n", "mean    1805.400000    80.380006     56.864115     0.203836     6.445014   \n", "std      958.156073    14.698333     35.935409     0.095991     0.811407   \n", "min      145.900000    54.928300      0.149300     0.059500     4.729900   \n", "25%      975.650000    71.513225     29.481175     0.146900     5.938300   \n", "50%     1805.400000    76.295350     58.005700     0.172700     6.352900   \n", "75%     2635.150000    85.365475     79.247200     0.222300     6.872600   \n", "max     3464.900000   125.982700    297.767300     0.557600    10.987600   \n", "\n", "              RHOB  \n", "count  3441.000000  \n", "mean      2.453695  \n", "std       0.129990  \n", "min       2.111800  \n", "25%       2.379200  \n", "50%       2.482100  \n", "75%       2.536700  \n", "max       3.051700  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["test_df.describe()"]}, {"cell_type": "code", "execution_count": 18, "id": "437ce729", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DEPTH</th>\n", "      <th>DT</th>\n", "      <th>GR</th>\n", "      <th>NPHI</th>\n", "      <th>PEF</th>\n", "      <th>RHOB</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>21688.000000</td>\n", "      <td>21688.000000</td>\n", "      <td>21688.000000</td>\n", "      <td>21688.000000</td>\n", "      <td>21688.000000</td>\n", "      <td>21688.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>3141.098875</td>\n", "      <td>77.235857</td>\n", "      <td>39.803246</td>\n", "      <td>0.166648</td>\n", "      <td>7.093603</td>\n", "      <td>2.475232</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>314.723749</td>\n", "      <td>14.336048</td>\n", "      <td>57.907158</td>\n", "      <td>0.099200</td>\n", "      <td>1.188313</td>\n", "      <td>0.147635</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>2577.000000</td>\n", "      <td>53.165000</td>\n", "      <td>0.852000</td>\n", "      <td>0.010000</td>\n", "      <td>4.297800</td>\n", "      <td>1.980600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>2869.475000</td>\n", "      <td>66.849300</td>\n", "      <td>9.416350</td>\n", "      <td>0.096000</td>\n", "      <td>6.218475</td>\n", "      <td>2.379000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>3140.550000</td>\n", "      <td>72.720750</td>\n", "      <td>27.552000</td>\n", "      <td>0.136000</td>\n", "      <td>7.487700</td>\n", "      <td>2.533000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>3411.625000</td>\n", "      <td>86.093800</td>\n", "      <td>44.877425</td>\n", "      <td>0.217200</td>\n", "      <td>8.001000</td>\n", "      <td>2.581400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>3723.300000</td>\n", "      <td>126.827000</td>\n", "      <td>1124.403000</td>\n", "      <td>0.593200</td>\n", "      <td>13.841000</td>\n", "      <td>3.025000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              DEPTH            DT            GR          NPHI           PEF  \\\n", "count  21688.000000  21688.000000  21688.000000  21688.000000  21688.000000   \n", "mean    3141.098875     77.235857     39.803246      0.166648      7.093603   \n", "std      314.723749     14.336048     57.907158      0.099200      1.188313   \n", "min     2577.000000     53.165000      0.852000      0.010000      4.297800   \n", "25%     2869.475000     66.849300      9.416350      0.096000      6.218475   \n", "50%     3140.550000     72.720750     27.552000      0.136000      7.487700   \n", "75%     3411.625000     86.093800     44.877425      0.217200      8.001000   \n", "max     3723.300000    126.827000   1124.403000      0.593200     13.841000   \n", "\n", "               RHOB  \n", "count  21688.000000  \n", "mean       2.475232  \n", "std        0.147635  \n", "min        1.980600  \n", "25%        2.379000  \n", "50%        2.533000  \n", "75%        2.581400  \n", "max        3.025000  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["train_val_df.dropna(inplace=True)\n", "test_df.dropna(inplace=True)\n", "train_val_df.describe()"]}, {"cell_type": "code", "execution_count": 19, "id": "c9b2aa6e", "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split\n", "from sklearn import metrics\n", "from sklearn.ensemble import RandomForestRegressor"]}, {"cell_type": "code", "execution_count": 20, "id": "84fbddf5", "metadata": {}, "outputs": [], "source": ["X = train_val_df[['RHOB', 'GR', 'NPHI', 'PEF']]\n", "y = train_val_df['DT']"]}, {"cell_type": "code", "execution_count": 21, "id": "7fcfba9f", "metadata": {}, "outputs": [], "source": ["X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2)"]}, {"cell_type": "code", "execution_count": 22, "id": "bf0607ec", "metadata": {}, "outputs": [], "source": ["regr = RandomForestRegressor()"]}, {"cell_type": "code", "execution_count": 23, "id": "e2cd8cc8", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-1 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-1 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-1 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-1 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-1 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-1 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-1 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-1 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-1 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-1 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-1 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-1 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-1 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-1 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-1 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-1 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-1 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>RandomForestRegressor()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;RandomForestRegressor<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.ensemble.RandomForestRegressor.html\">?<span>Documentation for RandomForestRegressor</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>RandomForestRegressor()</pre></div> </div></div></div></div>"], "text/plain": ["RandomForestRegressor()"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["regr.fit(X_train, y_train)"]}, {"cell_type": "code", "execution_count": 24, "id": "f2e49d72", "metadata": {}, "outputs": [], "source": ["y_pred = regr.predict(X_val)"]}, {"cell_type": "code", "execution_count": 25, "id": "4c8653ff", "metadata": {}, "outputs": [{"data": {"text/plain": ["1.5846597503457807"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["metrics.mean_absolute_error(y_val, y_pred)"]}, {"cell_type": "code", "execution_count": 26, "id": "c75d22a8", "metadata": {}, "outputs": [], "source": ["mse = metrics.mean_squared_error(y_val, y_pred)\n", "rmse = mse**0.5"]}, {"cell_type": "code", "execution_count": 27, "id": "fc9efb7c", "metadata": {}, "outputs": [{"data": {"text/plain": ["2.805512713549683"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["rmse"]}, {"cell_type": "code", "execution_count": 28, "id": "2d45c259", "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x21a0c06caa0>]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.scatter(y_val, y_pred)\n", "plt.xlim(40, 140)\n", "plt.ylim(40, 140)\n", "plt.ylabel('Predicted DT')\n", "plt.xlabel('Actual DT')\n", "plt.plot([40,140], [40,140], 'black') #1 to 1 line"]}, {"cell_type": "code", "execution_count": 29, "id": "db1b6626", "metadata": {}, "outputs": [], "source": ["test_well_x = test_df[['RHOB', 'GR', 'NPHI', 'PEF']]"]}, {"cell_type": "code", "execution_count": 30, "id": "6d1407c9", "metadata": {}, "outputs": [], "source": ["test_df['TEST_DT'] = regr.predict(test_well_x)"]}, {"cell_type": "code", "execution_count": 31, "id": "b527de20", "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x21a0eb4d850>]"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.scatter(test_df['DT'], test_df['TEST_DT'])\n", "plt.xlim(40, 140)\n", "plt.ylim(40, 140)\n", "plt.ylabel('Predicted DT')\n", "plt.xlabel('Actual DT')\n", "plt.plot([40,140], [40,140], 'black') #1 to 1 line"]}, {"cell_type": "code", "execution_count": 32, "id": "e00584e5", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(15, 5))\n", "plt.plot(test_df['DEPTH'], test_df['DT'], label='Actual DT')\n", "plt.plot(test_df['DEPTH'], test_df['TEST_DT'], label='Predicted DT')\n", "plt.xlabel('Depth (m)')\n", "plt.ylabel('DT')\n", "plt.ylim(40, 140)\n", "plt.legend()\n", "plt.grid()"]}, {"cell_type": "code", "execution_count": null, "id": "2d8ce303", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e83468ab", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}