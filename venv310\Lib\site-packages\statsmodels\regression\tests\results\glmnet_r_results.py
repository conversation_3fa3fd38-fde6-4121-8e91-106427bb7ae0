import numpy as np

rslt_0 = np.array([
    100, 2, 0, 38.51677, -0.0002828592, -0.01440669])

rslt_1 = np.array([
    100, 2, 0, 5.991962, -0.00144282, -0.08156386])

rslt_2 = np.array([
    100, 2, 0, 0.9321552, -0.003400212, -0.2968083])

rslt_3 = np.array([
    100, 2, 0.5, 0.2352493, 0, -0.4099712])

rslt_4 = np.array([
    100, 2, 0.5, 0.07703354, 0, -0.5185569])

rslt_5 = np.array([
    100, 2, 0.5, 0.02522501, 0, -0.5578565])

rslt_6 = np.array([
    100, 2, 1, 0.1416793, 0, -0.4346288])

rslt_7 = np.array([
    100, 2, 1, 0.04639359, 0, -0.530877])

rslt_8 = np.array([
    100, 2, 1, 0.01829859, 0, -0.5592558])

rslt_9 = np.array([
    100, 3, 0, 38.51677, -0.0002931891, -0.01441805, 0.01019003])

rslt_10 = np.array([
    100, 3, 0, 5.991962, -0.00177573, -0.08193033, 0.05812813])

rslt_11 = np.array([
    100, 3, 0, 0.9321552, -0.007886374, -0.3017527, 0.2167204])

rslt_12 = np.array([
    100, 3, 0.5, 0.2143504, 0, -0.4350311, 0.2878706])

rslt_13 = np.array([
    100, 3, 0.5, 0.07019009, 0, -0.540186, 0.3830582])

rslt_14 = np.array([
    100, 3, 0.5, 0.02298409, -0.007207869, -0.5779581, 0.417658])

rslt_15 = np.array([
    100, 3, 1, 0.1176247, 0, -0.4728138, 0.3103276])

rslt_16 = np.array([
    100, 3, 1, 0.03851677, 0, -0.5564642, 0.393978])

rslt_17 = np.array([
    100, 3, 1, 0.01384221, -0.004948496, -0.5824816, 0.4202673])

rslt_18 = np.array([
    100, 5, 0, 38.51677, -0.0002957436,
    -0.01439344, 0.01022347, -0.001361073, 0.01156754])

rslt_19 = np.array([
    100, 5, 0, 5.991962, -0.001844459,
    -0.08115691, 0.05920275, -0.006436703, 0.06552873])

rslt_20 = np.array([
    100, 5, 0, 0.9321552, -0.008226096,
    -0.2921193, 0.2311589, -0.008479325, 0.2404427])

rslt_21 = np.array([
    100, 5, 0.5, 0.2143504, 0, -0.4132961, 0.3222149, 0, 0.3262246])

rslt_22 = np.array([
    100, 5, 0.5, 0.0639546, 0, -0.5145012, 0.4359585, 0, 0.4294001])

rslt_23 = np.array([
    100, 5, 0.5, 0.02094225, -0.00787628,
    -0.5466386, 0.4728884, 0.0106367, 0.4627422])

rslt_24 = np.array([
    100, 5, 1, 0.1176247, 0, -0.4469856, 0.3513112, 0, 0.3519996])

rslt_25 = np.array([
    100, 5, 1, 0.03851677, 0, -0.5243974, 0.4448608, 0, 0.4370223])

rslt_26 = np.array([
    100, 5, 1, 0.01261251, -0.00600524,
    -0.5501652, 0.4761259, 0.009292623, 0.4655236])

rslt_27 = np.array([
    200, 2, 0, 37.37243, -0.0006197982, -0.01442844])

rslt_28 = np.array([
    200, 2, 0, 5.813939, -0.002965157, -0.0813082])

rslt_29 = np.array([
    200, 2, 0, 0.9044607, -0.0046755, -0.2916905])

rslt_30 = np.array([
    200, 2, 0.5, 0.207982, 0, -0.4102587])

rslt_31 = np.array([
    200, 2, 0.5, 0.06810473, 0, -0.5061677])

rslt_32 = np.array([
    200, 2, 0.5, 0.02230123, 0, -0.5404787])

rslt_33 = np.array([
    200, 2, 1, 0.11413, 0, -0.443054])

rslt_34 = np.array([
    200, 2, 1, 0.03737243, 0, -0.5201972])

rslt_35 = np.array([
    200, 2, 1, 0.01343095, 0, -0.544259])

rslt_36 = np.array([
    200, 3, 0, 37.37243, -0.0006182692, -0.01444479, 0.01107838])

rslt_37 = np.array([
    200, 3, 0, 5.813939, -0.002912734, -0.08183274, 0.06300276])

rslt_38 = np.array([
    200, 3, 0, 0.9044607, -0.003838731, -0.298655, 0.2325274])

rslt_39 = np.array([
    200, 3, 0.5, 0.207982, 0, -0.426489, 0.3142956])

rslt_40 = np.array([
    200, 3, 0.5, 0.06810473, 0, -0.5287444, 0.4093069])

rslt_41 = np.array([
    200, 3, 0.5, 0.02230123, 0, -0.5654985, 0.4434813])

rslt_42 = np.array([
    200, 3, 1, 0.1252575, 0, -0.450539, 0.3272248])

rslt_43 = np.array([
    200, 3, 1, 0.04101619, 0, -0.540326, 0.4170119])

rslt_44 = np.array([
    200, 3, 1, 0.01343095, 0, -0.5697273, 0.4464131])

rslt_45 = np.array([
    200, 5, 0, 37.37243, -0.0006213086,
    -0.01441334, 0.01110832, -0.00159415, 0.01237059])

rslt_46 = np.array([
    200, 5, 0, 5.813939, -0.003007826,
    -0.08084357, 0.06395108, -0.008286656, 0.06957285])

rslt_47 = np.array([
    200, 5, 0, 0.9044607, -0.004970054,
    -0.2862855, 0.2446989, -0.02121838, 0.2488685])

rslt_48 = np.array([
    200, 5, 0.5, 0.207982, 0, -0.398901, 0.3436805, 0, 0.3354236])

rslt_49 = np.array([
    200, 5, 0.5, 0.06810473, 0, -0.4912754, 0.4492343, 0, 0.4280285])

rslt_50 = np.array([
    200, 5, 0.5, 0.02230123, 0,
    -0.5240797, 0.4869532, -0.007872715, 0.4611676])

rslt_51 = np.array([
    200, 5, 1, 0.11413, 0, -0.4298238, 0.3738058, 0, 0.3604706])

rslt_52 = np.array([
    200, 5, 1, 0.03737243, 0, -0.5044899, 0.4632316, 0, 0.4395307])

rslt_53 = np.array([
    200, 5, 1, 0.01343095, 0, -0.5276608, 0.4907892, -0.005343287, 0.4641237])

rslt_54 = np.array([
    300, 2, 0, 34.28346, -0.0003952878, -0.01440603])

rslt_55 = np.array([
    300, 2, 0, 5.333396, -0.001501322, -0.08029644])

rslt_56 = np.array([
    300, 2, 0, 0.8297037, 0.002123792, -0.2786158])

rslt_57 = np.array([
    300, 2, 0.5, 0.1907915, 0, -0.3787717])

rslt_58 = np.array([
    300, 2, 0.5, 0.06247562, 0, -0.4648646])

rslt_59 = np.array([
    300, 2, 0.5, 0.02045795, 0.008282189, -0.4960003])

rslt_60 = np.array([
    300, 2, 1, 0.1046967, 0, -0.4057543])

rslt_61 = np.array([
    300, 2, 1, 0.03428346, 0, -0.476403])

rslt_62 = np.array([
    300, 2, 1, 0.01232084, 0.006463487, -0.4988773])

rslt_63 = np.array([
    300, 3, 0, 34.28346, -0.000389794, -0.01441521, 0.01276503])

rslt_64 = np.array([
    300, 3, 0, 5.333396, -0.001327446, -0.08058424, 0.07142295])

rslt_65 = np.array([
    300, 3, 0, 0.8297037, 0.004333533, -0.2821765, 0.2505531])

rslt_66 = np.array([
    300, 3, 0.5, 0.2093935, 0, -0.3745606, 0.3227598])

rslt_67 = np.array([
    300, 3, 0.5, 0.06856692, 0, -0.4707627, 0.4155033])

rslt_68 = np.array([
    300, 3, 0.5, 0.02045795, 0.01586083, -0.5078635, 0.4505092])

rslt_69 = np.array([
    300, 3, 1, 0.1149045, 0, -0.4043541, 0.347238])

rslt_70 = np.array([
    300, 3, 1, 0.03762605, 0, -0.483917, 0.4268009])

rslt_71 = np.array([
    300, 3, 1, 0.01232084, 0.01417268, -0.5109371, 0.4530943])

rslt_72 = np.array([
    300, 5, 0, 34.28346, -0.0003887545,
    -0.01441065, 0.01279253, -0.001336729, 0.01332977])

rslt_73 = np.array([
    300, 5, 0, 5.333396, -0.001294265,
    -0.08044256, 0.07228947, -0.006888477, 0.07488032])

rslt_74 = np.array([
    300, 5, 0, 0.8297037, 0.00476926,
    -0.2804704, 0.2614478, -0.01761104, 0.2661808])

rslt_75 = np.array([
    300, 5, 0.5, 0.2093935, 0, -0.3705814, 0.347887, 0, 0.351998])

rslt_76 = np.array([
    300, 5, 0.5, 0.06856692, 0, -0.465371, 0.449883, 0, 0.4508697])

rslt_77 = np.array([
    300, 5, 0.5, 0.02045795, 0.01744528,
    -0.5021737, 0.4881574, -0.007930221, 0.4878982])

rslt_78 = np.array([
    300, 5, 1, 0.1149045, 0, -0.3996701, 0.3772643, 0, 0.3807041])

rslt_79 = np.array([
    300, 5, 1, 0.03762605, 0, -0.4782089, 0.4633918, 0, 0.4639368])

rslt_80 = np.array([
    300, 5, 1, 0.01232084, 0.01578941,
    -0.5051176, 0.4915133, -0.005703398, 0.4910846])
