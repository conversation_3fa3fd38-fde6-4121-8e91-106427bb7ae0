import numpy as np

from statsmodels.tools.testing import ParamsTableTestBunch

est = dict(
    deviance=18.59164098607571,
    dispers=1.859164098607571,
    deviance_s=18.59164098607571,
    dispers_s=1.859164098607571,
    deviance_p=24.75374834715614,
    dispers_p=2.475374834715614,
    deviance_ps=24.75374834715614,
    dispers_ps=2.475374834715614,
    bic=-9.740492454486454,
    nbml=0,
    N=17,
    ic=3,
    k=7,
    k_eq=1,
    k_dv=1,
    converged=1,
    k_autoCns=0,
    ll=-31.92732830809848,
    chi2=128.8021169250575,
    p=2.29729497374e-25,
    rc=0,
    aic=4.579685683305704,
    rank=7,
    canonical=1,
    power=0,
    df_m=6,
    df=10,
    vf=1,
    phi=1,
    k_eq_model=0,
    properties="b V",
    depvar="executions",
    which="max",
    technique="nr",
    singularHmethod="m-marquardt",
    ml_method="e2",
    crittype="log likelihood",
    user="glim_lf",
    title="Generalized linear models",
    opt="moptimize",
    chi2type="Wald",
    link="glim_l03",
    varfunc="glim_v3",
    m="1",
    a="1",
    oim="oim",
    opt1="ML",
    varfuncf="u",
    varfunct="Poisson",
    linkf="ln(u)",
    linkt="Log",
    vce="oim",
    vcetype="OIM",
    hac_lag="15",
    marginsok="default",
    marginsnotok="stdp Anscombe Cooksd Deviance Hat Likelihood Pearson Response Score Working ADJusted STAndardized STUdentized MODified",  # noqa:E501
    predict="glim_p",
    cmd="glm",
    cmdline="glm executions income perpoverty perblack LN_VC100k96 south degree, family(poisson)",  # noqa:E501
)

params_table = np.array([
    .00026110166569,  .00005187148786,  5.0336259178483,  4.812884279e-07,
    .00015943541766,  .00036276791372, np.nan,  1.9599639845401,
    0,  .07781804809828,  .07940260798777,  .98004398180811,
    .32706440886796,  -.0778082038363,  .23344430003287, np.nan,
    1.9599639845401,                0, -.09493110013466,  .02291930335216,
    -4.1419714498302,  .00003443332141, -.13985210925565, -.05001009101367,
    np.nan,  1.9599639845401,                0,  .29693462055586,
    .43751760764129,  .67868038993144,  .49734039404176,  -.5605841330232,
    1.1544533741349, np.nan,  1.9599639845401,                0,
    2.3011832004524,  .42838381728481,  5.3717790159251,  7.796361708e-08,
    1.4615663470144,  3.1408000538904, np.nan,  1.9599639845401,
    0, -18.722067603077,  4.2839791307242, -4.3702518223781,
    .00001241033322, -27.118512409818, -10.325622796337, np.nan,
    1.9599639845401,                0, -6.8014789919532,   4.146873025502,
    -1.6401464308471,  .10097472438129, -14.929200770398,  1.3262427864914,
    np.nan,  1.9599639845401,                0]).reshape(7, 9)

params_table_colnames = 'b se z pvalue ll ul df crit eform'.split()

params_table_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                         'south', 'degree', '_cons']

cov = np.array([
    2.690651253e-09,  1.942168909e-06,  9.445812833e-08,  4.703695025e-06,
    -6.082922480e-06, -.00008108248895, -.00013492774575,  1.942168909e-06,
    .00630477415526,  .00017467012687,  .00328093520848, -.01768604570302,
    .11117887243846, -.19441636422025,  9.445812833e-08,  .00017467012687,
    .00052529446615, -.00313545508833, -.00516707569472, -.03253594627601,
    .01688876616272,  4.703695025e-06,  .00328093520848, -.00313545508833,
    .19142165699616, -.00179497953339,  .30391667530759, -1.4489146451821,
    -6.082922480e-06, -.01768604570302, -.00516707569472, -.00179497953339,
    .18351269491151,   .3016848477378,  .36484063612427, -.00008108248895,
    .11117887243846, -.03253594627601,  .30391667530759,   .3016848477378,
    18.352477192481, -4.0741043266703, -.00013492774575, -.19441636422025,
    .01688876616272, -1.4489146451821,  .36484063612427, -4.0741043266703,
    17.196555889636]).reshape(7, 7)

cov_colnames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

cov_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

infocrit = np.array([
    17, np.nan, -31.927328308098,                7,
    77.854656616197,   83.68715002459])

infocrit_colnames = 'N ll0 ll df AIC BIC'.split()

infocrit_rownames = '.'.split()

predicted = np.array([
    35.226364135742,  .16681243479252,  .98022246360779,  8.1965742111206,
    .33106967806816,  .89840310811996,  1.3118965625763,  .29945519566536,
    .11764223873615,  3.6862981319427,  .35516858100891,  .46500706672668,
    2.0823004245758,   .3434439599514,  .24561515450478,  1.0650315284729,
    .62310123443604,  .41350400447845,  1.9260421991348,  .40797635912895,
    .32057955861092,  2.4171404838562,  .36215576529503,  .31702440977097,
    1.8473218679428,   .3869916498661,  .27665960788727,  2.8643238544464,
    .43869277834892,  .55124300718307,  3.1211984157562,  .44224792718887,
    .61045408248901,   3.338207244873,  .42789322137833,  .61120104789734,
    2.5269968509674,  .42458593845367,  .45554983615875,  .89725440740585,
    .59187793731689,  .31432569026947,  .97933322191238,  .37813624739647,
    .14003194868565,  .53462094068527,  .38791963458061,  .08045063912868,
    1.9790935516357,  .31954729557037,  .20208616554737]).reshape(17, 3)

predicted_colnames = 'predict_mu predict_linpred_std predict_hat'.split()

predicted_rownames = ['r'+str(n) for n in range(1, 18)]

resids = np.array([
    1.773634314537,   1.773634314537,  .29638093709946,  .29637759923935,
    .2988341152668,  .05034962296486,  .80342543125153,  .80342543125153,
    .27623143792152,  .27622014284134,  .28062695264816,  .09801965206861,
    4.6881031990051,  4.6881031990051,  3.0157172679901,   2.977787733078,
    4.0930528640747,  3.5735311508179,  .31370183825493,  .31370183825493,
    .1611547768116,  .16114975512028,  .16338862478733,  .08509942144156,
    .91769951581955,  .91769951581955,  .59656941890717,  .59618371725082,
    .63595855236053,  .44071426987648,   .9349684715271,   .9349684715271,
    .80822360515594,  .80661898851395,  .90597397089005,  .87787866592407,
    .07395775616169,  .07395775616169,  .05295527353883,  .05295492336154,
    .05329062789679,  .03839882463217, -.41714036464691, -.41714036464691,
    -.27668312191963, -.27663832902908,  -.2683065533638, -.17257598042488,
    -.84732186794281, -.84732186794281, -.68459099531174, -.68349820375443,
    -.6234148144722,   -.458675801754, -1.8643238544464, -1.8643238544464,
    -1.2799508571625,  -1.274356007576, -1.1015654802322, -.65087747573853,
    -2.1211984157562, -2.1211984157562, -1.4092296361923, -1.4021278619766,
    -1.2006615400314, -.67961025238037,  -2.338207244873,  -2.338207244873,
    -1.5136297941208, -1.5051733255386, -1.2797535657883, -.70043802261353,
    -1.5269968509674, -1.5269968509674, -1.0992211103439, -1.0954134464264,
    -.9605849981308, -.60427337884903,  .10274560004473,  .10274560004473,
    .10649761557579,   .1064917370677,  .10846894979477,  .11451110988855,
    .02066676132381,  .02066676132381,  .02081091701984,  .02081087417901,
    .02088368684053,  .02110289037228,  .46537905931473,  .46537905931473,
    .56824368238449,  .56713002920151,  .63647866249084,  .87048417329788,
    -.97909361124039, -.97909361124039, -.77151334285736, -.77000600099564,
    -.69597083330154, -.49471819400787]).reshape(17, 6)

resids_colnames = ['score_factor', 'resid_response', 'resid_anscombe',
                   'resid_deviance', 'resid_pearson', 'resid_working']

resids_rownames = ['r'+str(n) for n in range(1, 18)]


results_poisson_none_nonrobust = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    infocrit=infocrit,
    infocrit_colnames=infocrit_colnames,
    infocrit_rownames=infocrit_rownames,
    predicted=predicted,
    predicted_colnames=predicted_colnames,
    predicted_rownames=predicted_rownames,
    resids=resids,
    resids_colnames=resids_colnames,
    resids_rownames=resids_rownames,
    **est
)

est = dict(
    deviance=23.34969514421719,
    dispers=.8980651978545075,
    deviance_s=23.34969514421719,
    dispers_s=.8980651978545075,
    deviance_p=30.06164170990202,
    dispers_p=1.156216988842385,
    deviance_ps=30.06164170990202,
    dispers_ps=1.156216988842385,
    bic=-67.5595014539113,
    nbml=0,
    N=33,
    ic=3,
    k=7,
    k_eq=1,
    k_dv=1,
    converged=1,
    k_autoCns=0,
    ll=-52.96941847346162,
    chi2=183.6836771894393,
    p=5.59891844113e-37,
    rc=0,
    aic=3.634510210512826,
    rank=7,
    canonical=1,
    power=0,
    df_m=6,
    df=26,
    vf=1,
    phi=1,
    k_eq_model=0,
    properties="b V",
    depvar="executions",
    which="max",
    technique="nr",
    singularHmethod="m-marquardt",
    ml_method="e2",
    crittype="log likelihood",
    user="glim_lf",
    title="Generalized linear models",
    opt="moptimize",
    chi2type="Wald",
    wtype="fweight",
    wexp="= fweight",
    link="glim_l03",
    varfunc="glim_v3",
    m="1",
    a="1",
    oim="oim",
    opt1="ML",
    varfuncf="u",
    varfunct="Poisson",
    linkf="ln(u)",
    linkt="Log",
    vce="oim",
    vcetype="OIM",
    hac_lag="15",
    marginsok="default",
    marginsnotok="stdp Anscombe Cooksd Deviance Hat Likelihood Pearson Response Score Working ADJusted STAndardized STUdentized MODified",  # noqa:E501
    predict="glim_p",
    cmd="glm",
    cmdline="glm executions income perpoverty perblack LN_VC100k96 south degree [fweight=fweight], family(poisson)",  # noqa:E501
)

params_table = np.array([
    .00025343868829,  .00004015414514,  6.3116444744157,  2.760858933e-10,
    .00017473800999,  .00033213936659, np.nan,  1.9599639845401,
    0,  .09081422305585,  .06472607217881,  1.4030547505642,
    .16060051303473, -.03604654727537,  .21767499338706, np.nan,
    1.9599639845401,                0, -.09416451429381,  .01795769655821,
    -5.2436855689475,  1.574003474e-07, -.12936095279319, -.05896807579442,
    np.nan,  1.9599639845401,                0,  .27652273809506,
    .38626128010796,   .7158955669017,  .47405583598111, -.48053545953887,
    1.033580935729, np.nan,  1.9599639845401,                0,
    2.239890838384,  .36339399714255,  6.1638080320445,  7.101602988e-10,
    1.5276516917866,  2.9521299849815, np.nan,  1.9599639845401,
    0, -18.842583191417,   3.736940161486, -5.0422491067996,
    4.600917913e-07,  -26.16685132031, -11.518315062523, np.nan,
    1.9599639845401,                0, -6.5630017977416,  3.2352486362722,
    -2.0285927097411,  .04249979172538, -12.903972605867, -.22203098961573,
    np.nan,  1.9599639845401,                0]).reshape(7, 9)

params_table_colnames = 'b se z pvalue ll ul df crit eform'.split()

params_table_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                         'south', 'degree', '_cons']

cov = np.array([
    1.612355372e-09,  1.270985149e-06,  8.789752394e-08, -1.636449642e-07,
    -3.213686689e-06, -.00005643188411, -.00006199883309,  1.270985149e-06,
    .0041894644197,  .00016567874308, -.00066453618021, -.00943379587945,
    .07218307550995, -.11262571631082,  8.789752394e-08,  .00016567874308,
    .00032247886568, -.00355795369216, -.00391377556228, -.01880905186772,
    .01900717143416, -1.636449642e-07, -.00066453618021, -.00355795369216,
    .14919777651064,  .02481983169552,  .26952997380446, -.95915288407306,
    -3.213686689e-06, -.00943379587945, -.00391377556228,  .02481983169552,
    .13205519715924,  .44364186152042,  -.0298149336078, -.00005643188411,
    .07218307550995, -.01880905186772,  .26952997380446,  .44364186152042,
    13.964721770527, -3.6510403528048, -.00006199883309, -.11262571631082,
    .01900717143416, -.95915288407306,  -.0298149336078, -3.6510403528048,
    10.466833738501]).reshape(7, 7)

cov_colnames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

cov_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

infocrit = np.array([
    33, np.nan, -52.969418473462,                7,
    119.93883694692,  130.41438987719])

infocrit_colnames = 'N ll0 ll df AIC BIC'.split()

infocrit_rownames = '.'.split()

predicted = np.array([
    34.815238952637,  .16658315062523,  .96612107753754,  7.3026847839355,
    .32757967710495,  .78363972902298,  1.2540435791016,  .26076200604439,
    .08527097851038,  3.9734709262848,  .24942673742771,  .24720433354378,
    2.0739872455597,  .24682784080505,  .12635557353497,  1.1471545696259,
    .45427960157394,  .23673823475838,  1.7763512134552,  .27608770132065,
    .13540133833885,  2.2698366641998,  .25641229748726,   .1492355465889,
    1.6349502801895,  .27634221315384,  .12485299259424,  2.7504913806915,
    .39550569653511,  .43024495244026,   2.862185716629,  .39729079604149,
    .45176732540131,  3.5617923736572,  .39150056242943,  .54592549800873,
    2.6135795116425,  .29556328058243,  .22831618785858,    .775799036026,
    .40655690431595,  .12823067605495,  .93375068902969,  .29390665888786,
    .08065843582153,  .56681954860687,  .28863781690598,  .04722274839878,
    1.8914022445679,  .21889741718769,  .09062857925892]).reshape(17, 3)

predicted_colnames = 'predict_mu predict_linpred_std predict_hat'.split()

predicted_rownames = ['r'+str(n) for n in range(1, 18)]

resids = np.array([
    2.1847612857819,  2.1847612857819,  .36650228500366,  .36649596691132,
    .3702706694603,  .06275302171707,  1.6973150968552,  1.6973150968552,
    .60597640275955,  .60585051774979,  .62808901071548,  .23242343962193,
    4.7459564208984,  4.7459564208984,  3.0897438526154,  3.0483965873718,
    4.2380628585815,  3.7845225334167,  .02652905881405,  .02652905881405,
    .01329397037625,  .01329396758229,  .01330873556435,  .00667654490098,
    .92601269483566,  .92601269483566,  .60273587703705,  .60233747959137,
    .64300429821014,  .44648909568787,   .8528453707695,   .8528453707695,
    .72065913677216,  .71955502033234,   .7962681055069,   .7434441447258,
    .22364875674248,  .22364875674248,  .16446639597416,  .16445553302765,
    .16780391335487,  .12590345740318, -.26983660459518, -.26983660459518,
    -.1828535348177, -.18284019827843,  -.1791032999754, -.11887931078672,
    -.63495022058487, -.63495022058487, -.53598040342331, -.53542107343674,
    -.49657794833183, -.38836058974266, -1.7504912614822, -1.7504912614822,
    -1.2204585075378, -1.2154930830002, -1.0554916858673, -.63642859458923,
    -1.862185716629,  -1.862185716629, -1.2788465023041, -1.2732635736465,
    -1.1007128953934, -.65061664581299, -2.5617923736572, -2.5617923736572,
    -1.617108464241, -1.6071890592575, -1.3574055433273, -.71924245357513,
    -1.6135795116425, -1.6135795116425, -1.1469231843948, -1.1426799297333,
    -.99809640645981, -.61738300323486,  .22420094907284,  .22420094907284,
    .24363535642624,  .24356025457382,  .25454398989677,  .28899359703064,
    .06624934077263,  .06624934077263,  .06777309626341,  .06777160614729,
    .06855925172567,  .07094971090555,  .43318045139313,  .43318045139313,
    .51954871416092,  .51871728897095,  .57536894083023,  .76422989368439,
    -.89140218496323, -.89140218496323,  -.7140833735466,  -.7128586769104,
    -.64815932512283, -.47129172086716]).reshape(17, 6)

resids_colnames = ['score_factor', 'resid_response', 'resid_anscombe',
                   'resid_deviance', 'resid_pearson', 'resid_working']

resids_rownames = ['r'+str(n) for n in range(1, 18)]


results_poisson_fweight_nonrobust = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    infocrit=infocrit,
    infocrit_colnames=infocrit_colnames,
    infocrit_rownames=infocrit_rownames,
    predicted=predicted,
    predicted_colnames=predicted_colnames,
    predicted_rownames=predicted_rownames,
    resids=resids,
    resids_colnames=resids_colnames,
    resids_rownames=resids_rownames,
    **est
)

est = dict(
    deviance=12.02863083186947,
    dispers=1.202863083186947,
    deviance_s=12.02863083186947,
    dispers_s=1.202863083186947,
    deviance_p=15.48630027479802,
    dispers_p=1.548630027479802,
    deviance_ps=15.48630027479802,
    dispers_ps=1.548630027479802,
    bic=-16.30350260869269,
    nbml=0,
    N=17,
    ic=3,
    k=7,
    k_eq=1,
    k_dv=1,
    converged=1,
    k_autoCns=0,
    ll=-27.28727618329841,
    chi2=94.62492461274286,
    p=3.30927661191e-18,
    rc=0,
    aic=4.033797198035106,
    rank=7,
    canonical=1,
    power=0,
    df_m=6,
    df=10,
    vf=1,
    phi=1,
    k_eq_model=0,
    properties="b V",
    depvar="executions",
    which="max",
    technique="nr",
    singularHmethod="m-marquardt",
    ml_method="e2",
    crittype="log likelihood",
    user="glim_lf",
    title="Generalized linear models",
    opt="moptimize",
    chi2type="Wald",
    wtype="aweight",
    wexp="= fweight",
    link="glim_l03",
    varfunc="glim_v3",
    m="1",
    a="1",
    oim="oim",
    opt1="ML",
    varfuncf="u",
    varfunct="Poisson",
    linkf="ln(u)",
    linkt="Log",
    vce="oim",
    vcetype="OIM",
    hac_lag="15",
    marginsok="default",
    marginsnotok="stdp Anscombe Cooksd Deviance Hat Likelihood Pearson Response Score Working ADJusted STAndardized STUdentized MODified",  # noqa:E501
    predict="glim_p",
    cmd="glm",
    cmdline="glm executions income perpoverty perblack LN_VC100k96 south degree [aweight=fweight], family(poisson)",  # noqa:E501
)

params_table = np.array([
    .00025343868829,  .00005594520811,  4.5301232557793,  5.894928560e-06,
    .00014378809529,  .00036308928129, np.nan,  1.9599639845401,
    0,  .09081422305585,  .09018031800722,  1.0070293059798,
    .31392069129295, -.08593595235267,  .26756439846436, np.nan,
    1.9599639845401,                0, -.09416451429381,  .02501975991718,
    -3.7636058301716,  .00016748080115, -.14320234263332, -.04512668595429,
    np.nan,  1.9599639845401,                0,  .27652273809507,
    .53816281293549,  .51382728692594,  .60737274844619, -.77825699307725,
    1.3313024692674, np.nan,  1.9599639845401,                0,
    2.239890838384,  .50630271729905,   4.424015044464,  9.688326910e-06,
    1.2475557472031,  3.2322259295649, np.nan,  1.9599639845401,
    0, -18.842583191417,  5.2065333302747, -3.6190267105084,
    .00029571311817, -29.047201003062, -8.6379653797707, np.nan,
    1.9599639845401,                0, -6.5630017977417,  4.5075460479893,
    -1.4560032727052,  .14539171490364, -15.397629710457,  2.2716261149733,
    np.nan,  1.9599639845401,                0]).reshape(7, 9)

params_table_colnames = 'b se z pvalue ll ul df crit eform'.split()

params_table_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                         'south', 'degree', '_cons']

cov = np.array([
    3.129866310e-09,  2.467206465e-06,  1.706246053e-07, -3.176637541e-07,
    -6.238332985e-06, -.00010954424563,   -.000120350676,  2.467206465e-06,
    .00813248975588,  .00032161167774, -.00128998199687, -.01831266258952,
    .14012008775466, -.21862639048575,  1.706246053e-07,  .00032161167774,
    .00062598838631, -.00690661599067, -.00759732903266, -.03651168891971,
    .03689627396044, -3.176637541e-07, -.00128998199687, -.00690661599067,
    .28961921322663,  .04817967329131,  .52320524326798, -1.8618850102603,
    -6.238332985e-06, -.01831266258952, -.00759732903266,  .04817967329131,
    .2563424415444,  .86118714295143, -.05787604759173, -.00010954424563,
    .14012008775466, -.03651168891971,  .52320524326798,  .86118714295143,
    27.107989319261, -7.0873136260377,   -.000120350676, -.21862639048575,
    .03689627396044, -1.8618850102603, -.05787604759173, -7.0873136260377,
    20.317971374744]).reshape(7, 7)

cov_colnames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

cov_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

infocrit = np.array([
    17, np.nan, -27.287276183298,                7,
    68.574552366597,   74.40704577499])

infocrit_colnames = 'N ll0 ll df AIC BIC'.split()

infocrit_rownames = '.'.split()

predicted = np.array([
    34.815238952637,  .23209382593632,  1.8754115104675,  7.3026847839355,
    .45640400052071,   1.521183013916,  1.2540435791016,  .36330956220627,
    .16552601754665,  3.9734709262848,  .34751656651497,  .47986721992493,
    2.0739872455597,  .34389564394951,   .2452784627676,  1.1471545696259,
    .63293009996414,  .45955070853233,  1.7763512134552,  .38466224074364,
    .2628378868103,  2.2698366641998,  .35724925994873,  .28969252109528,
    1.6349502801895,  .38501682877541,  .24236169457436,  2.7504913806915,
    .55104273557663,  .83518141508102,   2.862185716629,  .55352979898453,
    .87696009874344,  3.5617923736572,  .54546248912811,  1.0597376823425,
    2.6135795116425,  .41179683804512,  .44320201873779,    .775799036026,
    .5664399266243,  .24891836941242,  .93375068902969,  .40948873758316,
    .15657225251198,  .56681954860687,  .40214782953262,  .09166768193245,
    1.8914022445679,  .30498126149178,  .17592607438564]).reshape(17, 3)

predicted_colnames = 'predict_mu predict_linpred_std predict_hat'.split()

predicted_rownames = ['r'+str(n) for n in range(1, 18)]

resids = np.array([
    2.1847612857819,  2.1847612857819,  .36650228500366,  .36649596691132,
    .3702706694603,  .06275302171707,  1.6973150968552,  1.6973150968552,
    .60597640275955,  .60585051774979,  .62808901071548,  .23242343962193,
    4.7459564208984,  4.7459564208984,  3.0897438526154,  3.0483965873718,
    4.2380628585815,  3.7845225334167,  .02652905881405,  .02652905881405,
    .01329397037625,  .01329396758229,  .01330873556435,  .00667654490098,
    .92601269483566,  .92601269483566,  .60273587703705,  .60233747959137,
    .64300429821014,  .44648909568787,   .8528453707695,   .8528453707695,
    .72065913677216,  .71955502033234,   .7962681055069,   .7434441447258,
    .22364875674248,  .22364875674248,  .16446639597416,  .16445553302765,
    .16780391335487,  .12590345740318, -.26983660459518, -.26983660459518,
    -.1828535348177, -.18284019827843,  -.1791032999754, -.11887931078672,
    -.63495022058487, -.63495022058487, -.53598040342331, -.53542107343674,
    -.49657794833183, -.38836058974266, -1.7504912614822, -1.7504912614822,
    -1.2204585075378, -1.2154930830002, -1.0554916858673, -.63642859458923,
    -1.862185716629,  -1.862185716629, -1.2788465023041, -1.2732635736465,
    -1.1007128953934, -.65061664581299, -2.5617923736572, -2.5617923736572,
    -1.617108464241, -1.6071890592575, -1.3574055433273, -.71924245357513,
    -1.6135795116425, -1.6135795116425, -1.1469231843948, -1.1426799297333,
    -.99809640645981, -.61738300323486,  .22420094907284,  .22420094907284,
    .24363535642624,  .24356025457382,  .25454398989677,  .28899359703064,
    .06624934077263,  .06624934077263,  .06777309626341,  .06777160614729,
    .06855925172567,  .07094971090555,  .43318045139313,  .43318045139313,
    .51954871416092,  .51871728897095,  .57536894083023,  .76422989368439,
    -.89140218496323, -.89140218496323,  -.7140833735466,  -.7128586769104,
    -.64815932512283, -.47129172086716]).reshape(17, 6)

resids_colnames = ['score_factor', 'resid_response', 'resid_anscombe',
                   'resid_deviance', 'resid_pearson', 'resid_working']

resids_rownames = ['r'+str(n) for n in range(1, 18)]


results_poisson_aweight_nonrobust = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    infocrit=infocrit,
    infocrit_colnames=infocrit_colnames,
    infocrit_rownames=infocrit_rownames,
    predicted=predicted,
    predicted_colnames=predicted_colnames,
    predicted_rownames=predicted_rownames,
    resids=resids,
    resids_colnames=resids_colnames,
    resids_rownames=resids_rownames,
    **est
)

est = dict(
    deviance=23.34969514421719,
    dispers=2.33496951442172,
    deviance_s=23.34969514421719,
    dispers_s=2.33496951442172,
    deviance_p=30.06164170990202,
    dispers_p=3.006164170990202,
    deviance_ps=30.06164170990202,
    dispers_ps=3.006164170990202,
    bic=-4.982438296344967,
    nbml=0,
    N=17,
    ic=3,
    k=7,
    k_eq=1,
    k_dv=1,
    converged=1,
    k_autoCns=0,
    ll=-52.96941847346162,
    chi2=356.6637749656061,
    p=5.72458312679e-74,
    rc=0,
    aic=7.055225702760191,
    rank=7,
    canonical=1,
    power=0,
    df_m=6,
    df=10,
    vf=1,
    phi=1,
    k_eq_model=0,
    properties="b V",
    depvar="executions",
    which="max",
    technique="nr",
    singularHmethod="m-marquardt",
    ml_method="e2",
    crittype="log pseudolikelihood",
    user="glim_lf",
    title="Generalized linear models",
    opt="moptimize",
    chi2type="Wald",
    wtype="pweight",
    wexp="= fweight",
    link="glim_l03",
    varfunc="glim_v3",
    m="1",
    a="1",
    oim="oim",
    opt1="ML",
    varfuncf="u",
    varfunct="Poisson",
    linkf="ln(u)",
    linkt="Log",
    vcetype="Robust",
    hac_lag="15",
    marginsok="default",
    marginsnotok="stdp Anscombe Cooksd Deviance Hat Likelihood Pearson Response Score Working ADJusted STAndardized STUdentized MODified",  # noqa:E501
    predict="glim_p",
    cmd="glm",
    cmdline="glm executions income perpoverty perblack LN_VC100k96 south degree [pweight=fweight], family(poisson)",  # noqa:E501
)

params_table = np.array([
    .00025343868829,   .0000298866597,  8.4799937786829,  2.252059827e-17,
    .00019486191167,  .00031201546491, np.nan,  1.9599639845401,
    0,  .09081422305585,  .08414617969117,  1.0792435662456,
    .28047916301946, -.07410925857549,  .25573770468718, np.nan,
    1.9599639845401,                0, -.09416451429381,  .01946961498728,
    -4.8364856909253,  1.321547815e-06, -.13232425846174, -.05600477012587,
    np.nan,  1.9599639845401,                0,  .27652273809506,
    .36112179485191,  .76573261995571,  .44383541350407, -.43126297384714,
    .98430845003726, np.nan,  1.9599639845401,                0,
    2.239890838384,  .43098853454849,  5.1971007551989,  2.024206636e-07,
    1.3951688329193,  3.0846128438487, np.nan,  1.9599639845401,
    0, -18.842583191417,  4.5147658917489, -4.1735460139479,
    .00002998950578, -27.691361737874, -9.9938046449589, np.nan,
    1.9599639845401,                0, -6.5630017977416,  3.3999612612355,
    -1.930316639948,   .0535676165153, -13.226803418595,  .10079982311137,
    np.nan,  1.9599639845401,                0]).reshape(7, 9)

params_table_colnames = 'b se z pvalue ll ul df crit eform'.split()

params_table_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                         'south', 'degree', '_cons']

cov = np.array([
    8.932124278e-10,  1.512127962e-06,  1.877263788e-07, -4.562869239e-06,
    -2.023379829e-06, -.00001228516761, -.00002423071544,  1.512127962e-06,
    .00708057955662,  .00028427703202,  -.0019549511748, -.00596332288528,
    .20022061835302, -.18678265108673,  1.877263788e-07,  .00028427703202,
    .00037906590775, -.00453407701816, -.00623061980467, -.04659404972535,
    .02694184589715, -4.562869239e-06,  -.0019549511748, -.00453407701816,
    .13040895071706,   .0836259691825,  .89260578257395, -.82275604425197,
    -2.023379829e-06, -.00596332288528, -.00623061980467,   .0836259691825,
    .18575111691225,  1.0698498854979, -.64859219982217, -.00001228516761,
    .20022061835302, -.04659404972535,  .89260578257395,  1.0698498854979,
    20.383111057299, -12.482192460755, -.00002423071544, -.18678265108673,
    .02694184589715, -.82275604425197, -.64859219982217, -12.482192460755,
    11.559736577902]).reshape(7, 7)

cov_colnames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

cov_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

infocrit = np.array([
    17, np.nan, -52.969418473462,                7,
    119.93883694692,  125.77133035532])

infocrit_colnames = 'N ll0 ll df AIC BIC'.split()

infocrit_rownames = '.'.split()

predicted = np.array([
    34.815238952637,  .06858423352242,  7.3026847839355,  .25687274336815,
    1.2540435791016,  .41320022940636,  3.9734709262848,  .16020278632641,
    2.0739872455597,  .22170753777027,  1.1471545696259,  .51121062040329,
    1.7763512134552,   .2167394310236,  2.2698366641998,   .2456086575985,
    1.6349502801895,  .25546172261238,  2.7504913806915,   .4417819082737,
    2.862185716629,  .61734634637833,  3.5617923736572,  .51518148183823,
    2.6135795116425,  .34006628394127,    .775799036026,    .292076587677,
    .93375068902969,  .39795544743538,  .56681954860687,  .31529840826988,
    1.8914022445679,  .26116076111794]).reshape(17, 2)

predicted_colnames = 'predict_mu predict_linpred_std'.split()

predicted_rownames = ['r'+str(n) for n in range(1, 18)]

resids = np.array([
    2.1847612857819,  2.1847612857819,  .36650228500366,  .36649596691132,
    .3702706694603,  .06275302171707,  1.6973150968552,  1.6973150968552,
    .60597640275955,  .60585051774979,  .62808901071548,  .23242343962193,
    4.7459564208984,  4.7459564208984,  3.0897438526154,  3.0483965873718,
    4.2380628585815,  3.7845225334167,  .02652905881405,  .02652905881405,
    .01329397037625,  .01329396758229,  .01330873556435,  .00667654490098,
    .92601269483566,  .92601269483566,  .60273587703705,  .60233747959137,
    .64300429821014,  .44648909568787,   .8528453707695,   .8528453707695,
    .72065913677216,  .71955502033234,   .7962681055069,   .7434441447258,
    .22364875674248,  .22364875674248,  .16446639597416,  .16445553302765,
    .16780391335487,  .12590345740318, -.26983660459518, -.26983660459518,
    -.1828535348177, -.18284019827843,  -.1791032999754, -.11887931078672,
    -.63495022058487, -.63495022058487, -.53598040342331, -.53542107343674,
    -.49657794833183, -.38836058974266, -1.7504912614822, -1.7504912614822,
    -1.2204585075378, -1.2154930830002, -1.0554916858673, -.63642859458923,
    -1.862185716629,  -1.862185716629, -1.2788465023041, -1.2732635736465,
    -1.1007128953934, -.65061664581299, -2.5617923736572, -2.5617923736572,
    -1.617108464241, -1.6071890592575, -1.3574055433273, -.71924245357513,
    -1.6135795116425, -1.6135795116425, -1.1469231843948, -1.1426799297333,
    -.99809640645981, -.61738300323486,  .22420094907284,  .22420094907284,
    .24363535642624,  .24356025457382,  .25454398989677,  .28899359703064,
    .06624934077263,  .06624934077263,  .06777309626341,  .06777160614729,
    .06855925172567,  .07094971090555,  .43318045139313,  .43318045139313,
    .51954871416092,  .51871728897095,  .57536894083023,  .76422989368439,
    -.89140218496323, -.89140218496323,  -.7140833735466,  -.7128586769104,
    -.64815932512283, -.47129172086716]).reshape(17, 6)

resids_colnames = ['score_factor', 'resid_response', 'resid_anscombe',
                   'resid_deviance', 'resid_pearson', 'resid_working']

resids_rownames = ['r'+str(n) for n in range(1, 18)]


results_poisson_pweight_nonrobust = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    infocrit=infocrit,
    infocrit_colnames=infocrit_colnames,
    infocrit_rownames=infocrit_rownames,
    predicted=predicted,
    predicted_colnames=predicted_colnames,
    predicted_rownames=predicted_rownames,
    resids=resids,
    resids_colnames=resids_colnames,
    resids_rownames=resids_rownames,
    **est
)

est = dict(
    k_eq_model=0,
    phi=1,
    vf=1,
    df=10,
    df_m=6,
    power=0,
    canonical=1,
    rank=7,
    aic=4.579685683305704,
    rc=0,
    p=5.09268495340e-76,
    chi2=366.2131475852884,
    ll=-31.92732830809848,
    k_autoCns=0,
    converged=1,
    k_dv=1,
    k_eq=1,
    k=7,
    ic=3,
    N=17,
    nbml=0,
    bic=-9.740492454486454,
    dispers_ps=2.475374834715614,
    deviance_ps=24.75374834715614,
    dispers_p=2.475374834715614,
    deviance_p=24.75374834715614,
    dispers_s=1.859164098607571,
    deviance_s=18.59164098607571,
    dispers=1.859164098607571,
    deviance=18.59164098607571,
    cmdline="glm executions income perpoverty perblack LN_VC100k96 south degree, family(poisson) vce(robust)",  # noqa:E501
    cmd="glm",
    predict="glim_p",
    marginsnotok="stdp Anscombe Cooksd Deviance Hat Likelihood Pearson Response Score Working ADJusted STAndardized STUdentized MODified",  # noqa:E501
    marginsok="default",
    hac_lag="15",
    vcetype="Robust",
    vce="robust",
    linkt="Log",
    linkf="ln(u)",
    varfunct="Poisson",
    varfuncf="u",
    opt1="ML",
    oim="oim",
    a="1",
    m="1",
    varfunc="glim_v3",
    link="glim_l03",
    chi2type="Wald",
    opt="moptimize",
    title="Generalized linear models",
    user="glim_lf",
    crittype="log pseudolikelihood",
    ml_method="e2",
    singularHmethod="m-marquardt",
    technique="nr",
    which="max",
    depvar="executions",
    properties="b V",
)

params_table = np.array([
    .00026110166569,  .00003534474167,  7.3872845963787,  1.498576223e-13,
    .00019182724497,   .0003303760864, np.nan,  1.9599639845401,
    0,  .07781804809828,  .09819599835909,  .79247677500784,
    .42808272865983, -.11464257211148,  .27027866830805, np.nan,
    1.9599639845401,                0, -.09493110013466,  .01944446025221,
    -4.8821668950083,  1.049263903e-06, -.13304154192782,  -.0568206583415,
    np.nan,  1.9599639845401,                0,  .29693462055586,
    .34917491559373,  .85038932436186,  .39510866948496, -.38743563831266,
    .98130487942439, np.nan,  1.9599639845401,                0,
    2.3011832004524,  .45717041903387,  5.0335347709405,  4.815174289e-07,
    1.405145644349,  3.1972207565559, np.nan,  1.9599639845401,
    0, -18.722067603077,  4.5006120067298, -4.1598937155841,
    .00003183957242, -27.543105044656, -9.9010301614985, np.nan,
    1.9599639845401,                0, -6.8014789919532,    3.48445447794,
    -1.9519494471841,  .05094420680386, -13.630884274485,  .02792629057847,
    np.nan,  1.9599639845401,                0]).reshape(7, 9)

params_table_colnames = 'b se z pvalue ll ul df crit eform'.split()

params_table_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                         'south', 'degree', '_cons']

cov = np.array([
    1.249250764e-09,  2.158351725e-06,  1.068227835e-07, -5.170410321e-06,
    -5.047866044e-07, -.00001662944527, -.00004339679838,  2.158351725e-06,
    .00964245409374,  .00008635335196, -.00640596402935, -.00524426268669,
    .23390140895418, -.22653903184676,  1.068227835e-07,  .00008635335196,
    .0003780870345, -.00382751790532,  -.0064534643179, -.05137117620883,
    .02948709519544, -5.170410321e-06, -.00640596402935, -.00382751790532,
    .12192312167989,   .0907733380116,  .89729289134262, -.69004336039169,
    -5.047866044e-07, -.00524426268669,  -.0064534643179,   .0907733380116,
    .20900479203961,  .93952111535021, -.75843860743141, -.00001662944527,
    .23390140895418, -.05137117620883,  .89729289134262,  .93952111535021,
    20.25550843512, -12.691830440798, -.00004339679838, -.22653903184676,
    .02948709519544, -.69004336039169, -.75843860743141, -12.691830440798,
    12.141423008836]).reshape(7, 7)

cov_colnames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

cov_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

infocrit = np.array([
    17, np.nan, -31.927328308098,                7,
    77.854656616197,   83.68715002459])

infocrit_colnames = 'N ll0 ll df AIC BIC'.split()

infocrit_rownames = '.'.split()

predicted = np.array([
    35.226364135742,  .05631958693266,  8.1965742111206,  .14089094102383,
    1.3118965625763,  .51714926958084,  3.6862981319427,  .20286601781845,
    2.0823004245758,  .27275583148003,  1.0650315284729,  .58616667985916,
    1.9260421991348,  .30098018050194,  2.4171404838562,  .34251752495766,
    1.8473218679428,  .29685723781586,  2.8643238544464,  .47364214062691,
    3.1211984157562,  .72507524490356,   3.338207244873,  .54493451118469,
    2.5269968509674,  .34425318241119,  .89725440740585,  .37162157893181,
    .97933322191238,  .50227928161621,  .53462094068527,  .40906101465225,
    1.9790935516357,  .33805811405182]).reshape(17, 2)

predicted_colnames = 'predict_mu predict_linpred_std'.split()

predicted_rownames = ['r'+str(n) for n in range(1, 18)]


results_poisson_none_hc1 = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    infocrit=infocrit,
    infocrit_colnames=infocrit_colnames,
    infocrit_rownames=infocrit_rownames,
    predicted=predicted,
    predicted_colnames=predicted_colnames,
    predicted_rownames=predicted_rownames,
    **est
)

est = dict(
    k_eq_model=0,
    phi=1,
    vf=1,
    df=26,
    df_m=6,
    power=0,
    canonical=1,
    rank=7,
    aic=3.634510210512826,
    rc=0,
    p=1.5690245831e-115,
    chi2=549.7874580263729,
    ll=-52.96941847346162,
    k_autoCns=0,
    converged=1,
    k_dv=1,
    k_eq=1,
    k=7,
    ic=3,
    N=33,
    nbml=0,
    bic=-67.5595014539113,
    dispers_ps=1.156216988842385,
    deviance_ps=30.06164170990202,
    dispers_p=1.156216988842385,
    deviance_p=30.06164170990202,
    dispers_s=.8980651978545075,
    deviance_s=23.34969514421719,
    dispers=.8980651978545075,
    deviance=23.34969514421719,
    cmdline="glm executions income perpoverty perblack LN_VC100k96 south degree [fweight=fweight], family(poisson) vce(robust)",  # noqa:E501
    cmd="glm",
    predict="glim_p",
    marginsnotok="stdp Anscombe Cooksd Deviance Hat Likelihood Pearson Response Score Working ADJusted STAndardized STUdentized MODified",  # noqa:E501
    marginsok="default",
    hac_lag="15",
    vcetype="Robust",
    vce="robust",
    linkt="Log",
    linkf="ln(u)",
    varfunct="Poisson",
    varfuncf="u",
    opt1="ML",
    oim="oim",
    a="1",
    m="1",
    varfunc="glim_v3",
    link="glim_l03",
    wexp="= fweight",
    wtype="fweight",
    chi2type="Wald",
    opt="moptimize",
    title="Generalized linear models",
    user="glim_lf",
    crittype="log pseudolikelihood",
    ml_method="e2",
    singularHmethod="m-marquardt",
    technique="nr",
    which="max",
    depvar="executions",
    properties="b V",
)

params_table = np.array([
    .00025343868829,   .0000263369674,  9.6229259983619,  6.398464168e-22,
    .00020181918073,  .00030505819585, np.nan,  1.9599639845401,
    0,  .09081422305585,  .07431850776812,  1.2219597215163,
    .22172285914198, -.05484737555444,  .23647582166613, np.nan,
    1.9599639845401,                0, -.09416451429381,  .01609416304158,
    -5.8508487860178,  4.890707145e-09, -.12570849421662, -.06262053437099,
    np.nan,  1.9599639845401,                0,  .27652273809506,
    .34481886883624,  .80193621372381,  .42258985672342,  -.3993098260138,
    .95235530220392, np.nan,  1.9599639845401,                0,
    2.239890838384,  .39682271484988,  5.6445630619491,  1.656012749e-08,
    1.4621326090308,  3.0176490677372, np.nan,  1.9599639845401,
    0, -18.842583191417,  4.1473740870735, -4.5432562377589,
    5.539185130e-06, -26.971287032495, -10.713879350338, np.nan,
    1.9599639845401,                0, -6.5630017977416,  3.0810023455152,
    -2.1301515097173,  .03315910688542, -12.601655431235, -.52434816424841,
    np.nan,  1.9599639845401,                0]).reshape(7, 9)

params_table_colnames = 'b se z pvalue ll ul df crit eform'.split()

params_table_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                         'south', 'degree', '_cons']

cov = np.array([
    6.936358517e-10,  1.301395377e-06,  1.497821854e-07, -4.758016826e-06,
    -1.852598001e-06, -6.904571080e-06, -.00001327109619,  1.301395377e-06,
    .00552324059688,  .00014714335792, -.00376147485446, -.00118957690573,
    .15979100738539, -.13853266210904,  1.497821854e-07,  .00014714335792,
    .00025902208401, -.00418693954572, -.00513741847691, -.03987504442994,
    .02761179707845, -4.758016826e-06, -.00376147485446, -.00418693954572,
    .1189000523055,  .08682729933237,  .80541854027627, -.70545315416752,
    -1.852598001e-06, -.00118957690573, -.00513741847691,  .08682729933237,
    .15746826702083,  1.1366624064282, -.75098089879076, -6.904571080e-06,
    .15979100738539, -.03987504442994,  .80541854027627,  1.1366624064282,
    17.200711818129, -11.062121016981, -.00001327109619, -.13853266210904,
    .02761179707845, -.70545315416752, -.75098089879076, -11.062121016981,
    9.49257545307]).reshape(7, 7)

cov_colnames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

cov_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

infocrit = np.array([
    33, np.nan, -52.969418473462,                7,
    119.93883694692,  130.41438987719])

infocrit_colnames = 'N ll0 ll df AIC BIC'.split()

infocrit_rownames = '.'.split()

predicted = np.array([
    34.815238952637,  .06608480215073,  7.3026847839355,  .23366995155811,
    1.2540435791016,  .39606991410255,  3.9734709262848,  .12350843846798,
    2.0739872455597,  .18263976275921,  1.1471545696259,  .39735752344131,
    1.7763512134552,  .17952646315098,  2.2698366641998,  .21028706431389,
    1.6349502801895,  .17675416171551,  2.7504913806915,  .42150634527206,
    2.862185716629,  .58209121227264,  3.5617923736572,  .49835306406021,
    2.6135795116425,   .2456089258194,    .775799036026,  .23251366615295,
    .93375068902969,  .35320028662682,  .56681954860687,  .26245352625847,
    1.8914022445679,  .20374123752117]).reshape(17, 2)

predicted_colnames = 'predict_mu predict_linpred_std'.split()

predicted_rownames = ['r'+str(n) for n in range(1, 18)]


results_poisson_fweight_hc1 = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    infocrit=infocrit,
    infocrit_colnames=infocrit_colnames,
    infocrit_rownames=infocrit_rownames,
    predicted=predicted,
    predicted_colnames=predicted_colnames,
    predicted_rownames=predicted_rownames,
    **est
)

est = dict(
    k_eq_model=0,
    phi=1,
    vf=1,
    df=10,
    df_m=6,
    power=0,
    canonical=1,
    rank=7,
    aic=4.033797198035106,
    rc=0,
    p=5.72458312675e-74,
    chi2=356.663774965618,
    ll=-27.28727618329841,
    k_autoCns=0,
    converged=1,
    k_dv=1,
    k_eq=1,
    k=7,
    ic=3,
    N=17,
    nbml=0,
    bic=-16.30350260869269,
    dispers_ps=1.548630027479802,
    deviance_ps=15.48630027479802,
    dispers_p=1.548630027479802,
    deviance_p=15.48630027479802,
    dispers_s=1.202863083186947,
    deviance_s=12.02863083186947,
    dispers=1.202863083186947,
    deviance=12.02863083186947,
    cmdline="glm executions income perpoverty perblack LN_VC100k96 south degree [aweight=fweight], family(poisson) vce(robust)",  # noqa:E501
    cmd="glm",
    predict="glim_p",
    marginsnotok="stdp Anscombe Cooksd Deviance Hat Likelihood Pearson Response Score Working ADJusted STAndardized STUdentized MODified",  # noqa:E501
    marginsok="default",
    hac_lag="15",
    vcetype="Robust",
    vce="robust",
    linkt="Log",
    linkf="ln(u)",
    varfunct="Poisson",
    varfuncf="u",
    opt1="ML",
    oim="oim",
    a="1",
    m="1",
    varfunc="glim_v3",
    link="glim_l03",
    wexp="= fweight",
    wtype="aweight",
    chi2type="Wald",
    opt="moptimize",
    title="Generalized linear models",
    user="glim_lf",
    crittype="log pseudolikelihood",
    ml_method="e2",
    singularHmethod="m-marquardt",
    technique="nr",
    which="max",
    depvar="executions",
    properties="b V",
)

params_table = np.array([
    .00025343868829,   .0000298866597,  8.4799937786833,  2.252059827e-17,
    .00019486191167,  .00031201546491, np.nan,  1.9599639845401,
    0,  .09081422305585,  .08414617969118,  1.0792435662455,
    .28047916301948,  -.0741092585755,  .25573770468719, np.nan,
    1.9599639845401,                0, -.09416451429381,  .01946961498728,
    -4.8364856909248,  1.321547815e-06, -.13232425846174, -.05600477012587,
    np.nan,  1.9599639845401,                0,  .27652273809507,
    .36112179485206,  .76573261995541,  .44383541350425, -.43126297384744,
    .98430845003758, np.nan,  1.9599639845401,                0,
    2.239890838384,   .4309885345485,  5.1971007551988,  2.024206636e-07,
    1.3951688329193,  3.0846128438488, np.nan,  1.9599639845401,
    0, -18.842583191417,  4.5147658917496, -4.1735460139472,
    .00002998950578, -27.691361737876, -9.9938046449574, np.nan,
    1.9599639845401,                0, -6.5630017977417,  3.3999612612367,
    -1.9303166399474,  .05356761651539, -13.226803418597,  .10079982311369,
    np.nan,  1.9599639845401,                0]).reshape(7, 9)

params_table_colnames = 'b se z pvalue ll ul df crit eform'.split()

params_table_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                         'south', 'degree', '_cons']

cov = np.array([
    8.932124278e-10,  1.512127962e-06,  1.877263788e-07, -4.562869239e-06,
    -2.023379829e-06, -.00001228516761, -.00002423071544,  1.512127962e-06,
    .00708057955662,  .00028427703202, -.00195495117479, -.00596332288528,
    .2002206183531,  -.1867826510868,  1.877263788e-07,  .00028427703202,
    .00037906590775, -.00453407701816, -.00623061980468, -.04659404972537,
    .02694184589718, -4.562869239e-06, -.00195495117479, -.00453407701816,
    .13040895071718,  .08362596918255,  .89260578257483, -.82275604425296,
    -2.023379829e-06, -.00596332288528, -.00623061980468,  .08362596918255,
    .18575111691226,  1.0698498854982, -.64859219982256, -.00001228516761,
    .2002206183531, -.04659404972537,  .89260578257483,  1.0698498854982,
    20.383111057306, -12.482192460764, -.00002423071544,  -.1867826510868,
    .02694184589718, -.82275604425296, -.64859219982256, -12.482192460764,
    11.55973657791]).reshape(7, 7)

cov_colnames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

cov_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

infocrit = np.array([
    17, np.nan, -27.287276183298,                7,
    68.574552366597,   74.40704577499])

infocrit_colnames = 'N ll0 ll df AIC BIC'.split()

infocrit_rownames = '.'.split()

predicted = np.array([
    34.815238952637,  .06858423352242,  7.3026847839355,  .25687274336815,
    1.2540435791016,  .41320022940636,  3.9734709262848,  .16020278632641,
    2.0739872455597,  .22170753777027,  1.1471545696259,  .51121062040329,
    1.7763512134552,   .2167394310236,  2.2698366641998,   .2456086575985,
    1.6349502801895,  .25546172261238,  2.7504913806915,   .4417819082737,
    2.862185716629,  .61734634637833,  3.5617923736572,  .51518148183823,
    2.6135795116425,  .34006628394127,    .775799036026,    .292076587677,
    .93375068902969,  .39795544743538,  .56681954860687,  .31529840826988,
    1.8914022445679,  .26116076111794]).reshape(17, 2)

predicted_colnames = 'predict_mu predict_linpred_std'.split()

predicted_rownames = ['r'+str(n) for n in range(1, 18)]


results_poisson_aweight_hc1 = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    infocrit=infocrit,
    infocrit_colnames=infocrit_colnames,
    infocrit_rownames=infocrit_rownames,
    predicted=predicted,
    predicted_colnames=predicted_colnames,
    predicted_rownames=predicted_rownames,
    **est
)

est = dict(
    k_eq_model=0,
    phi=1,
    vf=1,
    df=10,
    df_m=6,
    power=0,
    canonical=1,
    rank=7,
    aic=7.055225702760191,
    rc=0,
    p=5.72458312679e-74,
    chi2=356.6637749656061,
    ll=-52.96941847346162,
    k_autoCns=0,
    converged=1,
    k_dv=1,
    k_eq=1,
    k=7,
    ic=3,
    N=17,
    nbml=0,
    bic=-4.982438296344967,
    dispers_ps=3.006164170990202,
    deviance_ps=30.06164170990202,
    dispers_p=3.006164170990202,
    deviance_p=30.06164170990202,
    dispers_s=2.33496951442172,
    deviance_s=23.34969514421719,
    dispers=2.33496951442172,
    deviance=23.34969514421719,
    cmdline="glm executions income perpoverty perblack LN_VC100k96 south degree [pweight=fweight], family(poisson) vce(robust)",  # noqa:E501
    cmd="glm",
    predict="glim_p",
    marginsnotok="stdp Anscombe Cooksd Deviance Hat Likelihood Pearson Response Score Working ADJusted STAndardized STUdentized MODified",  # noqa:E501
    marginsok="default",
    hac_lag="15",
    vcetype="Robust",
    vce="robust",
    linkt="Log",
    linkf="ln(u)",
    varfunct="Poisson",
    varfuncf="u",
    opt1="ML",
    oim="oim",
    a="1",
    m="1",
    varfunc="glim_v3",
    link="glim_l03",
    wexp="= fweight",
    wtype="pweight",
    chi2type="Wald",
    opt="moptimize",
    title="Generalized linear models",
    user="glim_lf",
    crittype="log pseudolikelihood",
    ml_method="e2",
    singularHmethod="m-marquardt",
    technique="nr",
    which="max",
    depvar="executions",
    properties="b V",
)

params_table = np.array([
    .00025343868829,   .0000298866597,  8.4799937786829,  2.252059827e-17,
    .00019486191167,  .00031201546491, np.nan,  1.9599639845401,
    0,  .09081422305585,  .08414617969117,  1.0792435662456,
    .28047916301946, -.07410925857549,  .25573770468718, np.nan,
    1.9599639845401,                0, -.09416451429381,  .01946961498728,
    -4.8364856909253,  1.321547815e-06, -.13232425846174, -.05600477012587,
    np.nan,  1.9599639845401,                0,  .27652273809506,
    .36112179485191,  .76573261995571,  .44383541350407, -.43126297384714,
    .98430845003726, np.nan,  1.9599639845401,                0,
    2.239890838384,  .43098853454849,  5.1971007551989,  2.024206636e-07,
    1.3951688329193,  3.0846128438487, np.nan,  1.9599639845401,
    0, -18.842583191417,  4.5147658917489, -4.1735460139479,
    .00002998950578, -27.691361737874, -9.9938046449589, np.nan,
    1.9599639845401,                0, -6.5630017977416,  3.3999612612355,
    -1.930316639948,   .0535676165153, -13.226803418595,  .10079982311137,
    np.nan,  1.9599639845401,                0]).reshape(7, 9)

params_table_colnames = 'b se z pvalue ll ul df crit eform'.split()

params_table_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                         'south', 'degree', '_cons']

cov = np.array([
    8.932124278e-10,  1.512127962e-06,  1.877263788e-07, -4.562869239e-06,
    -2.023379829e-06, -.00001228516761, -.00002423071544,  1.512127962e-06,
    .00708057955662,  .00028427703202,  -.0019549511748, -.00596332288528,
    .20022061835302, -.18678265108673,  1.877263788e-07,  .00028427703202,
    .00037906590775, -.00453407701816, -.00623061980467, -.04659404972535,
    .02694184589715, -4.562869239e-06,  -.0019549511748, -.00453407701816,
    .13040895071706,   .0836259691825,  .89260578257395, -.82275604425197,
    -2.023379829e-06, -.00596332288528, -.00623061980467,   .0836259691825,
    .18575111691225,  1.0698498854979, -.64859219982217, -.00001228516761,
    .20022061835302, -.04659404972535,  .89260578257395,  1.0698498854979,
    20.383111057299, -12.482192460755, -.00002423071544, -.18678265108673,
    .02694184589715, -.82275604425197, -.64859219982217, -12.482192460755,
    11.559736577902]).reshape(7, 7)

cov_colnames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

cov_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

infocrit = np.array([
    17, np.nan, -52.969418473462,                7,
    119.93883694692,  125.77133035532])

infocrit_colnames = 'N ll0 ll df AIC BIC'.split()

infocrit_rownames = '.'.split()

predicted = np.array([
    34.815238952637,  .06858423352242,  7.3026847839355,  .25687274336815,
    1.2540435791016,  .41320022940636,  3.9734709262848,  .16020278632641,
    2.0739872455597,  .22170753777027,  1.1471545696259,  .51121062040329,
    1.7763512134552,   .2167394310236,  2.2698366641998,   .2456086575985,
    1.6349502801895,  .25546172261238,  2.7504913806915,   .4417819082737,
    2.862185716629,  .61734634637833,  3.5617923736572,  .51518148183823,
    2.6135795116425,  .34006628394127,    .775799036026,    .292076587677,
    .93375068902969,  .39795544743538,  .56681954860687,  .31529840826988,
    1.8914022445679,  .26116076111794]).reshape(17, 2)

predicted_colnames = 'predict_mu predict_linpred_std'.split()

predicted_rownames = ['r'+str(n) for n in range(1, 18)]


results_poisson_pweight_hc1 = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    infocrit=infocrit,
    infocrit_colnames=infocrit_colnames,
    infocrit_rownames=infocrit_rownames,
    predicted=predicted,
    predicted_colnames=predicted_colnames,
    predicted_rownames=predicted_rownames,
    **est
)

est = dict(
    k_eq_model=0,
    vf=1,
    df=10,
    df_m=6,
    power=0,
    canonical=1,
    rank=7,
    aic=4.579685683305704,
    rc=0,
    p=4.1950730971e-123,
    chi2=584.908728768987,
    ll=-31.92732830809848,
    N_clust=9,
    k_autoCns=0,
    converged=1,
    k_dv=1,
    k_eq=1,
    k=7,
    ic=3,
    N=17,
    nbml=0,
    bic=-9.740492454486454,
    dispers_ps=2.475374834715614,
    deviance_ps=24.75374834715614,
    dispers_p=2.475374834715614,
    deviance_p=24.75374834715614,
    dispers_s=1.859164098607571,
    deviance_s=18.59164098607571,
    dispers=1.859164098607571,
    deviance=18.59164098607571,
    phi=1,
    cmdline="glm executions income perpoverty perblack LN_VC100k96 south degree, family(poisson) vce(cluster id)",  # noqa:E501
    cmd="glm",
    predict="glim_p",
    marginsnotok="stdp Anscombe Cooksd Deviance Hat Likelihood Pearson Response Score Working ADJusted STAndardized STUdentized MODified",  # noqa:E501
    marginsok="default",
    hac_lag="15",
    vcetype="Robust",
    vce="cluster",
    linkt="Log",
    linkf="ln(u)",
    varfunct="Poisson",
    varfuncf="u",
    opt1="ML",
    clustvar="id",
    oim="oim",
    a="1",
    m="1",
    varfunc="glim_v3",
    link="glim_l03",
    chi2type="Wald",
    opt="moptimize",
    title="Generalized linear models",
    user="glim_lf",
    crittype="log pseudolikelihood",
    ml_method="e2",
    singularHmethod="m-marquardt",
    technique="nr",
    which="max",
    depvar="executions",
    properties="b V",
)

params_table = np.array([
    .00026110166569,  .00004098448535,  6.3707440379489,  1.881133617e-10,
    .00018077355048,   .0003414297809, np.nan,  1.9599639845401,
    0,  .07781804809828,  .11602998752167,  .67067186475175,
    .50242959011024, -.14959654857083,   .3052326447674, np.nan,
    1.9599639845401,                0, -.09493110013466,  .02432927475974,
    -3.9019288931601,  .00009542919351, -.14261560243373, -.04724659783559,
    np.nan,  1.9599639845401,                0,  .29693462055586,
    .31774950884716,  .93449277587615,  .35004976070702, -.32584297288986,
    .91971221400158, np.nan,  1.9599639845401,                0,
    2.3011832004524,  .54874508731474,  4.1935376801516,  .00002746374324,
    1.2256625926223,  3.3767038082826, np.nan,  1.9599639845401,
    0, -18.722067603077,  2.8106198749749, -6.6611880780372,
    2.716227723e-11, -24.230781332261, -13.213353873894, np.nan,
    1.9599639845401,                0, -6.8014789919532,  3.1571598785659,
    -2.1543029981246,  .03121641791743, -12.989398647377, -.61355933652912,
    np.nan,  1.9599639845401,                0]).reshape(7, 9)

params_table_colnames = 'b se z pvalue ll ul df crit eform'.split()

params_table_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                         'south', 'degree', '_cons']

cov = np.array([
    1.679728039e-09,  4.034336761e-06,  1.735749447e-07, -5.093610363e-06,
    -4.552211884e-06,  .00001563785418, -.00009230028034,  4.034336761e-06,
    .01346295800428,  .00110922683659, -.01950093608551, -.02957572460439,
    .08545644123676, -.23518641056668,  1.735749447e-07,  .00110922683659,
    .00059191361033, -.00720622811203, -.01195031391163, -.04317371228367,
    .03351736744645, -5.093610363e-06, -.01950093608551, -.00720622811203,
    .10096475037261,  .13375578883899,  .49763538443989, -.27357574414228,
    -4.552211884e-06, -.02957572460439, -.01195031391163,  .13375578883899,
    .30112117085206,  .65342245458316, -.47102547759356,  .00001563785418,
    .08545644123676, -.04317371228367,  .49763538443989,  .65342245458316,
    7.8995840816039, -6.5824964755966, -.00009230028034, -.23518641056668,
    .03351736744645, -.27357574414228, -.47102547759356, -6.5824964755966,
    9.9676584988266]).reshape(7, 7)

cov_colnames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

cov_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

infocrit = np.array([
    17, np.nan, -31.927328308098,                7,
    77.854656616197,   83.68715002459])

infocrit_colnames = 'N ll0 ll df AIC BIC'.split()

infocrit_rownames = '.'.split()

predicted = np.array([
    35.226364135742,  .05941947177052,  8.1965742111206,  .09018591046333,
    1.3118965625763,  .53127920627594,  3.6862981319427,  .23996050655842,
    2.0823004245758,  .33554902672768,  1.0650315284729,  .53513532876968,
    1.9260421991348,  .32360115647316,  2.4171404838562,  .33078169822693,
    1.8473218679428,  .32581362128258,  2.8643238544464,  .46489810943604,
    3.1211984157562,  .71297109127045,   3.338207244873,  .58515930175781,
    2.5269968509674,  .42410242557526,  .89725440740585,  .40493285655975,
    .97933322191238,   .5560839176178,  .53462094068527,    .419488966465,
    1.9790935516357,   .3438538312912]).reshape(17, 2)

predicted_colnames = 'predict_mu predict_linpred_std'.split()

predicted_rownames = ['r'+str(n) for n in range(1, 18)]


results_poisson_none_clu1 = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    infocrit=infocrit,
    infocrit_colnames=infocrit_colnames,
    infocrit_rownames=infocrit_rownames,
    predicted=predicted,
    predicted_colnames=predicted_colnames,
    predicted_rownames=predicted_rownames,
    **est
)

est = dict(
    k_eq_model=0,
    vf=1,
    df=26,
    df_m=6,
    power=0,
    canonical=1,
    rank=7,
    aic=3.634510210512826,
    rc=0,
    p=6.87057569032e-91,
    chi2=435.380362705941,
    ll=-52.96941847346162,
    N_clust=9,
    k_autoCns=0,
    converged=1,
    k_dv=1,
    k_eq=1,
    k=7,
    ic=3,
    N=33,
    nbml=0,
    bic=-67.5595014539113,
    dispers_ps=1.156216988842385,
    deviance_ps=30.06164170990202,
    dispers_p=1.156216988842385,
    deviance_p=30.06164170990202,
    dispers_s=.8980651978545075,
    deviance_s=23.34969514421719,
    dispers=.8980651978545075,
    deviance=23.34969514421719,
    phi=1,
    cmdline="glm executions income perpoverty perblack LN_VC100k96 south degree [fweight=fweight], family(poisson) vce(cluster id)",  # noqa:E501
    cmd="glm",
    predict="glim_p",
    marginsnotok="stdp Anscombe Cooksd Deviance Hat Likelihood Pearson Response Score Working ADJusted STAndardized STUdentized MODified",  # noqa:E501
    marginsok="default",
    hac_lag="15",
    vcetype="Robust",
    vce="cluster",
    linkt="Log",
    linkf="ln(u)",
    varfunct="Poisson",
    varfuncf="u",
    opt1="ML",
    clustvar="id",
    oim="oim",
    a="1",
    m="1",
    varfunc="glim_v3",
    link="glim_l03",
    wexp="= fweight",
    wtype="fweight",
    chi2type="Wald",
    opt="moptimize",
    title="Generalized linear models",
    user="glim_lf",
    crittype="log pseudolikelihood",
    ml_method="e2",
    singularHmethod="m-marquardt",
    technique="nr",
    which="max",
    depvar="executions",
    properties="b V",
)

params_table = np.array([
    .00025343868829,   .0000293670276,  8.6300422274613,  6.132932700e-18,
    .00019588037186,  .00031099700472, np.nan,  1.9599639845401,
    0,  .09081422305585,  .09800194027664,  .92665739881773,
    .35410444288802, -.10126605030142,  .28289449641311, np.nan,
    1.9599639845401,                0, -.09416451429381,  .02511206083893,
    -3.7497724658197,  .00017699509401, -.14338324911569, -.04494577947193,
    np.nan,  1.9599639845401,                0,  .27652273809506,
    .36749499886987,  .75245306451906,  .45177864537662, -.44375422418847,
    .99679970037859, np.nan,  1.9599639845401,                0,
    2.239890838384,  .51564197481271,   4.343887712395,  .00001399830855,
    1.229251138834,   3.250530537934, np.nan,  1.9599639845401,
    0, -18.842583191417,  3.2292740757113, -5.8349284543976,
    5.381365332e-09,  -25.17184407602, -12.513322306813, np.nan,
    1.9599639845401,                0, -6.5630017977416,  3.1938260811459,
    -2.0549026875586,  .03988840483712, -12.822785889672, -.30321770581092,
    np.nan,  1.9599639845401,                0]).reshape(7, 9)

params_table_colnames = 'b se z pvalue ll ul df crit eform'.split()

params_table_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                         'south', 'degree', '_cons']

cov = np.array([
    8.624223101e-10,  2.413510691e-06,  3.123995891e-07, -4.358439015e-06,
    -8.084672085e-06, -4.785328653e-06, -.00003652286809,  2.413510691e-06,
    .00960438029799,  .00106422375754, -.00911884619892, -.03121758372723,
    .06803953530989, -.17715756048416,  3.123995891e-07,  .00106422375754,
    .00063061559958, -.00844230553011, -.01177586448603, -.05361546061036,
    .03844868195577, -4.358439015e-06, -.00911884619892, -.00844230553011,
    .13505257419436,  .14058853110927,  .86184257188631, -.74146699290106,
    -8.084672085e-06, -.03121758372723, -.01177586448603,  .14058853110927,
    .26588664618875,  .75712244813913, -.35118919402718, -4.785328653e-06,
    .06803953530989, -.05361546061036,  .86184257188631,  .75712244813913,
    10.428211056061, -8.3518020608948, -.00003652286809, -.17715756048416,
    .03844868195577, -.74146699290106, -.35118919402718, -8.3518020608948,
    10.200525036608]).reshape(7, 7)

cov_colnames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

cov_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

infocrit = np.array([
    33, np.nan, -52.969418473462,                7,
    119.93883694692,  130.41438987719])

infocrit_colnames = 'N ll0 ll df AIC BIC'.split()

infocrit_rownames = '.'.split()

predicted = np.array([
    34.815238952637,  .07249507308006,  7.3026847839355,  .17909966409206,
    1.2540435791016,  .36725598573685,  3.9734709262848,   .1719862818718,
    2.0739872455597,  .27532628178596,  1.1471545696259,  .51580721139908,
    1.7763512134552,  .23559851944447,  2.2698366641998,  .21655206382275,
    1.6349502801895,  .27835717797279,  2.7504913806915,  .44458091259003,
    2.862185716629,  .54439353942871,  3.5617923736572,  .57089400291443,
    2.6135795116425,  .41426089406013,    .775799036026,  .35101860761642,
    .93375068902969,  .39217269420624,  .56681954860687,  .27232182025909,
    1.8914022445679,  .24083258211613]).reshape(17, 2)

predicted_colnames = 'predict_mu predict_linpred_std'.split()

predicted_rownames = ['r'+str(n) for n in range(1, 18)]


results_poisson_fweight_clu1 = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    infocrit=infocrit,
    infocrit_colnames=infocrit_colnames,
    infocrit_rownames=infocrit_rownames,
    predicted=predicted,
    predicted_colnames=predicted_colnames,
    predicted_rownames=predicted_rownames,
    **est
)

est = dict(
    k_eq_model=0,
    vf=1,
    df=10,
    df_m=6,
    power=0,
    canonical=1,
    rank=7,
    aic=4.033797198035106,
    rc=0,
    p=6.87057569091e-91,
    chi2=435.3803627057688,
    ll=-27.28727618329841,
    N_clust=9,
    k_autoCns=0,
    converged=1,
    k_dv=1,
    k_eq=1,
    k=7,
    ic=3,
    N=17,
    nbml=0,
    bic=-16.30350260869269,
    dispers_ps=1.548630027479802,
    deviance_ps=15.48630027479802,
    dispers_p=1.548630027479802,
    deviance_p=15.48630027479802,
    dispers_s=1.202863083186947,
    deviance_s=12.02863083186947,
    dispers=1.202863083186947,
    deviance=12.02863083186947,
    phi=1,
    cmdline="glm executions income perpoverty perblack LN_VC100k96 south degree [aweight=fweight], family(poisson) vce(cluster id)",  # noqa:E501
    cmd="glm",
    predict="glim_p",
    marginsnotok="stdp Anscombe Cooksd Deviance Hat Likelihood Pearson Response Score Working ADJusted STAndardized STUdentized MODified",  # noqa:E501
    marginsok="default",
    hac_lag="15",
    vcetype="Robust",
    vce="cluster",
    linkt="Log",
    linkf="ln(u)",
    varfunct="Poisson",
    varfuncf="u",
    opt1="ML",
    clustvar="id",
    oim="oim",
    a="1",
    m="1",
    varfunc="glim_v3",
    link="glim_l03",
    wexp="= fweight",
    wtype="aweight",
    chi2type="Wald",
    opt="moptimize",
    title="Generalized linear models",
    user="glim_lf",
    crittype="log pseudolikelihood",
    ml_method="e2",
    singularHmethod="m-marquardt",
    technique="nr",
    which="max",
    depvar="executions",
    properties="b V",
)

params_table = np.array([
    .00025343868829,   .0000293670276,  8.6300422274633,  6.132932700e-18,
    .00019588037186,  .00031099700472, np.nan,  1.9599639845401,
    0,  .09081422305585,  .09800194027665,  .92665739881771,
    .35410444288803, -.10126605030143,  .28289449641312, np.nan,
    1.9599639845401,                0, -.09416451429381,  .02511206083893,
    -3.7497724658192,  .00017699509401, -.14338324911569, -.04494577947192,
    np.nan,  1.9599639845401,                0,  .27652273809507,
    .36749499887001,  .75245306451881,  .45177864537677, -.44375422418873,
    .99679970037887, np.nan,  1.9599639845401,                0,
    2.239890838384,  .51564197481271,   4.343887712395,  .00001399830855,
    1.229251138834,   3.250530537934, np.nan,  1.9599639845401,
    0, -18.842583191417,  3.2292740757119, -5.8349284543965,
    5.381365332e-09, -25.171844076021, -12.513322306812, np.nan,
    1.9599639845401,                0, -6.5630017977417,   3.193826081147,
    -2.054902687558,  .03988840483718, -12.822785889674, -.30321770580895,
    np.nan,  1.9599639845401,                0]).reshape(7, 9)

params_table_colnames = 'b se z pvalue ll ul df crit eform'.split()

params_table_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                         'south', 'degree', '_cons']

cov = np.array([
    8.624223101e-10,  2.413510691e-06,  3.123995891e-07, -4.358439015e-06,
    -8.084672085e-06, -4.785328653e-06, -.00003652286809,  2.413510691e-06,
    .00960438029799,  .00106422375754, -.00911884619892, -.03121758372723,
    .06803953530995,  -.1771575604842,  3.123995891e-07,  .00106422375754,
    .00063061559958, -.00844230553012, -.01177586448603, -.05361546061038,
    .03844868195581, -4.358439015e-06, -.00911884619892, -.00844230553012,
    .13505257419447,   .1405885311093,  .86184257188684, -.74146699290197,
    -8.084672085e-06, -.03121758372723, -.01177586448603,   .1405885311093,
    .26588664618875,  .75712244813928, -.35118919402768, -4.785328653e-06,
    .06803953530995, -.05361546061038,  .86184257188684,  .75712244813928,
    10.428211056065, -8.3518020609031, -.00003652286809,  -.1771575604842,
    .03844868195581, -.74146699290197, -.35118919402768, -8.3518020609031,
    10.200525036615]).reshape(7, 7)

cov_colnames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

cov_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

infocrit = np.array([
    17, np.nan, -27.287276183298,                7,
    68.574552366597,   74.40704577499])

infocrit_colnames = 'N ll0 ll df AIC BIC'.split()

infocrit_rownames = '.'.split()

predicted = np.array([
    34.815238952637,  .07249507308006,  7.3026847839355,  .17909966409206,
    1.2540435791016,  .36725598573685,  3.9734709262848,   .1719862818718,
    2.0739872455597,  .27532628178596,  1.1471545696259,  .51580721139908,
    1.7763512134552,  .23559851944447,  2.2698366641998,  .21655206382275,
    1.6349502801895,  .27835714817047,  2.7504913806915,  .44458091259003,
    2.862185716629,  .54439353942871,  3.5617923736572,  .57089400291443,
    2.6135795116425,  .41426089406013,    .775799036026,  .35101860761642,
    .93375068902969,  .39217269420624,  .56681954860687,  .27232182025909,
    1.8914022445679,  .24083258211613]).reshape(17, 2)

predicted_colnames = 'predict_mu predict_linpred_std'.split()

predicted_rownames = ['r'+str(n) for n in range(1, 18)]


results_poisson_aweight_clu1 = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    infocrit=infocrit,
    infocrit_colnames=infocrit_colnames,
    infocrit_rownames=infocrit_rownames,
    predicted=predicted,
    predicted_colnames=predicted_colnames,
    predicted_rownames=predicted_rownames,
    **est
)

est = dict(
    k_eq_model=0,
    vf=1,
    df=10,
    df_m=6,
    power=0,
    canonical=1,
    rank=7,
    aic=7.055225702760191,
    rc=0,
    p=6.87057569032e-91,
    chi2=435.380362705941,
    ll=-52.96941847346162,
    N_clust=9,
    k_autoCns=0,
    converged=1,
    k_dv=1,
    k_eq=1,
    k=7,
    ic=3,
    N=17,
    nbml=0,
    bic=-4.982438296344967,
    dispers_ps=3.006164170990202,
    deviance_ps=30.06164170990202,
    dispers_p=3.006164170990202,
    deviance_p=30.06164170990202,
    dispers_s=2.33496951442172,
    deviance_s=23.34969514421719,
    dispers=2.33496951442172,
    deviance=23.34969514421719,
    phi=1,
    cmdline="glm executions income perpoverty perblack LN_VC100k96 south degree [pweight=fweight], family(poisson) vce(cluster id)",  # noqa:E501
    cmd="glm",
    predict="glim_p",
    marginsnotok="stdp Anscombe Cooksd Deviance Hat Likelihood Pearson Response Score Working ADJusted STAndardized STUdentized MODified",  # noqa:E501
    marginsok="default",
    hac_lag="15",
    vcetype="Robust",
    vce="cluster",
    linkt="Log",
    linkf="ln(u)",
    varfunct="Poisson",
    varfuncf="u",
    opt1="ML",
    clustvar="id",
    oim="oim",
    a="1",
    m="1",
    varfunc="glim_v3",
    link="glim_l03",
    wexp="= fweight",
    wtype="pweight",
    chi2type="Wald",
    opt="moptimize",
    title="Generalized linear models",
    user="glim_lf",
    crittype="log pseudolikelihood",
    ml_method="e2",
    singularHmethod="m-marquardt",
    technique="nr",
    which="max",
    depvar="executions",
    properties="b V",
)

params_table = np.array([
    .00025343868829,   .0000293670276,  8.6300422274613,  6.132932700e-18,
    .00019588037186,  .00031099700472, np.nan,  1.9599639845401,
    0,  .09081422305585,  .09800194027664,  .92665739881773,
    .35410444288802, -.10126605030142,  .28289449641311, np.nan,
    1.9599639845401,                0, -.09416451429381,  .02511206083893,
    -3.7497724658197,  .00017699509401, -.14338324911569, -.04494577947193,
    np.nan,  1.9599639845401,                0,  .27652273809506,
    .36749499886987,  .75245306451906,  .45177864537662, -.44375422418847,
    .99679970037859, np.nan,  1.9599639845401,                0,
    2.239890838384,  .51564197481271,   4.343887712395,  .00001399830855,
    1.229251138834,   3.250530537934, np.nan,  1.9599639845401,
    0, -18.842583191417,  3.2292740757113, -5.8349284543976,
    5.381365332e-09,  -25.17184407602, -12.513322306813, np.nan,
    1.9599639845401,                0, -6.5630017977416,  3.1938260811459,
    -2.0549026875586,  .03988840483712, -12.822785889672, -.30321770581092,
    np.nan,  1.9599639845401,                0]).reshape(7, 9)

params_table_colnames = 'b se z pvalue ll ul df crit eform'.split()

params_table_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                         'south', 'degree', '_cons']

cov = np.array([
    8.624223101e-10,  2.413510691e-06,  3.123995891e-07, -4.358439015e-06,
    -8.084672085e-06, -4.785328653e-06, -.00003652286809,  2.413510691e-06,
    .00960438029799,  .00106422375754, -.00911884619892, -.03121758372723,
    .06803953530989, -.17715756048416,  3.123995891e-07,  .00106422375754,
    .00063061559958, -.00844230553011, -.01177586448603, -.05361546061036,
    .03844868195577, -4.358439015e-06, -.00911884619892, -.00844230553011,
    .13505257419436,  .14058853110927,  .86184257188631, -.74146699290106,
    -8.084672085e-06, -.03121758372723, -.01177586448603,  .14058853110927,
    .26588664618875,  .75712244813913, -.35118919402718, -4.785328653e-06,
    .06803953530989, -.05361546061036,  .86184257188631,  .75712244813913,
    10.428211056061, -8.3518020608948, -.00003652286809, -.17715756048416,
    .03844868195577, -.74146699290106, -.35118919402718, -8.3518020608948,
    10.200525036608]).reshape(7, 7)

cov_colnames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

cov_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

infocrit = np.array([
    17, np.nan, -52.969418473462,                7,
    119.93883694692,  125.77133035532])

infocrit_colnames = 'N ll0 ll df AIC BIC'.split()

infocrit_rownames = '.'.split()

predicted = np.array([
    34.815238952637,  .07249507308006,  7.3026847839355,  .17909966409206,
    1.2540435791016,  .36725598573685,  3.9734709262848,   .1719862818718,
    2.0739872455597,  .27532628178596,  1.1471545696259,  .51580721139908,
    1.7763512134552,  .23559851944447,  2.2698366641998,  .21655206382275,
    1.6349502801895,  .27835717797279,  2.7504913806915,  .44458091259003,
    2.862185716629,  .54439353942871,  3.5617923736572,  .57089400291443,
    2.6135795116425,  .41426089406013,    .775799036026,  .35101860761642,
    .93375068902969,  .39217269420624,  .56681954860687,  .27232182025909,
    1.8914022445679,  .24083258211613]).reshape(17, 2)

predicted_colnames = 'predict_mu predict_linpred_std'.split()

predicted_rownames = ['r'+str(n) for n in range(1, 18)]


results_poisson_pweight_clu1 = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    infocrit=infocrit,
    infocrit_colnames=infocrit_colnames,
    infocrit_rownames=infocrit_rownames,
    predicted=predicted,
    predicted_colnames=predicted_colnames,
    predicted_rownames=predicted_rownames,
    **est
)

est = dict(
    rank=7,
    ll_0=-55.23556912834824,
    ll=-47.54122045581504,
    r2_a=.3528737432046668,
    rss=267.3132086911238,
    mss=393.6105745962962,
    rmse=5.17023412130557,
    r2=.5955460895029168,
    F=.7279778160729128,
    df_r=10,
    df_m=6,
    N=17,
    cmdline="regress executions income perpoverty perblack LN_VC100k96 south degree [aweight=fweight], vce(robust)",  # noqa:E501
    title="Linear regression",
    marginsok="XB default",
    vce="robust",
    depvar="executions",
    cmd="regress",
    properties="b V",
    predict="regres_p",
    model="ols",
    estat_cmd="regress_estat",
    wexp="= fweight",
    wtype="aweight",
    vcetype="Robust",
)

params_table = np.array([
    .00177624355887,  .00100571734546,  1.7661458926668,  .10782432028789,
    -.00046463433267,   .0040171214504,               10,  2.2281388519863,
    0,  .70240571372092,  .54986275700055,  1.2774200557835,
    .23031379083217,  -.5227648584123,  1.9275762858541,               10,
    2.2281388519863,                0, -.76566360596606,  .46482124106144,
    -1.6472216377583,  .13053265392051, -1.8013498724035,  .27002266047141,
    10,  2.2281388519863,                0,  5.7915855647065,
    5.8518623033717,  .98969956305525,  .34566324660643, -7.2471761899099,
    18.830347319323,               10,  2.2281388519863,                0,
    13.018291494864,  7.3741002410906,  1.7654074489417,  .10795348742173,
    -3.412227750751,   29.44881074048,               10,  2.2281388519863,
    0, -140.99921608421,  84.973820309491, -1.6593253730463,
    .12803894207791, -330.33268651749,  48.334254349065,               10,
    2.2281388519863,                0, -68.484290889814,  50.764306481463,
    -1.3490638528633,  .20706938025917,  -181.5942144553,  44.625632675673,
    10,  2.2281388519863,                0]).reshape(7, 9)

params_table_colnames = 'b se t pvalue ll ul df crit eform'.split()

params_table_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                         'south', 'degree', '_cons']

cov = np.array([
    1.011467379e-06,  .00038778854684, -.00038909911416,  .00356508765632,
    .0056952104088, -.07926157334067, -.04218673068644,  .00038778854684,
    .30234905153625, -.10112236243026,  .59175926747871,  1.4744074711876,
    -25.6203584288, -14.793319880623, -.00038909911416, -.10112236243026,
    .21605878614189, -2.3405630815795, -3.2257627901142,   31.66920792546,
    20.934058595259,  .00356508765632,  .59175926747871, -2.3405630815795,
    34.244292417623,  34.810403897967, -270.34292245471, -270.19382562804,
    .0056952104088,  1.4744074711876, -3.2257627901142,  34.810403897967,
    54.377354365652,  -414.2817137548, -324.24739845086, -.07926157334067,
    -25.6203584288,   31.66920792546, -270.34292245471,  -414.2817137548,
    7220.5501379896,  2907.4556071681, -.04218673068644, -14.793319880623,
    20.934058595259, -270.19382562804, -324.24739845086,  2907.4556071681,
    2577.0148125439]).reshape(7, 7)

cov_colnames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

cov_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

infocrit = np.array([
    17, -55.235569128348, -47.541220455815,                7,
    109.08244091163,  114.91493432002])

infocrit_colnames = 'N ll0 ll df AIC BIC'.split()

infocrit_rownames = '.'.split()

predicted = np.array([
    23.018356323242,  11.030969619751,  7.6487560272217,  3.2376720905304,
    1.3298480510712,  2.4579885005951,  6.7120413780212,  2.8951823711395,
    .90416890382767,  2.1985862255096,  1.9608836174011,  2.5452246665955,
    4.6054129600525,  2.8738057613373,  2.9902882575989,  1.8505314588547,
    1.4887162446976,    1.47836124897,  5.9044842720032,  4.8891386985779,
    7.0818486213684,  4.6786789894104,  7.5460968017578,  5.5129766464233,
    4.1125593185425,  2.3989260196686, -2.7979807853699,  3.8943622112274,
    -1.4647831916809,  2.8729522228241, -3.5234127044678,  3.7701880931854,
    3.9779393672943,  1.9573417901993]).reshape(17, 2)

predicted_colnames = 'predict_mu predict_std'.split()

predicted_rownames = ['r'+str(n) for n in range(1, 18)]


results_wls_aweight_robust = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    infocrit=infocrit,
    infocrit_colnames=infocrit_colnames,
    infocrit_rownames=infocrit_rownames,
    predicted=predicted,
    predicted_colnames=predicted_colnames,
    predicted_rownames=predicted_rownames,
    **est
)

est = dict(
    rank=7,
    ll_0=-55.23556912834824,
    ll=-47.54122045581504,
    r2_a=.3528737432046668,
    rss=267.3132086911238,
    mss=393.6105745962962,
    rmse=5.17023412130557,
    r2=.5955460895029168,
    F=1.412187242235973,
    df_r=8,
    df_m=6,
    N=17,
    N_clust=9,
    cmdline="regress executions income perpoverty perblack LN_VC100k96 south degree [aweight=fweight], vce(cluster id)",  # noqa:E501
    title="Linear regression",
    marginsok="XB default",
    vce="cluster",
    depvar="executions",
    cmd="regress",
    properties="b V",
    predict="regres_p",
    model="ols",
    estat_cmd="regress_estat",
    wexp="= fweight",
    wtype="aweight",
    vcetype="Robust",
    clustvar="id",
)

params_table = np.array([
    .00177624355887,  .00103574504038,  1.7149428571794,  .12469817836724,
    -.00061218878728,  .00416467590501,                8,  2.3060041352042,
    0,  .70240571372092,  .64463869959516,  1.0896114585768,
    .30761438040884, -.78413379325815,     2.1889452207,                8,
    2.3060041352042,                0, -.76566360596606,  .50850811868177,
    -1.5057057652313,  .17056206446331, -1.9382854304311,  .40695821849901,
    8,  2.3060041352042,                0,  5.7915855647065,
    6.2948340440059,  .92005373362009,   .3844480847801, -8.7243277711951,
    20.307498900608,                8,  2.3060041352042,                0,
    13.018291494864,  7.9526248350517,  1.6369804642972,  .14027059672576,
    -5.3204942604922,  31.357077250221,                8,  2.3060041352042,
    0, -140.99921608421,  84.897180497105, -1.6608233071889,
    .13532738016362, -336.77246537771,  54.774033209288,                8,
    2.3060041352042,                0, -68.484290889814,  50.203382265366,
    -1.3641369923608,   .2096627597382, -184.25349799498,  47.284916215355,
    8,  2.3060041352042,                0]).reshape(7, 9)

params_table_colnames = 'b se t pvalue ll ul df crit eform'.split()

params_table_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                         'south', 'degree', '_cons']

cov = np.array([
    1.072767789e-06,  .00042569049255, -.00044272344175,  .00386796354086,
    .00653558563917, -.08376884119522, -.04513384476642,  .00042569049255,
    .41555905301573, -.07730648264729, -.34087330734824,  .82631440946934,
    -31.768811666606, -10.324414524804, -.00044272344175, -.07730648264729,
    .25858050676528, -2.8727606144729, -3.9481543148554,  35.836754991381,
    24.653552354067,  .00386796354086, -.34087330734824, -2.8727606144729,
    39.624935641576,  42.351437415382, -335.98208369348, -283.16728769825,
    .00653558563917,  .82631440946934, -3.9481543148554,  42.351437415382,
    63.24424176708, -502.21726015398, -366.49477518415, -.08376884119522,
    -31.768811666606,  35.836754991381, -335.98208369348, -502.21726015398,
    7207.531256358,  3532.1379707168, -.04513384476642, -10.324414524804,
    24.653552354067, -283.16728769825, -366.49477518415,  3532.1379707168,
    2520.3795908825]).reshape(7, 7)

cov_colnames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

cov_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

infocrit = np.array([
    17, -55.235569128348, -47.541220455815,                7,
    109.08244091163,  114.91493432002])

infocrit_colnames = 'N ll0 ll df AIC BIC'.split()

infocrit_rownames = '.'.split()

predicted = np.array([
    23.018356323242,  11.727355003357,  7.6487560272217,  3.4638004302979,
    1.3298480510712,  2.1195623874664,  6.7120413780212,  2.8227334022522,
    .90416890382767,  2.2036759853363,  1.9608836174011,  2.0707910060883,
    4.6054129600525,  2.9022018909454,  2.9902882575989,  1.6939970254898,
    1.4887162446976,  1.8477793931961,  5.9044842720032,  4.8752007484436,
    7.0818486213684,     4.4365234375,  7.5460968017578,  5.6850047111511,
    4.1125593185425,  2.7407164573669, -2.7979807853699,  3.9614858627319,
    -1.4647831916809,  2.4376966953278, -3.5234127044678,  3.5529434680939,
    3.9779393672943,  1.7075037956238]).reshape(17, 2)

predicted_colnames = 'predict_mu predict_std'.split()

predicted_rownames = ['r'+str(n) for n in range(1, 18)]


results_wls_aweight_clu1 = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    infocrit=infocrit,
    infocrit_colnames=infocrit_colnames,
    infocrit_rownames=infocrit_rownames,
    predicted=predicted,
    predicted_colnames=predicted_colnames,
    predicted_rownames=predicted_rownames,
    **est
)

est = dict(
    rank=7,
    ll_0=-107.2219871314995,
    ll=-92.28589853187629,
    r2_a=.5022105716958969,
    rss=518.9021109886529,
    mss=764.067585981045,
    rmse=4.467412394167744,
    r2=.5955460895029162,
    F=1.835843414931295,
    df_r=8,
    df_m=6,
    N=33,
    N_clust=9,
    cmdline="regress executions income perpoverty perblack LN_VC100k96 south degree [fweight=fweight], vce(cluster id)",  # noqa:E501
    title="Linear regression",
    marginsok="XB default",
    vce="cluster",
    depvar="executions",
    cmd="regress",
    properties="b V",
    predict="regres_p",
    model="ols",
    estat_cmd="regress_estat",
    wexp="= fweight",
    wtype="fweight",
    vcetype="Robust",
    clustvar="id",
)

params_table = np.array([
    .00177624355887,  .00090840849363,  1.9553357012053,  .08627786102497,
    -.00031855018389,  .00387103730162,                8,  2.3060041352042,
    0,  .70240571372091,  .56538554103558,  1.2423482079757,
    .24928937729829, -.60137568189177,  2.0061871093336,                8,
    2.3060041352042,                0, -.76566360596606,  .44599112337258,
    -1.7167687109468,  .12435346910262, -1.7941209807276,  .26279376879547,
    8,  2.3060041352042,                0,  5.7915855647065,
    5.5209346785031,  1.0490226568442,  .32482245151877, -6.9397126341137,
    18.522883763527,                8,  2.3060041352042,                0,
    13.018291494864,  6.9749133861223,   1.866444896759,  .09894610636006,
    -3.0658876162246,  29.102470605953,                8,  2.3060041352042,
    0, -140.99921608421,  74.459752971542, -1.8936299202886,
    .09489418422765, -312.70371434287,  30.705282174445,                8,
    2.3060041352042,                0, -68.484290889814,  44.031279012175,
    -1.5553554751584,  .15847103736706, -170.02060237022,   33.05202059059,
    8,  2.3060041352042,                0]).reshape(7, 9)

params_table_colnames = 'b se t pvalue ll ul df crit eform'.split()

params_table_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                         'south', 'degree', '_cons']

cov = np.array([
    8.252059913e-07,  .00032745422504, -.00034055649365,  .00297535656989,
    .0050273735686, -.06443757015017, -.03471834212801,  .00032745422504,
    .31966081001209, -.05946652511329, -.26221023642171,  .63562646882257,
    -24.437547435849, -7.9418573267692, -.00034055649365, -.05946652511329,
    .19890808212714, -2.2098158572872,  -3.037041780658,  27.566734608754,
    18.96427104159,  .00297535656989, -.26221023642171, -2.2098158572872,
    30.480719724298,  32.578028781062, -258.44775668729, -217.82099053713,
    .0050273735686,  .63562646882257,  -3.037041780658,  32.578028781062,
    48.649416743908, -386.32096934921, -281.91905783396, -.06443757015017,
    -24.437547435849,  27.566734608754, -258.44775668729, -386.32096934921,
    5544.254812583,  2717.0292082435, -.03471834212801, -7.9418573267692,
    18.96427104159, -217.82099053713, -281.91905783396,  2717.0292082435,
    1938.753531448]).reshape(7, 7)

cov_colnames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

cov_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

infocrit = np.array([
    33,  -107.2219871315, -92.285898531876,                7,
    198.57179706375,  209.04734999402])

infocrit_colnames = 'N ll0 ll df AIC BIC'.split()

infocrit_rownames = '.'.split()

predicted = np.array([
    23.018356323242,  10.285571098328,  7.6487560272217,  3.0379540920258,
    1.3298480510712,  1.8589791059494,  6.7120413780212,  2.4757008552551,
    .90416890382767,  1.9327516555786,  1.9608836174011,  1.8162038326263,
    4.6054129600525,  2.5453994274139,  2.9902882575989,   1.485733628273,
    1.4887162446976,  1.6206097602844,  5.9044842720032,  4.2758340835571,
    7.0818486213684,  3.8910882472992,  7.5460968017578,  4.9860787391663,
    4.1125593185425,  2.4037673473358, -2.7979807853699,  3.4744529724121,
    -1.4647831916809,  2.1380014419556, -3.5234127044678,  3.1161375045776,
    3.9779393672943,  1.4975799322128]).reshape(17, 2)

predicted_colnames = 'predict_mu predict_std'.split()

predicted_rownames = ['r'+str(n) for n in range(1, 18)]


results_wls_fweight_clu1 = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    infocrit=infocrit,
    infocrit_colnames=infocrit_colnames,
    infocrit_rownames=infocrit_rownames,
    predicted=predicted,
    predicted_colnames=predicted_colnames,
    predicted_rownames=predicted_rownames,
    **est
)

est = dict(
    rank=7,
    ll_0=-55.23556912834824,
    ll=-47.54122045581504,
    r2_a=.3528737432046668,
    rss=267.3132086911238,
    mss=393.6105745962962,
    rmse=5.17023412130557,
    r2=.5955460895029168,
    F=1.412187242235973,
    df_r=8,
    df_m=6,
    N=17,
    N_clust=9,
    cmdline="regress executions income perpoverty perblack LN_VC100k96 south degree [pweight=fweight], vce(cluster id)",  # noqa:E501
    title="Linear regression",
    marginsok="XB default",
    vce="cluster",
    depvar="executions",
    cmd="regress",
    properties="b V",
    predict="regres_p",
    model="ols",
    estat_cmd="regress_estat",
    wexp="= fweight",
    wtype="pweight",
    vcetype="Robust",
    clustvar="id",
)

params_table = np.array([
    .00177624355887,  .00103574504038,  1.7149428571794,  .12469817836724,
    -.00061218878728,  .00416467590501,                8,  2.3060041352042,
    0,  .70240571372092,  .64463869959516,  1.0896114585768,
    .30761438040884, -.78413379325815,     2.1889452207,                8,
    2.3060041352042,                0, -.76566360596606,  .50850811868177,
    -1.5057057652313,  .17056206446331, -1.9382854304311,  .40695821849901,
    8,  2.3060041352042,                0,  5.7915855647065,
    6.2948340440059,  .92005373362009,   .3844480847801, -8.7243277711951,
    20.307498900608,                8,  2.3060041352042,                0,
    13.018291494864,  7.9526248350517,  1.6369804642972,  .14027059672576,
    -5.3204942604922,  31.357077250221,                8,  2.3060041352042,
    0, -140.99921608421,  84.897180497105, -1.6608233071889,
    .13532738016362, -336.77246537771,  54.774033209288,                8,
    2.3060041352042,                0, -68.484290889814,  50.203382265366,
    -1.3641369923608,   .2096627597382, -184.25349799498,  47.284916215355,
    8,  2.3060041352042,                0]).reshape(7, 9)

params_table_colnames = 'b se t pvalue ll ul df crit eform'.split()

params_table_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                         'south', 'degree', '_cons']

cov = np.array([
    1.072767789e-06,  .00042569049255, -.00044272344175,  .00386796354086,
    .00653558563917, -.08376884119522, -.04513384476642,  .00042569049255,
    .41555905301573, -.07730648264729, -.34087330734824,  .82631440946934,
    -31.768811666606, -10.324414524804, -.00044272344175, -.07730648264729,
    .25858050676528, -2.8727606144729, -3.9481543148554,  35.836754991381,
    24.653552354067,  .00386796354086, -.34087330734824, -2.8727606144729,
    39.624935641576,  42.351437415382, -335.98208369348, -283.16728769825,
    .00653558563917,  .82631440946934, -3.9481543148554,  42.351437415382,
    63.24424176708, -502.21726015398, -366.49477518415, -.08376884119522,
    -31.768811666606,  35.836754991381, -335.98208369348, -502.21726015398,
    7207.531256358,  3532.1379707168, -.04513384476642, -10.324414524804,
    24.653552354067, -283.16728769825, -366.49477518415,  3532.1379707168,
    2520.3795908825]).reshape(7, 7)

cov_colnames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

cov_rownames = ['income', 'perpoverty', 'perblack', 'LN_VC100k96',
                'south', 'degree', '_cons']

infocrit = np.array([
    17, -55.235569128348, -47.541220455815,                7,
    109.08244091163,  114.91493432002])

infocrit_colnames = 'N ll0 ll df AIC BIC'.split()

infocrit_rownames = '.'.split()

predicted = np.array([
    23.018356323242,  11.727355003357,  7.6487560272217,  3.4638004302979,
    1.3298480510712,  2.1195623874664,  6.7120413780212,  2.8227334022522,
    .90416890382767,  2.2036759853363,  1.9608836174011,  2.0707910060883,
    4.6054129600525,  2.9022018909454,  2.9902882575989,  1.6939970254898,
    1.4887162446976,  1.8477793931961,  5.9044842720032,  4.8752007484436,
    7.0818486213684,     4.4365234375,  7.5460968017578,  5.6850047111511,
    4.1125593185425,  2.7407164573669, -2.7979807853699,  3.9614858627319,
    -1.4647831916809,  2.4376966953278, -3.5234127044678,  3.5529434680939,
    3.9779393672943,  1.7075037956238]).reshape(17, 2)

predicted_colnames = 'predict_mu predict_std'.split()

predicted_rownames = ['r'+str(n) for n in range(1, 18)]


results_wls_pweight_clu1 = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    infocrit=infocrit,
    infocrit_colnames=infocrit_colnames,
    infocrit_rownames=infocrit_rownames,
    predicted=predicted,
    predicted_colnames=predicted_colnames,
    predicted_rownames=predicted_rownames,
    **est
)
