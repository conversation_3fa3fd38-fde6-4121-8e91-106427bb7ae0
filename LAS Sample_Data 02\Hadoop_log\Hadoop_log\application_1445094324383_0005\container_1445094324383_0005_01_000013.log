2015-10-17 23:10:33,249 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 23:10:33,968 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 23:10:33,968 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-17 23:10:34,093 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 23:10:34,093 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445094324383_0005, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@12e9d40f)
2015-10-17 23:10:34,609 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 23:10:36,109 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445094324383_0005
2015-10-17 23:10:38,796 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 23:10:40,859 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 23:10:41,281 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@5504b2ab
2015-10-17 23:10:47,375 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/wordcount2.txt:1073741824+134217728
2015-10-17 23:10:47,562 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-17 23:10:47,562 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-17 23:10:47,562 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-17 23:10:47,562 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-17 23:10:47,562 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-17 23:10:47,640 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-17 23:11:15,266 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 23:11:15,266 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 34175830; bufvoid = 104857600
2015-10-17 23:11:15,266 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 13786836(55147344); length = 12427561/6553600
2015-10-17 23:11:15,266 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 44661579 kvi 11165388(44661552)
2015-10-17 23:11:48,548 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-17 23:11:48,689 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 44661579 kv 11165388(44661552) kvi 8543964(34175856)
2015-10-17 23:12:01,924 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 23:12:01,924 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 44661579; bufend = 78834490; bufvoid = 104857600
2015-10-17 23:12:01,924 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 11165388(44661552); kvend = 24951500(99806000); length = 12428289/6553600
2015-10-17 23:12:01,924 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 89320237 kvi 22330052(89320208)
