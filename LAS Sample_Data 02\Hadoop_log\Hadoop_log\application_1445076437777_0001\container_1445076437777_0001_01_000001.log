2015-10-17 18:09:20,829 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445076437777_0001_000001
2015-10-17 18:09:21,736 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 18:09:21,736 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 1 cluster_timestamp: 1445076437777 } attemptId: 1 } keyId: 291674728)
2015-10-17 18:09:21,986 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 18:09:23,001 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 18:09:23,095 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 18:09:23,142 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 18:09:23,142 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 18:09:23,142 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 18:09:23,142 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 18:09:23,142 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 18:09:23,158 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 18:09:23,158 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 18:09:23,158 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 18:09:23,220 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 18:09:23,251 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 18:09:23,283 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 18:09:23,298 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 18:09:23,361 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 18:09:23,704 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 18:09:23,783 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 18:09:23,783 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 18:09:23,783 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445076437777_0001 to jobTokenSecretManager
2015-10-17 18:09:23,986 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445076437777_0001 because: not enabled; too many maps; too much input;
2015-10-17 18:09:24,001 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445076437777_0001 = 1256521728. Number of splits = 10
2015-10-17 18:09:24,017 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445076437777_0001 = 1
2015-10-17 18:09:24,017 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445076437777_0001Job Transitioned from NEW to INITED
2015-10-17 18:09:24,017 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445076437777_0001.
2015-10-17 18:09:24,048 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 18:09:24,064 INFO [Socket Reader #1 for port 52829] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 52829
2015-10-17 18:09:24,095 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 18:09:24,095 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 18:09:24,095 INFO [IPC Server listener on 52829] org.apache.hadoop.ipc.Server: IPC Server listener on 52829: starting
2015-10-17 18:09:24,095 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MININT-FNANLI5.fareast.corp.microsoft.com/*************:52829
2015-10-17 18:09:24,189 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 18:09:24,189 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 18:09:24,204 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 18:09:24,204 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 18:09:24,204 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 18:09:24,204 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 18:09:24,204 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 18:09:24,220 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 52836
2015-10-17 18:09:24,220 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 18:09:24,298 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\Jetty_0_0_0_0_52836_mapreduce____wt7g63\webapp
2015-10-17 18:09:24,501 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:52836
2015-10-17 18:09:24,501 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 52836
2015-10-17 18:09:24,954 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 18:09:24,954 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445076437777_0001
2015-10-17 18:09:24,954 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 18:09:24,970 INFO [Socket Reader #1 for port 52839] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 52839
2015-10-17 18:09:24,970 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 18:09:24,970 INFO [IPC Server listener on 52839] org.apache.hadoop.ipc.Server: IPC Server listener on 52839: starting
2015-10-17 18:09:24,986 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 18:09:24,986 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 18:09:24,986 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 18:09:25,048 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-17 18:09:25,142 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 18:09:25,142 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 18:09:25,158 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 18:09:25,158 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 18:09:25,158 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445076437777_0001Job Transitioned from INITED to SETUP
2015-10-17 18:09:25,173 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 18:09:25,189 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445076437777_0001Job Transitioned from SETUP to RUNNING
2015-10-17 18:09:25,236 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:25,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:25,267 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 18:09:25,267 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 18:09:25,314 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445076437777_0001, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0001/job_1445076437777_0001_1.jhist
2015-10-17 18:09:26,173 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 18:09:26,376 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:18432, vCores:-17> knownNMs=5
2015-10-17 18:09:26,392 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:18432, vCores:-17>
2015-10-17 18:09:26,392 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:27,392 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:18432, vCores:-17>
2015-10-17 18:09:27,392 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:28,408 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:09:28,408 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:28,408 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0001_01_000002 to attempt_1445076437777_0001_m_000000_0
2015-10-17 18:09:28,408 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:14336, vCores:-21>
2015-10-17 18:09:28,408 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:28,408 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:9 ScheduledReds:0 AssignedMaps:1 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:1 ContRel:0 HostLocal:0 RackLocal:1
2015-10-17 18:09:28,502 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:28,533 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0001/job.jar
2015-10-17 18:09:28,533 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0001/job.xml
2015-10-17 18:09:28,548 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 18:09:28,548 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 18:09:28,548 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 18:09:28,627 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:28,627 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0001_01_000002 taskAttempt attempt_1445076437777_0001_m_000000_0
2015-10-17 18:09:28,627 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0001_m_000000_0
2015-10-17 18:09:28,627 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 18:09:28,814 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0001_m_000000_0 : 13562
2015-10-17 18:09:28,814 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0001_m_000000_0] using containerId: [container_1445076437777_0001_01_000002 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:64642]
2015-10-17 18:09:28,814 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:28,814 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0001_m_000000
2015-10-17 18:09:28,814 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:29,408 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:13312, vCores:-22> knownNMs=5
2015-10-17 18:09:29,408 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:13312, vCores:-22>
2015-10-17 18:09:29,408 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:30,423 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:09:30,423 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:30,423 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0001_01_000003 to attempt_1445076437777_0001_m_000001_0
2015-10-17 18:09:30,423 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-26>
2015-10-17 18:09:30,423 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:30,423 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:8 ScheduledReds:0 AssignedMaps:2 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:2 ContRel:0 HostLocal:0 RackLocal:2
2015-10-17 18:09:30,423 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:30,423 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:30,455 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0001_01_000003 taskAttempt attempt_1445076437777_0001_m_000001_0
2015-10-17 18:09:30,455 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0001_m_000001_0
2015-10-17 18:09:30,455 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:53425
2015-10-17 18:09:30,767 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0001_m_000001_0 : 13562
2015-10-17 18:09:30,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0001_m_000001_0] using containerId: [container_1445076437777_0001_01_000003 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:53425]
2015-10-17 18:09:30,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:30,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0001_m_000001
2015-10-17 18:09:30,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:31,580 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0001: ask=4 release= 0 newContainers=3 finishedContainers=0 resourcelimit=<memory:5120, vCores:-30> knownNMs=5
2015-10-17 18:09:31,580 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 3
2015-10-17 18:09:31,580 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:31,580 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0001_01_000004 to attempt_1445076437777_0001_m_000002_0
2015-10-17 18:09:31,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:31,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:31,611 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:31,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0001_01_000005 to attempt_1445076437777_0001_m_000003_0
2015-10-17 18:09:31,611 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:31,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0001_01_000006 to attempt_1445076437777_0001_m_000004_0
2015-10-17 18:09:31,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:5120, vCores:-30>
2015-10-17 18:09:31,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:31,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:5 ContRel:0 HostLocal:0 RackLocal:5
2015-10-17 18:09:31,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:31,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:31,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:31,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:31,705 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0001_01_000004 taskAttempt attempt_1445076437777_0001_m_000002_0
2015-10-17 18:09:31,705 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0001_m_000002_0
2015-10-17 18:09:31,705 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:53425
2015-10-17 18:09:31,720 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0001_01_000006 taskAttempt attempt_1445076437777_0001_m_000004_0
2015-10-17 18:09:31,720 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0001_m_000004_0
2015-10-17 18:09:31,720 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0001_01_000005 taskAttempt attempt_1445076437777_0001_m_000003_0
2015-10-17 18:09:31,720 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0001_m_000003_0
2015-10-17 18:09:31,720 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 18:09:31,720 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:53425
2015-10-17 18:09:31,814 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0001_m_000003_0 : 13562
2015-10-17 18:09:31,814 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0001_m_000003_0] using containerId: [container_1445076437777_0001_01_000005 on NM: [04DN8IQ.fareast.corp.microsoft.com:52150]
2015-10-17 18:09:31,814 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:31,814 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0001_m_000003
2015-10-17 18:09:31,814 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:32,002 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0001_m_000002_0 : 13562
2015-10-17 18:09:32,002 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0001_m_000002_0] using containerId: [container_1445076437777_0001_01_000004 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:53425]
2015-10-17 18:09:32,002 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:32,002 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0001_m_000002
2015-10-17 18:09:32,002 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:32,080 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0001_m_000004_0 : 13562
2015-10-17 18:09:32,080 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0001_m_000004_0] using containerId: [container_1445076437777_0001_01_000006 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:53425]
2015-10-17 18:09:32,080 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:32,080 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0001_m_000004
2015-10-17 18:09:32,080 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:32,595 INFO [Socket Reader #1 for port 52839] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0001 (auth:SIMPLE)
2015-10-17 18:09:32,689 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0001: ask=4 release= 0 newContainers=2 finishedContainers=0 resourcelimit=<memory:4096, vCores:-31> knownNMs=5
2015-10-17 18:09:32,689 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 18:09:32,689 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:32,689 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0001_01_000007 to attempt_1445076437777_0001_m_000005_0
2015-10-17 18:09:32,689 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:32,689 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0001_01_000008 to attempt_1445076437777_0001_m_000006_0
2015-10-17 18:09:32,689 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-31>
2015-10-17 18:09:32,689 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:32,689 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:0 RackLocal:7
2015-10-17 18:09:32,689 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:32,689 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:32,689 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:32,689 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:32,752 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0001_01_000007 taskAttempt attempt_1445076437777_0001_m_000005_0
2015-10-17 18:09:32,752 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0001_m_000005_0
2015-10-17 18:09:32,752 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 18:09:32,767 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0001_01_000008 taskAttempt attempt_1445076437777_0001_m_000006_0
2015-10-17 18:09:32,767 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0001_m_000006_0
2015-10-17 18:09:32,783 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 18:09:32,814 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0001_m_000002 asked for a task
2015-10-17 18:09:32,814 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0001_m_000002 given task: attempt_1445076437777_0001_m_000000_0
2015-10-17 18:09:32,970 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0001_m_000005_0 : 13562
2015-10-17 18:09:32,970 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0001_m_000005_0] using containerId: [container_1445076437777_0001_01_000007 on NM: [04DN8IQ.fareast.corp.microsoft.com:52150]
2015-10-17 18:09:32,970 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:32,970 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0001_m_000005
2015-10-17 18:09:32,970 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:33,564 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0001_m_000006_0 : 13562
2015-10-17 18:09:33,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0001_m_000006_0] using containerId: [container_1445076437777_0001_01_000008 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:64642]
2015-10-17 18:09:33,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:33,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0001_m_000006
2015-10-17 18:09:33,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:33,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0001: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:3072, vCores:-32> knownNMs=5
2015-10-17 18:09:33,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:09:33,939 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:33,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0001_01_000009 to attempt_1445076437777_0001_m_000007_0
2015-10-17 18:09:33,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-32>
2015-10-17 18:09:33,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:33,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:8 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:8 ContRel:0 HostLocal:0 RackLocal:8
2015-10-17 18:09:33,939 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:33,939 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:34,096 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0001_01_000009 taskAttempt attempt_1445076437777_0001_m_000007_0
2015-10-17 18:09:34,096 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0001_m_000007_0
2015-10-17 18:09:34,096 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 18:09:34,455 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0001_m_000007_0 : 13562
2015-10-17 18:09:34,455 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0001_m_000007_0] using containerId: [container_1445076437777_0001_01_000009 on NM: [04DN8IQ.fareast.corp.microsoft.com:52150]
2015-10-17 18:09:34,455 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:34,455 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0001_m_000007
2015-10-17 18:09:34,455 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:34,971 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0001: ask=4 release= 0 newContainers=2 finishedContainers=0 resourcelimit=<memory:1024, vCores:-34> knownNMs=5
2015-10-17 18:09:34,971 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 18:09:34,971 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:34,971 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0001_01_000010 to attempt_1445076437777_0001_m_000008_0
2015-10-17 18:09:34,971 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:34,971 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0001_01_000011 to attempt_1445076437777_0001_m_000009_0
2015-10-17 18:09:34,971 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:34,971 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 18:09:34,971 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:34,971 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:0 RackLocal:10
2015-10-17 18:09:34,986 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:34,986 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:34,986 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:35,017 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0001_01_000010 taskAttempt attempt_1445076437777_0001_m_000008_0
2015-10-17 18:09:35,017 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0001_m_000008_0
2015-10-17 18:09:35,017 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 18:09:35,096 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0001_01_000011 taskAttempt attempt_1445076437777_0001_m_000009_0
2015-10-17 18:09:35,096 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0001_m_000009_0
2015-10-17 18:09:35,096 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 18:09:35,221 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0001_m_000009_0 : 13562
2015-10-17 18:09:35,221 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0001_m_000009_0] using containerId: [container_1445076437777_0001_01_000011 on NM: [04DN8IQ.fareast.corp.microsoft.com:52150]
2015-10-17 18:09:35,221 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:35,221 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0001_m_000009
2015-10-17 18:09:35,221 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:35,361 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0001_m_000008_0 : 13562
2015-10-17 18:09:35,361 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0001_m_000008_0] using containerId: [container_1445076437777_0001_01_000010 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:64642]
2015-10-17 18:09:35,361 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:35,361 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0001_m_000008
2015-10-17 18:09:35,361 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:36,080 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-34> knownNMs=5
2015-10-17 18:09:38,143 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:38,143 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:39,033 INFO [Socket Reader #1 for port 52839] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0001 (auth:SIMPLE)
2015-10-17 18:09:39,111 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0001_m_000003 asked for a task
2015-10-17 18:09:39,111 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0001_m_000003 given task: attempt_1445076437777_0001_m_000001_0
2015-10-17 18:09:39,190 INFO [Socket Reader #1 for port 52839] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0001 (auth:SIMPLE)
2015-10-17 18:09:39,283 INFO [Socket Reader #1 for port 52839] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0001 (auth:SIMPLE)
2015-10-17 18:09:39,299 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0001_m_000004 asked for a task
2015-10-17 18:09:39,299 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0001_m_000004 given task: attempt_1445076437777_0001_m_000002_0
2015-10-17 18:09:39,377 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0001_m_000006 asked for a task
2015-10-17 18:09:39,377 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0001_m_000006 given task: attempt_1445076437777_0001_m_000004_0
2015-10-17 18:09:40,346 INFO [Socket Reader #1 for port 52839] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0001 (auth:SIMPLE)
2015-10-17 18:09:40,455 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0001_m_000005 asked for a task
2015-10-17 18:09:40,455 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0001_m_000005 given task: attempt_1445076437777_0001_m_000003_0
2015-10-17 18:09:43,799 INFO [Socket Reader #1 for port 52839] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0001 (auth:SIMPLE)
2015-10-17 18:09:43,987 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0001_m_000011 asked for a task
2015-10-17 18:09:43,987 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0001_m_000011 given task: attempt_1445076437777_0001_m_000009_0
2015-10-17 18:09:44,190 INFO [Socket Reader #1 for port 52839] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0001 (auth:SIMPLE)
2015-10-17 18:09:44,284 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0001_m_000008 asked for a task
2015-10-17 18:09:44,284 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0001_m_000008 given task: attempt_1445076437777_0001_m_000006_0
2015-10-17 18:09:45,315 INFO [Socket Reader #1 for port 52839] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0001 (auth:SIMPLE)
2015-10-17 18:09:45,627 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0001_m_000007 asked for a task
2015-10-17 18:09:45,627 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0001_m_000007 given task: attempt_1445076437777_0001_m_000005_0
2015-10-17 18:09:46,159 INFO [Socket Reader #1 for port 52839] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0001 (auth:SIMPLE)
2015-10-17 18:09:46,284 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0001_m_000010 asked for a task
2015-10-17 18:09:46,284 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0001_m_000010 given task: attempt_1445076437777_0001_m_000008_0
2015-10-17 18:09:46,752 INFO [Socket Reader #1 for port 52839] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0001 (auth:SIMPLE)
2015-10-17 18:09:47,127 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0001_m_000009 asked for a task
2015-10-17 18:09:47,127 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0001_m_000009 given task: attempt_1445076437777_0001_m_000007_0
2015-10-17 18:09:52,315 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.031263214
2015-10-17 18:09:52,643 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.01999609
2015-10-17 18:09:52,909 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.02442594
2015-10-17 18:09:54,784 INFO [IPC Server handler 1 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.0055330996
2015-10-17 18:09:54,956 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.060819574
2015-10-17 18:09:55,440 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.04038229
2015-10-17 18:09:55,706 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.045903794
2015-10-17 18:09:55,737 INFO [IPC Server handler 18 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.029635621
2015-10-17 18:09:55,987 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.008791983
2015-10-17 18:09:56,034 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.03907969
2015-10-17 18:09:56,722 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.0029267524
2015-10-17 18:09:57,894 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.010419989
2015-10-17 18:09:58,394 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.09607559
2015-10-17 18:09:58,597 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.04656846
2015-10-17 18:09:58,862 INFO [IPC Server handler 18 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.037450988
2015-10-17 18:09:59,065 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.07200554
2015-10-17 18:09:59,159 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.017910508
2015-10-17 18:09:59,847 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.05471313
2015-10-17 18:09:59,878 INFO [IPC Server handler 18 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.01595521
2015-10-17 18:10:00,206 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.020188509
2015-10-17 18:10:01,066 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.0136746075
2015-10-17 18:10:01,331 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.01921344
2015-10-17 18:10:01,769 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.056992814
2015-10-17 18:10:01,847 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.10635664
2015-10-17 18:10:02,066 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.049502663
2015-10-17 18:10:02,284 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.02312137
2015-10-17 18:10:02,284 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.090010166
2015-10-17 18:10:03,003 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.058946628
2015-10-17 18:10:03,222 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.02279529
2015-10-17 18:10:03,816 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.039406627
2015-10-17 18:10:04,175 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.01954007
2015-10-17 18:10:04,519 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.04070686
2015-10-17 18:10:04,878 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.06610942
2015-10-17 18:10:05,206 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.057318706
2015-10-17 18:10:05,441 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.03224203
2015-10-17 18:10:05,441 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.12302756
2015-10-17 18:10:05,519 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.10635664
2015-10-17 18:10:06,410 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.027029349
2015-10-17 18:10:06,816 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.07132264
2015-10-17 18:10:07,363 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.02766145
2015-10-17 18:10:07,472 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.052107573
2015-10-17 18:10:07,894 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.05992369
2015-10-17 18:10:08,394 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.06285369
2015-10-17 18:10:08,472 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.07099583
2015-10-17 18:10:08,582 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.035821836
2015-10-17 18:10:08,582 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.13411696
2015-10-17 18:10:09,019 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.10635664
2015-10-17 18:10:09,566 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.031914
2015-10-17 18:10:10,457 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.048850764
2015-10-17 18:10:10,754 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.074905396
2015-10-17 18:10:11,144 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.07653801
2015-10-17 18:10:11,519 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.07555503
2015-10-17 18:10:11,613 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.08076643
2015-10-17 18:10:11,707 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.08597843
2015-10-17 18:10:11,722 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.041358903
2015-10-17 18:10:11,722 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.15122175
2015-10-17 18:10:12,488 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.10635664
2015-10-17 18:10:12,754 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.03777393
2015-10-17 18:10:13,707 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.057872213
2015-10-17 18:10:14,879 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.10020929
2015-10-17 18:10:14,879 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.044941016
2015-10-17 18:10:14,879 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.18272488
2015-10-17 18:10:15,191 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.08663184
2015-10-17 18:10:15,363 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.097050875
2015-10-17 18:10:15,519 INFO [IPC Server handler 18 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.101198316
2015-10-17 18:10:15,519 INFO [IPC Server handler 18 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.09476967
2015-10-17 18:10:16,441 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.050476316
2015-10-17 18:10:16,769 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.06578643
2015-10-17 18:10:16,848 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.10635664
2015-10-17 18:10:18,019 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.2106263
2015-10-17 18:10:18,019 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.053735472
2015-10-17 18:10:18,535 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.10291471
2015-10-17 18:10:18,723 INFO [IPC Server handler 1 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.106964506
2015-10-17 18:10:18,816 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.10680563
2015-10-17 18:10:19,066 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.10617038
2015-10-17 18:10:19,488 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.106881365
2015-10-17 18:10:19,504 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.05667492
2015-10-17 18:10:19,863 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.07067241
2015-10-17 18:10:20,566 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.10635664
2015-10-17 18:10:21,113 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.060250524
2015-10-17 18:10:21,113 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.23943445
2015-10-17 18:10:21,863 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.10660437
2015-10-17 18:10:22,238 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.10680563
2015-10-17 18:10:22,379 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.106964506
2015-10-17 18:10:22,410 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.1066108
2015-10-17 18:10:22,645 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.0641559
2015-10-17 18:10:22,942 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.0788122
2015-10-17 18:10:23,223 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.106881365
2015-10-17 18:10:24,176 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.06871834
2015-10-17 18:10:24,176 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.24933223
2015-10-17 18:10:24,285 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.10635664
2015-10-17 18:10:25,348 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.10660437
2015-10-17 18:10:25,770 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.10680563
2015-10-17 18:10:25,770 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.07913794
2015-10-17 18:10:25,848 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.1066108
2015-10-17 18:10:26,129 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.106964506
2015-10-17 18:10:26,129 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.083698824
2015-10-17 18:10:26,973 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.106881365
2015-10-17 18:10:27,270 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.26534814
2015-10-17 18:10:27,270 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.07425525
2015-10-17 18:10:27,848 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.10635664
2015-10-17 18:10:28,926 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.085977964
2015-10-17 18:10:28,973 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.10660437
2015-10-17 18:10:29,426 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.10680563
2015-10-17 18:10:29,708 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.08825782
2015-10-17 18:10:29,848 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.106964506
2015-10-17 18:10:30,239 INFO [IPC Server handler 1 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.1066108
2015-10-17 18:10:30,551 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.106881365
2015-10-17 18:10:30,614 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.295472
2015-10-17 18:10:30,723 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.08825752
2015-10-17 18:10:31,786 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.10635664
2015-10-17 18:10:32,348 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.095097035
2015-10-17 18:10:32,348 INFO [IPC Server handler 1 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.10660437
2015-10-17 18:10:32,911 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.10680563
2015-10-17 18:10:33,130 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.10096029
2015-10-17 18:10:33,442 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.106964506
2015-10-17 18:10:33,630 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.1066108
2015-10-17 18:10:33,973 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.295472
2015-10-17 18:10:34,317 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.106881365
2015-10-17 18:10:34,395 INFO [IPC Server handler 24 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.09640168
2015-10-17 18:10:35,536 INFO [IPC Server handler 18 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.10635664
2015-10-17 18:10:35,739 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.10660437
2015-10-17 18:10:36,036 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.104539715
2015-10-17 18:10:36,552 INFO [IPC Server handler 1 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.10680563
2015-10-17 18:10:36,739 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.106493875
2015-10-17 18:10:37,005 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.1066108
2015-10-17 18:10:37,442 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.106964506
2015-10-17 18:10:38,333 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.10389046
2015-10-17 18:10:38,333 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.295472
2015-10-17 18:10:38,708 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.106881365
2015-10-17 18:10:39,286 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.10635664
2015-10-17 18:10:39,708 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.10660437
2015-10-17 18:10:39,880 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.10681946
2015-10-17 18:10:40,692 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.10680563
2015-10-17 18:10:40,771 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.106493875
2015-10-17 18:10:41,114 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.1066108
2015-10-17 18:10:41,192 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.106964506
2015-10-17 18:10:41,989 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.10685723
2015-10-17 18:10:41,989 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.295472
2015-10-17 18:10:43,599 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.10681946
2015-10-17 18:10:43,974 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.13666403
2015-10-17 18:10:44,130 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.106881365
2015-10-17 18:10:44,177 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.106493875
2015-10-17 18:10:44,349 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.10660437
2015-10-17 18:10:44,521 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.13216527
2015-10-17 18:10:44,568 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.1066108
2015-10-17 18:10:45,021 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.106964506
2015-10-17 18:10:45,380 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.10685723
2015-10-17 18:10:45,458 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.3267488
2015-10-17 18:10:46,974 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.10681946
2015-10-17 18:10:47,396 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.10877589
2015-10-17 18:10:47,552 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.106493875
2015-10-17 18:10:48,068 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.106881365
2015-10-17 18:10:48,068 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.17634216
2015-10-17 18:10:48,912 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.10685723
2015-10-17 18:10:48,912 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.40505454
2015-10-17 18:10:48,927 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.106964506
2015-10-17 18:10:49,177 INFO [IPC Server handler 16 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.17033094
2015-10-17 18:10:49,177 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.12636258
2015-10-17 18:10:50,724 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.10681946
2015-10-17 18:10:50,802 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.13743694
2015-10-17 18:10:51,318 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.106493875
2015-10-17 18:10:51,740 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.19158794
2015-10-17 18:10:51,771 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.106881365
2015-10-17 18:10:52,443 INFO [IPC Server handler 1 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.10685723
2015-10-17 18:10:52,599 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.5113961
2015-10-17 18:10:52,724 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.19242907
2015-10-17 18:10:52,724 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.15046245
2015-10-17 18:10:52,849 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.106964506
2015-10-17 18:10:54,209 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.10681946
2015-10-17 18:10:54,240 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.15966865
2015-10-17 18:10:54,834 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.106493875
2015-10-17 18:10:55,615 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.106881365
2015-10-17 18:10:55,818 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.19158794
2015-10-17 18:10:56,818 INFO [IPC Server handler 18 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.106964506
2015-10-17 18:10:56,928 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.17474464
2015-10-17 18:10:57,021 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.19242907
2015-10-17 18:10:57,209 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.5323719
2015-10-17 18:10:57,428 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.10685723
2015-10-17 18:10:57,787 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.10681946
2015-10-17 18:10:58,021 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.18643637
2015-10-17 18:10:58,334 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.106493875
2015-10-17 18:10:59,396 INFO [IPC Server handler 16 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.106881365
2015-10-17 18:10:59,725 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.19158794
2015-10-17 18:11:00,459 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.19211523
2015-10-17 18:11:00,522 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.5323719
2015-10-17 18:11:00,631 INFO [IPC Server handler 1 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.12847546
2015-10-17 18:11:00,662 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.19242907
2015-10-17 18:11:00,725 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.10685723
2015-10-17 18:11:01,100 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.10681946
2015-10-17 18:11:01,506 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.19212553
2015-10-17 18:11:01,834 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.11867386
2015-10-17 18:11:03,084 INFO [IPC Server handler 18 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.14134528
2015-10-17 18:11:03,303 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.19158794
2015-10-17 18:11:03,975 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.19211523
2015-10-17 18:11:04,115 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.15600072
2015-10-17 18:11:04,178 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.19242907
2015-10-17 18:11:04,631 INFO [IPC Server handler 16 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.5323719
2015-10-17 18:11:04,647 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.10942838
2015-10-17 18:11:04,897 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.11952324
2015-10-17 18:11:05,006 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.19212553
2015-10-17 18:11:05,037 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.14036712
2015-10-17 18:11:06,553 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.1605592
2015-10-17 18:11:06,991 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.19158794
2015-10-17 18:11:07,662 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.17684415
2015-10-17 18:11:07,944 INFO [IPC Server handler 1 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.5323719
2015-10-17 18:11:07,944 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.111056484
2015-10-17 18:11:08,147 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.19211523
2015-10-17 18:11:08,319 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.19242907
2015-10-17 18:11:08,459 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.19212553
2015-10-17 18:11:09,413 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.1634917
2015-10-17 18:11:09,788 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.15241806
2015-10-17 18:11:11,022 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.5323719
2015-10-17 18:11:11,038 INFO [IPC Server handler 1 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.121151805
2015-10-17 18:11:11,100 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.18963979
2015-10-17 18:11:11,506 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.19211523
2015-10-17 18:11:11,819 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.19242907
2015-10-17 18:11:12,147 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.19212553
2015-10-17 18:11:12,569 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.17032866
2015-10-17 18:11:12,928 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.16870272
2015-10-17 18:11:13,757 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.19158794
2015-10-17 18:11:13,944 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.19266446
2015-10-17 18:11:14,647 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.55358094
2015-10-17 18:11:14,663 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.12538546
2015-10-17 18:11:14,850 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.19258286
2015-10-17 18:11:14,850 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.19211523
2015-10-17 18:11:15,366 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.19242907
2015-10-17 18:11:15,585 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.19212553
2015-10-17 18:11:16,632 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.17912129
2015-10-17 18:11:17,163 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.1758662
2015-10-17 18:11:17,710 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.19266446
2015-10-17 18:11:17,710 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.19158794
2015-10-17 18:11:18,647 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.19258286
2015-10-17 18:11:18,882 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.19211523
2015-10-17 18:11:18,882 INFO [IPC Server handler 11 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.57338023
2015-10-17 18:11:18,976 INFO [IPC Server handler 12 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.19242907
2015-10-17 18:11:19,179 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.19212553
2015-10-17 18:11:19,397 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.13255124
2015-10-17 18:11:19,866 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.1888164
2015-10-17 18:11:20,335 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.18368477
2015-10-17 18:11:21,648 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.19266446
2015-10-17 18:11:21,804 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.19158794
2015-10-17 18:11:22,351 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.60128975
2015-10-17 18:11:22,351 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.21403481
2015-10-17 18:11:22,366 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.19258286
2015-10-17 18:11:22,398 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.19211523
2015-10-17 18:11:22,429 INFO [IPC Server handler 16 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.14688283
2015-10-17 18:11:22,694 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.19212553
2015-10-17 18:11:22,929 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.19255035
2015-10-17 18:11:23,460 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.18921807
2015-10-17 18:11:25,320 INFO [IPC Server handler 12 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.19266446
2015-10-17 18:11:25,429 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.19158794
2015-10-17 18:11:25,460 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.62002075
2015-10-17 18:11:25,538 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.15046383
2015-10-17 18:11:25,648 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.23817895
2015-10-17 18:11:25,866 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.19211523
2015-10-17 18:11:25,991 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.19212553
2015-10-17 18:11:26,054 INFO [IPC Server handler 11 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.19258286
2015-10-17 18:11:26,523 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.19209063
2015-10-17 18:11:26,632 INFO [IPC Server handler 16 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.19255035
2015-10-17 18:11:27,804 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.62002075
2015-10-17 18:11:28,867 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.667
2015-10-17 18:11:28,945 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.15372217
2015-10-17 18:11:28,960 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.19266446
2015-10-17 18:11:29,023 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.19158794
2015-10-17 18:11:29,179 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.27096793
2015-10-17 18:11:29,226 INFO [IPC Server handler 11 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.19671124
2015-10-17 18:11:29,742 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.19996788
2015-10-17 18:11:30,039 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.19258286
2015-10-17 18:11:30,117 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.19209063
2015-10-17 18:11:30,117 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.19255035
2015-10-17 18:11:32,836 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.19266446
2015-10-17 18:11:32,882 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.20268087
2015-10-17 18:11:32,882 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.667
2015-10-17 18:11:33,070 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.22830205
2015-10-17 18:11:33,070 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.22081058
2015-10-17 18:11:33,101 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.16577198
2015-10-17 18:11:33,117 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.2781602
2015-10-17 18:11:33,414 INFO [IPC Server handler 11 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.19255035
2015-10-17 18:11:33,476 INFO [IPC Server handler 12 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.1941065
2015-10-17 18:11:33,898 INFO [IPC Server handler 23 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.19258286
2015-10-17 18:11:36,367 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.667
2015-10-17 18:11:36,523 INFO [IPC Server handler 11 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.24235067
2015-10-17 18:11:36,523 INFO [IPC Server handler 12 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.19266446
2015-10-17 18:11:36,851 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.19801368
2015-10-17 18:11:36,851 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.2038747
2015-10-17 18:11:36,945 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.246216
2015-10-17 18:11:37,445 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.2781602
2015-10-17 18:11:37,648 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.19258286
2015-10-17 18:11:37,648 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.24360953
2015-10-17 18:11:37,976 INFO [IPC Server handler 23 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.19247705
2015-10-17 18:11:39,680 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.667
2015-10-17 18:11:40,148 INFO [IPC Server handler 2 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.2579398
2015-10-17 18:11:40,242 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.21755302
2015-10-17 18:11:40,258 INFO [IPC Server handler 16 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.21820512
2015-10-17 18:11:40,383 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.19266446
2015-10-17 18:11:40,648 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.27442503
2015-10-17 18:11:40,648 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.2781602
2015-10-17 18:11:40,773 INFO [IPC Server handler 12 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.25272688
2015-10-17 18:11:41,477 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.19247705
2015-10-17 18:11:41,492 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.19258286
2015-10-17 18:11:43,008 INFO [IPC Server handler 23 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.667
2015-10-17 18:11:43,195 INFO [IPC Server handler 2 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.2654286
2015-10-17 18:11:43,524 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.23123412
2015-10-17 18:11:43,602 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.2276501
2015-10-17 18:11:44,117 INFO [IPC Server handler 2 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.19266446
2015-10-17 18:11:44,555 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.27696857
2015-10-17 18:11:44,680 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.19247705
2015-10-17 18:11:45,149 INFO [IPC Server handler 23 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.19258286
2015-10-17 18:11:45,227 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.2781602
2015-10-17 18:11:45,305 INFO [IPC Server handler 22 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.271943
2015-10-17 18:11:46,867 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.24621503
2015-10-17 18:11:46,961 INFO [IPC Server handler 12 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.23481542
2015-10-17 18:11:46,961 INFO [IPC Server handler 1 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.667
2015-10-17 18:11:47,055 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.2774802
2015-10-17 18:11:47,805 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.19722714
2015-10-17 18:11:48,008 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.19247705
2015-10-17 18:11:48,446 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.29181042
2015-10-17 18:11:48,446 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.27696857
2015-10-17 18:11:48,508 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.27776006
2015-10-17 18:11:49,149 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.19258286
2015-10-17 18:11:50,290 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.26086873
2015-10-17 18:11:50,711 INFO [IPC Server handler 24 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.24458677
2015-10-17 18:11:51,102 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.6677285
2015-10-17 18:11:51,446 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.19247705
2015-10-17 18:11:51,665 INFO [IPC Server handler 22 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.22515008
2015-10-17 18:11:51,696 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.30711922
2015-10-17 18:11:51,774 INFO [IPC Server handler 18 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.27772525
2015-10-17 18:11:51,774 INFO [IPC Server handler 24 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.27776006
2015-10-17 18:11:52,227 INFO [IPC Server handler 1 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.27696857
2015-10-17 18:11:52,805 INFO [IPC Server handler 18 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.20614865
2015-10-17 18:11:53,930 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.27324715
2015-10-17 18:11:54,227 INFO [IPC Server handler 1 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.25696367
2015-10-17 18:11:54,977 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.25142607
2015-10-17 18:11:55,149 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.19247705
2015-10-17 18:11:55,634 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.31428403
2015-10-17 18:11:55,634 INFO [IPC Server handler 22 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.27772525
2015-10-17 18:11:55,634 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.27696857
2015-10-17 18:11:55,665 INFO [IPC Server handler 24 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.27776006
2015-10-17 18:11:55,852 INFO [IPC Server handler 18 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.7030613
2015-10-17 18:11:56,337 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.23514102
2015-10-17 18:11:57,165 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 18:11:57,165 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:57,993 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.27825075
2015-10-17 18:11:58,259 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-33>
2015-10-17 18:11:58,259 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:59,118 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.26813582
2015-10-17 18:11:59,759 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.27696857
2015-10-17 18:11:59,931 INFO [IPC Server handler 24 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.28073823
2015-10-17 18:12:00,024 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.27776006
2015-10-17 18:12:00,056 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.19247705
2015-10-17 18:12:00,931 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.26763028
2015-10-17 18:12:01,446 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.27825075
2015-10-17 18:12:02,134 INFO [IPC Server handler 24 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.27617908
2015-10-17 18:12:02,915 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-32>
2015-10-17 18:12:02,915 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:12:03,181 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.2783809
2015-10-17 18:12:03,603 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.27696857
2015-10-17 18:12:04,743 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.27811313
2015-10-17 18:12:04,806 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.27825075
2015-10-17 18:12:05,353 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.27765483
2015-10-17 18:12:06,603 INFO [Socket Reader #1 for port 52839] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0001 (auth:SIMPLE)
2015-10-17 18:12:06,665 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.8106567
2015-10-17 18:12:06,681 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.28171268
2015-10-17 18:12:06,978 INFO [IPC Server handler 12 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.2783809
2015-10-17 18:12:07,259 INFO [IPC Server handler 11 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.27696857
2015-10-17 18:12:08,447 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.27811313
2015-10-17 18:12:08,665 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.27765483
2015-10-17 18:12:09,353 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.27825075
2015-10-17 18:12:09,853 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.2885535
2015-10-17 18:12:10,009 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.8481658
2015-10-17 18:12:10,275 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-33>
2015-10-17 18:12:10,275 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:12:10,666 INFO [IPC Server handler 22 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.2783809
2015-10-17 18:12:11,056 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.27696857
2015-10-17 18:12:11,931 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.27765483
2015-10-17 18:12:12,119 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.27811313
2015-10-17 18:12:13,181 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.27825075
2015-10-17 18:12:13,369 INFO [Socket Reader #1 for port 52839] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0001 (auth:SIMPLE)
2015-10-17 18:12:13,416 INFO [IPC Server handler 11 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.36279422
2015-10-17 18:12:13,478 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.2924766
2015-10-17 18:12:13,634 INFO [Socket Reader #1 for port 52839] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0001 (auth:SIMPLE)
2015-10-17 18:12:13,728 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.22244106
2015-10-17 18:12:13,744 INFO [IPC Server handler 22 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.8878257
2015-10-17 18:12:14,463 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.2783809
2015-10-17 18:12:14,838 INFO [IPC Server handler 18 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.27696857
2015-10-17 18:12:15,884 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.27811313
2015-10-17 18:12:15,947 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.27765483
2015-10-17 18:12:16,635 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.36317363
2015-10-17 18:12:16,822 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.3012545
2015-10-17 18:12:16,853 INFO [IPC Server handler 22 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.27825075
2015-10-17 18:12:17,306 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.25761396
2015-10-17 18:12:17,416 INFO [IPC Server handler 12 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.91328233
2015-10-17 18:12:18,275 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.2783809
2015-10-17 18:12:18,478 INFO [IPC Server handler 21 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.27696857
2015-10-17 18:12:18,853 INFO [Socket Reader #1 for port 52839] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0001 (auth:SIMPLE)
2015-10-17 18:12:18,916 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.35629675
2015-10-17 18:12:19,322 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.27765483
2015-10-17 18:12:19,525 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.27811313
2015-10-17 18:12:19,869 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.36317363
2015-10-17 18:12:20,025 INFO [IPC Server handler 18 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.31623706
2015-10-17 18:12:20,260 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.3041874
2015-10-17 18:12:20,807 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.9388394
2015-10-17 18:12:21,338 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.26946113
2015-10-17 18:12:22,229 INFO [IPC Server handler 24 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.36388028
2015-10-17 18:12:22,400 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.2783809
2015-10-17 18:12:22,400 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.30192018
2015-10-17 18:12:22,760 INFO [IPC Server handler 11 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.27765483
2015-10-17 18:12:23,088 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.36317363
2015-10-17 18:12:23,291 INFO [IPC Server handler 24 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.33089197
2015-10-17 18:12:23,463 INFO [IPC Server handler 12 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.27811313
2015-10-17 18:12:23,572 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.32470456
2015-10-17 18:12:24,307 INFO [IPC Server handler 18 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.9656518
2015-10-17 18:12:24,651 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.27813601
2015-10-17 18:12:25,213 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 18:12:25,213 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:12:25,744 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.36388028
2015-10-17 18:12:26,010 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.2846463
2015-10-17 18:12:26,166 INFO [IPC Server handler 23 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.34248692
2015-10-17 18:12:26,229 INFO [IPC Server handler 2 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.2783809
2015-10-17 18:12:26,260 INFO [IPC Server handler 18 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.36317363
2015-10-17 18:12:26,479 INFO [IPC Server handler 12 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.34066337
2015-10-17 18:12:26,979 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.34359562
2015-10-17 18:12:27,338 INFO [IPC Server handler 22 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.27811313
2015-10-17 18:12:27,729 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 0.99933517
2015-10-17 18:12:28,010 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.27813601
2015-10-17 18:12:28,744 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000009_0 is : 1.0
2015-10-17 18:12:28,854 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0001_m_000009_0
2015-10-17 18:12:28,854 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:12:28,854 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0001_01_000011 taskAttempt attempt_1445076437777_0001_m_000009_0
2015-10-17 18:12:28,854 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0001_m_000009_0
2015-10-17 18:12:28,854 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 18:12:29,448 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.36388028
2015-10-17 18:12:29,776 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.36317363
2015-10-17 18:12:29,854 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:12:29,870 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0001_m_000009_0
2015-10-17 18:12:29,870 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:12:29,870 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-17 18:12:30,166 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.2783809
2015-10-17 18:12:30,166 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.34782642
2015-10-17 18:12:30,166 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.2895303
2015-10-17 18:12:30,166 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445076437777_0001_m_000006
2015-10-17 18:12:30,166 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 18:12:30,166 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445076437777_0001_m_000006
2015-10-17 18:12:30,166 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:12:30,166 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:12:30,166 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000006_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:12:30,260 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.3624012
2015-10-17 18:12:30,682 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:0 RackLocal:10
2015-10-17 18:12:30,682 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-34> knownNMs=5
2015-10-17 18:12:30,682 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 18:12:30,682 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-17 18:12:30,682 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:11264, vCores:-24> finalMapResourceLimit:<memory:10138, vCores:-22> finalReduceResourceLimit:<memory:1126, vCores:-2> netScheduledMapResource:<memory:11264, vCores:11> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-17 18:12:30,682 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping up 1
2015-10-17 18:12:30,682 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:0 RackLocal:10
2015-10-17 18:12:30,760 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.3638923
2015-10-17 18:12:31,041 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.27811313
2015-10-17 18:12:31,323 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.27813601
2015-10-17 18:12:31,760 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0001: ask=1 release= 0 newContainers=1 finishedContainers=1 resourcelimit=<memory:1024, vCores:-34> knownNMs=5
2015-10-17 18:12:31,760 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0001_01_000011
2015-10-17 18:12:31,760 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:12:31,760 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:12:31,760 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0001_01_000012 to attempt_1445076437777_0001_m_000006_1
2015-10-17 18:12:31,760 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:0 RackLocal:11
2015-10-17 18:12:31,760 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0001_m_000009_0: Container killed by the ApplicationMaster.

2015-10-17 18:12:31,760 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:12:31,760 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000006_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:12:31,760 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0001_01_000012 taskAttempt attempt_1445076437777_0001_m_000006_1
2015-10-17 18:12:31,760 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0001_m_000006_1
2015-10-17 18:12:31,760 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 18:12:32,229 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0001_m_000006_1 : 13562
2015-10-17 18:12:32,229 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0001_m_000006_1] using containerId: [container_1445076437777_0001_01_000012 on NM: [04DN8IQ.fareast.corp.microsoft.com:52150]
2015-10-17 18:12:32,229 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000006_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:12:32,229 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0001_m_000006
2015-10-17 18:12:32,807 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0001: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:12:32,807 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:12:32,807 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 18:12:32,807 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0001_01_000013 to attempt_1445076437777_0001_r_000000_0
2015-10-17 18:12:32,807 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:0 RackLocal:11
2015-10-17 18:12:32,870 INFO [IPC Server handler 12 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.36388028
2015-10-17 18:12:33,151 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:12:33,151 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:12:33,151 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0001_01_000013 taskAttempt attempt_1445076437777_0001_r_000000_0
2015-10-17 18:12:33,151 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0001_r_000000_0
2015-10-17 18:12:33,151 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:12:33,370 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.37160328
2015-10-17 18:12:33,370 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0001_r_000000_0 : 13562
2015-10-17 18:12:33,370 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0001_r_000000_0] using containerId: [container_1445076437777_0001_01_000013 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:12:33,370 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:12:33,370 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0001_r_000000
2015-10-17 18:12:33,370 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0001_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:12:33,682 INFO [IPC Server handler 22 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.36319977
2015-10-17 18:12:33,807 INFO [IPC Server handler 18 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.2783809
2015-10-17 18:12:33,854 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0001: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:12:33,932 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.3624012
2015-10-17 18:12:34,604 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.27811313
2015-10-17 18:12:34,807 INFO [IPC Server handler 2 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.3638923
2015-10-17 18:12:35,417 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.27813601
2015-10-17 18:12:35,667 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.30939752
2015-10-17 18:12:35,948 INFO [Socket Reader #1 for port 52839] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0001 (auth:SIMPLE)
2015-10-17 18:12:36,042 INFO [IPC Server handler 24 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0001_r_000013 asked for a task
2015-10-17 18:12:36,042 INFO [IPC Server handler 24 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0001_r_000013 given task: attempt_1445076437777_0001_r_000000_0
2015-10-17 18:12:36,839 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.36388028
2015-10-17 18:12:36,979 INFO [IPC Server handler 24 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.3862597
2015-10-17 18:12:37,542 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.36319977
2015-10-17 18:12:37,636 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 0 maxEvents 10000
2015-10-17 18:12:37,714 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.3624012
2015-10-17 18:12:37,823 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.2783809
2015-10-17 18:12:38,386 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.3638923
2015-10-17 18:12:38,464 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.27811313
2015-10-17 18:12:38,682 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:12:39,104 INFO [IPC Server handler 24 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.27813601
2015-10-17 18:12:39,261 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.3217347
2015-10-17 18:12:39,745 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:12:40,292 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.36388028
2015-10-17 18:12:40,417 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.4155694
2015-10-17 18:12:40,839 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:12:41,011 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.36319977
2015-10-17 18:12:41,573 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.3624012
2015-10-17 18:12:41,729 INFO [Socket Reader #1 for port 52839] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0001 (auth:SIMPLE)
2015-10-17 18:12:41,917 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:12:41,917 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.30415744
2015-10-17 18:12:42,167 INFO [IPC Server handler 2 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0001_m_000012 asked for a task
2015-10-17 18:12:42,167 INFO [IPC Server handler 2 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0001_m_000012 given task: attempt_1445076437777_0001_m_000006_1
2015-10-17 18:12:42,292 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.28607738
2015-10-17 18:12:42,558 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.27813601
2015-10-17 18:12:42,855 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.32828745
2015-10-17 18:12:42,980 INFO [IPC Server handler 16 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:12:43,636 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.0
2015-10-17 18:12:43,636 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.3638923
2015-10-17 18:12:43,776 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.37095222
2015-10-17 18:12:43,901 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.43934473
2015-10-17 18:12:44,026 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:12:44,558 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.36319977
2015-10-17 18:12:45,089 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:12:45,183 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.3624012
2015-10-17 18:12:45,183 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445076437777_0001_m_000005
2015-10-17 18:12:45,183 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445076437777_0001_m_000005
2015-10-17 18:12:45,183 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 18:12:45,183 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:12:45,183 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:12:45,183 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000005_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:12:45,355 INFO [IPC Server handler 18 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.3279619
2015-10-17 18:12:45,667 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:0 RackLocal:11
2015-10-17 18:12:45,745 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:12:45,870 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.30876803
2015-10-17 18:12:46,152 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:12:46,339 INFO [IPC Server handler 18 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.27813601
2015-10-17 18:12:46,745 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.0
2015-10-17 18:12:47,089 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.32828745
2015-10-17 18:12:47,198 INFO [IPC Server handler 1 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.3716038
2015-10-17 18:12:47,230 INFO [IPC Server handler 23 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:12:47,355 INFO [IPC Server handler 2 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.4409738
2015-10-17 18:12:47,527 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.3638923
2015-10-17 18:12:47,964 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.36319977
2015-10-17 18:12:48,308 INFO [IPC Server handler 18 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:12:48,574 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.3624012
2015-10-17 18:12:48,761 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.34229127
2015-10-17 18:12:49,370 INFO [IPC Server handler 2 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:12:49,449 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.33512682
2015-10-17 18:12:49,777 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.28171596
2015-10-17 18:12:49,886 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.0
2015-10-17 18:12:50,542 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:12:50,964 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.4426013
2015-10-17 18:12:51,027 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.37323174
2015-10-17 18:12:51,339 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.3638923
2015-10-17 18:12:51,339 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.36319977
2015-10-17 18:12:51,636 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:12:52,714 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:12:52,839 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.3624012
2015-10-17 18:12:53,027 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.36404583
2015-10-17 18:12:53,214 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.0
2015-10-17 18:12:53,261 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.28236467
2015-10-17 18:12:53,683 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.35772696
2015-10-17 18:12:53,793 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:12:54,121 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.33024105
2015-10-17 18:12:54,339 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.44859612
2015-10-17 18:12:54,386 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.38137493
2015-10-17 18:12:54,683 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.36319977
2015-10-17 18:12:54,730 INFO [IPC Server handler 18 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.37062657
2015-10-17 18:12:54,855 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:12:55,746 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:12:55,746 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0001_01_000014 to attempt_1445076437777_0001_m_000005_1
2015-10-17 18:12:55,746 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:1 RackLocal:11
2015-10-17 18:12:55,746 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:12:55,746 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000005_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:12:55,746 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0001_01_000014 taskAttempt attempt_1445076437777_0001_m_000005_1
2015-10-17 18:12:55,746 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0001_m_000005_1
2015-10-17 18:12:55,746 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:12:55,918 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:12:55,918 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0001_m_000005_1 : 13562
2015-10-17 18:12:55,918 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0001_m_000005_1] using containerId: [container_1445076437777_0001_01_000014 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 18:12:55,918 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000005_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:12:55,918 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0001_m_000005
2015-10-17 18:12:56,261 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:12:56,386 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.2833434
2015-10-17 18:12:56,605 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.36404583
2015-10-17 18:12:56,652 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.3624012
2015-10-17 18:12:56,808 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:12:56,980 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:12:57,277 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.33447424
2015-10-17 18:12:57,277 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_1 is : 0.0045567993
2015-10-17 18:12:57,418 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.44859612
2015-10-17 18:12:57,465 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.40384638
2015-10-17 18:12:57,683 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.3637686
2015-10-17 18:12:57,761 INFO [IPC Server handler 12 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.37127745
2015-10-17 18:12:57,871 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.37713918
2015-10-17 18:12:58,027 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:12:58,746 INFO [Socket Reader #1 for port 52839] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0001 (auth:SIMPLE)
2015-10-17 18:12:58,808 INFO [IPC Server handler 12 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0001_m_000014 asked for a task
2015-10-17 18:12:58,808 INFO [IPC Server handler 12 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0001_m_000014 given task: attempt_1445076437777_0001_m_000005_1
2015-10-17 18:12:59,168 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:12:59,293 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:12:59,590 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.28725138
2015-10-17 18:13:00,246 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:00,246 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.36404583
2015-10-17 18:13:00,246 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445076437777_0001_m_000003
2015-10-17 18:13:00,324 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 18:13:00,324 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445076437777_0001_m_000003
2015-10-17 18:13:00,324 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:13:00,324 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:13:00,324 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000003_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:13:00,355 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_1 is : 0.0068375473
2015-10-17 18:13:00,355 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.3383832
2015-10-17 18:13:00,387 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.3624012
2015-10-17 18:13:00,527 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.44859612
2015-10-17 18:13:00,652 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.41719773
2015-10-17 18:13:00,918 INFO [IPC Server handler 12 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.38039592
2015-10-17 18:13:01,027 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.3839794
2015-10-17 18:13:01,027 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:1 RackLocal:11
2015-10-17 18:13:01,230 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:13:01,293 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:01,449 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.3637686
2015-10-17 18:13:02,340 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:02,340 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:13:02,699 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.28822955
2015-10-17 18:13:03,371 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:03,559 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.45139408
2015-10-17 18:13:03,684 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.42143258
2015-10-17 18:13:03,965 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.36404583
2015-10-17 18:13:03,965 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.3624012
2015-10-17 18:13:04,043 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.38365126
2015-10-17 18:13:04,106 INFO [IPC Server handler 12 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.38463128
2015-10-17 18:13:04,402 INFO [IPC Server handler 1 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:05,262 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.3637686
2015-10-17 18:13:05,371 INFO [IPC Server handler 2 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:13:05,496 INFO [IPC Server handler 23 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:05,606 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_1 is : 0.014980018
2015-10-17 18:13:05,699 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.10685723
2015-10-17 18:13:06,559 INFO [IPC Server handler 22 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:06,699 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.4592114
2015-10-17 18:13:06,762 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.43478465
2015-10-17 18:13:06,918 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.29116064
2015-10-17 18:13:07,199 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.3869094
2015-10-17 18:13:07,199 INFO [IPC Server handler 12 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.3872349
2015-10-17 18:13:07,574 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:07,590 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.3624012
2015-10-17 18:13:07,684 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.36404583
2015-10-17 18:13:08,387 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:13:08,637 INFO [IPC Server handler 24 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:08,778 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.10685723
2015-10-17 18:13:08,778 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_1 is : 0.020515375
2015-10-17 18:13:09,075 INFO [IPC Server handler 12 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.3637686
2015-10-17 18:13:09,700 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:09,903 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.46930668
2015-10-17 18:13:09,934 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.44968578
2015-10-17 18:13:09,950 INFO [IPC Server handler 12 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.3458736
2015-10-17 18:13:10,075 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.2960443
2015-10-17 18:13:10,559 INFO [IPC Server handler 2 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.3940746
2015-10-17 18:13:10,762 INFO [IPC Server handler 24 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:11,262 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.36404583
2015-10-17 18:13:11,340 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.38308895
2015-10-17 18:13:11,403 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:13:11,653 INFO [IPC Server handler 22 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.3921214
2015-10-17 18:13:11,809 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:11,809 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.10685723
2015-10-17 18:13:11,903 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_1 is : 0.023122154
2015-10-17 18:13:12,887 INFO [IPC Server handler 24 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:12,887 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.3637686
2015-10-17 18:13:13,012 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.4764737
2015-10-17 18:13:13,090 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.44968578
2015-10-17 18:13:13,090 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.34782952
2015-10-17 18:13:13,215 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.29832408
2015-10-17 18:13:13,747 INFO [IPC Server handler 2 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.40026423
2015-10-17 18:13:13,950 INFO [IPC Server handler 24 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:14,497 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:13:14,856 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.12767081
2015-10-17 18:13:14,965 INFO [IPC Server handler 24 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:15,231 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.4214875
2015-10-17 18:13:15,278 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.36404583
2015-10-17 18:13:16,044 INFO [IPC Server handler 24 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:16,216 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.3514106
2015-10-17 18:13:16,669 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.47940397
2015-10-17 18:13:16,762 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.3637686
2015-10-17 18:13:16,762 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.44968578
2015-10-17 18:13:16,825 INFO [IPC Server handler 2 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.42306128
2015-10-17 18:13:17,106 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:17,153 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_1 is : 0.028984562
2015-10-17 18:13:17,309 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.3051634
2015-10-17 18:13:17,512 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:13:17,903 INFO [IPC Server handler 22 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.19247705
2015-10-17 18:13:18,153 INFO [IPC Server handler 23 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:18,716 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.4035199
2015-10-17 18:13:18,934 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.44789755
2015-10-17 18:13:19,075 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.36404583
2015-10-17 18:13:19,200 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:19,466 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.3540166
2015-10-17 18:13:19,856 INFO [IPC Server handler 21 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.4852663
2015-10-17 18:13:20,263 INFO [IPC Server handler 23 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:20,309 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_1 is : 0.03419334
2015-10-17 18:13:20,325 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.3637686
2015-10-17 18:13:20,528 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:13:20,747 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.43315512
2015-10-17 18:13:20,919 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.19247705
2015-10-17 18:13:21,091 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.44968578
2015-10-17 18:13:21,294 INFO [IPC Server handler 23 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:21,810 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.40775338
2015-10-17 18:13:22,341 INFO [IPC Server handler 23 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:22,560 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.35662207
2015-10-17 18:13:22,872 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.44789755
2015-10-17 18:13:22,872 INFO [IPC Server handler 21 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.36404583
2015-10-17 18:13:22,919 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.49015197
2015-10-17 18:13:22,981 INFO [IPC Server handler 22 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.31102464
2015-10-17 18:13:23,403 INFO [IPC Server handler 23 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:23,403 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_1 is : 0.03451936
2015-10-17 18:13:23,653 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:13:23,872 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.43804196
2015-10-17 18:13:23,981 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.19247705
2015-10-17 18:13:24,044 INFO [IPC Server handler 2 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.3637686
2015-10-17 18:13:24,450 INFO [IPC Server handler 24 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:25,091 INFO [IPC Server handler 22 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.41328922
2015-10-17 18:13:25,528 INFO [IPC Server handler 24 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:25,607 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.36052966
2015-10-17 18:13:26,497 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.36404583
2015-10-17 18:13:26,513 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.44789755
2015-10-17 18:13:26,560 INFO [IPC Server handler 1 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:26,669 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:13:26,950 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.4419515
2015-10-17 18:13:26,997 INFO [IPC Server handler 21 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.2159881
2015-10-17 18:13:27,232 INFO [IPC Server handler 22 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.5005746
2015-10-17 18:13:27,575 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:27,575 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.49275765
2015-10-17 18:13:27,732 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.3637686
2015-10-17 18:13:28,310 INFO [IPC Server handler 2 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.420131
2015-10-17 18:13:28,310 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_1 is : 0.037450608
2015-10-17 18:13:28,638 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:28,732 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.36378807
2015-10-17 18:13:29,701 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:29,747 INFO [IPC Server handler 24 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:13:29,997 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.27678746
2015-10-17 18:13:30,060 INFO [IPC Server handler 21 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.44748777
2015-10-17 18:13:30,263 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.44789755
2015-10-17 18:13:30,263 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.36404583
2015-10-17 18:13:30,341 INFO [IPC Server handler 22 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.5067614
2015-10-17 18:13:30,685 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.49568895
2015-10-17 18:13:30,794 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:31,404 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_1 is : 0.040709116
2015-10-17 18:13:31,419 INFO [IPC Server handler 2 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.42403913
2015-10-17 18:13:31,638 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.3637686
2015-10-17 18:13:31,857 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.36671844
2015-10-17 18:13:31,857 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:32,779 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:13:32,935 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:33,029 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.27813601
2015-10-17 18:13:33,998 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.37648782
2015-10-17 18:13:33,998 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:33,998 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.448704
2015-10-17 18:13:34,091 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.50839096
2015-10-17 18:13:34,138 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.44789755
2015-10-17 18:13:34,154 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.5279306
2015-10-17 18:13:34,545 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.42631736
2015-10-17 18:13:34,545 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_1 is : 0.041686013
2015-10-17 18:13:35,045 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.36932397
2015-10-17 18:13:35,060 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:35,107 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.38007185
2015-10-17 18:13:35,857 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:13:36,107 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.27813601
2015-10-17 18:13:36,123 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:37,170 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:37,373 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.38919073
2015-10-17 18:13:37,670 INFO [IPC Server handler 16 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.44789755
2015-10-17 18:13:37,748 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.43055144
2015-10-17 18:13:37,810 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_1 is : 0.044942934
2015-10-17 18:13:38,217 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.37225455
2015-10-17 18:13:38,248 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:38,701 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.40612516
2015-10-17 18:13:38,701 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.52728045
2015-10-17 18:13:38,888 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.5342037
2015-10-17 18:13:38,951 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:13:39,248 INFO [IPC Server handler 14 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.2843816
2015-10-17 18:13:39,326 INFO [IPC Server handler 12 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:40,404 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:40,920 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.40547547
2015-10-17 18:13:40,982 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.43543625
2015-10-17 18:13:41,076 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_1 is : 0.046895586
2015-10-17 18:13:41,373 INFO [IPC Server handler 12 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.44789755
2015-10-17 18:13:41,420 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.37714016
2015-10-17 18:13:41,498 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:41,576 INFO [IPC Server handler 22 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.44952816
2015-10-17 18:13:42,107 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:13:42,389 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.5342037
2015-10-17 18:13:42,389 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.36390656
2015-10-17 18:13:42,561 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:42,639 INFO [IPC Server handler 2 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.43478587
2015-10-17 18:13:43,607 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:44,357 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.44064826
2015-10-17 18:13:44,357 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_1 is : 0.04949992
2015-10-17 18:13:44,482 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.5352028
2015-10-17 18:13:44,701 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.384631
2015-10-17 18:13:44,701 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:44,983 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.47451773
2015-10-17 18:13:45,061 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.43279728
2015-10-17 18:13:45,154 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:13:45,436 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.36390656
2015-10-17 18:13:45,592 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.44789755
2015-10-17 18:13:45,686 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.5342037
2015-10-17 18:13:45,764 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:46,670 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.44950172
2015-10-17 18:13:46,795 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:47,592 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.4471618
2015-10-17 18:13:47,623 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_1 is : 0.05275976
2015-10-17 18:13:47,826 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.38919047
2015-10-17 18:13:47,905 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:48,076 INFO [IPC Server handler 16 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.5352028
2015-10-17 18:13:48,201 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:13:48,436 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.36390656
2015-10-17 18:13:48,670 INFO [IPC Server handler 1 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.5122989
2015-10-17 18:13:48,733 INFO [Socket Reader #1 for port 52839] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0001 (auth:SIMPLE)
2015-10-17 18:13:48,905 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.33708143
2015-10-17 18:13:48,905 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.44980705
2015-10-17 18:13:48,905 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:49,108 INFO [IPC Server handler 2 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.5342037
2015-10-17 18:13:49,530 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.44789755
2015-10-17 18:13:49,936 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:50,498 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.44950172
2015-10-17 18:13:50,936 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.44964966
2015-10-17 18:13:50,967 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_1 is : 0.056991782
2015-10-17 18:13:50,967 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:51,108 INFO [IPC Server handler 29 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.3937501
2015-10-17 18:13:51,233 INFO [IPC Server handler 22 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:13:51,530 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.36390656
2015-10-17 18:13:51,608 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.5352028
2015-10-17 18:13:51,998 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:52,217 INFO [IPC Server handler 22 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.5342439
2015-10-17 18:13:52,498 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.5342037
2015-10-17 18:13:52,623 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.34554985
2015-10-17 18:13:52,780 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.44980705
2015-10-17 18:13:53,045 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:53,342 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.44789755
2015-10-17 18:13:54,108 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:54,186 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.44950172
2015-10-17 18:13:54,264 INFO [IPC Server handler 22 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:13:54,561 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_1 is : 0.06480786
2015-10-17 18:13:54,561 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.44964966
2015-10-17 18:13:54,561 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.39581355
2015-10-17 18:13:54,639 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.4015646
2015-10-17 18:13:55,030 INFO [IPC Server handler 25 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.5352028
2015-10-17 18:13:55,108 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:55,874 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.53425497
2015-10-17 18:13:56,061 INFO [IPC Server handler 23 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.5342037
2015-10-17 18:13:56,171 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:56,171 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.36313498
2015-10-17 18:13:56,514 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.44980705
2015-10-17 18:13:57,233 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:57,342 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:13:57,405 INFO [IPC Server handler 22 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.44789755
2015-10-17 18:13:57,592 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.44950968
2015-10-17 18:13:57,874 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_1 is : 0.07978989
2015-10-17 18:13:57,936 INFO [IPC Server handler 11 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.44950172
2015-10-17 18:13:57,936 INFO [IPC Server handler 11 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.44964966
2015-10-17 18:13:58,077 INFO [IPC Server handler 23 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.4175253
2015-10-17 18:13:58,327 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:58,514 INFO [IPC Server handler 16 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.5352028
2015-10-17 18:13:59,311 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.53425497
2015-10-17 18:13:59,405 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:13:59,530 INFO [IPC Server handler 15 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.5342037
2015-10-17 18:13:59,733 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.36390656
2015-10-17 18:14:00,186 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:14:00,186 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0001_01_000015 to attempt_1445076437777_0001_m_000003_1
2015-10-17 18:14:00,186 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:2 RackLocal:11
2015-10-17 18:14:00,186 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:14:00,186 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000003_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:14:00,186 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0001_01_000015 taskAttempt attempt_1445076437777_0001_m_000003_1
2015-10-17 18:14:00,186 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0001_m_000003_1
2015-10-17 18:14:00,186 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:14:00,421 INFO [IPC Server handler 10 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:14:00,436 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0001_m_000003_1 : 13562
2015-10-17 18:14:00,436 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0001_m_000003_1] using containerId: [container_1445076437777_0001_01_000015 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:14:00,436 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000003_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:14:00,436 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0001_m_000003
2015-10-17 18:14:00,468 INFO [IPC Server handler 0 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:14:00,468 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.44980705
2015-10-17 18:14:00,624 INFO [IPC Server handler 16 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.44950968
2015-10-17 18:14:00,843 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445076437777_0001_m_000000
2015-10-17 18:14:00,843 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 18:14:00,843 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445076437777_0001_m_000000
2015-10-17 18:14:00,843 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:14:00,843 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:14:00,843 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000000_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:14:01,280 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:2 RackLocal:11
2015-10-17 18:14:01,280 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:14:01,515 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:14:01,530 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.4570822
2015-10-17 18:14:01,702 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_1 is : 0.097710796
2015-10-17 18:14:01,827 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.5352028
2015-10-17 18:14:01,858 INFO [IPC Server handler 13 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.4676805
2015-10-17 18:14:01,983 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.4354572
2015-10-17 18:14:02,124 INFO [IPC Server handler 11 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.44950172
2015-10-17 18:14:02,358 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:14:02,358 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0001_01_000016 to attempt_1445076437777_0001_m_000000_1
2015-10-17 18:14:02,358 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:3 RackLocal:11
2015-10-17 18:14:02,358 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:14:02,358 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000000_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:14:02,358 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0001_01_000016 taskAttempt attempt_1445076437777_0001_m_000000_1
2015-10-17 18:14:02,358 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0001_m_000000_1
2015-10-17 18:14:02,358 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:14:02,593 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:14:02,671 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0001_m_000000_1 : 13562
2015-10-17 18:14:02,671 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0001_m_000000_1] using containerId: [container_1445076437777_0001_01_000016 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:14:02,671 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0001_m_000000_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:14:02,671 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0001_m_000000
2015-10-17 18:14:02,890 INFO [IPC Server handler 16 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.53425497
2015-10-17 18:14:02,968 INFO [IPC Server handler 28 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.54779893
2015-10-17 18:14:03,358 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.36390656
2015-10-17 18:14:03,421 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:14:03,437 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:14:03,640 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:14:03,749 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.44950968
2015-10-17 18:14:04,030 INFO [Socket Reader #1 for port 52839] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0001 (auth:SIMPLE)
2015-10-17 18:14:04,093 INFO [IPC Server handler 11 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0001_m_000015 asked for a task
2015-10-17 18:14:04,093 INFO [IPC Server handler 11 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0001_m_000015 given task: attempt_1445076437777_0001_m_000003_1
2015-10-17 18:14:04,124 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.44980705
2015-10-17 18:14:04,687 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:14:04,952 INFO [IPC Server handler 16 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.49105638
2015-10-17 18:14:05,030 INFO [IPC Server handler 11 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.54226154
2015-10-17 18:14:05,218 INFO [IPC Server handler 3 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_1 is : 0.106964506
2015-10-17 18:14:05,296 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.4781025
2015-10-17 18:14:05,562 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.44814026
2015-10-17 18:14:05,733 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:14:06,015 INFO [IPC Server handler 16 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.44950172
2015-10-17 18:14:06,046 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.53425497
2015-10-17 18:14:06,124 INFO [IPC Server handler 26 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.566036
2015-10-17 18:14:06,405 INFO [Socket Reader #1 for port 52839] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0001 (auth:SIMPLE)
2015-10-17 18:14:06,452 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0001_m_000016 asked for a task
2015-10-17 18:14:06,452 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0001_m_000016 given task: attempt_1445076437777_0001_m_000000_1
2015-10-17 18:14:06,484 INFO [IPC Server handler 27 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:14:06,796 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:14:06,796 INFO [IPC Server handler 1 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.44950968
2015-10-17 18:14:06,874 INFO [IPC Server handler 22 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.36390656
2015-10-17 18:14:07,843 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:14:07,937 INFO [IPC Server handler 16 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_0 is : 0.44980705
2015-10-17 18:14:08,218 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.55679584
2015-10-17 18:14:08,671 INFO [IPC Server handler 9 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000000_0 is : 0.52436155
2015-10-17 18:14:08,921 INFO [IPC Server handler 4 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:14:09,124 INFO [IPC Server handler 7 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000001_0 is : 0.53425497
2015-10-17 18:14:09,171 INFO [IPC Server handler 2 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000002_0 is : 0.5761323
2015-10-17 18:14:09,281 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000007_0 is : 0.499271
2015-10-17 18:14:09,640 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_r_000000_0 is : 0.033333335
2015-10-17 18:14:09,781 INFO [IPC Server handler 17 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000008_0 is : 0.44950172
2015-10-17 18:14:09,812 INFO [IPC Server handler 18 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_1 is : 0.4941754
2015-10-17 18:14:10,015 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:14:10,265 INFO [IPC Server handler 8 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000005_0 is : 0.36390656
2015-10-17 18:14:10,593 INFO [IPC Server handler 5 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000006_1 is : 0.106964506
2015-10-17 18:14:10,765 INFO [IPC Server handler 6 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000003_0 is : 0.44814026
2015-10-17 18:14:11,156 INFO [IPC Server handler 20 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0001_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:14:11,374 INFO [IPC Server handler 19 on 52839] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0001_m_000004_0 is : 0.56701446
