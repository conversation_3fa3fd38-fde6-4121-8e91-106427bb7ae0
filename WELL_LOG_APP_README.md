# 🛢️ Well Log Analyzer - Streamlit App

A comprehensive **offline** Streamlit web application for well log data visualization and petrophysical analysis, designed specifically for Oil & Gas domain experts.

## 🎯 Features

### ✅ Core Functionality
- **📁 CSV File Upload** - Easy drag-and-drop interface for well log data
- **🔍 Data Validation** - Automatic validation of required columns and data types
- **📊 Interactive Visualizations** - Four main plot types using Plotly
- **🌐 Fully Offline** - No external dependencies or internet connection required
- **📱 Responsive Design** - Works on desktop, tablet, and mobile devices

### 📈 Visualization Types

1. **🟢 Gamma Ray Track**
   - GR vs DEPTH_MD
   - Depth increases downward (y-axis reversed)
   - Interactive hover tooltips

2. **🔴 Deep Resistivity Track**
   - RDEP vs DEPTH_MD with logarithmic scale
   - Depth increases downward
   - Hover data inspection

3. **🔵 Density-Neutron Track**
   - RHOB and NPHI vs DEPTH_MD on dual y-axes
   - Combined visualization for porosity analysis
   - Color-coded curves

4. **📊 Density-Neutron Crossplot**
   - RHOB vs NPHI scatter plot
   - Color-coded by Gamma Ray values
   - Depth information in hover tooltips

## 🛠️ Technical Stack

- **Frontend/UI**: Streamlit
- **Plotting**: Plotly (interactive charts)
- **Data Processing**: pandas, numpy
- **Styling**: Custom CSS for Oil & Gas theme
- **Export**: Built-in PNG download capabilities

## 📋 Required Data Format

Your CSV file must contain these columns:

| Column | Description | Unit |
|--------|-------------|------|
| `DEPTH_MD` | Measured Depth | meters (m) |
| `GR` | Gamma Ray | API units |
| `RDEP` | Deep Resistivity | ohm.m |
| `RHOB` | Bulk Density | g/cc |
| `NPHI` | Neutron Porosity | v/v (volume/volume) |
| `CALI` | Caliper | inches |
| `DTC` | Delta Time Compressional | us/ft |
| `PEF` | Photoelectric Factor | - |

## 🚀 Quick Start

### 1. Installation

```bash
# Install required packages
pip install -r requirements.txt
```

### 2. Run the Application

```bash
# Start the Streamlit app
streamlit run well_log_app.py
```

### 3. Generate Sample Data (Optional)

```bash
# Create sample well log data for testing
python test_well_log_app.py
```

### 4. Upload and Analyze

1. Open your browser to `http://localhost:8501`
2. Upload your CSV file using the file uploader
3. Explore the interactive visualizations in different tabs
4. Download plots or processed data as needed

## 📊 App Interface

### Main Sections:

1. **📁 File Upload Area**
   - Drag-and-drop CSV upload
   - Data validation feedback
   - Sample format display

2. **📈 Visualization Tabs**
   - Gamma Ray Track
   - Resistivity Track  
   - Density-Neutron Track
   - Crossplot Analysis
   - Raw Data Table

3. **⚙️ Sidebar Controls**
   - Application settings
   - Feature descriptions
   - Help information

## 🎨 Styling & UX

- **Professional Oil & Gas Color Scheme**
- **Responsive Layout** - Adapts to different screen sizes
- **Interactive Tooltips** - Hover for detailed data points
- **Clean Typography** - Easy to read for technical professionals
- **Intuitive Navigation** - Tab-based organization

## 📁 File Structure

```
├── well_log_app.py           # Main Streamlit application
├── test_well_log_app.py      # Sample data generator
├── requirements.txt          # Python dependencies
├── WELL_LOG_APP_README.md    # This documentation
└── sample_well_log_*.csv     # Generated sample data files
```

## 🔧 Customization Options

### Adding New Log Curves
To add additional log curves, modify the `REQUIRED_COLUMNS` list and create new plotting functions following the existing pattern.

### Styling Changes
Update the CSS in the `st.markdown()` section at the top of `well_log_app.py`.

### Export Options
The app supports CSV download. PNG export is available through Plotly's built-in toolbar.

## 🚨 Troubleshooting

### Common Issues:

1. **Missing Columns Error**
   - Ensure your CSV has all required columns
   - Check column names match exactly (case-sensitive)

2. **Data Type Errors**
   - Verify numeric columns contain only numbers
   - Remove any text headers or footers from CSV

3. **Performance Issues**
   - Large datasets (>10,000 points) may load slowly
   - Consider data sampling for very large files

## 🔮 Future Enhancements

Potential additions mentioned in requirements:
- ✅ **Archie's Water Saturation** calculation (Pickett plot)
- ✅ **Lithology Facies** classification overlay  
- ✅ **PDF Report Generation** with all plots
- ✅ **Multi-well Comparison** capabilities
- ✅ **Advanced Petrophysical Calculations**

## 📞 Support

For technical support or feature requests, refer to the main project documentation or contact the development team.

---

**Built for ONGC Project1** - Offline AI-Powered Technical Solutions
