2015-10-17 15:53:26,069 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445062781478_0013_000002
2015-10-17 15:53:26,647 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 15:53:26,647 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 13 cluster_timestamp: 1445062781478 } attemptId: 2 } keyId: 471522253)
2015-10-17 15:53:26,929 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 15:53:27,929 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 15:53:28,007 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 15:53:28,038 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 15:53:28,038 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 15:53:28,054 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 15:53:28,054 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 15:53:28,054 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 15:53:28,054 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 15:53:28,054 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 15:53:28,054 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 15:53:28,116 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 15:53:28,147 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 15:53:28,163 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 15:53:28,179 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 15:53:28,179 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Recovery is enabled. Will try to recover from previous life on best effort basis.
2015-10-17 15:53:28,210 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 15:53:28,210 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Previous history file is at hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0013/job_1445062781478_0013_1.jhist
2015-10-17 15:53:28,241 WARN [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Unable to parse prior job history, aborting recovery
java.io.IOException: Incompatible event log version: null
	at org.apache.hadoop.mapreduce.jobhistory.EventReader.<init>(EventReader.java:71)
	at org.apache.hadoop.mapreduce.jobhistory.JobHistoryParser.parse(JobHistoryParser.java:139)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.parsePreviousJobHistory(MRAppMaster.java:1183)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.processRecovery(MRAppMaster.java:1152)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.serviceStart(MRAppMaster.java:1016)
	at org.apache.hadoop.service.AbstractService.start(AbstractService.java:193)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster$4.run(MRAppMaster.java:1500)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.initAndStartAppMaster(MRAppMaster.java:1496)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.main(MRAppMaster.java:1429)
2015-10-17 15:53:28,257 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 15:53:28,272 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Previous history file is at hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0013/job_1445062781478_0013_1.jhist
2015-10-17 15:53:28,272 WARN [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Could not parse the old history file. Will not have old AMinfos 
java.io.IOException: Incompatible event log version: null
	at org.apache.hadoop.mapreduce.jobhistory.EventReader.<init>(EventReader.java:71)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.readJustAMInfos(MRAppMaster.java:1229)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.processRecovery(MRAppMaster.java:1156)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.serviceStart(MRAppMaster.java:1016)
	at org.apache.hadoop.service.AbstractService.start(AbstractService.java:193)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster$4.run(MRAppMaster.java:1500)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.initAndStartAppMaster(MRAppMaster.java:1496)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.main(MRAppMaster.java:1429)
2015-10-17 15:53:28,335 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 15:53:28,616 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 15:53:28,725 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 15:53:28,725 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 15:53:28,741 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445062781478_0013 to jobTokenSecretManager
2015-10-17 15:53:28,913 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445062781478_0013 because: not enabled; too many maps; too much input;
2015-10-17 15:53:28,929 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445062781478_0013 = 1256521728. Number of splits = 10
2015-10-17 15:53:28,929 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445062781478_0013 = 1
2015-10-17 15:53:28,929 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0013Job Transitioned from NEW to INITED
2015-10-17 15:53:28,944 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445062781478_0013.
2015-10-17 15:53:28,991 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 15:53:29,007 INFO [Socket Reader #1 for port 52871] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 52871
2015-10-17 15:53:29,038 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 15:53:29,038 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 15:53:29,038 INFO [IPC Server listener on 52871] org.apache.hadoop.ipc.Server: IPC Server listener on 52871: starting
2015-10-17 15:53:29,038 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MININT-75DGDAM1.fareast.corp.microsoft.com/************:52871
2015-10-17 15:53:29,116 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 15:53:29,116 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 15:53:29,132 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 15:53:29,132 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 15:53:29,132 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 15:53:29,147 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 15:53:29,147 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 15:53:29,163 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 52878
2015-10-17 15:53:29,163 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 15:53:29,210 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\Jetty_0_0_0_0_52878_mapreduce____.q8s55v\webapp
2015-10-17 15:53:29,413 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:52878
2015-10-17 15:53:29,413 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 52878
2015-10-17 15:53:29,757 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 15:53:29,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445062781478_0013
2015-10-17 15:53:29,757 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 15:53:29,772 INFO [Socket Reader #1 for port 52881] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 52881
2015-10-17 15:53:29,772 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 15:53:29,772 INFO [IPC Server listener on 52881] org.apache.hadoop.ipc.Server: IPC Server listener on 52881: starting
2015-10-17 15:53:29,804 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 15:53:29,804 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 15:53:29,804 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 15:53:29,851 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-17 15:53:29,960 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 15:53:29,960 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 15:53:29,960 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 15:53:29,960 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 15:53:29,976 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0013Job Transitioned from INITED to SETUP
2015-10-17 15:53:29,976 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 15:53:29,991 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0013Job Transitioned from SETUP to RUNNING
2015-10-17 15:53:30,038 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000000_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000001_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000002_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000003_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000004_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000005_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000006_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000007_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000008_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:53:30,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000009_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:53:30,069 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_r_000000_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:53:30,069 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 15:53:30,085 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 15:53:30,116 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445062781478_0013, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0013/job_1445062781478_0013_2.jhist
2015-10-17 15:53:30,960 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 15:53:31,022 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0013: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:28672, vCores:-1> knownNMs=4
2015-10-17 15:53:31,022 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:28672, vCores:-1>
2015-10-17 15:53:31,022 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:53:32,054 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 10
2015-10-17 15:53:32,054 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_02_000002 to attempt_1445062781478_0013_m_000000_1000
2015-10-17 15:53:32,069 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_02_000003 to attempt_1445062781478_0013_m_000001_1000
2015-10-17 15:53:32,069 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_02_000004 to attempt_1445062781478_0013_m_000002_1000
2015-10-17 15:53:32,069 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_02_000005 to attempt_1445062781478_0013_m_000003_1000
2015-10-17 15:53:32,069 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_02_000006 to attempt_1445062781478_0013_m_000004_1000
2015-10-17 15:53:32,069 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_02_000007 to attempt_1445062781478_0013_m_000005_1000
2015-10-17 15:53:32,069 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_02_000008 to attempt_1445062781478_0013_m_000006_1000
2015-10-17 15:53:32,069 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_02_000009 to attempt_1445062781478_0013_m_000007_1000
2015-10-17 15:53:32,069 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_02_000010 to attempt_1445062781478_0013_m_000008_1000
2015-10-17 15:53:32,069 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_02_000011 to attempt_1445062781478_0013_m_000009_1000
2015-10-17 15:53:32,069 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:18432, vCores:-11>
2015-10-17 15:53:32,069 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:53:32,069 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 15:53:32,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:32,148 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0013/job.jar
2015-10-17 15:53:32,148 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0013/job.xml
2015-10-17 15:53:32,148 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 15:53:32,148 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 15:53:32,148 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 15:53:32,210 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000000_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:53:32,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:32,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000001_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:53:32,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:32,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000002_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:53:32,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:32,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000003_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:53:32,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:32,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000004_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:53:32,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:32,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000005_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:53:32,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:32,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000006_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:53:32,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:32,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000007_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:53:32,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:32,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000008_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:53:32,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:53:32,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000009_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:53:32,226 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_02_000002 taskAttempt attempt_1445062781478_0013_m_000000_1000
2015-10-17 15:53:32,226 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_02_000003 taskAttempt attempt_1445062781478_0013_m_000001_1000
2015-10-17 15:53:32,226 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_02_000004 taskAttempt attempt_1445062781478_0013_m_000002_1000
2015-10-17 15:53:32,226 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_02_000006 taskAttempt attempt_1445062781478_0013_m_000004_1000
2015-10-17 15:53:32,226 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_02_000007 taskAttempt attempt_1445062781478_0013_m_000005_1000
2015-10-17 15:53:32,226 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_02_000005 taskAttempt attempt_1445062781478_0013_m_000003_1000
2015-10-17 15:53:32,226 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_02_000009 taskAttempt attempt_1445062781478_0013_m_000007_1000
2015-10-17 15:53:32,226 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_02_000011 taskAttempt attempt_1445062781478_0013_m_000009_1000
2015-10-17 15:53:32,226 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_02_000010 taskAttempt attempt_1445062781478_0013_m_000008_1000
2015-10-17 15:53:32,226 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_02_000008 taskAttempt attempt_1445062781478_0013_m_000006_1000
2015-10-17 15:53:32,241 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000005_1000
2015-10-17 15:53:32,241 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000009_1000
2015-10-17 15:53:32,241 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000002_1000
2015-10-17 15:53:32,241 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000007_1000
2015-10-17 15:53:32,241 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000006_1000
2015-10-17 15:53:32,241 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000003_1000
2015-10-17 15:53:32,241 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000008_1000
2015-10-17 15:53:32,241 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000001_1000
2015-10-17 15:53:32,241 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000004_1000
2015-10-17 15:53:32,241 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000000_1000
2015-10-17 15:53:32,241 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:53:32,257 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:53:32,257 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:53:32,257 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:53:32,272 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:53:32,272 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:53:32,272 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:53:32,272 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:53:32,272 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:53:32,272 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:53:32,351 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000000_1000 : 13562
2015-10-17 15:53:32,351 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000005_1000 : 13562
2015-10-17 15:53:32,351 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000009_1000 : 13562
2015-10-17 15:53:32,351 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000008_1000 : 13562
2015-10-17 15:53:32,351 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000001_1000 : 13562
2015-10-17 15:53:32,351 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000000_1000] using containerId: [container_1445062781478_0013_02_000002 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:53:32,351 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000003_1000 : 13562
2015-10-17 15:53:32,351 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000006_1000 : 13562
2015-10-17 15:53:32,351 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000004_1000 : 13562
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000000_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000009_1000] using containerId: [container_1445062781478_0013_02_000011 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:53:32,366 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000007_1000 : 13562
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000009_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000008_1000] using containerId: [container_1445062781478_0013_02_000010 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000008_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000005_1000] using containerId: [container_1445062781478_0013_02_000007 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000005_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000001_1000] using containerId: [container_1445062781478_0013_02_000003 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000001_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:53:32,366 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000002_1000 : 13562
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000003_1000] using containerId: [container_1445062781478_0013_02_000005 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000003_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000006_1000] using containerId: [container_1445062781478_0013_02_000008 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000006_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000004_1000] using containerId: [container_1445062781478_0013_02_000006 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000004_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000000
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000007_1000] using containerId: [container_1445062781478_0013_02_000009 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000007_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000009
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000008
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000005
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000001
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000002_1000] using containerId: [container_1445062781478_0013_02_000004 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000002_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000003
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000006
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000004
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000007
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000002
2015-10-17 15:53:32,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:53:33,069 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0013: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:18432, vCores:-11> knownNMs=4
2015-10-17 15:53:35,413 INFO [Socket Reader #1 for port 52881] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:53:35,444 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000010 asked for a task
2015-10-17 15:53:35,444 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000010 given task: attempt_1445062781478_0013_m_000008_1000
2015-10-17 15:53:35,648 INFO [Socket Reader #1 for port 52881] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:53:35,663 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000011 asked for a task
2015-10-17 15:53:35,663 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000011 given task: attempt_1445062781478_0013_m_000009_1000
2015-10-17 15:53:36,632 INFO [Socket Reader #1 for port 52881] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:53:36,648 INFO [Socket Reader #1 for port 52881] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:53:36,648 INFO [Socket Reader #1 for port 52881] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:53:36,679 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000009 asked for a task
2015-10-17 15:53:36,679 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000009 given task: attempt_1445062781478_0013_m_000007_1000
2015-10-17 15:53:36,679 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000006 asked for a task
2015-10-17 15:53:36,679 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000006 given task: attempt_1445062781478_0013_m_000004_1000
2015-10-17 15:53:36,679 INFO [Socket Reader #1 for port 52881] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:53:36,695 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000007 asked for a task
2015-10-17 15:53:36,695 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000007 given task: attempt_1445062781478_0013_m_000005_1000
2015-10-17 15:53:36,710 INFO [Socket Reader #1 for port 52881] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:53:36,710 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000005 asked for a task
2015-10-17 15:53:36,710 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000005 given task: attempt_1445062781478_0013_m_000003_1000
2015-10-17 15:53:36,726 INFO [Socket Reader #1 for port 52881] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:53:36,726 INFO [Socket Reader #1 for port 52881] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:53:36,741 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000002 asked for a task
2015-10-17 15:53:36,741 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000002 given task: attempt_1445062781478_0013_m_000000_1000
2015-10-17 15:53:36,741 INFO [Socket Reader #1 for port 52881] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:53:36,741 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000008 asked for a task
2015-10-17 15:53:36,741 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000008 given task: attempt_1445062781478_0013_m_000006_1000
2015-10-17 15:53:36,757 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000003 asked for a task
2015-10-17 15:53:36,757 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000003 given task: attempt_1445062781478_0013_m_000001_1000
2015-10-17 15:53:36,757 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000004 asked for a task
2015-10-17 15:53:36,773 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000004 given task: attempt_1445062781478_0013_m_000002_1000
2015-10-17 15:53:42,523 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.092950314
2015-10-17 15:53:42,710 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 0.28971758
2015-10-17 15:53:44,117 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.10685723
2015-10-17 15:53:44,164 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.106493875
2015-10-17 15:53:44,179 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.10680563
2015-10-17 15:53:44,179 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.10681946
2015-10-17 15:53:44,226 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.106964506
2015-10-17 15:53:44,226 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.10635664
2015-10-17 15:53:44,226 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.10660437
2015-10-17 15:53:44,304 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.1066108
2015-10-17 15:53:45,585 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.106881365
2015-10-17 15:53:45,757 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 0.295472
2015-10-17 15:53:47,132 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.10685723
2015-10-17 15:53:47,179 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.106493875
2015-10-17 15:53:47,210 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.10681946
2015-10-17 15:53:47,210 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.10680563
2015-10-17 15:53:47,242 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.106964506
2015-10-17 15:53:47,257 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.10635664
2015-10-17 15:53:47,257 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.10660437
2015-10-17 15:53:47,320 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.1066108
2015-10-17 15:53:48,601 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.106881365
2015-10-17 15:53:48,789 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 0.295472
2015-10-17 15:53:50,164 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.10685723
2015-10-17 15:53:50,195 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.106493875
2015-10-17 15:53:50,226 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.10681946
2015-10-17 15:53:50,226 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.10680563
2015-10-17 15:53:50,273 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.10635664
2015-10-17 15:53:50,273 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.106964506
2015-10-17 15:53:50,273 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.10660437
2015-10-17 15:53:50,336 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.1066108
2015-10-17 15:53:51,632 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.106881365
2015-10-17 15:53:51,820 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 0.31403428
2015-10-17 15:53:53,164 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.10685723
2015-10-17 15:53:53,211 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.106493875
2015-10-17 15:53:53,242 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.10680563
2015-10-17 15:53:53,257 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.10681946
2015-10-17 15:53:53,289 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.10660437
2015-10-17 15:53:53,289 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.10635664
2015-10-17 15:53:53,289 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.106964506
2015-10-17 15:53:53,351 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.1066108
2015-10-17 15:53:54,679 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.12562405
2015-10-17 15:53:54,851 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 0.36955076
2015-10-17 15:53:56,195 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.13379757
2015-10-17 15:53:56,226 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.11664579
2015-10-17 15:53:56,273 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.1482162
2015-10-17 15:53:56,273 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.1583242
2015-10-17 15:53:56,304 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.13475002
2015-10-17 15:53:56,304 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.12671193
2015-10-17 15:53:56,304 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.13127935
2015-10-17 15:53:56,367 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.15241612
2015-10-17 15:53:57,711 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.14433534
2015-10-17 15:53:57,898 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 0.420893
2015-10-17 15:53:59,211 INFO [IPC Server handler 3 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.19247705
2015-10-17 15:53:59,226 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.18452089
2015-10-17 15:53:59,289 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.19242907
2015-10-17 15:53:59,289 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.19255035
2015-10-17 15:53:59,305 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.19212553
2015-10-17 15:53:59,320 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.19049086
2015-10-17 15:53:59,320 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.19158794
2015-10-17 15:53:59,367 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.19211523
2015-10-17 15:54:00,758 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.16634543
2015-10-17 15:54:00,930 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 0.49424258
2015-10-17 15:54:02,211 INFO [IPC Server handler 8 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.19247705
2015-10-17 15:54:02,226 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.19209063
2015-10-17 15:54:02,289 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.19255035
2015-10-17 15:54:02,305 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.19242907
2015-10-17 15:54:02,336 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.19212553
2015-10-17 15:54:02,336 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.19266446
2015-10-17 15:54:02,336 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.19158794
2015-10-17 15:54:02,383 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.19211523
2015-10-17 15:54:03,789 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.18531701
2015-10-17 15:54:03,977 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 0.5323719
2015-10-17 15:54:05,242 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.19247705
2015-10-17 15:54:05,242 INFO [IPC Server handler 29 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.19209063
2015-10-17 15:54:05,320 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.19242907
2015-10-17 15:54:05,320 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.19255035
2015-10-17 15:54:05,352 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.19212553
2015-10-17 15:54:05,352 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.19158794
2015-10-17 15:54:05,352 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.19266446
2015-10-17 15:54:05,399 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.19211523
2015-10-17 15:54:06,820 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.19258286
2015-10-17 15:54:06,992 INFO [IPC Server handler 29 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 0.5323719
2015-10-17 15:54:08,258 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.19247705
2015-10-17 15:54:08,258 INFO [IPC Server handler 3 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.19209063
2015-10-17 15:54:08,336 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.22385606
2015-10-17 15:54:08,336 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.23592299
2015-10-17 15:54:08,367 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.19158794
2015-10-17 15:54:08,367 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.19212553
2015-10-17 15:54:08,367 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.19266446
2015-10-17 15:54:08,414 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.22387302
2015-10-17 15:54:09,852 INFO [IPC Server handler 29 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.19258286
2015-10-17 15:54:10,024 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 0.5323719
2015-10-17 15:54:11,274 INFO [IPC Server handler 3 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.19209063
2015-10-17 15:54:11,274 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.24297826
2015-10-17 15:54:11,352 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.2781602
2015-10-17 15:54:11,352 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.27825075
2015-10-17 15:54:11,383 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.2554959
2015-10-17 15:54:11,383 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.24488291
2015-10-17 15:54:11,383 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.24712968
2015-10-17 15:54:11,430 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.27776006
2015-10-17 15:54:12,883 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.19258286
2015-10-17 15:54:13,055 INFO [IPC Server handler 3 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 0.5836644
2015-10-17 15:54:14,274 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.19209063
2015-10-17 15:54:14,289 INFO [IPC Server handler 9 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.27813601
2015-10-17 15:54:14,368 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.27825075
2015-10-17 15:54:14,368 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.2781602
2015-10-17 15:54:14,399 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.27772525
2015-10-17 15:54:14,399 INFO [IPC Server handler 2 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.27696857
2015-10-17 15:54:14,399 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.2783809
2015-10-17 15:54:14,446 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.27776006
2015-10-17 15:54:15,930 INFO [IPC Server handler 3 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.20931192
2015-10-17 15:54:16,102 INFO [IPC Server handler 9 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 0.6410125
2015-10-17 15:54:17,305 INFO [IPC Server handler 8 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.27813601
2015-10-17 15:54:17,383 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.2781602
2015-10-17 15:54:17,383 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.27825075
2015-10-17 15:54:17,414 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.27772525
2015-10-17 15:54:17,414 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.2783809
2015-10-17 15:54:17,414 INFO [IPC Server handler 16 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.27696857
2015-10-17 15:54:17,461 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.27776006
2015-10-17 15:54:17,602 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 0.6410125
2015-10-17 15:54:18,961 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.2282237
2015-10-17 15:54:19,133 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 0.667
2015-10-17 15:54:20,305 INFO [IPC Server handler 8 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.27765483
2015-10-17 15:54:20,321 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.27813601
2015-10-17 15:54:20,399 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.29499036
2015-10-17 15:54:20,399 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.3122608
2015-10-17 15:54:20,430 INFO [IPC Server handler 16 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.27772525
2015-10-17 15:54:20,430 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.27696857
2015-10-17 15:54:20,430 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.2783809
2015-10-17 15:54:20,477 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.29309493
2015-10-17 15:54:22,008 INFO [IPC Server handler 9 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.24841674
2015-10-17 15:54:22,165 INFO [IPC Server handler 8 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 0.667
2015-10-17 15:54:23,321 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.27765483
2015-10-17 15:54:23,337 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.27813601
2015-10-17 15:54:23,415 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.36242077
2015-10-17 15:54:23,415 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.36388028
2015-10-17 15:54:23,446 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.29550478
2015-10-17 15:54:23,446 INFO [IPC Server handler 16 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.2802434
2015-10-17 15:54:23,446 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.28433952
2015-10-17 15:54:23,493 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.36208108
2015-10-17 15:54:25,055 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.26806062
2015-10-17 15:54:25,196 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 0.667
2015-10-17 15:54:26,337 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.27765483
2015-10-17 15:54:26,352 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.27813601
2015-10-17 15:54:26,430 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.36388028
2015-10-17 15:54:26,430 INFO [IPC Server handler 16 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.3638923
2015-10-17 15:54:26,462 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.34839422
2015-10-17 15:54:26,462 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.36035278
2015-10-17 15:54:26,462 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.35251015
2015-10-17 15:54:26,509 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.36319977
2015-10-17 15:54:28,087 INFO [IPC Server handler 8 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.27811313
2015-10-17 15:54:28,227 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 0.7357755
2015-10-17 15:54:29,337 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.2998284
2015-10-17 15:54:29,430 INFO [IPC Server handler 16 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.3638923
2015-10-17 15:54:29,430 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.36388028
2015-10-17 15:54:29,462 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.3624012
2015-10-17 15:54:29,477 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.36404583
2015-10-17 15:54:29,477 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.36317363
2015-10-17 15:54:29,524 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.36319977
2015-10-17 15:54:31,384 INFO [IPC Server handler 16 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.27811313
2015-10-17 15:54:31,384 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 0.7838756
2015-10-17 15:54:32,618 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.38790795
2015-10-17 15:54:32,618 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.37480605
2015-10-17 15:54:32,634 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.36317363
2015-10-17 15:54:32,634 INFO [IPC Server handler 2 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.36404583
2015-10-17 15:54:32,634 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.37173992
2015-10-17 15:54:32,634 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.3624012
2015-10-17 15:54:32,899 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.36323506
2015-10-17 15:54:34,399 INFO [IPC Server handler 16 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.27811313
2015-10-17 15:54:34,399 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 0.8410912
2015-10-17 15:54:35,634 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.44964966
2015-10-17 15:54:35,634 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.44968578
2015-10-17 15:54:35,649 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.448704
2015-10-17 15:54:35,649 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.36509693
2015-10-17 15:54:35,649 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.37193277
2015-10-17 15:54:35,649 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.3708171
2015-10-17 15:54:35,915 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.36390656
2015-10-17 15:54:35,915 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.36323506
2015-10-17 15:54:37,431 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 0.91525644
2015-10-17 15:54:37,431 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.28945553
2015-10-17 15:54:38,649 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.44968578
2015-10-17 15:54:38,649 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.44964966
2015-10-17 15:54:38,665 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.448704
2015-10-17 15:54:38,665 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.4413361
2015-10-17 15:54:38,665 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.4422576
2015-10-17 15:54:38,665 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.4362066
2015-10-17 15:54:38,931 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.36323506
2015-10-17 15:54:38,931 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.36390656
2015-10-17 15:54:40,462 INFO [IPC Server handler 16 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.31022295
2015-10-17 15:54:40,478 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 0.9789254
2015-10-17 15:54:41,587 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_1000 is : 1.0
2015-10-17 15:54:41,587 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0013_m_000009_1000
2015-10-17 15:54:41,587 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000009_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:54:41,587 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0013_02_000011 taskAttempt attempt_1445062781478_0013_m_000009_1000
2015-10-17 15:54:41,587 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0013_m_000009_1000
2015-10-17 15:54:41,603 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:54:41,634 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000009_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:54:41,650 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0013_m_000009_1000
2015-10-17 15:54:41,650 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:54:41,650 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-17 15:54:41,665 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.44968578
2015-10-17 15:54:41,665 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.44964966
2015-10-17 15:54:41,681 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.44980705
2015-10-17 15:54:41,681 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.44789755
2015-10-17 15:54:41,681 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.44859612
2015-10-17 15:54:41,681 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.448704
2015-10-17 15:54:41,884 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0013_m_000008
2015-10-17 15:54:41,884 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 15:54:41,884 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0013_m_000008
2015-10-17 15:54:41,884 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:54:41,884 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:54:41,884 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000008_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:54:41,931 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.39730358
2015-10-17 15:54:41,946 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.36390656
2015-10-17 15:54:42,634 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 15:54:42,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0013: ask=4 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:19456, vCores:-10> knownNMs=4
2015-10-17 15:54:42,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0013_02_000011
2015-10-17 15:54:42,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:19456, vCores:-10>
2015-10-17 15:54:42,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-17 15:54:42,650 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0013_m_000009_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:54:42,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:28672, vCores:-1> finalMapResourceLimit:<memory:10240, vCores:10> finalReduceResourceLimit:<memory:18432, vCores:-11> netScheduledMapResource:<memory:10240, vCores:10> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-17 15:54:42,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping up 1
2015-10-17 15:54:42,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 15:54:43,493 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.32982275
2015-10-17 15:54:43,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0013: ask=1 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:18432, vCores:-11> knownNMs=4
2015-10-17 15:54:43,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:54:43,650 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:54:43,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_02_000012 to attempt_1445062781478_0013_m_000008_1001
2015-10-17 15:54:43,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:10 RackLocal:1
2015-10-17 15:54:43,650 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:54:43,665 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000008_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:54:43,665 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_02_000012 taskAttempt attempt_1445062781478_0013_m_000008_1001
2015-10-17 15:54:43,665 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000008_1001
2015-10-17 15:54:43,665 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 15:54:43,728 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000008_1001 : 13562
2015-10-17 15:54:43,728 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000008_1001] using containerId: [container_1445062781478_0013_02_000012 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:51951]
2015-10-17 15:54:43,743 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000008_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:54:43,743 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000008
2015-10-17 15:54:44,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0013: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:17408, vCores:-12> knownNMs=4
2015-10-17 15:54:44,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:54:44,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 15:54:44,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_02_000013 to attempt_1445062781478_0013_r_000000_1000
2015-10-17 15:54:44,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-17 15:54:44,665 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:54:44,665 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_r_000000_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:54:44,665 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_02_000013 taskAttempt attempt_1445062781478_0013_r_000000_1000
2015-10-17 15:54:44,665 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_r_000000_1000
2015-10-17 15:54:44,665 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 15:54:44,681 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.45034802
2015-10-17 15:54:44,681 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.46717677
2015-10-17 15:54:44,697 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_r_000000_1000 : 13562
2015-10-17 15:54:44,697 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_r_000000_1000] using containerId: [container_1445062781478_0013_02_000013 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:51951]
2015-10-17 15:54:44,697 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.44980705
2015-10-17 15:54:44,697 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_r_000000_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:54:44,697 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_r_000000
2015-10-17 15:54:44,697 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.44789755
2015-10-17 15:54:44,697 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:54:44,697 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.44859612
2015-10-17 15:54:44,697 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.45450947
2015-10-17 15:54:44,962 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.38211644
2015-10-17 15:54:44,962 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.4486067
2015-10-17 15:54:45,665 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0013: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:17408, vCores:-12> knownNMs=4
2015-10-17 15:54:46,384 INFO [Socket Reader #1 for port 52881] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:54:46,400 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000012 asked for a task
2015-10-17 15:54:46,400 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000012 given task: attempt_1445062781478_0013_m_000008_1001
2015-10-17 15:54:46,540 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.34716597
2015-10-17 15:54:47,697 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.51798344
2015-10-17 15:54:47,697 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.48865053
2015-10-17 15:54:47,728 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.44789755
2015-10-17 15:54:47,728 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.44859612
2015-10-17 15:54:47,728 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.49313363
2015-10-17 15:54:47,743 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.44980705
2015-10-17 15:54:47,978 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.4486067
2015-10-17 15:54:47,978 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.44134519
2015-10-17 15:54:48,494 INFO [Socket Reader #1 for port 52881] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:54:48,509 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_r_000013 asked for a task
2015-10-17 15:54:48,509 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_r_000013 given task: attempt_1445062781478_0013_r_000000_1000
2015-10-17 15:54:49,572 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.36079413
2015-10-17 15:54:50,712 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.5352028
2015-10-17 15:54:50,728 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.5352825
2015-10-17 15:54:50,744 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.44789755
2015-10-17 15:54:50,744 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.44859612
2015-10-17 15:54:50,744 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.4764576
2015-10-17 15:54:50,759 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.53425497
2015-10-17 15:54:50,775 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 0 maxEvents 10000
2015-10-17 15:54:50,994 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.4486067
2015-10-17 15:54:50,994 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.44950968
2015-10-17 15:54:51,790 INFO [IPC Server handler 2 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:54:52,650 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.3637686
2015-10-17 15:54:52,791 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:54:53,759 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.5352028
2015-10-17 15:54:53,775 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.5352825
2015-10-17 15:54:53,791 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:54:53,791 INFO [IPC Server handler 2 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.44789755
2015-10-17 15:54:53,791 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.44859612
2015-10-17 15:54:53,806 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.53543663
2015-10-17 15:54:53,806 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.53425497
2015-10-17 15:54:54,072 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.4486067
2015-10-17 15:54:54,072 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.44950968
2015-10-17 15:54:54,791 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:54:54,947 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.08003151
2015-10-17 15:54:55,712 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.3637686
2015-10-17 15:54:55,791 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:54:56,681 INFO [IPC Server handler 16 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.0
2015-10-17 15:54:56,791 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:54:56,791 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.5352028
2015-10-17 15:54:56,806 INFO [IPC Server handler 2 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.5352825
2015-10-17 15:54:56,822 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.53543663
2015-10-17 15:54:56,822 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.53425497
2015-10-17 15:54:56,900 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0013_m_000003
2015-10-17 15:54:56,900 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 15:54:56,900 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0013_m_000003
2015-10-17 15:54:56,900 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:54:56,900 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:54:56,900 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000003_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:54:56,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-17 15:54:56,978 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0013: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:17408, vCores:-12> knownNMs=4
2015-10-17 15:54:57,103 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.5013991
2015-10-17 15:54:57,119 INFO [IPC Server handler 29 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.44950968
2015-10-17 15:54:57,791 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:54:57,978 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.106881365
2015-10-17 15:54:57,994 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:54:57,994 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:54:57,994 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_02_000014 to attempt_1445062781478_0013_m_000003_1001
2015-10-17 15:54:57,994 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 15:54:57,994 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:54:57,994 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000003_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:54:57,994 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_02_000014 taskAttempt attempt_1445062781478_0013_m_000003_1001
2015-10-17 15:54:57,994 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000003_1001
2015-10-17 15:54:57,994 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 15:54:58,025 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000003_1001 : 13562
2015-10-17 15:54:58,025 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000003_1001] using containerId: [container_1445062781478_0013_02_000014 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:51951]
2015-10-17 15:54:58,025 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000003_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:54:58,025 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000003
2015-10-17 15:54:58,775 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.36639652
2015-10-17 15:54:58,806 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:54:59,009 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0013: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-13> knownNMs=4
2015-10-17 15:54:59,822 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.57767975
2015-10-17 15:54:59,822 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:54:59,900 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.53162974
2015-10-17 15:54:59,900 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.5362036
2015-10-17 15:54:59,900 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.53020203
2015-10-17 15:54:59,916 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.53425497
2015-10-17 15:54:59,916 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.53543663
2015-10-17 15:54:59,916 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.0
2015-10-17 15:55:00,134 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.5343203
2015-10-17 15:55:00,150 INFO [IPC Server handler 3 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.44950968
2015-10-17 15:55:00,822 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:00,947 INFO [Socket Reader #1 for port 52881] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:55:00,963 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000014 asked for a task
2015-10-17 15:55:00,963 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000014 given task: attempt_1445062781478_0013_m_000003_1001
2015-10-17 15:55:01,010 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.106881365
2015-10-17 15:55:01,806 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.3864765
2015-10-17 15:55:01,838 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:02,822 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.6208445
2015-10-17 15:55:02,838 INFO [IPC Server handler 2 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:02,916 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.6103248
2015-10-17 15:55:02,916 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.53341997
2015-10-17 15:55:02,931 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.53543663
2015-10-17 15:55:02,931 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.5342037
2015-10-17 15:55:02,931 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.53425497
2015-10-17 15:55:03,041 INFO [IPC Server handler 29 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:55:03,135 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.5343203
2015-10-17 15:55:03,166 INFO [IPC Server handler 3 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.44950968
2015-10-17 15:55:03,869 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:04,041 INFO [IPC Server handler 29 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.106881365
2015-10-17 15:55:04,853 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.40563926
2015-10-17 15:55:04,869 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:05,838 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.6208445
2015-10-17 15:55:05,869 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:05,932 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.620844
2015-10-17 15:55:05,932 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.53341997
2015-10-17 15:55:05,994 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.5342037
2015-10-17 15:55:05,994 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.58460397
2015-10-17 15:55:05,994 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.53425497
2015-10-17 15:55:06,072 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:55:06,197 INFO [IPC Server handler 3 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.5343203
2015-10-17 15:55:06,869 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:07,072 INFO [IPC Server handler 29 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.106881365
2015-10-17 15:55:07,869 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:07,885 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.42795306
2015-10-17 15:55:08,869 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:08,869 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.6208445
2015-10-17 15:55:08,885 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1001 is : 0.1031613
2015-10-17 15:55:08,979 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.620844
2015-10-17 15:55:08,979 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.53341997
2015-10-17 15:55:09,010 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.5342037
2015-10-17 15:55:09,010 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.6210422
2015-10-17 15:55:09,119 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:55:09,213 INFO [IPC Server handler 3 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.5343203
2015-10-17 15:55:09,213 INFO [IPC Server handler 8 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.5281836
2015-10-17 15:55:09,869 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:10,104 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.106881365
2015-10-17 15:55:10,869 INFO [IPC Server handler 16 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:10,963 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.44444773
2015-10-17 15:55:11,869 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:11,900 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.6208445
2015-10-17 15:55:11,916 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0013_m_000005
2015-10-17 15:55:11,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0013_m_000005
2015-10-17 15:55:11,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:55:11,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:55:11,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000005_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:55:11,916 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 15:55:11,932 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1001 is : 0.106493875
2015-10-17 15:55:11,979 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.620844
2015-10-17 15:55:11,994 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.56465465
2015-10-17 15:55:12,025 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.6108331
2015-10-17 15:55:12,041 INFO [IPC Server handler 29 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.577422
2015-10-17 15:55:12,041 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.6210422
2015-10-17 15:55:12,150 INFO [IPC Server handler 8 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:55:12,244 INFO [IPC Server handler 9 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.5352021
2015-10-17 15:55:12,244 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.5805739
2015-10-17 15:55:12,494 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 15:55:12,494 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0013: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-13> knownNMs=4
2015-10-17 15:55:12,869 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:13,166 INFO [IPC Server handler 3 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.17667823
2015-10-17 15:55:13,494 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:55:13,494 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_02_000015 to attempt_1445062781478_0013_m_000005_1001
2015-10-17 15:55:13,494 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 15:55:13,494 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:55:13,494 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000005_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:55:13,494 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_02_000015 taskAttempt attempt_1445062781478_0013_m_000005_1001
2015-10-17 15:55:13,494 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000005_1001
2015-10-17 15:55:13,494 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:55:13,572 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000005_1001 : 13562
2015-10-17 15:55:13,572 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000005_1001] using containerId: [container_1445062781478_0013_02_000015 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:55:13,572 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000005_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:55:13,572 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000005
2015-10-17 15:55:13,869 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:13,994 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.44950172
2015-10-17 15:55:14,494 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0013: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:15360, vCores:-14> knownNMs=4
2015-10-17 15:55:14,838 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.6208445
2015-10-17 15:55:14,869 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:14,932 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.667
2015-10-17 15:55:14,963 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1001 is : 0.106493875
2015-10-17 15:55:15,010 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.61898744
2015-10-17 15:55:15,010 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.620844
2015-10-17 15:55:15,041 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.6197233
2015-10-17 15:55:15,057 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.6210422
2015-10-17 15:55:15,057 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.6196791
2015-10-17 15:55:15,197 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:55:15,276 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.5352021
2015-10-17 15:55:15,276 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.6199081
2015-10-17 15:55:15,869 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:16,197 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.19258286
2015-10-17 15:55:16,479 INFO [Socket Reader #1 for port 52881] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:55:16,494 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000015 asked for a task
2015-10-17 15:55:16,494 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000015 given task: attempt_1445062781478_0013_m_000005_1001
2015-10-17 15:55:16,823 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.620844
2015-10-17 15:55:16,869 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:17,041 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.44950172
2015-10-17 15:55:17,869 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:17,948 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.667
2015-10-17 15:55:18,010 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1001 is : 0.106493875
2015-10-17 15:55:18,026 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.667
2015-10-17 15:55:18,026 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.61898744
2015-10-17 15:55:18,041 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.6197233
2015-10-17 15:55:18,073 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.6196791
2015-10-17 15:55:18,073 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.6210422
2015-10-17 15:55:18,229 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:55:18,291 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.6199081
2015-10-17 15:55:18,291 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.5352021
2015-10-17 15:55:18,869 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:19,229 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.19258286
2015-10-17 15:55:19,916 INFO [IPC Server handler 16 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:20,073 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.44950172
2015-10-17 15:55:20,432 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.6210422
2015-10-17 15:55:20,916 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:20,963 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.667
2015-10-17 15:55:21,026 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.667
2015-10-17 15:55:21,041 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.61898744
2015-10-17 15:55:21,041 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1001 is : 0.106493875
2015-10-17 15:55:21,057 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.6197233
2015-10-17 15:55:21,088 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.6196791
2015-10-17 15:55:21,088 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.667
2015-10-17 15:55:21,260 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:55:21,307 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.57409567
2015-10-17 15:55:21,307 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.6199081
2015-10-17 15:55:21,916 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:22,260 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.19258286
2015-10-17 15:55:22,916 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:23,120 INFO [IPC Server handler 29 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.4558335
2015-10-17 15:55:23,760 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1001 is : 0.03835166
2015-10-17 15:55:23,916 INFO [IPC Server handler 16 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:23,979 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.6687696
2015-10-17 15:55:24,041 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.667
2015-10-17 15:55:24,057 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.61898744
2015-10-17 15:55:24,073 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.6369356
2015-10-17 15:55:24,088 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1001 is : 0.16207267
2015-10-17 15:55:24,120 INFO [IPC Server handler 29 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.6196791
2015-10-17 15:55:24,120 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.667
2015-10-17 15:55:24,323 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.6209487
2015-10-17 15:55:24,323 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.6199081
2015-10-17 15:55:24,323 INFO [IPC Server handler 9 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:55:24,916 INFO [IPC Server handler 16 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:25,307 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.19258286
2015-10-17 15:55:25,776 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.6369356
2015-10-17 15:55:25,917 INFO [IPC Server handler 16 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:26,073 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.6196791
2015-10-17 15:55:26,167 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.4747815
2015-10-17 15:55:26,651 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.6199081
2015-10-17 15:55:26,651 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.61898744
2015-10-17 15:55:26,807 INFO [IPC Server handler 16 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1001 is : 0.056886606
2015-10-17 15:55:26,917 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:26,932 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0013_m_000000
2015-10-17 15:55:26,932 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0013_m_000000
2015-10-17 15:55:26,932 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:55:26,932 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:55:26,932 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000000_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:55:26,932 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 15:55:27,010 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.6947772
2015-10-17 15:55:27,073 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.6722386
2015-10-17 15:55:27,088 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.667
2015-10-17 15:55:27,104 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.667
2015-10-17 15:55:27,120 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1001 is : 0.19209063
2015-10-17 15:55:27,135 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.667
2015-10-17 15:55:27,135 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.667
2015-10-17 15:55:27,338 INFO [IPC Server handler 9 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.6209487
2015-10-17 15:55:27,338 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.667
2015-10-17 15:55:27,354 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:55:27,526 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 15:55:27,557 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0013: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:15360, vCores:-14> knownNMs=4
2015-10-17 15:55:27,917 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:28,338 INFO [IPC Server handler 9 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.23110837
2015-10-17 15:55:28,588 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:55:28,588 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_02_000016 to attempt_1445062781478_0013_m_000000_1001
2015-10-17 15:55:28,588 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:12 RackLocal:2
2015-10-17 15:55:28,588 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:55:28,604 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000000_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:55:28,604 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_02_000016 taskAttempt attempt_1445062781478_0013_m_000000_1001
2015-10-17 15:55:28,604 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000000_1001
2015-10-17 15:55:28,604 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:55:28,635 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000000_1001 : 13562
2015-10-17 15:55:28,635 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000000_1001] using containerId: [container_1445062781478_0013_02_000016 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:55:28,635 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000000_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:55:28,635 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000000
2015-10-17 15:55:28,917 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:29,214 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.49379122
2015-10-17 15:55:29,917 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:30,151 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1001 is : 0.19209063
2015-10-17 15:55:30,339 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.73942274
2015-10-17 15:55:30,354 INFO [IPC Server handler 9 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.6209487
2015-10-17 15:55:30,370 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.667
2015-10-17 15:55:30,401 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.7150977
2015-10-17 15:55:30,417 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:55:30,417 INFO [IPC Server handler 16 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.667
2015-10-17 15:55:30,432 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.667
2015-10-17 15:55:30,464 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.6919759
2015-10-17 15:55:30,495 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.667
2015-10-17 15:55:30,495 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0013: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:14336, vCores:-15> knownNMs=4
2015-10-17 15:55:30,760 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1001 is : 0.07652328
2015-10-17 15:55:30,917 INFO [IPC Server handler 2 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:31,370 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.27811313
2015-10-17 15:55:31,917 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:32,260 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.5158175
2015-10-17 15:55:32,917 INFO [IPC Server handler 2 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:33,042 INFO [Socket Reader #1 for port 52881] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:55:33,073 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000016 asked for a task
2015-10-17 15:55:33,073 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000016 given task: attempt_1445062781478_0013_m_000000_1001
2015-10-17 15:55:33,182 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1001 is : 0.19209063
2015-10-17 15:55:33,354 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.77497
2015-10-17 15:55:33,386 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.6230547
2015-10-17 15:55:33,386 INFO [IPC Server handler 9 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.667
2015-10-17 15:55:33,417 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.7489722
2015-10-17 15:55:33,432 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.667
2015-10-17 15:55:33,448 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.667
2015-10-17 15:55:33,448 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:55:33,479 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.72573876
2015-10-17 15:55:33,511 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.667
2015-10-17 15:55:33,792 INFO [IPC Server handler 2 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1001 is : 0.106472634
2015-10-17 15:55:33,917 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:34,401 INFO [IPC Server handler 8 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.27811313
2015-10-17 15:55:34,917 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:35,292 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.53521925
2015-10-17 15:55:35,636 INFO [IPC Server handler 2 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.6230547
2015-10-17 15:55:35,917 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:36,214 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1001 is : 0.19209063
2015-10-17 15:55:36,370 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.8121104
2015-10-17 15:55:36,401 INFO [IPC Server handler 8 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.667
2015-10-17 15:55:36,401 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.6732489
2015-10-17 15:55:36,432 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.78364056
2015-10-17 15:55:36,448 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.6704236
2015-10-17 15:55:36,464 INFO [IPC Server handler 16 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.6830358
2015-10-17 15:55:36,479 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:55:36,495 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.7583936
2015-10-17 15:55:36,526 INFO [IPC Server handler 2 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.6861221
2015-10-17 15:55:36,823 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1001 is : 0.10685723
2015-10-17 15:55:36,917 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:37,433 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.27811313
2015-10-17 15:55:37,917 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:38,323 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.53521925
2015-10-17 15:55:38,917 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:39,245 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1001 is : 0.22179785
2015-10-17 15:55:39,386 INFO [IPC Server handler 8 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.84558964
2015-10-17 15:55:39,448 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.667
2015-10-17 15:55:39,448 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.70972365
2015-10-17 15:55:39,464 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.81565565
2015-10-17 15:55:39,464 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.7063403
2015-10-17 15:55:39,479 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.7160068
2015-10-17 15:55:39,511 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:55:39,542 INFO [IPC Server handler 2 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.79829746
2015-10-17 15:55:39,573 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.7238595
2015-10-17 15:55:39,854 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1001 is : 0.10685723
2015-10-17 15:55:39,917 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:40,386 INFO [IPC Server handler 8 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1001 is : 0.0653663
2015-10-17 15:55:40,464 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.27811313
2015-10-17 15:55:40,917 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:41,386 INFO [IPC Server handler 8 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.53521925
2015-10-17 15:55:41,917 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:42,292 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1001 is : 0.2768227
2015-10-17 15:55:42,401 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.87832177
2015-10-17 15:55:42,464 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.667
2015-10-17 15:55:42,464 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.74320525
2015-10-17 15:55:42,480 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.84787524
2015-10-17 15:55:42,480 INFO [IPC Server handler 3 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.73812795
2015-10-17 15:55:42,495 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.7492471
2015-10-17 15:55:42,542 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:55:42,558 INFO [IPC Server handler 2 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.83498245
2015-10-17 15:55:42,589 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.75923204
2015-10-17 15:55:42,886 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1001 is : 0.12495353
2015-10-17 15:55:42,917 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:43,433 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1001 is : 0.10635664
2015-10-17 15:55:43,511 INFO [IPC Server handler 16 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.28466558
2015-10-17 15:55:43,917 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:44,448 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.56822854
2015-10-17 15:55:44,917 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:45,323 INFO [IPC Server handler 29 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1001 is : 0.27765483
2015-10-17 15:55:45,433 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.913616
2015-10-17 15:55:45,495 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.77476525
2015-10-17 15:55:45,495 INFO [IPC Server handler 3 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.667
2015-10-17 15:55:45,511 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.8821187
2015-10-17 15:55:45,511 INFO [IPC Server handler 9 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.772309
2015-10-17 15:55:45,526 INFO [IPC Server handler 16 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.7857171
2015-10-17 15:55:45,589 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.87003344
2015-10-17 15:55:45,605 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.79335093
2015-10-17 15:55:45,605 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:55:45,917 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:45,933 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1001 is : 0.1681483
2015-10-17 15:55:46,495 INFO [IPC Server handler 3 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1001 is : 0.10635664
2015-10-17 15:55:46,589 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.35634378
2015-10-17 15:55:46,917 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:47,480 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.6105321
2015-10-17 15:55:47,917 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:48,355 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1001 is : 0.27765483
2015-10-17 15:55:48,448 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.94550705
2015-10-17 15:55:48,527 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.913176
2015-10-17 15:55:48,527 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.6938847
2015-10-17 15:55:48,527 INFO [IPC Server handler 9 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.80782926
2015-10-17 15:55:48,527 INFO [IPC Server handler 16 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.80670583
2015-10-17 15:55:48,558 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.8196392
2015-10-17 15:55:48,605 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.906236
2015-10-17 15:55:48,620 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.8280999
2015-10-17 15:55:48,652 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:55:48,917 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:48,964 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1001 is : 0.19247705
2015-10-17 15:55:49,527 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1001 is : 0.10635664
2015-10-17 15:55:49,620 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.3637686
2015-10-17 15:55:49,917 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:50,511 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.6207798
2015-10-17 15:55:50,917 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:51,386 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1001 is : 0.27765483
2015-10-17 15:55:51,464 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 0.9744215
2015-10-17 15:55:51,542 INFO [IPC Server handler 16 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.7259294
2015-10-17 15:55:51,542 INFO [IPC Server handler 3 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.8383579
2015-10-17 15:55:51,542 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.9416592
2015-10-17 15:55:51,542 INFO [IPC Server handler 9 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.83927596
2015-10-17 15:55:51,574 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.8486866
2015-10-17 15:55:51,620 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.9392383
2015-10-17 15:55:51,636 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.8610281
2015-10-17 15:55:51,683 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:55:51,917 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:51,995 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1001 is : 0.19247705
2015-10-17 15:55:52,558 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1001 is : 0.11008497
2015-10-17 15:55:52,652 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.3637686
2015-10-17 15:55:52,917 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:53,542 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.6207798
2015-10-17 15:55:53,917 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:54,105 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_1000 is : 1.0
2015-10-17 15:55:54,121 INFO [IPC Server handler 29 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0013_m_000004_1000
2015-10-17 15:55:54,121 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000004_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:55:54,121 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0013_02_000006 taskAttempt attempt_1445062781478_0013_m_000004_1000
2015-10-17 15:55:54,121 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0013_m_000004_1000
2015-10-17 15:55:54,121 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:55:54,152 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000004_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:55:54,152 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0013_m_000004_1000
2015-10-17 15:55:54,152 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:55:54,152 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-17 15:55:54,417 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1001 is : 0.27765483
2015-10-17 15:55:54,558 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:12 RackLocal:2
2015-10-17 15:55:54,574 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.87448895
2015-10-17 15:55:54,574 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.7617473
2015-10-17 15:55:54,574 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 0.9706254
2015-10-17 15:55:54,574 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.8727367
2015-10-17 15:55:54,589 INFO [IPC Server handler 13 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.8801285
2015-10-17 15:55:54,652 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 0.97622955
2015-10-17 15:55:54,652 INFO [IPC Server handler 2 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.8969844
2015-10-17 15:55:54,714 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:55:54,917 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-17 15:55:55,074 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1001 is : 0.19247705
2015-10-17 15:55:55,589 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0013_02_000006
2015-10-17 15:55:55,589 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:12 RackLocal:2
2015-10-17 15:55:55,589 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0013_m_000004_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:55:55,605 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1001 is : 0.13145499
2015-10-17 15:55:55,683 INFO [IPC Server handler 2 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.3637686
2015-10-17 15:55:55,917 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-17 15:55:56,605 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.6207798
2015-10-17 15:55:56,792 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_1000 is : 1.0
2015-10-17 15:55:56,933 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-17 15:55:56,996 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0013_m_000006_1000
2015-10-17 15:55:56,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000006_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:55:56,996 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0013_02_000008 taskAttempt attempt_1445062781478_0013_m_000006_1000
2015-10-17 15:55:56,996 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0013_m_000006_1000
2015-10-17 15:55:56,996 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:55:57,136 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000006_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:55:57,136 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0013_m_000006_1000
2015-10-17 15:55:57,136 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:55:57,136 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-17 15:55:57,449 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1001 is : 0.31363034
2015-10-17 15:55:57,605 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:12 RackLocal:2
2015-10-17 15:55:57,621 INFO [IPC Server handler 2 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.7944363
2015-10-17 15:55:57,621 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.90702593
2015-10-17 15:55:57,621 INFO [IPC Server handler 2 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 1.0
2015-10-17 15:55:57,621 INFO [IPC Server handler 17 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.9048501
2015-10-17 15:55:57,636 INFO [IPC Server handler 25 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.9123
2015-10-17 15:55:57,683 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.9291294
2015-10-17 15:55:57,714 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_1000 is : 1.0
2015-10-17 15:55:57,746 INFO [IPC Server handler 29 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0013_m_000007_1000
2015-10-17 15:55:57,746 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000007_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:55:57,746 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0013_02_000009 taskAttempt attempt_1445062781478_0013_m_000007_1000
2015-10-17 15:55:57,746 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0013_m_000007_1000
2015-10-17 15:55:57,746 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:55:57,746 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:55:57,902 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000007_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:55:57,902 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0013_m_000007_1000
2015-10-17 15:55:57,902 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:55:57,902 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-17 15:55:57,964 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-17 15:55:58,136 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1001 is : 0.19273388
2015-10-17 15:55:58,636 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:12 RackLocal:2
2015-10-17 15:55:58,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0013_02_000008
2015-10-17 15:55:58,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:12 RackLocal:2
2015-10-17 15:55:58,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0013_m_000006_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:55:58,699 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1001 is : 0.15819967
2015-10-17 15:55:58,714 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.3720318
2015-10-17 15:55:58,964 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-17 15:55:59,683 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.6261806
2015-10-17 15:55:59,730 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0013_02_000009
2015-10-17 15:55:59,730 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:12 RackLocal:2
2015-10-17 15:55:59,730 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0013_m_000007_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:55:59,964 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-17 15:56:00,464 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1001 is : 0.34164008
2015-10-17 15:56:00,683 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.93549895
2015-10-17 15:56:00,683 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.93953586
2015-10-17 15:56:00,683 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.8267144
2015-10-17 15:56:00,699 INFO [IPC Server handler 29 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 0.9496789
2015-10-17 15:56:00,777 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:56:00,964 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-17 15:56:01,043 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.96098495
2015-10-17 15:56:01,214 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1001 is : 0.22771005
2015-10-17 15:56:01,746 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.39830786
2015-10-17 15:56:01,777 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1001 is : 0.18978389
2015-10-17 15:56:01,964 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-17 15:56:02,761 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.6570215
2015-10-17 15:56:02,965 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-17 15:56:03,293 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.6570215
2015-10-17 15:56:03,465 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_1000 is : 1.0
2015-10-17 15:56:03,511 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1001 is : 0.36323506
2015-10-17 15:56:03,527 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0013_m_000001_1000
2015-10-17 15:56:03,527 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000001_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:56:03,527 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0013_02_000003 taskAttempt attempt_1445062781478_0013_m_000001_1000
2015-10-17 15:56:03,527 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0013_m_000001_1000
2015-10-17 15:56:03,527 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:56:03,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000001_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:56:03,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0013_m_000001_1000
2015-10-17 15:56:03,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:56:03,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-17 15:56:03,715 INFO [IPC Server handler 29 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 0.9672037
2015-10-17 15:56:03,730 INFO [IPC Server handler 7 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 0.97198063
2015-10-17 15:56:03,730 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.8617815
2015-10-17 15:56:03,824 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:56:03,871 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:12 RackLocal:2
2015-10-17 15:56:03,965 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-17 15:56:04,090 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 0.99730444
2015-10-17 15:56:04,277 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1001 is : 0.2617582
2015-10-17 15:56:04,402 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_1000 is : 1.0
2015-10-17 15:56:04,449 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0013_m_000002_1000
2015-10-17 15:56:04,449 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000002_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:56:04,449 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0013_02_000004 taskAttempt attempt_1445062781478_0013_m_000002_1000
2015-10-17 15:56:04,449 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0013_m_000002_1000
2015-10-17 15:56:04,449 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:56:04,636 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000002_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:56:04,636 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0013_m_000002_1000
2015-10-17 15:56:04,636 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:56:04,636 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-17 15:56:04,808 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.4359982
2015-10-17 15:56:04,840 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1001 is : 0.19158794
2015-10-17 15:56:04,886 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:12 RackLocal:2
2015-10-17 15:56:04,965 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-17 15:56:05,777 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.667
2015-10-17 15:56:05,793 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0013_02_000003
2015-10-17 15:56:05,793 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:12 RackLocal:2
2015-10-17 15:56:05,793 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0013_m_000001_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:56:05,949 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_1000 is : 1.0
2015-10-17 15:56:05,965 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-17 15:56:05,965 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0013_m_000000_1000
2015-10-17 15:56:05,965 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000000_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:56:05,965 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0013_02_000002 taskAttempt attempt_1445062781478_0013_m_000000_1000
2015-10-17 15:56:05,965 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0013_m_000000_1000
2015-10-17 15:56:05,965 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:56:06,027 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000000_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:56:06,027 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0013_m_000000_1000
2015-10-17 15:56:06,027 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0013_m_000000_1001
2015-10-17 15:56:06,027 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:56:06,027 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-17 15:56:06,027 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000000_1001 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 15:56:06,027 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0013_02_000016 taskAttempt attempt_1445062781478_0013_m_000000_1001
2015-10-17 15:56:06,027 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0013_m_000000_1001
2015-10-17 15:56:06,027 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:56:06,090 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000000_1001 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 15:56:06,090 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 15:56:06,121 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out3/_temporary/2/_temporary/attempt_1445062781478_0013_m_000000_1001
2015-10-17 15:56:06,121 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000000_1001 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 15:56:06,121 INFO [Socket Reader #1 for port 52881] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 52881: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 15:56:06,465 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1000 is : 1.0
2015-10-17 15:56:06,496 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0013_m_000003_1000
2015-10-17 15:56:06,496 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000003_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:56:06,496 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0013_02_000005 taskAttempt attempt_1445062781478_0013_m_000003_1000
2015-10-17 15:56:06,496 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0013_m_000003_1000
2015-10-17 15:56:06,496 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:56:06,558 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_1001 is : 0.36323506
2015-10-17 15:56:06,605 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000003_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:56:06,605 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0013_m_000003_1000
2015-10-17 15:56:06,605 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0013_m_000003_1001
2015-10-17 15:56:06,605 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:56:06,605 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-17 15:56:06,605 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000003_1001 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 15:56:06,605 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0013_02_000014 taskAttempt attempt_1445062781478_0013_m_000003_1001
2015-10-17 15:56:06,605 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0013_m_000003_1001
2015-10-17 15:56:06,605 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 15:56:06,637 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000003_1001 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 15:56:06,637 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 15:56:06,652 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out3/_temporary/2/_temporary/attempt_1445062781478_0013_m_000003_1001
2015-10-17 15:56:06,652 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000003_1001 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 15:56:06,730 INFO [Socket Reader #1 for port 52881] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 52881: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 15:56:06,762 INFO [IPC Server handler 1 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.9009216
2015-10-17 15:56:06,793 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:12 RackLocal:2
2015-10-17 15:56:06,808 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0013_02_000004
2015-10-17 15:56:06,808 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0013_02_000002
2015-10-17 15:56:06,808 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:12 RackLocal:2
2015-10-17 15:56:06,808 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0013_m_000002_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:56:06,808 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0013_m_000000_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:56:06,871 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.033333335
2015-10-17 15:56:06,965 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-17 15:56:07,324 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1001 is : 0.27813601
2015-10-17 15:56:07,840 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.44950172
2015-10-17 15:56:07,840 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0013_02_000014
2015-10-17 15:56:07,840 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0013_02_000016
2015-10-17 15:56:07,840 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0013_m_000003_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:56:07,840 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0013_02_000005
2015-10-17 15:56:07,840 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0013_m_000000_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:56:07,840 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:12 RackLocal:2
2015-10-17 15:56:07,840 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0013_m_000003_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:56:07,965 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-17 15:56:08,855 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.667
2015-10-17 15:56:08,965 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-17 15:56:09,824 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.93807864
2015-10-17 15:56:09,902 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.06666667
2015-10-17 15:56:09,965 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-17 15:56:10,387 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1001 is : 0.27813601
2015-10-17 15:56:10,871 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.44950172
2015-10-17 15:56:10,965 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-17 15:56:11,902 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.67818296
2015-10-17 15:56:11,965 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-17 15:56:12,887 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.969365
2015-10-17 15:56:12,934 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.06666667
2015-10-17 15:56:12,965 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-17 15:56:13,449 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1001 is : 0.28379336
2015-10-17 15:56:13,902 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.44950172
2015-10-17 15:56:13,965 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-17 15:56:14,965 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-17 15:56:14,996 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.7076069
2015-10-17 15:56:15,918 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 0.99898994
2015-10-17 15:56:15,981 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.10000001
2015-10-17 15:56:15,965 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-17 15:56:16,246 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1000 is : 1.0
2015-10-17 15:56:16,277 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0013_m_000005_1000
2015-10-17 15:56:16,277 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000005_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:56:16,277 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0013_02_000007 taskAttempt attempt_1445062781478_0013_m_000005_1000
2015-10-17 15:56:16,277 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0013_m_000005_1000
2015-10-17 15:56:16,277 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:56:16,418 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000005_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:56:16,418 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0013_m_000005_1000
2015-10-17 15:56:16,418 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0013_m_000005_1001
2015-10-17 15:56:16,418 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:56:16,418 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-17 15:56:16,418 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000005_1001 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 15:56:16,418 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0013_02_000015 taskAttempt attempt_1445062781478_0013_m_000005_1001
2015-10-17 15:56:16,418 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0013_m_000005_1001
2015-10-17 15:56:16,418 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:56:16,434 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0013_m_000005
2015-10-17 15:56:16,434 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 15:56:16,512 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_1001 is : 0.3118686
2015-10-17 15:56:16,606 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000005_1001 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 15:56:16,606 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 15:56:16,637 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out3/_temporary/2/_temporary/attempt_1445062781478_0013_m_000005_1001
2015-10-17 15:56:16,637 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000005_1001 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 15:56:16,668 INFO [Socket Reader #1 for port 52881] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 52881: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 15:56:16,918 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.4601896
2015-10-17 15:56:17,153 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:12 RackLocal:2
2015-10-17 15:56:17,199 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-17 15:56:18,199 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:18,481 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0013_02_000015
2015-10-17 15:56:18,481 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0013_02_000007
2015-10-17 15:56:18,481 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:12 RackLocal:2
2015-10-17 15:56:18,481 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0013_m_000005_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:56:18,481 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0013_m_000005_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:56:18,934 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.7250651
2015-10-17 15:56:19,199 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:19,231 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.10000001
2015-10-17 15:56:19,965 INFO [IPC Server handler 12 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.48500553
2015-10-17 15:56:20,199 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:21,199 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:21,996 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.7573105
2015-10-17 15:56:22,200 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:22,262 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.10000001
2015-10-17 15:56:22,996 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.53521925
2015-10-17 15:56:23,200 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:24,215 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:25,059 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.78628886
2015-10-17 15:56:25,215 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:25,293 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.10000001
2015-10-17 15:56:26,028 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.53521925
2015-10-17 15:56:26,215 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:27,215 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:28,106 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.8153477
2015-10-17 15:56:28,215 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:28,325 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.13333334
2015-10-17 15:56:29,059 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.53521925
2015-10-17 15:56:29,215 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:30,215 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:31,184 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.8443755
2015-10-17 15:56:31,215 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:31,356 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.13333334
2015-10-17 15:56:32,090 INFO [IPC Server handler 6 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.53521925
2015-10-17 15:56:32,215 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:33,215 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:34,216 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:34,403 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.16666667
2015-10-17 15:56:34,497 INFO [IPC Server handler 8 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.87400395
2015-10-17 15:56:35,122 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.5641302
2015-10-17 15:56:35,216 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:36,231 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:37,231 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:37,434 INFO [IPC Server handler 8 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.16666667
2015-10-17 15:56:37,559 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.90801364
2015-10-17 15:56:38,153 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.6207798
2015-10-17 15:56:38,231 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:39,231 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:40,231 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:40,466 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.16666667
2015-10-17 15:56:40,622 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.93911546
2015-10-17 15:56:41,184 INFO [IPC Server handler 23 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.6207798
2015-10-17 15:56:41,231 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:42,231 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:43,231 INFO [IPC Server handler 4 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:43,497 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.16666667
2015-10-17 15:56:43,669 INFO [IPC Server handler 9 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 0.9701697
2015-10-17 15:56:44,231 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1001 is : 0.6207798
2015-10-17 15:56:44,231 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:45,231 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:46,231 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:46,528 INFO [IPC Server handler 8 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.20000002
2015-10-17 15:56:46,731 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 1.0
2015-10-17 15:56:46,794 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_1000 is : 1.0
2015-10-17 15:56:46,825 INFO [IPC Server handler 29 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0013_m_000008_1000
2015-10-17 15:56:46,825 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000008_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:56:46,825 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0013_02_000010 taskAttempt attempt_1445062781478_0013_m_000008_1000
2015-10-17 15:56:46,825 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0013_m_000008_1000
2015-10-17 15:56:46,825 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:56:46,997 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000008_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:56:46,997 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0013_m_000008_1000
2015-10-17 15:56:46,997 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0013_m_000008_1001
2015-10-17 15:56:46,997 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:56:46,997 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-17 15:56:46,997 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000008_1001 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 15:56:46,997 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0013_02_000012 taskAttempt attempt_1445062781478_0013_m_000008_1001
2015-10-17 15:56:46,997 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0013_m_000008_1001
2015-10-17 15:56:46,997 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 15:56:47,028 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000008_1001 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 15:56:47,028 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 15:56:47,075 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:12 RackLocal:2
2015-10-17 15:56:47,091 INFO [Socket Reader #1 for port 52881] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 52881: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 15:56:47,169 WARN [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out3/_temporary/2/_temporary/attempt_1445062781478_0013_m_000008_1001
2015-10-17 15:56:47,169 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000008_1001 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 15:56:47,231 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-17 15:56:48,294 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:56:49,044 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0013_02_000012
2015-10-17 15:56:49,044 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0013_02_000010
2015-10-17 15:56:49,044 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0013_m_000008_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:56:49,044 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:12 RackLocal:2
2015-10-17 15:56:49,044 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0013_m_000008_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:56:49,310 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:56:49,560 INFO [IPC Server handler 8 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.20000002
2015-10-17 15:56:50,310 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:56:51,310 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:56:52,310 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:56:52,591 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.20000002
2015-10-17 15:56:53,310 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:56:54,326 INFO [IPC Server handler 26 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:56:55,341 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:56:55,622 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.20000002
2015-10-17 15:56:56,357 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:56:57,357 INFO [IPC Server handler 21 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:56:58,373 INFO [IPC Server handler 8 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:56:58,654 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.23333333
2015-10-17 15:56:59,388 INFO [IPC Server handler 24 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:57:00,388 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:57:01,404 INFO [IPC Server handler 8 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:57:01,701 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.23333333
2015-10-17 15:57:02,404 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:57:03,404 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:57:04,404 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:57:04,748 INFO [IPC Server handler 27 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.26666668
2015-10-17 15:57:05,404 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:57:06,420 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:57:07,420 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:57:07,795 INFO [IPC Server handler 29 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.3
2015-10-17 15:57:08,420 INFO [IPC Server handler 14 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:57:09,435 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:57:10,435 INFO [IPC Server handler 11 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:57:10,826 INFO [IPC Server handler 9 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.3
2015-10-17 15:57:11,435 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:57:12,435 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:57:13,435 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:57:13,857 INFO [IPC Server handler 18 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.3
2015-10-17 15:57:14,435 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:57:15,436 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0013_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-17 15:57:15,873 INFO [IPC Server handler 10 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.3
2015-10-17 15:57:15,920 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.3
2015-10-17 15:57:16,951 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.6676421
2015-10-17 15:57:19,967 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.68842435
2015-10-17 15:57:22,998 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.7104549
2015-10-17 15:57:26,030 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.7328932
2015-10-17 15:57:29,045 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.7550758
2015-10-17 15:57:32,077 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.77736974
2015-10-17 15:57:35,108 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.7996395
2015-10-17 15:57:38,139 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.821921
2015-10-17 15:57:41,155 INFO [IPC Server handler 28 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.84418154
2015-10-17 15:57:44,186 INFO [IPC Server handler 20 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.8656788
2015-10-17 15:57:47,233 INFO [IPC Server handler 19 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.8871545
2015-10-17 15:57:50,249 INFO [IPC Server handler 29 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.90947205
2015-10-17 15:57:53,265 INFO [IPC Server handler 9 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.9317645
2015-10-17 15:57:56,280 INFO [IPC Server handler 15 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.95389533
2015-10-17 15:57:59,296 INFO [IPC Server handler 9 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.9760674
2015-10-17 15:58:02,343 INFO [IPC Server handler 9 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 0.9983846
2015-10-17 15:58:02,718 INFO [IPC Server handler 3 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445062781478_0013_r_000000_1000
2015-10-17 15:58:02,718 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_r_000000_1000 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-17 15:58:02,718 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445062781478_0013_r_000000_1000 given a go for committing the task output.
2015-10-17 15:58:02,718 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445062781478_0013_r_000000_1000
2015-10-17 15:58:02,718 INFO [IPC Server handler 0 on 52881] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445062781478_0013_r_000000_1000:true
2015-10-17 15:58:02,749 INFO [IPC Server handler 5 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_r_000000_1000 is : 1.0
2015-10-17 15:58:02,765 INFO [IPC Server handler 22 on 52881] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0013_r_000000_1000
2015-10-17 15:58:02,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_r_000000_1000 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:58:02,765 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0013_02_000013 taskAttempt attempt_1445062781478_0013_r_000000_1000
2015-10-17 15:58:02,765 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0013_r_000000_1000
2015-10-17 15:58:02,765 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 15:58:02,781 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_r_000000_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:58:02,781 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0013_r_000000_1000
2015-10-17 15:58:02,781 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:58:02,781 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-17 15:58:02,781 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0013Job Transitioned from RUNNING to COMMITTING
2015-10-17 15:58:02,781 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-17 15:58:02,874 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-17 15:58:02,874 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0013Job Transitioned from COMMITTING to SUCCEEDED
2015-10-17 15:58:02,874 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-17 15:58:02,874 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-17 15:58:02,874 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-17 15:58:02,874 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-17 15:58:02,874 INFO [Thread-104] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-17 15:58:02,874 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-17 15:58:02,874 INFO [Thread-104] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-17 15:58:03,046 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0013/job_1445062781478_0013_2.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0013-1445067474867-msrabi-pagerank-1445068682874-10-1-SUCCEEDED-default-1445068409976.jhist_tmp
2015-10-17 15:58:03,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:15 ContRel:0 HostLocal:12 RackLocal:2
2015-10-17 15:58:03,312 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0013-1445067474867-msrabi-pagerank-1445068682874-10-1-SUCCEEDED-default-1445068409976.jhist_tmp
2015-10-17 15:58:03,328 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0013/job_1445062781478_0013_2_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0013_conf.xml_tmp
2015-10-17 15:58:03,453 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0013_conf.xml_tmp
2015-10-17 15:58:03,453 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0013.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0013.summary
2015-10-17 15:58:03,453 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0013_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0013_conf.xml
2015-10-17 15:58:03,468 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0013-1445067474867-msrabi-pagerank-1445068682874-10-1-SUCCEEDED-default-1445068409976.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0013-1445067474867-msrabi-pagerank-1445068682874-10-1-SUCCEEDED-default-1445068409976.jhist
2015-10-17 15:58:03,468 INFO [Thread-104] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-17 15:58:03,484 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-17 15:58:03,484 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MININT-75DGDAM1.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445062781478_0013
2015-10-17 15:58:03,484 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-17 15:58:04,484 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:15 ContRel:0 HostLocal:12 RackLocal:2
2015-10-17 15:58:04,484 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0013
2015-10-17 15:58:04,500 INFO [Thread-104] org.apache.hadoop.ipc.Server: Stopping server on 52881
2015-10-17 15:58:04,500 INFO [IPC Server listener on 52881] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 52881
2015-10-17 15:58:04,500 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-17 15:58:04,500 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
