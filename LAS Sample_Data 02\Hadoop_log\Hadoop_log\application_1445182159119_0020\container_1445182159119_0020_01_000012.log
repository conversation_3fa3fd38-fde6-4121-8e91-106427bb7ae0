2015-10-19 17:53:16,225 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 17:53:16,303 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 17:53:16,303 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: ReduceTask metrics system started
2015-10-19 17:53:16,334 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-19 17:53:16,334 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445182159119_0020, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@11628d93)
2015-10-19 17:53:16,553 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-19 17:53:17,162 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445182159119_0020
2015-10-19 17:53:18,725 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-19 17:53:19,444 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-19 17:53:19,569 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@294c9ed9
2015-10-19 17:53:19,600 INFO [main] org.apache.hadoop.mapred.ReduceTask: Using ShuffleConsumerPlugin: org.apache.hadoop.mapreduce.task.reduce.Shuffle@333861df
2015-10-19 17:53:19,913 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: MergerManager: memoryLimit=130652568, maxSingleShuffleLimit=32663142, mergeThreshold=86230696, ioSortFactor=10, memToMemMergeOutputsThreshold=10
2015-10-19 17:53:19,928 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445182159119_0020_r_000000_0 Thread started: EventFetcher for fetching Map Completion Events
2015-10-19 17:53:19,991 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445182159119_0020_r_000000_0: Got 2 new map-outputs
2015-10-19 17:53:19,991 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MININT-FNANLI5.fareast.corp.microsoft.com:13562 with 2 to fetcher#5
2015-10-19 17:53:19,991 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 2 of 2 to MININT-FNANLI5.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-19 17:53:20,053 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445182159119_0020&reduce=0&map=attempt_1445182159119_0020_m_000006_0,attempt_1445182159119_0020_m_000005_0 sent hash and received reply
2015-10-19 17:53:20,053 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445182159119_0020_m_000006_0: Shuffling to disk since 60515100 is greater than maxSingleShuffleLimit (32663142)
2015-10-19 17:53:20,084 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445182159119_0020_m_000006_0 decomp: 60515100 len: 60515104 to DISK
2015-10-19 17:53:20,741 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515104 bytes from map-output for attempt_1445182159119_0020_m_000006_0
2015-10-19 17:53:20,944 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445182159119_0020_m_000005_0: Shuffling to disk since 60514806 is greater than maxSingleShuffleLimit (32663142)
2015-10-19 17:53:20,959 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445182159119_0020_m_000005_0 decomp: 60514806 len: 60514810 to DISK
2015-10-19 17:53:21,678 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60514810 bytes from map-output for attempt_1445182159119_0020_m_000005_0
2015-10-19 17:53:21,850 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MININT-FNANLI5.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 1850ms
2015-10-19 17:53:30,616 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445182159119_0020_r_000000_0: Got 1 new map-outputs
2015-10-19 17:53:30,616 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning 04DN8IQ.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-19 17:53:30,616 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to 04DN8IQ.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-19 17:53:30,710 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445182159119_0020&reduce=0&map=attempt_1445182159119_0020_m_000000_0 sent hash and received reply
2015-10-19 17:53:30,726 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445182159119_0020_m_000000_0: Shuffling to disk since 60515385 is greater than maxSingleShuffleLimit (32663142)
2015-10-19 17:53:30,726 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445182159119_0020_m_000000_0 decomp: 60515385 len: 60515389 to DISK
2015-10-19 17:54:01,868 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445182159119_0020_r_000000_0: Got 2 new map-outputs
2015-10-19 17:54:25,244 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515389 bytes from map-output for attempt_1445182159119_0020_m_000000_0
2015-10-19 17:54:25,338 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: 04DN8IQ.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 54720ms
2015-10-19 17:54:25,338 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning 04DN8IQ.fareast.corp.microsoft.com:13562 with 2 to fetcher#5
2015-10-19 17:54:25,338 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 2 of 2 to 04DN8IQ.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-19 17:54:25,447 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445182159119_0020&reduce=0&map=attempt_1445182159119_0020_m_000003_0,attempt_1445182159119_0020_m_000001_0 sent hash and received reply
2015-10-19 17:54:25,463 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445182159119_0020_m_000003_0: Shuffling to disk since 60515787 is greater than maxSingleShuffleLimit (32663142)
2015-10-19 17:54:25,463 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445182159119_0020_m_000003_0 decomp: 60515787 len: 60515791 to DISK
2015-10-19 17:54:27,588 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445182159119_0020_r_000000_0: Got 1 new map-outputs
2015-10-19 17:54:47,323 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445182159119_0020_r_000000_0: Got 1 new map-outputs
2015-10-19 17:54:59,105 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445182159119_0020_r_000000_0: Got 1 new map-outputs
2015-10-19 17:54:59,105 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#1
2015-10-19 17:54:59,105 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-19 17:54:59,371 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445182159119_0020&reduce=0&map=attempt_1445182159119_0020_m_000008_0 sent hash and received reply
2015-10-19 17:54:59,371 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445182159119_0020_m_000008_0: Shuffling to disk since 60516677 is greater than maxSingleShuffleLimit (32663142)
2015-10-19 17:54:59,371 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445182159119_0020_m_000008_0 decomp: 60516677 len: 60516681 to DISK
2015-10-19 17:55:20,935 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445182159119_0020_r_000000_0: Got 1 new map-outputs
2015-10-19 17:55:20,935 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MININT-FNANLI5.fareast.corp.microsoft.com:13562 with 1 to fetcher#4
2015-10-19 17:55:20,935 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MININT-FNANLI5.fareast.corp.microsoft.com:13562 to fetcher#4
2015-10-19 17:55:21,013 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445182159119_0020&reduce=0&map=attempt_1445182159119_0020_m_000007_0 sent hash and received reply
2015-10-19 17:55:21,013 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445182159119_0020_m_000007_0: Shuffling to disk since 60517368 is greater than maxSingleShuffleLimit (32663142)
2015-10-19 17:55:21,013 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#4 about to shuffle output of map attempt_1445182159119_0020_m_000007_0 decomp: 60517368 len: 60517372 to DISK
2015-10-19 17:55:22,232 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60517372 bytes from map-output for attempt_1445182159119_0020_m_000007_0
2015-10-19 17:55:22,247 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MININT-FNANLI5.fareast.corp.microsoft.com:13562 freed by fetcher#4 in 1315ms
2015-10-19 17:55:48,592 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60516681 bytes from map-output for attempt_1445182159119_0020_m_000008_0
2015-10-19 17:55:48,655 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 49554ms
2015-10-19 17:56:04,750 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445182159119_0020_r_000000_0: Got 1 new map-outputs
2015-10-19 17:57:09,285 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515791 bytes from map-output for attempt_1445182159119_0020_m_000003_0
2015-10-19 17:57:09,332 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445182159119_0020_m_000001_0: Shuffling to disk since 60515836 is greater than maxSingleShuffleLimit (32663142)
2015-10-19 17:57:09,347 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445182159119_0020_m_000001_0 decomp: 60515836 len: 60515840 to DISK
2015-10-19 17:58:31,760 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515840 bytes from map-output for attempt_1445182159119_0020_m_000001_0
2015-10-19 17:58:32,026 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: 04DN8IQ.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 246671ms
2015-10-19 17:58:32,026 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning 04DN8IQ.fareast.corp.microsoft.com:13562 with 3 to fetcher#5
2015-10-19 17:58:32,026 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 3 of 3 to 04DN8IQ.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-19 17:58:32,198 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445182159119_0020&reduce=0&map=attempt_1445182159119_0020_m_000002_0,attempt_1445182159119_0020_m_000004_0,attempt_1445182159119_0020_m_000009_0 sent hash and received reply
2015-10-19 17:58:32,354 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445182159119_0020_m_000002_0: Shuffling to disk since 60514392 is greater than maxSingleShuffleLimit (32663142)
2015-10-19 17:58:32,354 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445182159119_0020_m_000002_0 decomp: 60514392 len: 60514396 to DISK
2015-10-19 17:58:56,715 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60514396 bytes from map-output for attempt_1445182159119_0020_m_000002_0
2015-10-19 17:58:56,809 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445182159119_0020_m_000004_0: Shuffling to disk since 60513765 is greater than maxSingleShuffleLimit (32663142)
2015-10-19 17:58:56,825 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445182159119_0020_m_000004_0 decomp: 60513765 len: 60513769 to DISK
2015-10-19 17:59:21,280 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60513769 bytes from map-output for attempt_1445182159119_0020_m_000004_0
2015-10-19 17:59:21,373 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445182159119_0020_m_000009_0: Shuffling to disk since 56695786 is greater than maxSingleShuffleLimit (32663142)
2015-10-19 17:59:21,389 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445182159119_0020_m_000009_0 decomp: 56695786 len: 56695790 to DISK
2015-10-19 18:00:08,611 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 56695790 bytes from map-output for attempt_1445182159119_0020_m_000009_0
2015-10-19 18:00:08,627 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: 04DN8IQ.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 96601ms
2015-10-19 18:00:08,627 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: EventFetcher is interrupted.. Returning
2015-10-19 18:00:08,674 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: finalMerge called with 0 in-memory map-outputs and 10 on-disk map-outputs
2015-10-19 18:00:08,690 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 10 files, 601334942 bytes from disk
2015-10-19 18:00:08,690 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 0 segments, 0 bytes from memory into reduce
2015-10-19 18:00:08,690 INFO [main] org.apache.hadoop.mapred.Merger: Merging 10 sorted segments
2015-10-19 18:00:08,736 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 10 segments left of total size: 601334842 bytes
2015-10-19 18:00:09,002 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: mapred.skip.on is deprecated. Instead, use mapreduce.job.skiprecords
2015-10-19 18:01:03,788 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445182159119_0020_r_000000_0 is done. And is in the process of committing
2015-10-19 18:01:03,835 INFO [main] org.apache.hadoop.mapred.Task: Task attempt_1445182159119_0020_r_000000_0 is allowed to commit now
2015-10-19 18:01:03,850 INFO [main] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Saved output of task 'attempt_1445182159119_0020_r_000000_0' to hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/task_1445182159119_0020_r_000000
2015-10-19 18:01:03,897 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445182159119_0020_r_000000_0' done.
2015-10-19 18:01:04,007 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Stopping ReduceTask metrics system...
2015-10-19 18:01:04,007 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: ReduceTask metrics system stopped.
2015-10-19 18:01:04,007 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: ReduceTask metrics system shutdown complete.
