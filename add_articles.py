"""
Simple CLI tool to quickly add your own articles to the search system
"""

import sys
import os
from custom_dataset_manager import CustomDataset<PERSON>anager

def add_article_interactive():
    """Interactive article addition"""
    print("📝 Add New Article to ONGC Knowledge System")
    print("=" * 50)
    
    manager = CustomDatasetManager()
    
    title = input("📄 Article Title: ").strip()
    if not title:
        print("❌ Title is required!")
        return False
    
    print("\n📝 Article Content (Press Ctrl+Z then Enter on Windows, or Ctrl+D on Mac/Linux when done):")
    print("=" * 50)
    
    content_lines = []
    try:
        while True:
            line = input()
            content_lines.append(line)
    except EOFError:
        pass
    
    content = '\n'.join(content_lines).strip()
    if not content:
        print("❌ Content is required!")
        return False
    
    print("\n📋 Additional Information:")
    category = input("📂 Category (default: General): ").strip() or "General"
    author = input("👤 Author (default: User): ").strip() or "User"
    tags_input = input("🏷️  Tags (comma-separated): ").strip()
    tags = [tag.strip() for tag in tags_input.split(",")] if tags_input else []
    
    # Add the article
    article = manager.add_article(
        title=title,
        content=content,
        category=category,
        author=author,
        tags=tags
    )
    
    manager.save_articles()
    
    print(f"\n✅ Article '{title}' added successfully!")
    print(f"📊 Total articles in system: {len(manager.articles)}")
    
    return True

def add_from_file(file_path, title=None, category="Imported", author="User"):
    """Add article from file"""
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        if not content:
            print(f"❌ File is empty: {file_path}")
            return False
        
        filename = os.path.basename(file_path)
        article_title = title or f"Document: {filename}"
        
        manager = CustomDatasetManager()
        manager.add_article(
            title=article_title,
            content=content,
            category=category,
            author=author,
            tags=["imported", "file"],
            source=file_path
        )
        
        manager.save_articles()
        
        print(f"✅ Added article from file: {filename}")
        print(f"📊 Total articles in system: {len(manager.articles)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False

def add_from_directory(directory, extension=".txt", category="Documents", author="User"):
    """Add all files from directory"""
    if not os.path.exists(directory):
        print(f"❌ Directory not found: {directory}")
        return False
    
    manager = CustomDatasetManager()
    added_count = 0
    
    for filename in os.listdir(directory):
        if filename.endswith(extension):
            file_path = os.path.join(directory, filename)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                
                if content and len(content) > 10:  # Skip very short files
                    manager.add_article(
                        title=f"Document: {filename}",
                        content=content,
                        category=category,
                        author=author,
                        tags=["imported", "directory", extension.replace(".", "")],
                        source=file_path
                    )
                    added_count += 1
                    print(f"✅ Added: {filename}")
                
            except Exception as e:
                print(f"⚠️ Skipped {filename}: {e}")
    
    if added_count > 0:
        manager.save_articles()
        print(f"\n🎉 Added {added_count} articles from directory!")
        print(f"📊 Total articles in system: {len(manager.articles)}")
    else:
        print("❌ No valid files found to add.")
    
    return added_count > 0

def show_current_articles():
    """Show current articles in the system"""
    manager = CustomDatasetManager()
    
    if not manager.articles:
        print("📭 No articles in the system yet.")
        return
    
    print(f"📚 Current Articles ({len(manager.articles)} total):")
    print("=" * 60)
    
    for article in manager.articles[-10:]:  # Show last 10
        content_preview = article['content'][:100] + "..." if len(article['content']) > 100 else article['content']
        print(f"{article['id']:3d}. {article['title']}")
        print(f"     📂 {article['category']} | 👤 {article['author']} | 📅 {article['date']}")
        print(f"     📝 {content_preview}")
        print()
    
    if len(manager.articles) > 10:
        print(f"... and {len(manager.articles) - 10} more articles")

def main():
    """Main CLI interface"""
    if len(sys.argv) == 1:
        # Interactive mode
        print("🔍 ONGC Knowledge System - Article Manager")
        print("=" * 50)
        print("Choose an option:")
        print("1. Add article interactively")
        print("2. Add from text file")
        print("3. Add from directory")
        print("4. Show current articles")
        print("5. Exit")
        
        while True:
            choice = input("\nEnter choice (1-5): ").strip()
            
            if choice == "1":
                add_article_interactive()
            elif choice == "2":
                file_path = input("Enter file path: ").strip()
                title = input("Enter title (optional): ").strip() or None
                category = input("Enter category (default: Imported): ").strip() or "Imported"
                add_from_file(file_path, title, category)
            elif choice == "3":
                directory = input("Enter directory path: ").strip()
                extension = input("File extension (default: .txt): ").strip() or ".txt"
                category = input("Category (default: Documents): ").strip() or "Documents"
                add_from_directory(directory, extension, category)
            elif choice == "4":
                show_current_articles()
            elif choice == "5":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please try again.")
    
    else:
        # Command line arguments
        if sys.argv[1] == "add":
            if len(sys.argv) < 3:
                print("Usage: python add_articles.py add <file_path> [title] [category]")
                return
            
            file_path = sys.argv[2]
            title = sys.argv[3] if len(sys.argv) > 3 else None
            category = sys.argv[4] if len(sys.argv) > 4 else "Imported"
            
            add_from_file(file_path, title, category)
        
        elif sys.argv[1] == "dir":
            if len(sys.argv) < 3:
                print("Usage: python add_articles.py dir <directory_path> [extension] [category]")
                return
            
            directory = sys.argv[2]
            extension = sys.argv[3] if len(sys.argv) > 3 else ".txt"
            category = sys.argv[4] if len(sys.argv) > 4 else "Documents"
            
            add_from_directory(directory, extension, category)
        
        elif sys.argv[1] == "list":
            show_current_articles()
        
        else:
            print("Usage:")
            print("  python add_articles.py                    # Interactive mode")
            print("  python add_articles.py add <file>         # Add from file")
            print("  python add_articles.py dir <directory>    # Add from directory")
            print("  python add_articles.py list               # Show current articles")

if __name__ == "__main__":
    main()
