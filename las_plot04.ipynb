{"cells": [{"cell_type": "code", "execution_count": 1, "id": "10dd497a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import missingno as msn"]}, {"cell_type": "code", "execution_count": 2, "id": "877c3a4d", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(\"C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/xeek_train_subset.csv\")"]}, {"cell_type": "code", "execution_count": 3, "id": "708a836c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>WELL</th>\n", "      <th>DEPTH_MD</th>\n", "      <th>X_LOC</th>\n", "      <th>Y_LOC</th>\n", "      <th>Z_LOC</th>\n", "      <th>GROUP</th>\n", "      <th>FORMATION</th>\n", "      <th>CALI</th>\n", "      <th>RSHA</th>\n", "      <th>RMED</th>\n", "      <th>...</th>\n", "      <th>ROP</th>\n", "      <th>DTS</th>\n", "      <th>DCAL</th>\n", "      <th>DRHO</th>\n", "      <th>MUDWEIGHT</th>\n", "      <th>RMIC</th>\n", "      <th>ROPA</th>\n", "      <th>RXO</th>\n", "      <th>FORCE_2020_LITHOFACIES_LITHOLOGY</th>\n", "      <th>FORCE_2020_LITHOFACIES_CONFIDENCE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>15/9-13</td>\n", "      <td>494.528000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.501831</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>NaN</td>\n", "      <td>19.480835</td>\n", "      <td>NaN</td>\n", "      <td>1.611410</td>\n", "      <td>...</td>\n", "      <td>34.636410</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.574928</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>15/9-13</td>\n", "      <td>494.680000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.653809</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>NaN</td>\n", "      <td>19.468800</td>\n", "      <td>NaN</td>\n", "      <td>1.618070</td>\n", "      <td>...</td>\n", "      <td>34.636410</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.570188</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>15/9-13</td>\n", "      <td>494.832000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.805786</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>NaN</td>\n", "      <td>19.468800</td>\n", "      <td>NaN</td>\n", "      <td>1.626459</td>\n", "      <td>...</td>\n", "      <td>34.779556</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.574245</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>15/9-13</td>\n", "      <td>494.984000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.957794</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>NaN</td>\n", "      <td>19.459282</td>\n", "      <td>NaN</td>\n", "      <td>1.621594</td>\n", "      <td>...</td>\n", "      <td>39.965164</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.586315</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>15/9-13</td>\n", "      <td>495.136000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-470.109772</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>NaN</td>\n", "      <td>19.453100</td>\n", "      <td>NaN</td>\n", "      <td>1.602679</td>\n", "      <td>...</td>\n", "      <td>57.483765</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.597914</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133193</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.310396</td>\n", "      <td>476770.12500</td>\n", "      <td>6523587.5</td>\n", "      <td>-2180.926514</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.698007</td>\n", "      <td>NaN</td>\n", "      <td>5.382444</td>\n", "      <td>...</td>\n", "      <td>22.291321</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.107520</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133194</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.462396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.078613</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.721210</td>\n", "      <td>NaN</td>\n", "      <td>5.651109</td>\n", "      <td>...</td>\n", "      <td>22.230320</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.101821</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133195</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.614396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.230469</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.739136</td>\n", "      <td>NaN</td>\n", "      <td>5.870602</td>\n", "      <td>...</td>\n", "      <td>22.388054</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.097832</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133196</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.766396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.382324</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.749738</td>\n", "      <td>NaN</td>\n", "      <td>5.756996</td>\n", "      <td>...</td>\n", "      <td>23.331690</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.092334</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133197</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.918396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.534424</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.732694</td>\n", "      <td>NaN</td>\n", "      <td>5.544824</td>\n", "      <td>...</td>\n", "      <td>24.340328</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.090736</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>133198 rows × 29 columns</p>\n", "</div>"], "text/plain": ["           WELL     DEPTH_MD         X_LOC      Y_LOC        Z_LOC  \\\n", "0       15/9-13   494.528000  437641.96875  6470972.5  -469.501831   \n", "1       15/9-13   494.680000  437641.96875  6470972.5  -469.653809   \n", "2       15/9-13   494.832000  437641.96875  6470972.5  -469.805786   \n", "3       15/9-13   494.984000  437641.96875  6470972.5  -469.957794   \n", "4       15/9-13   495.136000  437641.96875  6470972.5  -470.109772   \n", "...         ...          ...           ...        ...          ...   \n", "133193  16/2-16  2207.310396  476770.12500  6523587.5 -2180.926514   \n", "133194  16/2-16  2207.462396  476770.15625  6523587.5 -2181.078613   \n", "133195  16/2-16  2207.614396  476770.15625  6523587.5 -2181.230469   \n", "133196  16/2-16  2207.766396  476770.15625  6523587.5 -2181.382324   \n", "133197  16/2-16  2207.918396  476770.15625  6523587.5 -2181.534424   \n", "\n", "                   GROUP      FORMATION       CALI  RSHA      RMED  ...  \\\n", "0           NORDLAND GP.            NaN  19.480835   NaN  1.611410  ...   \n", "1           NORDLAND GP.            NaN  19.468800   NaN  1.618070  ...   \n", "2           NORDLAND GP.            NaN  19.468800   NaN  1.626459  ...   \n", "3           NORDLAND GP.            NaN  19.459282   NaN  1.621594  ...   \n", "4           NORDLAND GP.            NaN  19.453100   NaN  1.602679  ...   \n", "...                  ...            ...        ...   ...       ...  ...   \n", "133193  ROTLIEGENDES GP.  Skagerrak Fm.   8.698007   NaN  5.382444  ...   \n", "133194  ROTLIEGENDES GP.  Skagerrak Fm.   8.721210   NaN  5.651109  ...   \n", "133195  ROTLIEGENDES GP.  Skagerrak Fm.   8.739136   NaN  5.870602  ...   \n", "133196  ROTLIEGENDES GP.  Skagerrak Fm.   8.749738   NaN  5.756996  ...   \n", "133197  ROTLIEGENDES GP.  Skagerrak Fm.   8.732694   NaN  5.544824  ...   \n", "\n", "              ROP  DTS  DCAL      DRHO  MUDWEIGHT  RMIC  ROPA  RXO  \\\n", "0       34.636410  NaN   NaN -0.574928        NaN   NaN   NaN  NaN   \n", "1       34.636410  NaN   NaN -0.570188        NaN   NaN   NaN  NaN   \n", "2       34.779556  NaN   NaN -0.574245        NaN   NaN   NaN  NaN   \n", "3       39.965164  NaN   NaN -0.586315        NaN   NaN   NaN  NaN   \n", "4       57.483765  NaN   NaN -0.597914        NaN   NaN   NaN  NaN   \n", "...           ...  ...   ...       ...        ...   ...   ...  ...   \n", "133193  22.291321  NaN   NaN  0.107520        NaN   NaN   NaN  NaN   \n", "133194  22.230320  NaN   NaN  0.101821        NaN   NaN   NaN  NaN   \n", "133195  22.388054  NaN   NaN  0.097832        NaN   NaN   NaN  NaN   \n", "133196  23.331690  NaN   NaN  0.092334        NaN   NaN   NaN  NaN   \n", "133197  24.340328  NaN   NaN  0.090736        NaN   NaN   NaN  NaN   \n", "\n", "        FORCE_2020_LITHOFACIES_LITHOLOGY  FORCE_2020_LITHOFACIES_CONFIDENCE  \n", "0                                  65000                                1.0  \n", "1                                  65000                                1.0  \n", "2                                  65000                                1.0  \n", "3                                  65000                                1.0  \n", "4                                  65000                                1.0  \n", "...                                  ...                                ...  \n", "133193                             30000                                2.0  \n", "133194                             30000                                2.0  \n", "133195                             30000                                2.0  \n", "133196                             30000                                2.0  \n", "133197                             30000                                2.0  \n", "\n", "[133198 rows x 29 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 4, "id": "7b5f4d4e", "metadata": {}, "outputs": [{"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 2500x1000 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["msn.matrix(df);"]}, {"cell_type": "code", "execution_count": 5, "id": "daed4ce7", "metadata": {}, "outputs": [{"data": {"text/plain": ["WELL                                      0\n", "DEPTH_MD                                  0\n", "X_LOC                                  7393\n", "Y_LOC                                  7393\n", "Z_LOC                                  7393\n", "GROUP                                     0\n", "FORMATION                             21566\n", "CALI                                    192\n", "RSHA                                  71159\n", "RMED                                   7642\n", "RDEP                                   7393\n", "RHOB                                  25145\n", "GR                                        0\n", "SGR                                  115848\n", "NPHI                                  41473\n", "PEF                                   32358\n", "DTC                                     563\n", "SP                                    39518\n", "BS                                    35669\n", "ROP                                    2744\n", "DTS                                  121014\n", "DCAL                                  76998\n", "DRHO                                  27659\n", "MUDWEIGHT                             31554\n", "RMIC                                 131563\n", "ROPA                                 119725\n", "RXO                                   94252\n", "FORCE_2020_LITHOFACIES_LITHOLOGY          0\n", "FORCE_2020_LITHOFACIES_CONFIDENCE        15\n", "dtype: int64"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df.isnull().sum()"]}, {"cell_type": "code", "execution_count": 6, "id": "2bc0b0a8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>WELL</th>\n", "      <th>DEPTH_MD</th>\n", "      <th>X_LOC</th>\n", "      <th>Y_LOC</th>\n", "      <th>Z_LOC</th>\n", "      <th>GROUP</th>\n", "      <th>FORMATION</th>\n", "      <th>CALI</th>\n", "      <th>RSHA</th>\n", "      <th>RMED</th>\n", "      <th>...</th>\n", "      <th>ROP</th>\n", "      <th>DTS</th>\n", "      <th>DCAL</th>\n", "      <th>DRHO</th>\n", "      <th>MUDWEIGHT</th>\n", "      <th>RMIC</th>\n", "      <th>ROPA</th>\n", "      <th>RXO</th>\n", "      <th>FORCE_2020_LITHOFACIES_LITHOLOGY</th>\n", "      <th>FORCE_2020_LITHOFACIES_CONFIDENCE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>15/9-13</td>\n", "      <td>494.528000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.501831</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>NaN</td>\n", "      <td>19.480835</td>\n", "      <td>NaN</td>\n", "      <td>1.611410</td>\n", "      <td>...</td>\n", "      <td>34.636410</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.574928</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>15/9-13</td>\n", "      <td>494.680000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.653809</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>NaN</td>\n", "      <td>19.468800</td>\n", "      <td>NaN</td>\n", "      <td>1.618070</td>\n", "      <td>...</td>\n", "      <td>34.636410</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.570188</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>15/9-13</td>\n", "      <td>494.832000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.805786</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>NaN</td>\n", "      <td>19.468800</td>\n", "      <td>NaN</td>\n", "      <td>1.626459</td>\n", "      <td>...</td>\n", "      <td>34.779556</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.574245</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>15/9-13</td>\n", "      <td>494.984000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.957794</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>NaN</td>\n", "      <td>19.459282</td>\n", "      <td>NaN</td>\n", "      <td>1.621594</td>\n", "      <td>...</td>\n", "      <td>39.965164</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.586315</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>15/9-13</td>\n", "      <td>495.136000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-470.109772</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>NaN</td>\n", "      <td>19.453100</td>\n", "      <td>NaN</td>\n", "      <td>1.602679</td>\n", "      <td>...</td>\n", "      <td>57.483765</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.597914</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133193</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.310396</td>\n", "      <td>476770.12500</td>\n", "      <td>6523587.5</td>\n", "      <td>-2180.926514</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.698007</td>\n", "      <td>NaN</td>\n", "      <td>5.382444</td>\n", "      <td>...</td>\n", "      <td>22.291321</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.107520</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133194</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.462396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.078613</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.721210</td>\n", "      <td>NaN</td>\n", "      <td>5.651109</td>\n", "      <td>...</td>\n", "      <td>22.230320</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.101821</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133195</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.614396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.230469</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.739136</td>\n", "      <td>NaN</td>\n", "      <td>5.870602</td>\n", "      <td>...</td>\n", "      <td>22.388054</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.097832</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133196</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.766396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.382324</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.749738</td>\n", "      <td>NaN</td>\n", "      <td>5.756996</td>\n", "      <td>...</td>\n", "      <td>23.331690</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.092334</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133197</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.918396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.534424</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.732694</td>\n", "      <td>NaN</td>\n", "      <td>5.544824</td>\n", "      <td>...</td>\n", "      <td>24.340328</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.090736</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>133198 rows × 29 columns</p>\n", "</div>"], "text/plain": ["           WELL     DEPTH_MD         X_LOC      Y_LOC        Z_LOC  \\\n", "0       15/9-13   494.528000  437641.96875  6470972.5  -469.501831   \n", "1       15/9-13   494.680000  437641.96875  6470972.5  -469.653809   \n", "2       15/9-13   494.832000  437641.96875  6470972.5  -469.805786   \n", "3       15/9-13   494.984000  437641.96875  6470972.5  -469.957794   \n", "4       15/9-13   495.136000  437641.96875  6470972.5  -470.109772   \n", "...         ...          ...           ...        ...          ...   \n", "133193  16/2-16  2207.310396  476770.12500  6523587.5 -2180.926514   \n", "133194  16/2-16  2207.462396  476770.15625  6523587.5 -2181.078613   \n", "133195  16/2-16  2207.614396  476770.15625  6523587.5 -2181.230469   \n", "133196  16/2-16  2207.766396  476770.15625  6523587.5 -2181.382324   \n", "133197  16/2-16  2207.918396  476770.15625  6523587.5 -2181.534424   \n", "\n", "                   GROUP      FORMATION       CALI  RSHA      RMED  ...  \\\n", "0           NORDLAND GP.            NaN  19.480835   NaN  1.611410  ...   \n", "1           NORDLAND GP.            NaN  19.468800   NaN  1.618070  ...   \n", "2           NORDLAND GP.            NaN  19.468800   NaN  1.626459  ...   \n", "3           NORDLAND GP.            NaN  19.459282   NaN  1.621594  ...   \n", "4           NORDLAND GP.            NaN  19.453100   NaN  1.602679  ...   \n", "...                  ...            ...        ...   ...       ...  ...   \n", "133193  ROTLIEGENDES GP.  Skagerrak Fm.   8.698007   NaN  5.382444  ...   \n", "133194  ROTLIEGENDES GP.  Skagerrak Fm.   8.721210   NaN  5.651109  ...   \n", "133195  ROTLIEGENDES GP.  Skagerrak Fm.   8.739136   NaN  5.870602  ...   \n", "133196  ROTLIEGENDES GP.  Skagerrak Fm.   8.749738   NaN  5.756996  ...   \n", "133197  ROTLIEGENDES GP.  Skagerrak Fm.   8.732694   NaN  5.544824  ...   \n", "\n", "              ROP  DTS  DCAL      DRHO  MUDWEIGHT  RMIC  ROPA  RXO  \\\n", "0       34.636410  NaN   NaN -0.574928        NaN   NaN   NaN  NaN   \n", "1       34.636410  NaN   NaN -0.570188        NaN   NaN   NaN  NaN   \n", "2       34.779556  NaN   NaN -0.574245        NaN   NaN   NaN  NaN   \n", "3       39.965164  NaN   NaN -0.586315        NaN   NaN   NaN  NaN   \n", "4       57.483765  NaN   NaN -0.597914        NaN   NaN   NaN  NaN   \n", "...           ...  ...   ...       ...        ...   ...   ...  ...   \n", "133193  22.291321  NaN   NaN  0.107520        NaN   NaN   NaN  NaN   \n", "133194  22.230320  NaN   NaN  0.101821        NaN   NaN   NaN  NaN   \n", "133195  22.388054  NaN   NaN  0.097832        NaN   NaN   NaN  NaN   \n", "133196  23.331690  NaN   NaN  0.092334        NaN   NaN   NaN  NaN   \n", "133197  24.340328  NaN   NaN  0.090736        NaN   NaN   NaN  NaN   \n", "\n", "        FORCE_2020_LITHOFACIES_LITHOLOGY  FORCE_2020_LITHOFACIES_CONFIDENCE  \n", "0                                  65000                                1.0  \n", "1                                  65000                                1.0  \n", "2                                  65000                                1.0  \n", "3                                  65000                                1.0  \n", "4                                  65000                                1.0  \n", "...                                  ...                                ...  \n", "133193                             30000                                2.0  \n", "133194                             30000                                2.0  \n", "133195                             30000                                2.0  \n", "133196                             30000                                2.0  \n", "133197                             30000                                2.0  \n", "\n", "[133198 rows x 29 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df[df.isnull().any(axis=1)]"]}, {"cell_type": "code", "execution_count": 7, "id": "1dfe2dfb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>WELL</th>\n", "      <th>DEPTH_MD</th>\n", "      <th>X_LOC</th>\n", "      <th>Y_LOC</th>\n", "      <th>Z_LOC</th>\n", "      <th>GROUP</th>\n", "      <th>FORMATION</th>\n", "      <th>CALI</th>\n", "      <th>RSHA</th>\n", "      <th>RMED</th>\n", "      <th>...</th>\n", "      <th>ROP</th>\n", "      <th>DTS</th>\n", "      <th>DCAL</th>\n", "      <th>DRHO</th>\n", "      <th>MUDWEIGHT</th>\n", "      <th>RMIC</th>\n", "      <th>ROPA</th>\n", "      <th>RXO</th>\n", "      <th>FORCE_2020_LITHOFACIES_LITHOLOGY</th>\n", "      <th>FORCE_2020_LITHOFACIES_CONFIDENCE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "<p>0 rows × 29 columns</p>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON><PERSON>, DEPTH_MD, X_LOC, Y_LOC, Z_LOC, GROUP, FORMATION, CALI, RSHA, RMED, RDEP, RHOB, GR, SGR, NPHI, PEF, DTC, SP, BS, ROP, DTS, DCAL, DRHO, MUDWEIGHT, RMIC, ROPA, RXO, FORCE_2020_LITHOFACIES_LITHOLOGY, FORCE_2020_LITHOFACIES_CONFIDENCE]\n", "Index: []\n", "\n", "[0 rows x 29 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df.dropna()"]}, {"cell_type": "code", "execution_count": 8, "id": "ae42fb37", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>WELL</th>\n", "      <th>DEPTH_MD</th>\n", "      <th>GROUP</th>\n", "      <th>GR</th>\n", "      <th>FORCE_2020_LITHOFACIES_LITHOLOGY</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>15/9-13</td>\n", "      <td>494.528000</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>80.200851</td>\n", "      <td>65000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>15/9-13</td>\n", "      <td>494.680000</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>79.262886</td>\n", "      <td>65000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>15/9-13</td>\n", "      <td>494.832000</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>74.821999</td>\n", "      <td>65000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>15/9-13</td>\n", "      <td>494.984000</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>72.878922</td>\n", "      <td>65000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>15/9-13</td>\n", "      <td>495.136000</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>71.729141</td>\n", "      <td>65000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133193</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.310396</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>127.456139</td>\n", "      <td>30000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133194</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.462396</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>128.403305</td>\n", "      <td>30000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133195</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.614396</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>127.829262</td>\n", "      <td>30000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133196</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.766396</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>126.552277</td>\n", "      <td>30000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133197</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.918396</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>128.842270</td>\n", "      <td>30000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>133198 rows × 5 columns</p>\n", "</div>"], "text/plain": ["           WELL     DEPTH_MD             GROUP          GR  \\\n", "0       15/9-13   494.528000      NORDLAND GP.   80.200851   \n", "1       15/9-13   494.680000      NORDLAND GP.   79.262886   \n", "2       15/9-13   494.832000      NORDLAND GP.   74.821999   \n", "3       15/9-13   494.984000      NORDLAND GP.   72.878922   \n", "4       15/9-13   495.136000      NORDLAND GP.   71.729141   \n", "...         ...          ...               ...         ...   \n", "133193  16/2-16  2207.310396  ROTLIEGENDES GP.  127.456139   \n", "133194  16/2-16  2207.462396  ROTLIEGENDES GP.  128.403305   \n", "133195  16/2-16  2207.614396  ROTLIEGENDES GP.  127.829262   \n", "133196  16/2-16  2207.766396  ROTLIEGENDES GP.  126.552277   \n", "133197  16/2-16  2207.918396  ROTLIEGENDES GP.  128.842270   \n", "\n", "        FORCE_2020_LITHOFACIES_LITHOLOGY  \n", "0                                  65000  \n", "1                                  65000  \n", "2                                  65000  \n", "3                                  65000  \n", "4                                  65000  \n", "...                                  ...  \n", "133193                             30000  \n", "133194                             30000  \n", "133195                             30000  \n", "133196                             30000  \n", "133197                             30000  \n", "\n", "[133198 rows x 5 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df.dropna(axis=1)"]}, {"cell_type": "code", "execution_count": 9, "id": "fed0d93c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>WELL</th>\n", "      <th>DEPTH_MD</th>\n", "      <th>X_LOC</th>\n", "      <th>Y_LOC</th>\n", "      <th>Z_LOC</th>\n", "      <th>GROUP</th>\n", "      <th>FORMATION</th>\n", "      <th>CALI</th>\n", "      <th>RSHA</th>\n", "      <th>RMED</th>\n", "      <th>...</th>\n", "      <th>ROP</th>\n", "      <th>DTS</th>\n", "      <th>DCAL</th>\n", "      <th>DRHO</th>\n", "      <th>MUDWEIGHT</th>\n", "      <th>RMIC</th>\n", "      <th>ROPA</th>\n", "      <th>RXO</th>\n", "      <th>FORCE_2020_LITHOFACIES_LITHOLOGY</th>\n", "      <th>FORCE_2020_LITHOFACIES_CONFIDENCE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>15/9-13</td>\n", "      <td>494.528000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.501831</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>1000</td>\n", "      <td>19.480835</td>\n", "      <td>1000.0</td>\n", "      <td>1.611410</td>\n", "      <td>...</td>\n", "      <td>34.636410</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>-0.574928</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>15/9-13</td>\n", "      <td>494.680000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.653809</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>1000</td>\n", "      <td>19.468800</td>\n", "      <td>1000.0</td>\n", "      <td>1.618070</td>\n", "      <td>...</td>\n", "      <td>34.636410</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>-0.570188</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>15/9-13</td>\n", "      <td>494.832000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.805786</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>1000</td>\n", "      <td>19.468800</td>\n", "      <td>1000.0</td>\n", "      <td>1.626459</td>\n", "      <td>...</td>\n", "      <td>34.779556</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>-0.574245</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>15/9-13</td>\n", "      <td>494.984000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.957794</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>1000</td>\n", "      <td>19.459282</td>\n", "      <td>1000.0</td>\n", "      <td>1.621594</td>\n", "      <td>...</td>\n", "      <td>39.965164</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>-0.586315</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>15/9-13</td>\n", "      <td>495.136000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-470.109772</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>1000</td>\n", "      <td>19.453100</td>\n", "      <td>1000.0</td>\n", "      <td>1.602679</td>\n", "      <td>...</td>\n", "      <td>57.483765</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>-0.597914</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133193</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.310396</td>\n", "      <td>476770.12500</td>\n", "      <td>6523587.5</td>\n", "      <td>-2180.926514</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.698007</td>\n", "      <td>1000.0</td>\n", "      <td>5.382444</td>\n", "      <td>...</td>\n", "      <td>22.291321</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>0.107520</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133194</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.462396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.078613</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.721210</td>\n", "      <td>1000.0</td>\n", "      <td>5.651109</td>\n", "      <td>...</td>\n", "      <td>22.230320</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>0.101821</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133195</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.614396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.230469</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.739136</td>\n", "      <td>1000.0</td>\n", "      <td>5.870602</td>\n", "      <td>...</td>\n", "      <td>22.388054</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>0.097832</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133196</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.766396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.382324</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.749738</td>\n", "      <td>1000.0</td>\n", "      <td>5.756996</td>\n", "      <td>...</td>\n", "      <td>23.331690</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>0.092334</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133197</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.918396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.534424</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.732694</td>\n", "      <td>1000.0</td>\n", "      <td>5.544824</td>\n", "      <td>...</td>\n", "      <td>24.340328</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>0.090736</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>133198 rows × 29 columns</p>\n", "</div>"], "text/plain": ["           WELL     DEPTH_MD         X_LOC      Y_LOC        Z_LOC  \\\n", "0       15/9-13   494.528000  437641.96875  6470972.5  -469.501831   \n", "1       15/9-13   494.680000  437641.96875  6470972.5  -469.653809   \n", "2       15/9-13   494.832000  437641.96875  6470972.5  -469.805786   \n", "3       15/9-13   494.984000  437641.96875  6470972.5  -469.957794   \n", "4       15/9-13   495.136000  437641.96875  6470972.5  -470.109772   \n", "...         ...          ...           ...        ...          ...   \n", "133193  16/2-16  2207.310396  476770.12500  6523587.5 -2180.926514   \n", "133194  16/2-16  2207.462396  476770.15625  6523587.5 -2181.078613   \n", "133195  16/2-16  2207.614396  476770.15625  6523587.5 -2181.230469   \n", "133196  16/2-16  2207.766396  476770.15625  6523587.5 -2181.382324   \n", "133197  16/2-16  2207.918396  476770.15625  6523587.5 -2181.534424   \n", "\n", "                   GROUP      FORMATION       CALI    RSHA      RMED  ...  \\\n", "0           NORDLAND GP.           1000  19.480835  1000.0  1.611410  ...   \n", "1           NORDLAND GP.           1000  19.468800  1000.0  1.618070  ...   \n", "2           NORDLAND GP.           1000  19.468800  1000.0  1.626459  ...   \n", "3           NORDLAND GP.           1000  19.459282  1000.0  1.621594  ...   \n", "4           NORDLAND GP.           1000  19.453100  1000.0  1.602679  ...   \n", "...                  ...            ...        ...     ...       ...  ...   \n", "133193  ROTLIEGENDES GP.  Skagerrak Fm.   8.698007  1000.0  5.382444  ...   \n", "133194  ROTLIEGENDES GP.  Skagerrak Fm.   8.721210  1000.0  5.651109  ...   \n", "133195  ROTLIEGENDES GP.  Skagerrak Fm.   8.739136  1000.0  5.870602  ...   \n", "133196  ROTLIEGENDES GP.  Skagerrak Fm.   8.749738  1000.0  5.756996  ...   \n", "133197  ROTLIEGENDES GP.  Skagerrak Fm.   8.732694  1000.0  5.544824  ...   \n", "\n", "              ROP     DTS    DCAL      DRHO  MUDWEIGHT    RMIC    ROPA  \\\n", "0       34.636410  1000.0  1000.0 -0.574928     1000.0  1000.0  1000.0   \n", "1       34.636410  1000.0  1000.0 -0.570188     1000.0  1000.0  1000.0   \n", "2       34.779556  1000.0  1000.0 -0.574245     1000.0  1000.0  1000.0   \n", "3       39.965164  1000.0  1000.0 -0.586315     1000.0  1000.0  1000.0   \n", "4       57.483765  1000.0  1000.0 -0.597914     1000.0  1000.0  1000.0   \n", "...           ...     ...     ...       ...        ...     ...     ...   \n", "133193  22.291321  1000.0  1000.0  0.107520     1000.0  1000.0  1000.0   \n", "133194  22.230320  1000.0  1000.0  0.101821     1000.0  1000.0  1000.0   \n", "133195  22.388054  1000.0  1000.0  0.097832     1000.0  1000.0  1000.0   \n", "133196  23.331690  1000.0  1000.0  0.092334     1000.0  1000.0  1000.0   \n", "133197  24.340328  1000.0  1000.0  0.090736     1000.0  1000.0  1000.0   \n", "\n", "           RXO  FORCE_2020_LITHOFACIES_LITHOLOGY  \\\n", "0       1000.0                             65000   \n", "1       1000.0                             65000   \n", "2       1000.0                             65000   \n", "3       1000.0                             65000   \n", "4       1000.0                             65000   \n", "...        ...                               ...   \n", "133193  1000.0                             30000   \n", "133194  1000.0                             30000   \n", "133195  1000.0                             30000   \n", "133196  1000.0                             30000   \n", "133197  1000.0                             30000   \n", "\n", "        FORCE_2020_LITHOFACIES_CONFIDENCE  \n", "0                                     1.0  \n", "1                                     1.0  \n", "2                                     1.0  \n", "3                                     1.0  \n", "4                                     1.0  \n", "...                                   ...  \n", "133193                                2.0  \n", "133194                                2.0  \n", "133195                                2.0  \n", "133196                                2.0  \n", "133197                                2.0  \n", "\n", "[133198 rows x 29 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df.fillna(1000)"]}, {"cell_type": "code", "execution_count": 10, "id": "e1284a7c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20636\\1193302488.py:1: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  df.fillna(method='ffill')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>WELL</th>\n", "      <th>DEPTH_MD</th>\n", "      <th>X_LOC</th>\n", "      <th>Y_LOC</th>\n", "      <th>Z_LOC</th>\n", "      <th>GROUP</th>\n", "      <th>FORMATION</th>\n", "      <th>CALI</th>\n", "      <th>RSHA</th>\n", "      <th>RMED</th>\n", "      <th>...</th>\n", "      <th>ROP</th>\n", "      <th>DTS</th>\n", "      <th>DCAL</th>\n", "      <th>DRHO</th>\n", "      <th>MUDWEIGHT</th>\n", "      <th>RMIC</th>\n", "      <th>ROPA</th>\n", "      <th>RXO</th>\n", "      <th>FORCE_2020_LITHOFACIES_LITHOLOGY</th>\n", "      <th>FORCE_2020_LITHOFACIES_CONFIDENCE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>15/9-13</td>\n", "      <td>494.528000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.501831</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>NaN</td>\n", "      <td>19.480835</td>\n", "      <td>NaN</td>\n", "      <td>1.611410</td>\n", "      <td>...</td>\n", "      <td>34.636410</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.574928</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>15/9-13</td>\n", "      <td>494.680000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.653809</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>NaN</td>\n", "      <td>19.468800</td>\n", "      <td>NaN</td>\n", "      <td>1.618070</td>\n", "      <td>...</td>\n", "      <td>34.636410</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.570188</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>15/9-13</td>\n", "      <td>494.832000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.805786</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>NaN</td>\n", "      <td>19.468800</td>\n", "      <td>NaN</td>\n", "      <td>1.626459</td>\n", "      <td>...</td>\n", "      <td>34.779556</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.574245</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>15/9-13</td>\n", "      <td>494.984000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.957794</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>NaN</td>\n", "      <td>19.459282</td>\n", "      <td>NaN</td>\n", "      <td>1.621594</td>\n", "      <td>...</td>\n", "      <td>39.965164</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.586315</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>15/9-13</td>\n", "      <td>495.136000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-470.109772</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>NaN</td>\n", "      <td>19.453100</td>\n", "      <td>NaN</td>\n", "      <td>1.602679</td>\n", "      <td>...</td>\n", "      <td>57.483765</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.597914</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133193</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.310396</td>\n", "      <td>476770.12500</td>\n", "      <td>6523587.5</td>\n", "      <td>-2180.926514</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.698007</td>\n", "      <td>2.080469</td>\n", "      <td>5.382444</td>\n", "      <td>...</td>\n", "      <td>22.291321</td>\n", "      <td>130.68544</td>\n", "      <td>0.064463</td>\n", "      <td>0.107520</td>\n", "      <td>1.378004</td>\n", "      <td>0.861109</td>\n", "      <td>22.08317</td>\n", "      <td>0.17</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133194</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.462396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.078613</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.721210</td>\n", "      <td>2.080469</td>\n", "      <td>5.651109</td>\n", "      <td>...</td>\n", "      <td>22.230320</td>\n", "      <td>130.68544</td>\n", "      <td>0.064463</td>\n", "      <td>0.101821</td>\n", "      <td>1.378004</td>\n", "      <td>0.861109</td>\n", "      <td>22.08317</td>\n", "      <td>0.17</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133195</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.614396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.230469</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.739136</td>\n", "      <td>2.080469</td>\n", "      <td>5.870602</td>\n", "      <td>...</td>\n", "      <td>22.388054</td>\n", "      <td>130.68544</td>\n", "      <td>0.064463</td>\n", "      <td>0.097832</td>\n", "      <td>1.378004</td>\n", "      <td>0.861109</td>\n", "      <td>22.08317</td>\n", "      <td>0.17</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133196</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.766396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.382324</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.749738</td>\n", "      <td>2.080469</td>\n", "      <td>5.756996</td>\n", "      <td>...</td>\n", "      <td>23.331690</td>\n", "      <td>130.68544</td>\n", "      <td>0.064463</td>\n", "      <td>0.092334</td>\n", "      <td>1.378004</td>\n", "      <td>0.861109</td>\n", "      <td>22.08317</td>\n", "      <td>0.17</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133197</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.918396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.534424</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.732694</td>\n", "      <td>2.080469</td>\n", "      <td>5.544824</td>\n", "      <td>...</td>\n", "      <td>24.340328</td>\n", "      <td>130.68544</td>\n", "      <td>0.064463</td>\n", "      <td>0.090736</td>\n", "      <td>1.378004</td>\n", "      <td>0.861109</td>\n", "      <td>22.08317</td>\n", "      <td>0.17</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>133198 rows × 29 columns</p>\n", "</div>"], "text/plain": ["           WELL     DEPTH_MD         X_LOC      Y_LOC        Z_LOC  \\\n", "0       15/9-13   494.528000  437641.96875  6470972.5  -469.501831   \n", "1       15/9-13   494.680000  437641.96875  6470972.5  -469.653809   \n", "2       15/9-13   494.832000  437641.96875  6470972.5  -469.805786   \n", "3       15/9-13   494.984000  437641.96875  6470972.5  -469.957794   \n", "4       15/9-13   495.136000  437641.96875  6470972.5  -470.109772   \n", "...         ...          ...           ...        ...          ...   \n", "133193  16/2-16  2207.310396  476770.12500  6523587.5 -2180.926514   \n", "133194  16/2-16  2207.462396  476770.15625  6523587.5 -2181.078613   \n", "133195  16/2-16  2207.614396  476770.15625  6523587.5 -2181.230469   \n", "133196  16/2-16  2207.766396  476770.15625  6523587.5 -2181.382324   \n", "133197  16/2-16  2207.918396  476770.15625  6523587.5 -2181.534424   \n", "\n", "                   GROUP      FORMATION       CALI      RSHA      RMED  ...  \\\n", "0           NORDLAND GP.            NaN  19.480835       NaN  1.611410  ...   \n", "1           NORDLAND GP.            NaN  19.468800       NaN  1.618070  ...   \n", "2           NORDLAND GP.            NaN  19.468800       NaN  1.626459  ...   \n", "3           NORDLAND GP.            NaN  19.459282       NaN  1.621594  ...   \n", "4           NORDLAND GP.            NaN  19.453100       NaN  1.602679  ...   \n", "...                  ...            ...        ...       ...       ...  ...   \n", "133193  ROTLIEGENDES GP.  Skagerrak Fm.   8.698007  2.080469  5.382444  ...   \n", "133194  ROTLIEGENDES GP.  Skagerrak Fm.   8.721210  2.080469  5.651109  ...   \n", "133195  ROTLIEGENDES GP.  Skagerrak Fm.   8.739136  2.080469  5.870602  ...   \n", "133196  ROTLIEGENDES GP.  Skagerrak Fm.   8.749738  2.080469  5.756996  ...   \n", "133197  ROTLIEGENDES GP.  Skagerrak Fm.   8.732694  2.080469  5.544824  ...   \n", "\n", "              ROP        DTS      DCAL      DRHO  MUDWEIGHT      RMIC  \\\n", "0       34.636410        NaN       NaN -0.574928        NaN       NaN   \n", "1       34.636410        NaN       NaN -0.570188        NaN       NaN   \n", "2       34.779556        NaN       NaN -0.574245        NaN       NaN   \n", "3       39.965164        NaN       NaN -0.586315        NaN       NaN   \n", "4       57.483765        NaN       NaN -0.597914        NaN       NaN   \n", "...           ...        ...       ...       ...        ...       ...   \n", "133193  22.291321  130.68544  0.064463  0.107520   1.378004  0.861109   \n", "133194  22.230320  130.68544  0.064463  0.101821   1.378004  0.861109   \n", "133195  22.388054  130.68544  0.064463  0.097832   1.378004  0.861109   \n", "133196  23.331690  130.68544  0.064463  0.092334   1.378004  0.861109   \n", "133197  24.340328  130.68544  0.064463  0.090736   1.378004  0.861109   \n", "\n", "            ROPA   RXO  FORCE_2020_LITHOFACIES_LITHOLOGY  \\\n", "0            NaN   NaN                             65000   \n", "1            NaN   NaN                             65000   \n", "2            NaN   NaN                             65000   \n", "3            NaN   NaN                             65000   \n", "4            NaN   NaN                             65000   \n", "...          ...   ...                               ...   \n", "133193  22.08317  0.17                             30000   \n", "133194  22.08317  0.17                             30000   \n", "133195  22.08317  0.17                             30000   \n", "133196  22.08317  0.17                             30000   \n", "133197  22.08317  0.17                             30000   \n", "\n", "        FORCE_2020_LITHOFACIES_CONFIDENCE  \n", "0                                     1.0  \n", "1                                     1.0  \n", "2                                     1.0  \n", "3                                     1.0  \n", "4                                     1.0  \n", "...                                   ...  \n", "133193                                2.0  \n", "133194                                2.0  \n", "133195                                2.0  \n", "133196                                2.0  \n", "133197                                2.0  \n", "\n", "[133198 rows x 29 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df.fillna(method='ffill')"]}, {"cell_type": "code", "execution_count": 11, "id": "b9d92bb2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20636\\2831856154.py:1: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  df.fillna(method='bfill')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>WELL</th>\n", "      <th>DEPTH_MD</th>\n", "      <th>X_LOC</th>\n", "      <th>Y_LOC</th>\n", "      <th>Z_LOC</th>\n", "      <th>GROUP</th>\n", "      <th>FORMATION</th>\n", "      <th>CALI</th>\n", "      <th>RSHA</th>\n", "      <th>RMED</th>\n", "      <th>...</th>\n", "      <th>ROP</th>\n", "      <th>DTS</th>\n", "      <th>DCAL</th>\n", "      <th>DRHO</th>\n", "      <th>MUDWEIGHT</th>\n", "      <th>RMIC</th>\n", "      <th>ROPA</th>\n", "      <th>RXO</th>\n", "      <th>FORCE_2020_LITHOFACIES_LITHOLOGY</th>\n", "      <th>FORCE_2020_LITHOFACIES_CONFIDENCE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>15/9-13</td>\n", "      <td>494.528000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.501831</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>Utsira Fm.</td>\n", "      <td>19.480835</td>\n", "      <td>0.297633</td>\n", "      <td>1.611410</td>\n", "      <td>...</td>\n", "      <td>34.636410</td>\n", "      <td>166.533279</td>\n", "      <td>-6.611575</td>\n", "      <td>-0.574928</td>\n", "      <td>0.129413</td>\n", "      <td>2.276416</td>\n", "      <td>8.825735</td>\n", "      <td>0.271762</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>15/9-13</td>\n", "      <td>494.680000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.653809</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>Utsira Fm.</td>\n", "      <td>19.468800</td>\n", "      <td>0.297633</td>\n", "      <td>1.618070</td>\n", "      <td>...</td>\n", "      <td>34.636410</td>\n", "      <td>166.533279</td>\n", "      <td>-6.611575</td>\n", "      <td>-0.570188</td>\n", "      <td>0.129413</td>\n", "      <td>2.276416</td>\n", "      <td>8.825735</td>\n", "      <td>0.271762</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>15/9-13</td>\n", "      <td>494.832000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.805786</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>Utsira Fm.</td>\n", "      <td>19.468800</td>\n", "      <td>0.297633</td>\n", "      <td>1.626459</td>\n", "      <td>...</td>\n", "      <td>34.779556</td>\n", "      <td>166.533279</td>\n", "      <td>-6.611575</td>\n", "      <td>-0.574245</td>\n", "      <td>0.129413</td>\n", "      <td>2.276416</td>\n", "      <td>8.825735</td>\n", "      <td>0.271762</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>15/9-13</td>\n", "      <td>494.984000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.957794</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>Utsira Fm.</td>\n", "      <td>19.459282</td>\n", "      <td>0.297633</td>\n", "      <td>1.621594</td>\n", "      <td>...</td>\n", "      <td>39.965164</td>\n", "      <td>166.533279</td>\n", "      <td>-6.611575</td>\n", "      <td>-0.586315</td>\n", "      <td>0.129413</td>\n", "      <td>2.276416</td>\n", "      <td>8.825735</td>\n", "      <td>0.271762</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>15/9-13</td>\n", "      <td>495.136000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-470.109772</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>Utsira Fm.</td>\n", "      <td>19.453100</td>\n", "      <td>0.297633</td>\n", "      <td>1.602679</td>\n", "      <td>...</td>\n", "      <td>57.483765</td>\n", "      <td>166.533279</td>\n", "      <td>-6.611575</td>\n", "      <td>-0.597914</td>\n", "      <td>0.129413</td>\n", "      <td>2.276416</td>\n", "      <td>8.825735</td>\n", "      <td>0.271762</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133193</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.310396</td>\n", "      <td>476770.12500</td>\n", "      <td>6523587.5</td>\n", "      <td>-2180.926514</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.698007</td>\n", "      <td>NaN</td>\n", "      <td>5.382444</td>\n", "      <td>...</td>\n", "      <td>22.291321</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.107520</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133194</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.462396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.078613</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.721210</td>\n", "      <td>NaN</td>\n", "      <td>5.651109</td>\n", "      <td>...</td>\n", "      <td>22.230320</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.101821</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133195</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.614396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.230469</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.739136</td>\n", "      <td>NaN</td>\n", "      <td>5.870602</td>\n", "      <td>...</td>\n", "      <td>22.388054</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.097832</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133196</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.766396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.382324</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.749738</td>\n", "      <td>NaN</td>\n", "      <td>5.756996</td>\n", "      <td>...</td>\n", "      <td>23.331690</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.092334</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133197</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.918396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.534424</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.732694</td>\n", "      <td>NaN</td>\n", "      <td>5.544824</td>\n", "      <td>...</td>\n", "      <td>24.340328</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.090736</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>133198 rows × 29 columns</p>\n", "</div>"], "text/plain": ["           WELL     DEPTH_MD         X_LOC      Y_LOC        Z_LOC  \\\n", "0       15/9-13   494.528000  437641.96875  6470972.5  -469.501831   \n", "1       15/9-13   494.680000  437641.96875  6470972.5  -469.653809   \n", "2       15/9-13   494.832000  437641.96875  6470972.5  -469.805786   \n", "3       15/9-13   494.984000  437641.96875  6470972.5  -469.957794   \n", "4       15/9-13   495.136000  437641.96875  6470972.5  -470.109772   \n", "...         ...          ...           ...        ...          ...   \n", "133193  16/2-16  2207.310396  476770.12500  6523587.5 -2180.926514   \n", "133194  16/2-16  2207.462396  476770.15625  6523587.5 -2181.078613   \n", "133195  16/2-16  2207.614396  476770.15625  6523587.5 -2181.230469   \n", "133196  16/2-16  2207.766396  476770.15625  6523587.5 -2181.382324   \n", "133197  16/2-16  2207.918396  476770.15625  6523587.5 -2181.534424   \n", "\n", "                   GROUP      FORMATION       CALI      RSHA      RMED  ...  \\\n", "0           NORDLAND GP.     Utsira Fm.  19.480835  0.297633  1.611410  ...   \n", "1           NORDLAND GP.     Utsira Fm.  19.468800  0.297633  1.618070  ...   \n", "2           NORDLAND GP.     Utsira Fm.  19.468800  0.297633  1.626459  ...   \n", "3           NORDLAND GP.     Utsira Fm.  19.459282  0.297633  1.621594  ...   \n", "4           NORDLAND GP.     Utsira Fm.  19.453100  0.297633  1.602679  ...   \n", "...                  ...            ...        ...       ...       ...  ...   \n", "133193  ROTLIEGENDES GP.  Skagerrak Fm.   8.698007       NaN  5.382444  ...   \n", "133194  ROTLIEGENDES GP.  Skagerrak Fm.   8.721210       NaN  5.651109  ...   \n", "133195  ROTLIEGENDES GP.  Skagerrak Fm.   8.739136       NaN  5.870602  ...   \n", "133196  ROTLIEGENDES GP.  Skagerrak Fm.   8.749738       NaN  5.756996  ...   \n", "133197  ROTLIEGENDES GP.  Skagerrak Fm.   8.732694       NaN  5.544824  ...   \n", "\n", "              ROP         DTS      DCAL      DRHO  MUDWEIGHT      RMIC  \\\n", "0       34.636410  166.533279 -6.611575 -0.574928   0.129413  2.276416   \n", "1       34.636410  166.533279 -6.611575 -0.570188   0.129413  2.276416   \n", "2       34.779556  166.533279 -6.611575 -0.574245   0.129413  2.276416   \n", "3       39.965164  166.533279 -6.611575 -0.586315   0.129413  2.276416   \n", "4       57.483765  166.533279 -6.611575 -0.597914   0.129413  2.276416   \n", "...           ...         ...       ...       ...        ...       ...   \n", "133193  22.291321         NaN       NaN  0.107520        NaN       NaN   \n", "133194  22.230320         NaN       NaN  0.101821        NaN       NaN   \n", "133195  22.388054         NaN       NaN  0.097832        NaN       NaN   \n", "133196  23.331690         NaN       NaN  0.092334        NaN       NaN   \n", "133197  24.340328         NaN       NaN  0.090736        NaN       NaN   \n", "\n", "            ROPA       RXO  FORCE_2020_LITHOFACIES_LITHOLOGY  \\\n", "0       8.825735  0.271762                             65000   \n", "1       8.825735  0.271762                             65000   \n", "2       8.825735  0.271762                             65000   \n", "3       8.825735  0.271762                             65000   \n", "4       8.825735  0.271762                             65000   \n", "...          ...       ...                               ...   \n", "133193       NaN       NaN                             30000   \n", "133194       NaN       NaN                             30000   \n", "133195       NaN       NaN                             30000   \n", "133196       NaN       NaN                             30000   \n", "133197       NaN       NaN                             30000   \n", "\n", "        FORCE_2020_LITHOFACIES_CONFIDENCE  \n", "0                                     1.0  \n", "1                                     1.0  \n", "2                                     1.0  \n", "3                                     1.0  \n", "4                                     1.0  \n", "...                                   ...  \n", "133193                                2.0  \n", "133194                                2.0  \n", "133195                                2.0  \n", "133196                                2.0  \n", "133197                                2.0  \n", "\n", "[133198 rows x 29 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df.fillna(method='bfill')"]}, {"cell_type": "code", "execution_count": 12, "id": "642d482e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20636\\1321276365.py:1: FutureWarning: DataFrame.interpolate with object dtype is deprecated and will raise in a future version. Call obj.infer_objects(copy=False) before interpolating instead.\n", "  df.interpolate(method='linear')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>WELL</th>\n", "      <th>DEPTH_MD</th>\n", "      <th>X_LOC</th>\n", "      <th>Y_LOC</th>\n", "      <th>Z_LOC</th>\n", "      <th>GROUP</th>\n", "      <th>FORMATION</th>\n", "      <th>CALI</th>\n", "      <th>RSHA</th>\n", "      <th>RMED</th>\n", "      <th>...</th>\n", "      <th>ROP</th>\n", "      <th>DTS</th>\n", "      <th>DCAL</th>\n", "      <th>DRHO</th>\n", "      <th>MUDWEIGHT</th>\n", "      <th>RMIC</th>\n", "      <th>ROPA</th>\n", "      <th>RXO</th>\n", "      <th>FORCE_2020_LITHOFACIES_LITHOLOGY</th>\n", "      <th>FORCE_2020_LITHOFACIES_CONFIDENCE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>15/9-13</td>\n", "      <td>494.528000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.501831</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>NaN</td>\n", "      <td>19.480835</td>\n", "      <td>NaN</td>\n", "      <td>1.611410</td>\n", "      <td>...</td>\n", "      <td>34.636410</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.574928</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>15/9-13</td>\n", "      <td>494.680000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.653809</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>NaN</td>\n", "      <td>19.468800</td>\n", "      <td>NaN</td>\n", "      <td>1.618070</td>\n", "      <td>...</td>\n", "      <td>34.636410</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.570188</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>15/9-13</td>\n", "      <td>494.832000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.805786</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>NaN</td>\n", "      <td>19.468800</td>\n", "      <td>NaN</td>\n", "      <td>1.626459</td>\n", "      <td>...</td>\n", "      <td>34.779556</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.574245</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>15/9-13</td>\n", "      <td>494.984000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-469.957794</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>NaN</td>\n", "      <td>19.459282</td>\n", "      <td>NaN</td>\n", "      <td>1.621594</td>\n", "      <td>...</td>\n", "      <td>39.965164</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.586315</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>15/9-13</td>\n", "      <td>495.136000</td>\n", "      <td>437641.96875</td>\n", "      <td>6470972.5</td>\n", "      <td>-470.109772</td>\n", "      <td>NORDLAND GP.</td>\n", "      <td>NaN</td>\n", "      <td>19.453100</td>\n", "      <td>NaN</td>\n", "      <td>1.602679</td>\n", "      <td>...</td>\n", "      <td>57.483765</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.597914</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133193</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.310396</td>\n", "      <td>476770.12500</td>\n", "      <td>6523587.5</td>\n", "      <td>-2180.926514</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.698007</td>\n", "      <td>2.080469</td>\n", "      <td>5.382444</td>\n", "      <td>...</td>\n", "      <td>22.291321</td>\n", "      <td>130.68544</td>\n", "      <td>0.064463</td>\n", "      <td>0.107520</td>\n", "      <td>1.378004</td>\n", "      <td>0.861109</td>\n", "      <td>22.08317</td>\n", "      <td>0.17</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133194</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.462396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.078613</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.721210</td>\n", "      <td>2.080469</td>\n", "      <td>5.651109</td>\n", "      <td>...</td>\n", "      <td>22.230320</td>\n", "      <td>130.68544</td>\n", "      <td>0.064463</td>\n", "      <td>0.101821</td>\n", "      <td>1.378004</td>\n", "      <td>0.861109</td>\n", "      <td>22.08317</td>\n", "      <td>0.17</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133195</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.614396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.230469</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.739136</td>\n", "      <td>2.080469</td>\n", "      <td>5.870602</td>\n", "      <td>...</td>\n", "      <td>22.388054</td>\n", "      <td>130.68544</td>\n", "      <td>0.064463</td>\n", "      <td>0.097832</td>\n", "      <td>1.378004</td>\n", "      <td>0.861109</td>\n", "      <td>22.08317</td>\n", "      <td>0.17</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133196</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.766396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.382324</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.749738</td>\n", "      <td>2.080469</td>\n", "      <td>5.756996</td>\n", "      <td>...</td>\n", "      <td>23.331690</td>\n", "      <td>130.68544</td>\n", "      <td>0.064463</td>\n", "      <td>0.092334</td>\n", "      <td>1.378004</td>\n", "      <td>0.861109</td>\n", "      <td>22.08317</td>\n", "      <td>0.17</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133197</th>\n", "      <td>16/2-16</td>\n", "      <td>2207.918396</td>\n", "      <td>476770.15625</td>\n", "      <td>6523587.5</td>\n", "      <td>-2181.534424</td>\n", "      <td>ROTLIEGENDES GP.</td>\n", "      <td>Skagerrak Fm.</td>\n", "      <td>8.732694</td>\n", "      <td>2.080469</td>\n", "      <td>5.544824</td>\n", "      <td>...</td>\n", "      <td>24.340328</td>\n", "      <td>130.68544</td>\n", "      <td>0.064463</td>\n", "      <td>0.090736</td>\n", "      <td>1.378004</td>\n", "      <td>0.861109</td>\n", "      <td>22.08317</td>\n", "      <td>0.17</td>\n", "      <td>30000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>133198 rows × 29 columns</p>\n", "</div>"], "text/plain": ["           WELL     DEPTH_MD         X_LOC      Y_LOC        Z_LOC  \\\n", "0       15/9-13   494.528000  437641.96875  6470972.5  -469.501831   \n", "1       15/9-13   494.680000  437641.96875  6470972.5  -469.653809   \n", "2       15/9-13   494.832000  437641.96875  6470972.5  -469.805786   \n", "3       15/9-13   494.984000  437641.96875  6470972.5  -469.957794   \n", "4       15/9-13   495.136000  437641.96875  6470972.5  -470.109772   \n", "...         ...          ...           ...        ...          ...   \n", "133193  16/2-16  2207.310396  476770.12500  6523587.5 -2180.926514   \n", "133194  16/2-16  2207.462396  476770.15625  6523587.5 -2181.078613   \n", "133195  16/2-16  2207.614396  476770.15625  6523587.5 -2181.230469   \n", "133196  16/2-16  2207.766396  476770.15625  6523587.5 -2181.382324   \n", "133197  16/2-16  2207.918396  476770.15625  6523587.5 -2181.534424   \n", "\n", "                   GROUP      FORMATION       CALI      RSHA      RMED  ...  \\\n", "0           NORDLAND GP.            NaN  19.480835       NaN  1.611410  ...   \n", "1           NORDLAND GP.            NaN  19.468800       NaN  1.618070  ...   \n", "2           NORDLAND GP.            NaN  19.468800       NaN  1.626459  ...   \n", "3           NORDLAND GP.            NaN  19.459282       NaN  1.621594  ...   \n", "4           NORDLAND GP.            NaN  19.453100       NaN  1.602679  ...   \n", "...                  ...            ...        ...       ...       ...  ...   \n", "133193  ROTLIEGENDES GP.  Skagerrak Fm.   8.698007  2.080469  5.382444  ...   \n", "133194  ROTLIEGENDES GP.  Skagerrak Fm.   8.721210  2.080469  5.651109  ...   \n", "133195  ROTLIEGENDES GP.  Skagerrak Fm.   8.739136  2.080469  5.870602  ...   \n", "133196  ROTLIEGENDES GP.  Skagerrak Fm.   8.749738  2.080469  5.756996  ...   \n", "133197  ROTLIEGENDES GP.  Skagerrak Fm.   8.732694  2.080469  5.544824  ...   \n", "\n", "              ROP        DTS      DCAL      DRHO  MUDWEIGHT      RMIC  \\\n", "0       34.636410        NaN       NaN -0.574928        NaN       NaN   \n", "1       34.636410        NaN       NaN -0.570188        NaN       NaN   \n", "2       34.779556        NaN       NaN -0.574245        NaN       NaN   \n", "3       39.965164        NaN       NaN -0.586315        NaN       NaN   \n", "4       57.483765        NaN       NaN -0.597914        NaN       NaN   \n", "...           ...        ...       ...       ...        ...       ...   \n", "133193  22.291321  130.68544  0.064463  0.107520   1.378004  0.861109   \n", "133194  22.230320  130.68544  0.064463  0.101821   1.378004  0.861109   \n", "133195  22.388054  130.68544  0.064463  0.097832   1.378004  0.861109   \n", "133196  23.331690  130.68544  0.064463  0.092334   1.378004  0.861109   \n", "133197  24.340328  130.68544  0.064463  0.090736   1.378004  0.861109   \n", "\n", "            ROPA   RXO  FORCE_2020_LITHOFACIES_LITHOLOGY  \\\n", "0            NaN   NaN                             65000   \n", "1            NaN   NaN                             65000   \n", "2            NaN   NaN                             65000   \n", "3            NaN   NaN                             65000   \n", "4            NaN   NaN                             65000   \n", "...          ...   ...                               ...   \n", "133193  22.08317  0.17                             30000   \n", "133194  22.08317  0.17                             30000   \n", "133195  22.08317  0.17                             30000   \n", "133196  22.08317  0.17                             30000   \n", "133197  22.08317  0.17                             30000   \n", "\n", "        FORCE_2020_LITHOFACIES_CONFIDENCE  \n", "0                                     1.0  \n", "1                                     1.0  \n", "2                                     1.0  \n", "3                                     1.0  \n", "4                                     1.0  \n", "...                                   ...  \n", "133193                                2.0  \n", "133194                                2.0  \n", "133195                                2.0  \n", "133196                                2.0  \n", "133197                                2.0  \n", "\n", "[133198 rows x 29 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["df.interpolate(method='linear')"]}, {"cell_type": "code", "execution_count": 14, "id": "df72bd04", "metadata": {}, "outputs": [], "source": ["# df.fillna(value=df.mean())"]}, {"cell_type": "code", "execution_count": 15, "id": "ad78c712", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DEPTH_MD</th>\n", "      <th>X_LOC</th>\n", "      <th>Y_LOC</th>\n", "      <th>Z_LOC</th>\n", "      <th>CALI</th>\n", "      <th>RSHA</th>\n", "      <th>RMED</th>\n", "      <th>RDEP</th>\n", "      <th>RHOB</th>\n", "      <th>GR</th>\n", "      <th>...</th>\n", "      <th>ROP</th>\n", "      <th>DTS</th>\n", "      <th>DCAL</th>\n", "      <th>DRHO</th>\n", "      <th>MUDWEIGHT</th>\n", "      <th>RMIC</th>\n", "      <th>ROPA</th>\n", "      <th>RXO</th>\n", "      <th>FORCE_2020_LITHOFACIES_LITHOLOGY</th>\n", "      <th>FORCE_2020_LITHOFACIES_CONFIDENCE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>133198.000000</td>\n", "      <td>125805.000000</td>\n", "      <td>1.258050e+05</td>\n", "      <td>125805.000000</td>\n", "      <td>133006.000000</td>\n", "      <td>62039.000000</td>\n", "      <td>125556.000000</td>\n", "      <td>125805.000000</td>\n", "      <td>108053.000000</td>\n", "      <td>133198.000000</td>\n", "      <td>...</td>\n", "      <td>130454.000000</td>\n", "      <td>12184.000000</td>\n", "      <td>56200.000000</td>\n", "      <td>105539.000000</td>\n", "      <td>101644.000000</td>\n", "      <td>1635.000000</td>\n", "      <td>13473.000000</td>\n", "      <td>38946.000000</td>\n", "      <td>133198.000000</td>\n", "      <td>133183.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1792.944663</td>\n", "      <td>451235.640835</td>\n", "      <td>6.471392e+06</td>\n", "      <td>-1719.214522</td>\n", "      <td>13.199399</td>\n", "      <td>10.561825</td>\n", "      <td>1.708851</td>\n", "      <td>1.743774</td>\n", "      <td>2.199556</td>\n", "      <td>69.553872</td>\n", "      <td>...</td>\n", "      <td>28.163816</td>\n", "      <td>176.583712</td>\n", "      <td>0.972165</td>\n", "      <td>0.009307</td>\n", "      <td>2.291368</td>\n", "      <td>2.568148</td>\n", "      <td>25.253839</td>\n", "      <td>-112.944168</td>\n", "      <td>63261.582892</td>\n", "      <td>1.235796</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>739.441515</td>\n", "      <td>15299.395264</td>\n", "      <td>3.094449e+04</td>\n", "      <td>740.536678</td>\n", "      <td>3.561386</td>\n", "      <td>116.359983</td>\n", "      <td>9.127200</td>\n", "      <td>13.233330</td>\n", "      <td>0.236902</td>\n", "      <td>39.328728</td>\n", "      <td>...</td>\n", "      <td>45.592228</td>\n", "      <td>44.358622</td>\n", "      <td>43.000304</td>\n", "      <td>0.067312</td>\n", "      <td>17.882420</td>\n", "      <td>2.261201</td>\n", "      <td>14.319902</td>\n", "      <td>333.547482</td>\n", "      <td>13623.352961</td>\n", "      <td>0.480781</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>415.261599</td>\n", "      <td>436790.843750</td>\n", "      <td>6.429286e+06</td>\n", "      <td>-3246.156250</td>\n", "      <td>5.946157</td>\n", "      <td>0.130193</td>\n", "      <td>-0.008419</td>\n", "      <td>0.123068</td>\n", "      <td>1.366913</td>\n", "      <td>0.109284</td>\n", "      <td>...</td>\n", "      <td>0.018832</td>\n", "      <td>93.068260</td>\n", "      <td>-12.215459</td>\n", "      <td>-1.795894</td>\n", "      <td>0.125818</td>\n", "      <td>0.362225</td>\n", "      <td>0.556113</td>\n", "      <td>-999.900024</td>\n", "      <td>30000.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1182.822400</td>\n", "      <td>437640.781250</td>\n", "      <td>6.453743e+06</td>\n", "      <td>-2334.161865</td>\n", "      <td>11.381848</td>\n", "      <td>0.759227</td>\n", "      <td>0.779763</td>\n", "      <td>0.777323</td>\n", "      <td>2.029237</td>\n", "      <td>43.244637</td>\n", "      <td>...</td>\n", "      <td>9.650485</td>\n", "      <td>149.754013</td>\n", "      <td>0.007800</td>\n", "      <td>-0.007736</td>\n", "      <td>0.137800</td>\n", "      <td>1.387308</td>\n", "      <td>14.260232</td>\n", "      <td>0.537328</td>\n", "      <td>65000.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>1747.524495</td>\n", "      <td>444152.093750</td>\n", "      <td>6.463019e+06</td>\n", "      <td>-1626.893433</td>\n", "      <td>12.698571</td>\n", "      <td>0.997515</td>\n", "      <td>1.095681</td>\n", "      <td>1.130621</td>\n", "      <td>2.162121</td>\n", "      <td>66.266132</td>\n", "      <td>...</td>\n", "      <td>22.362363</td>\n", "      <td>162.537949</td>\n", "      <td>0.415419</td>\n", "      <td>0.003215</td>\n", "      <td>0.152180</td>\n", "      <td>2.133351</td>\n", "      <td>24.598738</td>\n", "      <td>0.879455</td>\n", "      <td>65000.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>2413.874901</td>\n", "      <td>460442.093750</td>\n", "      <td>6.478963e+06</td>\n", "      <td>-1119.113525</td>\n", "      <td>14.944049</td>\n", "      <td>1.450392</td>\n", "      <td>1.535653</td>\n", "      <td>1.644573</td>\n", "      <td>2.404375</td>\n", "      <td>93.808681</td>\n", "      <td>...</td>\n", "      <td>35.444058</td>\n", "      <td>190.082294</td>\n", "      <td>1.118416</td>\n", "      <td>0.022928</td>\n", "      <td>0.168955</td>\n", "      <td>2.860770</td>\n", "      <td>33.749100</td>\n", "      <td>1.519061</td>\n", "      <td>65030.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>3272.024000</td>\n", "      <td>476770.156250</td>\n", "      <td>6.539631e+06</td>\n", "      <td>-375.251495</td>\n", "      <td>25.717396</td>\n", "      <td>2193.904541</td>\n", "      <td>1796.209106</td>\n", "      <td>1856.935059</td>\n", "      <td>3.115611</td>\n", "      <td>804.298950</td>\n", "      <td>...</td>\n", "      <td>2224.647461</td>\n", "      <td>377.327789</td>\n", "      <td>10006.789063</td>\n", "      <td>0.433157</td>\n", "      <td>185.730927</td>\n", "      <td>23.590172</td>\n", "      <td>86.378632</td>\n", "      <td>2000.000000</td>\n", "      <td>99000.000000</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8 rows × 26 columns</p>\n", "</div>"], "text/plain": ["            DEPTH_MD          X_LOC         Y_LOC          Z_LOC  \\\n", "count  133198.000000  125805.000000  1.258050e+05  125805.000000   \n", "mean     1792.944663  451235.640835  6.471392e+06   -1719.214522   \n", "std       739.441515   15299.395264  3.094449e+04     740.536678   \n", "min       415.261599  436790.843750  6.429286e+06   -3246.156250   \n", "25%      1182.822400  437640.781250  6.453743e+06   -2334.161865   \n", "50%      1747.524495  444152.093750  6.463019e+06   -1626.893433   \n", "75%      2413.874901  460442.093750  6.478963e+06   -1119.113525   \n", "max      3272.024000  476770.156250  6.539631e+06    -375.251495   \n", "\n", "                CALI          RSHA           RMED           RDEP  \\\n", "count  133006.000000  62039.000000  125556.000000  125805.000000   \n", "mean       13.199399     10.561825       1.708851       1.743774   \n", "std         3.561386    116.359983       9.127200      13.233330   \n", "min         5.946157      0.130193      -0.008419       0.123068   \n", "25%        11.381848      0.759227       0.779763       0.777323   \n", "50%        12.698571      0.997515       1.095681       1.130621   \n", "75%        14.944049      1.450392       1.535653       1.644573   \n", "max        25.717396   2193.904541    1796.209106    1856.935059   \n", "\n", "                RHOB             GR  ...            ROP           DTS  \\\n", "count  108053.000000  133198.000000  ...  130454.000000  12184.000000   \n", "mean        2.199556      69.553872  ...      28.163816    176.583712   \n", "std         0.236902      39.328728  ...      45.592228     44.358622   \n", "min         1.366913       0.109284  ...       0.018832     93.068260   \n", "25%         2.029237      43.244637  ...       9.650485    149.754013   \n", "50%         2.162121      66.266132  ...      22.362363    162.537949   \n", "75%         2.404375      93.808681  ...      35.444058    190.082294   \n", "max         3.115611     804.298950  ...    2224.647461    377.327789   \n", "\n", "               DCAL           DRHO      MUDWEIGHT         RMIC          ROPA  \\\n", "count  56200.000000  105539.000000  101644.000000  1635.000000  13473.000000   \n", "mean       0.972165       0.009307       2.291368     2.568148     25.253839   \n", "std       43.000304       0.067312      17.882420     2.261201     14.319902   \n", "min      -12.215459      -1.795894       0.125818     0.362225      0.556113   \n", "25%        0.007800      -0.007736       0.137800     1.387308     14.260232   \n", "50%        0.415419       0.003215       0.152180     2.133351     24.598738   \n", "75%        1.118416       0.022928       0.168955     2.860770     33.749100   \n", "max    10006.789063       0.433157     185.730927    23.590172     86.378632   \n", "\n", "                RXO  FORCE_2020_LITHOFACIES_LITHOLOGY  \\\n", "count  38946.000000                     133198.000000   \n", "mean    -112.944168                      63261.582892   \n", "std      333.547482                      13623.352961   \n", "min     -999.900024                      30000.000000   \n", "25%        0.537328                      65000.000000   \n", "50%        0.879455                      65000.000000   \n", "75%        1.519061                      65030.000000   \n", "max     2000.000000                      99000.000000   \n", "\n", "       FORCE_2020_LITHOFACIES_CONFIDENCE  \n", "count                      133183.000000  \n", "mean                            1.235796  \n", "std                             0.480781  \n", "min                             1.000000  \n", "25%                             1.000000  \n", "50%                             1.000000  \n", "75%                             1.000000  \n", "max                             3.000000  \n", "\n", "[8 rows x 26 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe()"]}, {"cell_type": "code", "execution_count": null, "id": "7f88cadc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5f76977b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7c363d8b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1d942baf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e68dc9a8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "48726909", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3a682cf4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9bdd0ef9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9bde3546", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a4e54059", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b2b712ea", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}