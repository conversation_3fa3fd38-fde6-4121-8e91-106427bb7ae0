"""
🔍 CSV FILE DIAGNOSTIC TOOL
===========================
This tool helps diagnose CSV file format issues and suggests solutions.

Usage:
    python csv_diagnostic.py --file your_file.csv

Author: ONGC Petrophysical Analysis Team
Date: 2025-01-19
"""

import pandas as pd
import numpy as np
import argparse
import os

def analyze_csv_file(file_path):
    """Comprehensive analysis of CSV file structure"""
    print("🔍 CSV FILE DIAGNOSTIC ANALYSIS")
    print("=" * 50)
    print(f"📁 File: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return
    
    # Get file size
    file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
    print(f"📊 File size: {file_size:.2f} MB")
    
    # Try different delimiters and encodings
    delimiters = [',', ';', '\t', '|', ' ']
    encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
    
    best_df = None
    best_config = None
    max_columns = 0
    
    print(f"\n🔄 TESTING DIFFERENT FORMATS:")
    
    for delimiter in delimiters:
        for encoding in encodings:
            try:
                # Read first few rows to test
                df_test = pd.read_csv(file_path, delimiter=delimiter, encoding=encoding, nrows=5)
                
                if len(df_test.columns) > max_columns:
                    max_columns = len(df_test.columns)
                    best_df = df_test
                    best_config = {'delimiter': delimiter, 'encoding': encoding}
                
                delimiter_name = {'\\t': 'TAB', ',': 'COMMA', ';': 'SEMICOLON', '|': 'PIPE', ' ': 'SPACE'}.get(delimiter, delimiter)
                print(f"   {delimiter_name:10} + {encoding:10}: {len(df_test.columns):2d} columns")
                
            except Exception as e:
                continue
    
    if best_df is None:
        print("❌ Could not read file with any format combination")
        return
    
    print(f"\n✅ BEST FORMAT DETECTED:")
    delimiter_name = {'\\t': 'TAB', ',': 'COMMA', ';': 'SEMICOLON', '|': 'PIPE', ' ': 'SPACE'}.get(best_config['delimiter'], best_config['delimiter'])
    print(f"   Delimiter: {delimiter_name}")
    print(f"   Encoding: {best_config['encoding']}")
    print(f"   Columns: {len(best_df.columns)}")
    
    # Load full file with best configuration
    try:
        df = pd.read_csv(file_path, delimiter=best_config['delimiter'], encoding=best_config['encoding'])
        print(f"   Rows: {len(df):,}")
    except:
        df = best_df
        print(f"   Rows: Limited sample only")
    
    print(f"\n📋 COLUMN ANALYSIS:")
    print(f"   Total columns: {len(df.columns)}")
    
    for i, col in enumerate(df.columns, 1):
        # Get column info
        non_null = df[col].count()
        null_count = len(df) - non_null
        dtype = df[col].dtype
        
        # Sample values
        sample_values = df[col].dropna().head(3).tolist()
        sample_str = ', '.join([str(v)[:20] for v in sample_values])
        
        print(f"   {i:2d}. '{col}' ({dtype}) - {non_null:,} values, {null_count:,} nulls")
        print(f"       Sample: {sample_str}")
    
    # Check for well log features
    print(f"\n🪨 WELL LOG FEATURE DETECTION:")
    required_features = ['GR', 'RHOB', 'NPHI', 'RDEP', 'DTC', 'PEF']
    
    found_features = []
    for feature in required_features:
        if feature in df.columns:
            found_features.append(feature)
            print(f"   ✅ {feature}: Found")
        else:
            print(f"   ❌ {feature}: Missing")
    
    print(f"\n💡 COLUMN MAPPING SUGGESTIONS:")
    column_mapping = {
        'gamma': 'GR', 'gamma_ray': 'GR', 'gr': 'GR', 'gammaray': 'GR',
        'density': 'RHOB', 'bulk_density': 'RHOB', 'rhob': 'RHOB', 'den': 'RHOB', 'bulkdensity': 'RHOB',
        'neutron': 'NPHI', 'neutron_porosity': 'NPHI', 'nphi': 'NPHI', 'neu': 'NPHI', 'neutronporosity': 'NPHI',
        'resistivity': 'RDEP', 'deep_resistivity': 'RDEP', 'rdep': 'RDEP', 'res': 'RDEP', 'deepresistivity': 'RDEP',
        'dt': 'DTC', 'delta_time': 'DTC', 'dtc': 'DTC', 'sonic': 'DTC', 'deltatime': 'DTC',
        'pe': 'PEF', 'photoelectric': 'PEF', 'pef': 'PEF', 'photoelectricfactor': 'PEF'
    }
    
    suggestions = []
    for col in df.columns:
        col_lower = col.lower().strip().replace('_', '').replace(' ', '')
        if col_lower in column_mapping:
            suggestions.append(f"{col}:{column_mapping[col_lower]}")
            print(f"   🔄 '{col}' → '{column_mapping[col_lower]}'")
    
    if suggestions:
        mapping_string = ','.join(suggestions)
        print(f"\n🚀 READY-TO-USE COMMAND:")
        print(f"python lithology_cli.py --input \"{file_path}\" --column-mapping \"{mapping_string}\"")
    else:
        print(f"\n⚠️  NO AUTOMATIC MAPPING POSSIBLE")
        print(f"   Manual mapping required. Use:")
        print(f"   python lithology_cli.py --input \"{file_path}\" --show-columns")
    
    print(f"\n📊 DATA SAMPLE:")
    print(df.head().to_string())

def main():
    parser = argparse.ArgumentParser(description="🔍 CSV File Diagnostic Tool")
    parser.add_argument('--file', '-f', type=str, required=True,
                       help='CSV file to analyze')
    
    args = parser.parse_args()
    
    analyze_csv_file(args.file)

if __name__ == "__main__":
    main()
