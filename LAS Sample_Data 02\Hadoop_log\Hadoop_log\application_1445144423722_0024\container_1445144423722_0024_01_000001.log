2015-10-18 18:09:35,551 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445144423722_0024_000001
2015-10-18 18:09:36,394 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-18 18:09:36,394 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 24 cluster_timestamp: 1445144423722 } attemptId: 1 } keyId: -127633188)
2015-10-18 18:09:36,738 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-18 18:09:37,879 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-18 18:09:37,988 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-18 18:09:38,066 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-18 18:09:38,066 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-18 18:09:38,066 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-18 18:09:38,066 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-18 18:09:38,066 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-18 18:09:38,082 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-18 18:09:38,082 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-18 18:09:38,082 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-18 18:09:38,160 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 18:09:38,207 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 18:09:38,238 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 18:09:38,426 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-18 18:09:38,504 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-18 18:09:38,957 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-18 18:09:39,051 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-18 18:09:39,051 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-18 18:09:39,066 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445144423722_0024 to jobTokenSecretManager
2015-10-18 18:09:39,473 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445144423722_0024 because: not enabled; too many maps; too much input;
2015-10-18 18:09:39,520 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445144423722_0024 = 1256521728. Number of splits = 10
2015-10-18 18:09:39,520 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445144423722_0024 = 1
2015-10-18 18:09:39,520 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445144423722_0024Job Transitioned from NEW to INITED
2015-10-18 18:09:39,520 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445144423722_0024.
2015-10-18 18:09:39,582 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-18 18:09:39,613 INFO [Socket Reader #1 for port 57848] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 57848
2015-10-18 18:09:39,660 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-18 18:09:39,660 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-18 18:09:39,660 INFO [IPC Server listener on 57848] org.apache.hadoop.ipc.Server: IPC Server listener on 57848: starting
2015-10-18 18:09:39,676 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at 04DN8IQ.fareast.corp.microsoft.com/************:57848
2015-10-18 18:09:39,832 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-18 18:09:39,848 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-18 18:09:39,863 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-18 18:09:39,879 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-18 18:09:39,879 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-18 18:09:39,879 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-18 18:09:39,879 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-18 18:09:39,895 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 57855
2015-10-18 18:09:39,895 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-18 18:09:39,988 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\Jetty_0_0_0_0_57855_mapreduce____.a5e35p\webapp
2015-10-18 18:09:40,254 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:57855
2015-10-18 18:09:40,254 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 57855
2015-10-18 18:09:40,879 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-18 18:09:40,895 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-18 18:09:40,895 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445144423722_0024
2015-10-18 18:09:40,910 INFO [Socket Reader #1 for port 57861] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 57861
2015-10-18 18:09:40,926 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-18 18:09:40,957 INFO [IPC Server listener on 57861] org.apache.hadoop.ipc.Server: IPC Server listener on 57861: starting
2015-10-18 18:09:40,973 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-18 18:09:40,973 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-18 18:09:40,973 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-18 18:09:41,066 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-18 18:09:41,238 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-18 18:09:41,238 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-18 18:09:41,254 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-18 18:09:41,254 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-18 18:09:41,270 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445144423722_0024Job Transitioned from INITED to SETUP
2015-10-18 18:09:41,301 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-18 18:09:41,348 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445144423722_0024Job Transitioned from SETUP to RUNNING
2015-10-18 18:09:41,395 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:09:41,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:09:41,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:09:41,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:09:41,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:09:41,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:09:41,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:09:41,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:09:41,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:09:41,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:09:41,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:09:41,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:09:41,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:09:41,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:09:41,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:09:41,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:09:41,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:09:41,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:09:41,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:09:41,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:09:41,504 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-18 18:09:41,520 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445144423722_0024, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0024/job_1445144423722_0024_1.jhist
2015-10-18 18:09:41,520 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-18 18:09:42,270 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-18 18:09:42,332 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0024: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 18:09:42,332 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:09:42,332 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:09:42,332 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:09:42,332 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:09:43,332 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:09:43,332 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:09:43,332 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:09:43,332 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:09:44,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:09:44,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:09:44,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:09:44,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:09:45,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:09:45,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:09:45,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:09:45,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:09:46,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:09:46,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:09:46,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:09:46,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:09:47,426 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:09:47,426 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:09:47,426 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:09:47,426 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:09:48,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:09:48,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:09:48,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:09:48,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:09:49,473 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:09:49,473 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:09:49,473 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:09:49,473 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:09:50,488 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:09:50,488 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:09:50,488 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:09:50,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:09:51,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:09:51,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:09:51,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:09:51,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:09:52,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:09:52,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:09:52,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:09:52,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:09:53,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:09:53,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:09:53,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:09:53,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:09:54,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:09:54,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:09:54,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:09:54,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:09:55,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:09:55,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:09:55,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:09:55,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:09:56,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:09:56,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:09:56,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:09:56,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:09:57,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:09:57,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:09:57,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:09:57,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:09:58,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:09:58,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:09:58,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:09:58,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:09:59,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:09:59,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:09:59,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:09:59,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:00,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:00,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:00,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:00,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:01,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:01,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:01,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:01,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:02,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:02,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:02,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:02,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:03,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:03,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:03,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:03,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:04,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:04,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:04,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:04,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:05,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:05,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:05,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:05,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:06,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:06,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:06,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:06,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:07,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:07,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:07,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:07,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:08,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:08,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:08,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:08,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:09,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:09,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:09,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:09,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:10,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:10,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:10,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:10,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:11,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:11,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:11,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:11,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:12,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:12,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:12,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:12,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:13,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:13,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:13,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:13,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:14,520 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:14,520 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:14,520 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:14,520 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:15,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:15,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:15,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:15,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:16,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:16,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:16,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:16,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:17,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:17,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:17,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:17,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:18,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:18,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:18,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:18,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:19,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:19,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:19,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:19,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:20,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:20,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:20,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:20,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:21,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:21,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:21,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:21,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:22,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:22,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:22,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:22,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:23,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:23,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:23,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:23,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:24,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:24,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:24,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:24,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:25,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:25,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:25,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:25,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:26,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:26,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:26,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:26,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:27,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:27,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:27,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:27,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:28,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:28,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:28,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:28,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:29,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:29,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:29,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:29,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:30,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:30,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:30,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:30,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:31,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:31,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:31,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:31,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:32,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:32,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:32,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:32,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:33,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:33,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:33,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:33,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:34,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:34,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:34,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:34,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:35,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:35,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:35,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:35,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:36,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:36,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:36,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:36,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:37,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:37,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:37,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:37,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:38,584 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:38,584 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:38,584 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:38,584 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:39,584 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:39,584 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:39,584 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:39,584 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:40,600 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:40,600 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:40,600 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:40,600 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:41,600 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:41,600 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:41,600 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:41,600 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:42,600 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:42,600 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:42,600 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:42,600 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:43,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:43,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:43,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:43,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:44,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:44,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:44,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:44,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:45,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:45,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:45,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:45,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:46,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:46,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:46,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:46,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:47,616 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:47,616 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:47,616 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:47,616 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:48,616 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:48,616 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:48,616 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:48,616 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:49,616 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:49,631 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:49,631 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:49,631 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:50,631 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:50,631 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:50,631 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:50,631 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:51,631 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:51,631 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:51,631 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:51,631 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:52,631 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:52,631 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:52,631 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:52,631 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:53,631 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:53,631 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:53,631 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:53,631 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:54,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-18 18:10:54,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-18 18:10:54,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:54,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:55,678 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:10:55,678 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0024_01_000002 to attempt_1445144423722_0024_m_000000_0
2015-10-18 18:10:55,678 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:55,678 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:55,678 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:9 ScheduledReds:0 AssignedMaps:1 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:1 ContRel:0 HostLocal:1 RackLocal:0
2015-10-18 18:10:55,772 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:10:55,819 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0024/job.jar
2015-10-18 18:10:55,819 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0024/job.xml
2015-10-18 18:10:55,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-18 18:10:55,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-18 18:10:55,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-18 18:10:55,881 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:10:55,897 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0024_01_000002 taskAttempt attempt_1445144423722_0024_m_000000_0
2015-10-18 18:10:55,897 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0024_m_000000_0
2015-10-18 18:10:55,897 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:10:55,960 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0024_m_000000_0 : 13562
2015-10-18 18:10:55,960 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0024_m_000000_0] using containerId: [container_1445144423722_0024_01_000002 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:10:55,975 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:10:55,975 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0024_m_000000
2015-10-18 18:10:55,975 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:10:56,678 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0024: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 18:10:56,678 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:56,678 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:57,678 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:57,678 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:58,350 INFO [Socket Reader #1 for port 57861] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0024 (auth:SIMPLE)
2015-10-18 18:10:58,366 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0024_m_000002 asked for a task
2015-10-18 18:10:58,366 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0024_m_000002 given task: attempt_1445144423722_0024_m_000000_0
2015-10-18 18:10:58,679 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:58,679 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:10:59,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:10:59,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:00,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:00,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:01,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:01,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:02,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:02,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:03,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:03,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:04,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:04,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:05,648 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.10635664
2015-10-18 18:11:05,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:05,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:06,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:06,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:07,695 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:07,695 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:08,679 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.10635664
2015-10-18 18:11:08,695 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:08,695 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:09,695 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:09,695 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:10,695 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:10,695 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:11,695 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:11,695 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:11,695 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.10635664
2015-10-18 18:11:12,710 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:12,710 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:13,710 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:13,710 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:14,710 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:14,710 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:14,710 INFO [IPC Server handler 25 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.1434364
2015-10-18 18:11:15,711 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:15,711 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:16,711 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:16,711 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:17,711 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:17,711 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:17,726 INFO [IPC Server handler 25 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.19158794
2015-10-18 18:11:18,711 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:18,711 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:19,711 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:19,711 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:20,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:20,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:20,742 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.19158794
2015-10-18 18:11:21,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:21,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:22,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:22,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:23,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:23,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:23,758 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.19158794
2015-10-18 18:11:24,727 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:24,727 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:25,727 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:25,727 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:26,727 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:26,727 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:26,773 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.26158828
2015-10-18 18:11:27,742 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:27,742 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:28,742 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:28,742 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:29,742 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:29,742 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:29,789 INFO [IPC Server handler 25 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.27696857
2015-10-18 18:11:30,742 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:30,742 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:31,742 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:31,742 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:32,742 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:32,742 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:32,805 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.27696857
2015-10-18 18:11:33,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:33,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:34,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:34,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:35,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:35,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:35,821 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.27696857
2015-10-18 18:11:36,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:36,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:37,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:37,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:38,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:38,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:38,836 INFO [IPC Server handler 25 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.34698632
2015-10-18 18:11:39,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:39,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:40,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:40,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:41,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:41,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:42,024 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.3624012
2015-10-18 18:11:42,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:42,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:43,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:43,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:44,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:44,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:45,040 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.3624012
2015-10-18 18:11:45,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:45,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:46,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:46,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:47,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:47,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:48,056 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.373312
2015-10-18 18:11:48,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:48,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:49,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:49,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:50,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:50,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:51,071 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.44543478
2015-10-18 18:11:51,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:51,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:52,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:52,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:53,947 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:53,947 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:54,087 INFO [IPC Server handler 12 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.44789755
2015-10-18 18:11:54,962 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:54,962 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:55,962 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:55,962 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:56,962 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:56,962 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:57,103 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.44789755
2015-10-18 18:11:57,962 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:57,962 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:58,962 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:58,962 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:11:59,962 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:11:59,962 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:00,119 INFO [IPC Server handler 12 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.474011
2015-10-18 18:12:00,962 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:00,962 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:01,962 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:01,962 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:02,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:02,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:03,134 INFO [IPC Server handler 12 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.53341997
2015-10-18 18:12:03,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:03,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:04,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:04,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:05,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:05,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:06,150 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.53341997
2015-10-18 18:12:06,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:06,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:07,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:07,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:08,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:08,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:09,166 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.53341997
2015-10-18 18:12:09,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:09,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:10,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:10,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:11,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:11,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:12,197 INFO [IPC Server handler 19 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.5369352
2015-10-18 18:12:12,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:12,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:13,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:13,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:14,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:14,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:15,197 INFO [IPC Server handler 19 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.6104054
2015-10-18 18:12:15,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:15,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:16,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:16,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:17,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:17,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:18,213 INFO [IPC Server handler 19 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.61898744
2015-10-18 18:12:18,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:18,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:19,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:19,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:20,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:20,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:21,229 INFO [IPC Server handler 29 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.61898744
2015-10-18 18:12:21,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:21,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:22,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:22,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:23,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:23,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:24,245 INFO [IPC Server handler 29 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.61898744
2015-10-18 18:12:24,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:24,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:25,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:25,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:26,479 INFO [IPC Server handler 4 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.61898744
2015-10-18 18:12:26,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:26,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:27,260 INFO [IPC Server handler 4 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.667
2015-10-18 18:12:27,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:27,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:28,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:28,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:29,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:29,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:30,292 INFO [IPC Server handler 23 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.667
2015-10-18 18:12:30,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:30,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:31,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:31,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:32,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:32,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:33,292 INFO [IPC Server handler 29 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.667
2015-10-18 18:12:33,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:33,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:34,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:34,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:35,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:35,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:36,308 INFO [IPC Server handler 19 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.68253446
2015-10-18 18:12:36,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:36,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:37,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:37,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:38,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:38,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:39,323 INFO [IPC Server handler 19 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.72144985
2015-10-18 18:12:39,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:39,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:40,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:40,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:41,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:41,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:42,355 INFO [IPC Server handler 4 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.75839216
2015-10-18 18:12:42,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:42,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:43,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:43,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:44,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:44,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:45,355 INFO [IPC Server handler 4 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.78909296
2015-10-18 18:12:45,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:45,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:46,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:46,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:48,089 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:48,089 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:48,386 INFO [IPC Server handler 23 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.82313836
2015-10-18 18:12:49,308 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:49,308 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:50,449 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:50,449 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:51,402 INFO [IPC Server handler 23 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.8565705
2015-10-18 18:12:51,449 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:51,449 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:52,449 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:52,449 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:53,449 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:53,449 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:54,418 INFO [IPC Server handler 2 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.8916778
2015-10-18 18:12:54,449 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:54,449 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:55,449 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:55,449 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:56,449 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:56,449 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:57,449 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.9207139
2015-10-18 18:12:57,465 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:57,465 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:58,465 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:58,465 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:12:59,465 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:12:59,465 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:13:00,449 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.9517016
2015-10-18 18:13:00,481 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:00,481 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:13:01,481 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:01,481 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:13:02,481 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:02,481 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:13:03,465 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 0.99173856
2015-10-18 18:13:03,496 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:03,496 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:13:04,465 INFO [IPC Server handler 4 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000000_0 is : 1.0
2015-10-18 18:13:04,481 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0024_m_000000_0
2015-10-18 18:13:04,481 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:13:04,481 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0024_01_000002 taskAttempt attempt_1445144423722_0024_m_000000_0
2015-10-18 18:13:04,481 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0024_m_000000_0
2015-10-18 18:13:04,481 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:13:04,496 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:04,496 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:13:04,528 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:13:04,528 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0024_m_000000_0
2015-10-18 18:13:04,528 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:13:04,528 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-18 18:13:05,496 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:9 ScheduledReds:0 AssignedMaps:1 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:1 ContRel:0 HostLocal:1 RackLocal:0
2015-10-18 18:13:05,496 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:05,496 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-18 18:13:05,496 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:10240, vCores:10> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:06,496 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0024_01_000002
2015-10-18 18:13:06,496 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:13:06,496 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0024_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:13:06,496 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0024_01_000003 to attempt_1445144423722_0024_m_000001_0
2015-10-18 18:13:06,496 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:06,496 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:06,496 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:8 ScheduledReds:0 AssignedMaps:1 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:2 ContRel:0 HostLocal:2 RackLocal:0
2015-10-18 18:13:06,496 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:13:06,512 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:13:06,512 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0024_01_000003 taskAttempt attempt_1445144423722_0024_m_000001_0
2015-10-18 18:13:06,512 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0024_m_000001_0
2015-10-18 18:13:06,512 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:13:06,528 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0024_m_000001_0 : 13562
2015-10-18 18:13:06,528 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0024_m_000001_0] using containerId: [container_1445144423722_0024_01_000003 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:13:06,528 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:13:06,528 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0024_m_000001
2015-10-18 18:13:06,528 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:13:07,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0024: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 18:13:07,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:07,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:08,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:08,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:09,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:09,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:09,653 INFO [Socket Reader #1 for port 57861] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0024 (auth:SIMPLE)
2015-10-18 18:13:09,684 INFO [IPC Server handler 20 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0024_m_000003 asked for a task
2015-10-18 18:13:09,684 INFO [IPC Server handler 20 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0024_m_000003 given task: attempt_1445144423722_0024_m_000001_0
2015-10-18 18:13:10,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:10,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:11,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:11,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:12,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:12,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:13,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:13,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:14,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:14,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:15,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:15,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:16,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:16,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:17,200 INFO [IPC Server handler 25 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.10659867
2015-10-18 18:13:17,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:17,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:18,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:18,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:19,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:19,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:20,200 INFO [IPC Server handler 25 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.1066108
2015-10-18 18:13:20,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:20,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:21,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:21,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:22,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:22,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:23,232 INFO [IPC Server handler 25 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.1066108
2015-10-18 18:13:23,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:23,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:24,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:24,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:25,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:25,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:26,232 INFO [IPC Server handler 25 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.1066108
2015-10-18 18:13:26,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:26,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:27,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:27,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:28,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:28,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:29,247 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.15642221
2015-10-18 18:13:29,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:29,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:30,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:30,497 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:31,513 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:31,513 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:32,279 INFO [IPC Server handler 12 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.19211523
2015-10-18 18:13:32,513 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:32,513 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:33,513 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:33,513 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:34,513 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:34,513 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:35,279 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.19211523
2015-10-18 18:13:35,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:35,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:36,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:36,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:37,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:37,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:38,310 INFO [IPC Server handler 25 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.22191341
2015-10-18 18:13:38,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:38,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:39,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:39,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:40,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:40,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:41,310 INFO [IPC Server handler 25 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.27776006
2015-10-18 18:13:41,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:41,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:42,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:42,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:43,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:43,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:44,342 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.27776006
2015-10-18 18:13:44,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:44,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:45,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:45,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:46,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:46,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:47,342 INFO [IPC Server handler 25 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.28693596
2015-10-18 18:13:47,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:47,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:48,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:48,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:49,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:49,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:50,358 INFO [IPC Server handler 25 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.36319977
2015-10-18 18:13:50,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:50,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:51,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:51,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:52,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:52,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:53,389 INFO [IPC Server handler 25 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.36319977
2015-10-18 18:13:53,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:53,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:54,733 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:54,733 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:55,967 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:55,967 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:56,592 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.36319977
2015-10-18 18:13:57,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:57,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:58,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:58,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:59,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:13:59,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:13:59,608 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.448704
2015-10-18 18:14:00,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:00,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:01,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:01,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:02,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:02,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:02,639 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.448704
2015-10-18 18:14:03,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:03,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:04,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:04,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:05,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:05,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:05,671 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.448704
2015-10-18 18:14:06,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:06,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:07,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:07,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:08,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:08,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:08,671 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.53425497
2015-10-18 18:14:09,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:09,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:10,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:10,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:11,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:11,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:11,702 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.53425497
2015-10-18 18:14:12,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:12,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:13,202 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:13,202 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:14,202 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:14,202 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:14,703 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.53425497
2015-10-18 18:14:15,203 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:15,203 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:16,203 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:16,203 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:17,203 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:17,203 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:17,718 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.6168694
2015-10-18 18:14:18,218 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:18,218 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:19,218 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:19,218 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:20,218 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:20,218 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:20,734 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.6197233
2015-10-18 18:14:21,218 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:21,218 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:22,218 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:22,218 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:23,218 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:23,218 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:23,765 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.6197233
2015-10-18 18:14:24,219 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:24,219 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:25,219 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:25,219 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:26,219 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:26,219 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:26,265 INFO [IPC Server handler 3 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.6197233
2015-10-18 18:14:26,766 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.667
2015-10-18 18:14:27,219 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:27,219 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:28,219 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:28,219 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:29,219 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:29,219 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:29,797 INFO [IPC Server handler 29 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.667
2015-10-18 18:14:30,219 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:30,219 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:31,219 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:31,219 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:32,219 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:32,219 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:32,797 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.6706217
2015-10-18 18:14:33,281 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:33,281 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:34,281 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:34,281 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:35,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:35,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:35,828 INFO [IPC Server handler 29 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.72325814
2015-10-18 18:14:36,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:36,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:37,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:37,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:38,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:38,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:38,844 INFO [IPC Server handler 2 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.7585825
2015-10-18 18:14:39,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:39,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:40,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:40,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:41,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:41,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:41,844 INFO [IPC Server handler 29 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.7888517
2015-10-18 18:14:42,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:42,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:43,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:43,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:44,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:44,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:44,876 INFO [IPC Server handler 2 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.81795025
2015-10-18 18:14:45,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:45,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:46,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:46,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:47,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:47,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:47,891 INFO [IPC Server handler 2 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.8525165
2015-10-18 18:14:48,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:48,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:49,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:49,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:50,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:50,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:50,923 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.8854084
2015-10-18 18:14:51,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:51,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:52,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:52,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:53,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:53,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:53,938 INFO [IPC Server handler 2 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.92928016
2015-10-18 18:14:54,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:54,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:55,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:55,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:56,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:56,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:56,954 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.95997083
2015-10-18 18:14:57,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:57,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:58,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:58,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:59,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:14:59,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:14:59,954 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 0.9925406
2015-10-18 18:15:00,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:00,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:922, vCores:-24> finalReduceResourceLimit:<memory:102, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:00,829 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000001_0 is : 1.0
2015-10-18 18:15:00,829 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0024_m_000001_0
2015-10-18 18:15:00,829 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:15:00,829 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0024_01_000003 taskAttempt attempt_1445144423722_0024_m_000001_0
2015-10-18 18:15:00,845 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0024_m_000001_0
2015-10-18 18:15:00,845 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:15:00,861 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:15:00,861 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0024_m_000001_0
2015-10-18 18:15:00,861 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:15:00,861 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-18 18:15:01,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:8 ScheduledReds:0 AssignedMaps:1 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:2 ContRel:0 HostLocal:2 RackLocal:0
2015-10-18 18:15:01,408 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:01,408 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:02,642 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0024_01_000003
2015-10-18 18:15:02,642 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:15:02,642 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0024_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:15:02,642 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0024_01_000004 to attempt_1445144423722_0024_m_000002_0
2015-10-18 18:15:02,642 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:02,642 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:02,642 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:7 ScheduledReds:0 AssignedMaps:1 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:3 ContRel:0 HostLocal:3 RackLocal:0
2015-10-18 18:15:02,642 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:15:02,642 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:15:02,642 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0024_01_000004 taskAttempt attempt_1445144423722_0024_m_000002_0
2015-10-18 18:15:02,642 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0024_m_000002_0
2015-10-18 18:15:02,642 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:15:02,970 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0024_m_000002_0 : 13562
2015-10-18 18:15:02,970 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0024_m_000002_0] using containerId: [container_1445144423722_0024_01_000004 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:15:02,970 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:15:02,970 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0024_m_000002
2015-10-18 18:15:02,970 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:15:03,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0024: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 18:15:03,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:03,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:04,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:04,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:05,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:05,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:05,830 INFO [Socket Reader #1 for port 57861] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0024 (auth:SIMPLE)
2015-10-18 18:15:05,861 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0024_m_000004 asked for a task
2015-10-18 18:15:05,861 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0024_m_000004 given task: attempt_1445144423722_0024_m_000002_0
2015-10-18 18:15:06,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:06,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:07,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:07,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:08,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:08,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:09,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:09,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:10,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:10,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:11,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:11,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:12,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:12,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:13,330 INFO [IPC Server handler 13 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.10660437
2015-10-18 18:15:13,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:13,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:14,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:14,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:15,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:15,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:16,346 INFO [IPC Server handler 3 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.10660437
2015-10-18 18:15:16,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:16,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:17,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:17,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:18,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:18,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:19,361 INFO [IPC Server handler 3 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.10660437
2015-10-18 18:15:19,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:19,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:20,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:20,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:21,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:21,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:22,377 INFO [IPC Server handler 13 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.11419221
2015-10-18 18:15:22,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:22,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:23,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:23,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:24,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:24,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:25,393 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.18336903
2015-10-18 18:15:25,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:25,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:26,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:26,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:27,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:27,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:28,408 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.19212553
2015-10-18 18:15:28,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:28,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:29,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:29,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:30,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:30,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:31,424 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.19212553
2015-10-18 18:15:31,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:31,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:32,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:32,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:33,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:33,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:34,440 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.22735715
2015-10-18 18:15:34,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:34,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:35,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:35,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:36,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:36,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:37,456 INFO [IPC Server handler 18 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.27772525
2015-10-18 18:15:37,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:37,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:38,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:38,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:39,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:39,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:40,487 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.27772525
2015-10-18 18:15:40,799 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:40,799 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:41,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:41,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:42,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:42,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:43,487 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.27772525
2015-10-18 18:15:43,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:43,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:44,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:44,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:45,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:45,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:46,503 INFO [IPC Server handler 18 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.32546502
2015-10-18 18:15:46,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:46,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:47,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:47,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:48,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:48,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:49,518 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.36317363
2015-10-18 18:15:49,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:49,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:50,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:50,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:51,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:51,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:52,534 INFO [IPC Server handler 18 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.36317363
2015-10-18 18:15:52,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:52,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:53,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:53,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:54,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:54,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:55,550 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.36317363
2015-10-18 18:15:55,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:55,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:56,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:56,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:57,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:57,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:58,566 INFO [IPC Server handler 13 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.42680076
2015-10-18 18:15:58,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:58,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:15:59,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:15:59,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:00,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:00,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:01,581 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.44859612
2015-10-18 18:16:01,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:01,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:02,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:02,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:03,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:03,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:04,597 INFO [IPC Server handler 15 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.44859612
2015-10-18 18:16:04,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:04,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:05,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:05,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:06,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:06,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:07,613 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.46919575
2015-10-18 18:16:08,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:08,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:09,300 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:09,300 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:10,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:10,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:10,628 INFO [IPC Server handler 6 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.5342037
2015-10-18 18:16:11,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:11,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:12,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:12,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:13,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:13,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:13,644 INFO [IPC Server handler 6 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.5342037
2015-10-18 18:16:14,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:14,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:15,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:15,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:16,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:16,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:16,660 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.5342037
2015-10-18 18:16:17,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:17,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:18,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:18,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:19,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:19,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:19,676 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.600587
2015-10-18 18:16:20,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:20,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:21,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:21,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:22,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:22,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:22,691 INFO [IPC Server handler 6 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.6196791
2015-10-18 18:16:23,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:23,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:24,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:24,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:25,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:25,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:25,707 INFO [IPC Server handler 0 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.6196791
2015-10-18 18:16:26,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:26,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:27,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:27,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:28,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:28,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:28,723 INFO [IPC Server handler 7 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.6196791
2015-10-18 18:16:29,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:29,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:30,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:30,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:31,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:31,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:31,738 INFO [IPC Server handler 12 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.65061396
2015-10-18 18:16:32,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:32,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:32,489 INFO [IPC Server handler 3 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.65061396
2015-10-18 18:16:33,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:33,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:34,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:34,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:34,754 INFO [IPC Server handler 12 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.667
2015-10-18 18:16:35,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:35,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:36,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:36,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:37,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:37,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:37,770 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.667
2015-10-18 18:16:38,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:38,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:39,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:39,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:40,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:40,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:40,848 INFO [IPC Server handler 17 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.667
2015-10-18 18:16:41,457 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:41,457 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:42,458 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:42,458 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:43,458 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:43,458 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:43,879 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.68921167
2015-10-18 18:16:44,458 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:44,458 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:45,458 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:45,458 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:46,458 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:46,458 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:46,895 INFO [IPC Server handler 10 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.72280943
2015-10-18 18:16:47,473 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:47,473 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:48,489 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:48,489 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:49,520 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:49,520 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:49,926 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.7571956
2015-10-18 18:16:50,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:50,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:51,583 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:51,583 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:52,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:52,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:52,958 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.7965447
2015-10-18 18:16:53,630 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:53,630 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:54,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:54,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:55,677 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:55,692 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:56,005 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.8324673
2015-10-18 18:16:56,708 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:56,708 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:57,724 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:57,724 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:58,739 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:58,739 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:16:59,036 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.8633859
2015-10-18 18:16:59,755 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:16:59,755 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:17:00,771 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:17:00,771 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:17:01,771 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:17:01,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:17:02,068 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.89473677
2015-10-18 18:17:02,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:17:02,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:17:03,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:17:03,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:1024, vCores:-26> finalMapResourceLimit:<memory:820, vCores:-21> finalReduceResourceLimit:<memory:204, vCores:-5> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:17:04,818 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:17408, vCores:-3>
2015-10-18 18:17:04,818 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:18432, vCores:-2> finalMapResourceLimit:<memory:8192, vCores:8> finalReduceResourceLimit:<memory:10240, vCores:-10> netScheduledMapResource:<memory:8192, vCores:8> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:17:04,818 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping up 1
2015-10-18 18:17:04,818 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:7 ScheduledReds:1 AssignedMaps:1 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:3 ContRel:0 HostLocal:3 RackLocal:0
2015-10-18 18:17:05,099 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.9281936
2015-10-18 18:17:05,833 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0024: ask=1 release= 0 newContainers=7 finishedContainers=0 resourcelimit=<memory:10240, vCores:-10> knownNMs=3
2015-10-18 18:17:05,833 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 7
2015-10-18 18:17:05,833 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0024_01_000005 to attempt_1445144423722_0024_m_000003_0
2015-10-18 18:17:05,849 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0024_01_000006 to attempt_1445144423722_0024_m_000004_0
2015-10-18 18:17:05,849 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0024_01_000007 to attempt_1445144423722_0024_m_000005_0
2015-10-18 18:17:05,849 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0024_01_000008 to attempt_1445144423722_0024_m_000006_0
2015-10-18 18:17:05,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:05,849 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0024_01_000009 to attempt_1445144423722_0024_m_000007_0
2015-10-18 18:17:05,849 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0024_01_000010 to attempt_1445144423722_0024_m_000008_0
2015-10-18 18:17:05,849 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0024_01_000011 to attempt_1445144423722_0024_m_000009_0
2015-10-18 18:17:05,849 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:8 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-18 18:17:05,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:17:05,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:05,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:17:05,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:05,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:17:05,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:05,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:17:05,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:05,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:17:05,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:05,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:17:05,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:05,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:17:05,849 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0024_01_000006 taskAttempt attempt_1445144423722_0024_m_000004_0
2015-10-18 18:17:05,849 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0024_01_000005 taskAttempt attempt_1445144423722_0024_m_000003_0
2015-10-18 18:17:05,849 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0024_m_000004_0
2015-10-18 18:17:05,849 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0024_m_000003_0
2015-10-18 18:17:05,849 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0024_01_000007 taskAttempt attempt_1445144423722_0024_m_000005_0
2015-10-18 18:17:05,849 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0024_01_000010 taskAttempt attempt_1445144423722_0024_m_000008_0
2015-10-18 18:17:05,849 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:17:05,849 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0024_01_000011 taskAttempt attempt_1445144423722_0024_m_000009_0
2015-10-18 18:17:05,864 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0024_m_000008_0
2015-10-18 18:17:05,864 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0024_m_000005_0
2015-10-18 18:17:05,864 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0024_m_000009_0
2015-10-18 18:17:05,864 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0024_01_000008 taskAttempt attempt_1445144423722_0024_m_000006_0
2015-10-18 18:17:05,864 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0024_m_000006_0
2015-10-18 18:17:05,864 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0024_01_000009 taskAttempt attempt_1445144423722_0024_m_000007_0
2015-10-18 18:17:05,864 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0024_m_000007_0
2015-10-18 18:17:05,864 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:17:05,864 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:17:05,864 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:17:05,864 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:17:05,864 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:17:05,864 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:17:05,974 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0024_m_000009_0 : 13562
2015-10-18 18:17:05,974 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0024_m_000005_0 : 13562
2015-10-18 18:17:05,974 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0024_m_000006_0 : 13562
2015-10-18 18:17:05,974 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0024_m_000003_0 : 13562
2015-10-18 18:17:05,974 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0024_m_000004_0 : 13562
2015-10-18 18:17:05,974 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0024_m_000007_0 : 13562
2015-10-18 18:17:05,974 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0024_m_000008_0 : 13562
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0024_m_000009_0] using containerId: [container_1445144423722_0024_01_000011 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0024_m_000005_0] using containerId: [container_1445144423722_0024_01_000007 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0024_m_000006_0] using containerId: [container_1445144423722_0024_01_000008 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0024_m_000003_0] using containerId: [container_1445144423722_0024_01_000005 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0024_m_000004_0] using containerId: [container_1445144423722_0024_01_000006 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0024_m_000007_0] using containerId: [container_1445144423722_0024_01_000009 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0024_m_000008_0] using containerId: [container_1445144423722_0024_01_000010 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0024_m_000009
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0024_m_000005
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0024_m_000006
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0024_m_000003
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0024_m_000004
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0024_m_000007
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0024_m_000008
2015-10-18 18:17:05,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:17:06,880 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0024: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:9216, vCores:-11> knownNMs=3
2015-10-18 18:17:06,880 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:17:06,880 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-18 18:17:06,880 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0024_01_000012 to attempt_1445144423722_0024_r_000000_0
2015-10-18 18:17:06,880 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:10 RackLocal:0
2015-10-18 18:17:06,896 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:06,896 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:17:06,896 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0024_01_000012 taskAttempt attempt_1445144423722_0024_r_000000_0
2015-10-18 18:17:06,896 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0024_r_000000_0
2015-10-18 18:17:06,896 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:17:07,005 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0024_r_000000_0 : 13562
2015-10-18 18:17:07,005 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0024_r_000000_0] using containerId: [container_1445144423722_0024_01_000012 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:7109]
2015-10-18 18:17:07,005 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:17:07,005 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0024_r_000000
2015-10-18 18:17:07,005 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:17:07,911 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0024: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:9216, vCores:-11> knownNMs=3
2015-10-18 18:17:08,193 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.9541282
2015-10-18 18:17:08,849 INFO [Socket Reader #1 for port 57861] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0024 (auth:SIMPLE)
2015-10-18 18:17:08,865 INFO [IPC Server handler 7 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0024_r_000012 asked for a task
2015-10-18 18:17:08,865 INFO [IPC Server handler 7 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0024_r_000012 given task: attempt_1445144423722_0024_r_000000_0
2015-10-18 18:17:09,427 INFO [Socket Reader #1 for port 57861] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0024 (auth:SIMPLE)
2015-10-18 18:17:09,458 INFO [IPC Server handler 19 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0024_m_000005 asked for a task
2015-10-18 18:17:09,458 INFO [IPC Server handler 19 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0024_m_000005 given task: attempt_1445144423722_0024_m_000003_0
2015-10-18 18:17:09,474 INFO [Socket Reader #1 for port 57861] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0024 (auth:SIMPLE)
2015-10-18 18:17:09,505 INFO [IPC Server handler 2 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0024_m_000007 asked for a task
2015-10-18 18:17:09,505 INFO [IPC Server handler 2 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0024_m_000007 given task: attempt_1445144423722_0024_m_000005_0
2015-10-18 18:17:09,536 INFO [Socket Reader #1 for port 57861] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0024 (auth:SIMPLE)
2015-10-18 18:17:09,568 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0024_m_000008 asked for a task
2015-10-18 18:17:09,568 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0024_m_000008 given task: attempt_1445144423722_0024_m_000006_0
2015-10-18 18:17:09,583 INFO [Socket Reader #1 for port 57861] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0024 (auth:SIMPLE)
2015-10-18 18:17:09,599 INFO [Socket Reader #1 for port 57861] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0024 (auth:SIMPLE)
2015-10-18 18:17:09,615 INFO [Socket Reader #1 for port 57861] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0024 (auth:SIMPLE)
2015-10-18 18:17:09,615 INFO [IPC Server handler 23 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0024_m_000010 asked for a task
2015-10-18 18:17:09,615 INFO [IPC Server handler 23 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0024_m_000010 given task: attempt_1445144423722_0024_m_000008_0
2015-10-18 18:17:09,615 INFO [Socket Reader #1 for port 57861] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0024 (auth:SIMPLE)
2015-10-18 18:17:09,630 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0024_m_000006 asked for a task
2015-10-18 18:17:09,630 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0024_m_000006 given task: attempt_1445144423722_0024_m_000004_0
2015-10-18 18:17:09,630 INFO [IPC Server handler 3 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0024_m_000009 asked for a task
2015-10-18 18:17:09,630 INFO [IPC Server handler 3 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0024_m_000009 given task: attempt_1445144423722_0024_m_000007_0
2015-10-18 18:17:09,646 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0024_m_000011 asked for a task
2015-10-18 18:17:09,646 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0024_m_000011 given task: attempt_1445144423722_0024_m_000009_0
2015-10-18 18:17:09,958 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 0 maxEvents 10000
2015-10-18 18:17:10,958 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 2 maxEvents 10000
2015-10-18 18:17:11,224 INFO [IPC Server handler 25 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 0.9846972
2015-10-18 18:17:11,974 INFO [IPC Server handler 12 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 2 maxEvents 10000
2015-10-18 18:17:12,974 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 2 maxEvents 10000
2015-10-18 18:17:13,130 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000002_0 is : 1.0
2015-10-18 18:17:13,130 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0024_m_000002_0
2015-10-18 18:17:13,130 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:17:13,130 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0024_01_000004 taskAttempt attempt_1445144423722_0024_m_000002_0
2015-10-18 18:17:13,130 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0024_m_000002_0
2015-10-18 18:17:13,130 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:17:13,162 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:17:13,162 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0024_m_000002_0
2015-10-18 18:17:13,162 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:17:13,162 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-18 18:17:13,912 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:10 RackLocal:0
2015-10-18 18:17:13,974 INFO [IPC Server handler 12 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 2 maxEvents 10000
2015-10-18 18:17:14,927 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0024_01_000004
2015-10-18 18:17:14,927 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:10 RackLocal:0
2015-10-18 18:17:14,927 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0024_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:17:14,990 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:15,896 INFO [IPC Server handler 15 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.10000001
2015-10-18 18:17:15,990 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:16,177 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0024_m_000006
2015-10-18 18:17:16,177 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445144423722_0024_m_000006
2015-10-18 18:17:16,177 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:17:16,177 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:16,177 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:16,177 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000006_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:17:16,896 INFO [IPC Server handler 15 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.09585765
2015-10-18 18:17:16,927 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:10 RackLocal:0
2015-10-18 18:17:16,927 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0024: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:5120, vCores:-15> knownNMs=3
2015-10-18 18:17:16,990 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.102374054
2015-10-18 18:17:16,990 INFO [IPC Server handler 10 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:17,068 INFO [IPC Server handler 17 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.10601924
2015-10-18 18:17:17,084 INFO [IPC Server handler 29 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000009_0 is : 0.28808534
2015-10-18 18:17:17,084 INFO [IPC Server handler 25 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.10423812
2015-10-18 18:17:17,084 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.10190583
2015-10-18 18:17:17,084 INFO [IPC Server handler 19 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.09526266
2015-10-18 18:17:17,990 INFO [IPC Server handler 10 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:18,912 INFO [IPC Server handler 15 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.10000001
2015-10-18 18:17:18,974 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:17:18,974 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:18,974 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0024_01_000013 to attempt_1445144423722_0024_m_000006_1
2015-10-18 18:17:18,974 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-18 18:17:18,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:18,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000006_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:17:18,974 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0024_01_000013 taskAttempt attempt_1445144423722_0024_m_000006_1
2015-10-18 18:17:18,974 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0024_m_000006_1
2015-10-18 18:17:18,974 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:17:19,021 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:19,099 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0024_m_000006_1 : 13562
2015-10-18 18:17:19,099 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0024_m_000006_1] using containerId: [container_1445144423722_0024_01_000013 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 18:17:19,099 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000006_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:17:19,099 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0024_m_000006
2015-10-18 18:17:19,912 INFO [IPC Server handler 15 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.10685723
2015-10-18 18:17:20,037 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0024: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:3072, vCores:-17> knownNMs=3
2015-10-18 18:17:20,037 INFO [IPC Server handler 17 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.106964506
2015-10-18 18:17:20,037 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:20,084 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.106881365
2015-10-18 18:17:20,115 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000009_0 is : 0.295472
2015-10-18 18:17:20,115 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.10681946
2015-10-18 18:17:20,115 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.10680563
2015-10-18 18:17:20,115 INFO [IPC Server handler 29 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.106493875
2015-10-18 18:17:21,037 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:21,974 INFO [IPC Server handler 15 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.10000001
2015-10-18 18:17:22,099 INFO [IPC Server handler 18 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:22,927 INFO [IPC Server handler 15 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.10685723
2015-10-18 18:17:23,052 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.106964506
2015-10-18 18:17:23,224 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:23,224 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.106881365
2015-10-18 18:17:23,224 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.106493875
2015-10-18 18:17:23,224 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.10681946
2015-10-18 18:17:23,240 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.10680563
2015-10-18 18:17:23,240 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000009_0 is : 0.295472
2015-10-18 18:17:24,256 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:24,365 INFO [Socket Reader #1 for port 57861] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0024 (auth:SIMPLE)
2015-10-18 18:17:24,490 INFO [IPC Server handler 2 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0024_m_000013 asked for a task
2015-10-18 18:17:24,490 INFO [IPC Server handler 2 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0024_m_000013 given task: attempt_1445144423722_0024_m_000006_1
2015-10-18 18:17:25,021 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.10000001
2015-10-18 18:17:25,318 INFO [IPC Server handler 19 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:26,006 INFO [IPC Server handler 15 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.10685723
2015-10-18 18:17:26,068 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.106964506
2015-10-18 18:17:26,240 INFO [IPC Server handler 17 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.106493875
2015-10-18 18:17:26,240 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.106881365
2015-10-18 18:17:26,240 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.10681946
2015-10-18 18:17:26,303 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000009_0 is : 0.295472
2015-10-18 18:17:26,303 INFO [IPC Server handler 12 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.10680563
2015-10-18 18:17:26,334 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:27,381 INFO [IPC Server handler 2 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:28,037 INFO [IPC Server handler 15 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.10000001
2015-10-18 18:17:28,381 INFO [IPC Server handler 2 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:29,021 INFO [IPC Server handler 15 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.14648391
2015-10-18 18:17:29,100 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.14820671
2015-10-18 18:17:29,256 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.14901432
2015-10-18 18:17:29,271 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.14306158
2015-10-18 18:17:29,271 INFO [IPC Server handler 17 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.13841195
2015-10-18 18:17:29,318 INFO [IPC Server handler 12 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.14774069
2015-10-18 18:17:29,318 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000009_0 is : 0.3720078
2015-10-18 18:17:29,412 INFO [IPC Server handler 20 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:30,443 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:31,084 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.10000001
2015-10-18 18:17:31,193 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0024_m_000008
2015-10-18 18:17:31,193 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445144423722_0024_m_000008
2015-10-18 18:17:31,193 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:31,193 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:31,193 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000008_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:17:31,193 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:17:31,459 INFO [IPC Server handler 20 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:31,537 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-18 18:17:31,537 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0024: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-20> knownNMs=3
2015-10-18 18:17:32,100 INFO [IPC Server handler 23 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.19247705
2015-10-18 18:17:32,131 INFO [IPC Server handler 15 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.19266446
2015-10-18 18:17:32,271 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.19209063
2015-10-18 18:17:32,287 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.1910481
2015-10-18 18:17:32,287 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.19258286
2015-10-18 18:17:32,334 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.19242907
2015-10-18 18:17:32,334 INFO [IPC Server handler 12 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000009_0 is : 0.5323719
2015-10-18 18:17:32,475 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:33,568 INFO [IPC Server handler 13 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:34,147 INFO [IPC Server handler 23 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.10000001
2015-10-18 18:17:34,600 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:35,115 INFO [IPC Server handler 23 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.19247705
2015-10-18 18:17:35,147 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.19266446
2015-10-18 18:17:35,287 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.19209063
2015-10-18 18:17:35,303 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.19258286
2015-10-18 18:17:35,303 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.19255035
2015-10-18 18:17:35,350 INFO [IPC Server handler 12 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.19242907
2015-10-18 18:17:35,350 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000009_0 is : 0.5323719
2015-10-18 18:17:35,600 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:36,647 INFO [IPC Server handler 10 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:37,194 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.10000001
2015-10-18 18:17:37,662 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:38,147 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.19247705
2015-10-18 18:17:38,162 INFO [IPC Server handler 23 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.19266446
2015-10-18 18:17:38,319 INFO [IPC Server handler 15 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.19209063
2015-10-18 18:17:38,334 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.19255035
2015-10-18 18:17:38,334 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.19258286
2015-10-18 18:17:38,365 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.19242907
2015-10-18 18:17:38,365 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000009_0 is : 0.5323719
2015-10-18 18:17:38,678 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:39,694 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:40,225 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.10000001
2015-10-18 18:17:40,740 INFO [IPC Server handler 19 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:41,194 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.20143886
2015-10-18 18:17:41,194 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.2120821
2015-10-18 18:17:41,412 INFO [IPC Server handler 4 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.19258286
2015-10-18 18:17:41,412 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.19255035
2015-10-18 18:17:41,412 INFO [IPC Server handler 3 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000009_0 is : 0.5323719
2015-10-18 18:17:41,412 INFO [IPC Server handler 15 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.19238852
2015-10-18 18:17:41,412 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.19242907
2015-10-18 18:17:41,834 INFO [IPC Server handler 20 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:42,912 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:43,209 INFO [IPC Server handler 23 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000009_0 is : 0.5323719
2015-10-18 18:17:43,272 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.10000001
2015-10-18 18:17:43,959 INFO [IPC Server handler 23 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:43,991 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.05174137
2015-10-18 18:17:44,256 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.2783809
2015-10-18 18:17:44,256 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.27343068
2015-10-18 18:17:44,459 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000009_0 is : 0.667
2015-10-18 18:17:44,475 INFO [IPC Server handler 15 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.27811313
2015-10-18 18:17:44,475 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.27017733
2015-10-18 18:17:44,475 INFO [IPC Server handler 4 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.2709789
2015-10-18 18:17:44,475 INFO [IPC Server handler 3 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.2645595
2015-10-18 18:17:45,022 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:46,116 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:46,334 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.10000001
2015-10-18 18:17:47,147 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:47,163 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.069366984
2015-10-18 18:17:47,303 INFO [IPC Server handler 15 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.27813601
2015-10-18 18:17:47,303 INFO [IPC Server handler 4 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.2783809
2015-10-18 18:17:47,538 INFO [IPC Server handler 3 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000009_0 is : 0.667
2015-10-18 18:17:47,538 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.2781602
2015-10-18 18:17:47,538 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.27825075
2015-10-18 18:17:47,538 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.27765483
2015-10-18 18:17:47,538 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.27811313
2015-10-18 18:17:48,209 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:49,272 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:49,397 INFO [IPC Server handler 3 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.10000001
2015-10-18 18:17:50,335 INFO [IPC Server handler 15 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:50,413 INFO [IPC Server handler 4 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.2783809
2015-10-18 18:17:50,428 INFO [IPC Server handler 4 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.27813601
2015-10-18 18:17:50,585 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000009_0 is : 0.66737163
2015-10-18 18:17:50,631 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.27811313
2015-10-18 18:17:50,631 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.27825075
2015-10-18 18:17:50,631 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.27765483
2015-10-18 18:17:50,631 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.2781602
2015-10-18 18:17:50,710 INFO [IPC Server handler 18 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.100819364
2015-10-18 18:17:51,381 INFO [IPC Server handler 4 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:52,428 INFO [IPC Server handler 4 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:52,460 INFO [IPC Server handler 3 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.10000001
2015-10-18 18:17:53,428 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:53,475 INFO [IPC Server handler 4 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.2841492
2015-10-18 18:17:53,475 INFO [IPC Server handler 4 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.27813601
2015-10-18 18:17:53,632 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000009_0 is : 0.7384181
2015-10-18 18:17:53,678 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.27811313
2015-10-18 18:17:53,678 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.27825075
2015-10-18 18:17:53,678 INFO [IPC Server handler 18 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.27765483
2015-10-18 18:17:53,694 INFO [IPC Server handler 12 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.2781602
2015-10-18 18:17:54,444 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.106964506
2015-10-18 18:17:54,444 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:55,507 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:55,538 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.10000001
2015-10-18 18:17:56,491 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.36404583
2015-10-18 18:17:56,491 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.3461651
2015-10-18 18:17:56,538 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:56,647 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000009_0 is : 0.82529014
2015-10-18 18:17:56,694 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.35408732
2015-10-18 18:17:56,694 INFO [IPC Server handler 18 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.34669766
2015-10-18 18:17:56,694 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.34473106
2015-10-18 18:17:56,710 INFO [IPC Server handler 12 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.33936226
2015-10-18 18:17:57,585 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:57,913 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.106964506
2015-10-18 18:17:58,585 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.10000001
2015-10-18 18:17:58,632 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:59,507 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.36390656
2015-10-18 18:17:59,507 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.36404583
2015-10-18 18:17:59,679 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:17:59,725 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000009_0 is : 0.9322912
2015-10-18 18:17:59,725 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.3637686
2015-10-18 18:17:59,725 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.3638923
2015-10-18 18:17:59,725 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.36323506
2015-10-18 18:17:59,725 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.36388028
2015-10-18 18:18:00,694 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:18:01,366 INFO [IPC Server handler 23 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000009_0 is : 1.0
2015-10-18 18:18:01,366 INFO [IPC Server handler 20 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0024_m_000009_0
2015-10-18 18:18:01,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:18:01,366 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0024_01_000011 taskAttempt attempt_1445144423722_0024_m_000009_0
2015-10-18 18:18:01,366 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0024_m_000009_0
2015-10-18 18:18:01,366 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:18:01,522 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:18:01,522 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0024_m_000009_0
2015-10-18 18:18:01,522 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:18:01,522 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-18 18:18:01,554 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-18 18:18:01,554 INFO [IPC Server handler 19 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.106964506
2015-10-18 18:18:01,600 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.10000001
2015-10-18 18:18:01,694 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 18:18:02,522 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.36390656
2015-10-18 18:18:02,522 INFO [IPC Server handler 25 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.36404583
2015-10-18 18:18:02,569 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0024_01_000011
2015-10-18 18:18:02,569 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-18 18:18:02,569 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0024_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:18:02,726 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:02,741 INFO [IPC Server handler 4 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.3637686
2015-10-18 18:18:02,741 INFO [IPC Server handler 15 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.3638923
2015-10-18 18:18:02,741 INFO [IPC Server handler 3 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.36323506
2015-10-18 18:18:02,741 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.36388028
2015-10-18 18:18:03,757 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:04,616 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:18:04,772 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:05,054 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.106964506
2015-10-18 18:18:05,538 INFO [IPC Server handler 25 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.36404583
2015-10-18 18:18:05,538 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.36390656
2015-10-18 18:18:05,772 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.36388028
2015-10-18 18:18:05,772 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.3637686
2015-10-18 18:18:05,772 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.36323506
2015-10-18 18:18:05,772 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.3638923
2015-10-18 18:18:05,772 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:06,788 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:07,648 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:18:07,819 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:08,569 INFO [IPC Server handler 13 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.106964506
2015-10-18 18:18:08,569 INFO [IPC Server handler 13 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.37282878
2015-10-18 18:18:08,569 INFO [IPC Server handler 13 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.36808512
2015-10-18 18:18:08,835 INFO [IPC Server handler 19 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.4269691
2015-10-18 18:18:08,835 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.36323506
2015-10-18 18:18:08,835 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.44217896
2015-10-18 18:18:08,835 INFO [IPC Server handler 2 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:08,835 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.42652342
2015-10-18 18:18:09,866 INFO [IPC Server handler 19 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:10,663 INFO [IPC Server handler 23 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:18:10,929 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:11,570 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.4477475
2015-10-18 18:18:11,570 INFO [IPC Server handler 0 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.44572872
2015-10-18 18:18:11,835 INFO [IPC Server handler 29 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.44968578
2015-10-18 18:18:11,835 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.43220872
2015-10-18 18:18:11,835 INFO [IPC Server handler 29 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.44950172
2015-10-18 18:18:11,851 INFO [IPC Server handler 2 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.44964966
2015-10-18 18:18:11,945 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:12,116 INFO [IPC Server handler 18 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.106964506
2015-10-18 18:18:12,976 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:13,695 INFO [IPC Server handler 6 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:18:13,991 INFO [IPC Server handler 19 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:14,585 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.44980705
2015-10-18 18:18:14,585 INFO [IPC Server handler 0 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.44950968
2015-10-18 18:18:14,867 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.44950172
2015-10-18 18:18:14,867 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.4486067
2015-10-18 18:18:14,867 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.44968578
2015-10-18 18:18:14,867 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.44964966
2015-10-18 18:18:15,007 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:15,507 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.106964506
2015-10-18 18:18:16,023 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:16,726 INFO [IPC Server handler 6 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:18:17,038 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:17,601 INFO [IPC Server handler 0 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.44950968
2015-10-18 18:18:17,601 INFO [IPC Server handler 6 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.44980705
2015-10-18 18:18:17,882 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.46496654
2015-10-18 18:18:17,882 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.45604783
2015-10-18 18:18:17,882 INFO [IPC Server handler 25 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.48400843
2015-10-18 18:18:17,882 INFO [IPC Server handler 20 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.4486067
2015-10-18 18:18:18,070 INFO [IPC Server handler 19 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:18,914 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.106964506
2015-10-18 18:18:19,242 INFO [IPC Server handler 18 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:19,867 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:18:20,507 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:20,773 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.44950968
2015-10-18 18:18:20,773 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.45641905
2015-10-18 18:18:21,101 INFO [IPC Server handler 2 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.5352825
2015-10-18 18:18:21,101 INFO [IPC Server handler 29 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.4486067
2015-10-18 18:18:21,101 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.53521925
2015-10-18 18:18:21,101 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.5352028
2015-10-18 18:18:21,523 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:22,351 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.106964506
2015-10-18 18:18:22,570 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:22,898 INFO [IPC Server handler 13 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:18:23,617 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:23,789 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.53543663
2015-10-18 18:18:23,789 INFO [IPC Server handler 13 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.5352021
2015-10-18 18:18:24,132 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.53521925
2015-10-18 18:18:24,132 INFO [IPC Server handler 29 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.5352825
2015-10-18 18:18:24,132 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.52049345
2015-10-18 18:18:24,132 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.5352028
2015-10-18 18:18:24,664 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:25,679 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:25,898 INFO [IPC Server handler 17 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.106964506
2015-10-18 18:18:25,929 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:18:26,726 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:26,820 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.53543663
2015-10-18 18:18:26,820 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.5352021
2015-10-18 18:18:27,148 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.53521925
2015-10-18 18:18:27,148 INFO [IPC Server handler 7 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.5352825
2015-10-18 18:18:27,148 INFO [IPC Server handler 10 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.5343203
2015-10-18 18:18:27,179 INFO [IPC Server handler 23 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.5352028
2015-10-18 18:18:27,758 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:28,789 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:29,039 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:18:29,383 INFO [IPC Server handler 3 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.14287439
2015-10-18 18:18:29,851 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:29,851 INFO [IPC Server handler 0 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.53543663
2015-10-18 18:18:29,851 INFO [IPC Server handler 6 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.5352021
2015-10-18 18:18:30,195 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.5352825
2015-10-18 18:18:30,195 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.5343203
2015-10-18 18:18:30,195 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.5758969
2015-10-18 18:18:30,195 INFO [IPC Server handler 7 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.5542984
2015-10-18 18:18:30,883 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:31,930 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:32,101 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:18:32,726 INFO [IPC Server handler 12 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.1764579
2015-10-18 18:18:32,914 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.5677271
2015-10-18 18:18:32,914 INFO [IPC Server handler 0 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.5524173
2015-10-18 18:18:32,976 INFO [IPC Server handler 17 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:33,258 INFO [IPC Server handler 7 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.6192229
2015-10-18 18:18:33,273 INFO [IPC Server handler 10 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.5343203
2015-10-18 18:18:33,273 INFO [IPC Server handler 20 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.6207798
2015-10-18 18:18:33,273 INFO [IPC Server handler 25 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.6208445
2015-10-18 18:18:34,039 INFO [IPC Server handler 13 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:35,133 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:35,180 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:18:35,977 INFO [IPC Server handler 6 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.6209487
2015-10-18 18:18:35,977 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.6210422
2015-10-18 18:18:36,164 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:36,195 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.19266446
2015-10-18 18:18:36,289 INFO [IPC Server handler 7 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.6208445
2015-10-18 18:18:36,289 INFO [IPC Server handler 10 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.620844
2015-10-18 18:18:36,305 INFO [IPC Server handler 7 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.6207798
2015-10-18 18:18:36,305 INFO [IPC Server handler 10 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.6122337
2015-10-18 18:18:37,211 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:38,258 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:38,258 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:18:38,992 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.6209487
2015-10-18 18:18:38,992 INFO [IPC Server handler 6 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.6210422
2015-10-18 18:18:39,289 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:39,320 INFO [IPC Server handler 7 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.6207798
2015-10-18 18:18:39,320 INFO [IPC Server handler 10 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.6208445
2015-10-18 18:18:39,336 INFO [IPC Server handler 25 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.6199081
2015-10-18 18:18:39,336 INFO [IPC Server handler 20 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.620844
2015-10-18 18:18:39,695 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.19266446
2015-10-18 18:18:40,305 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:41,289 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:18:41,320 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:41,992 INFO [IPC Server handler 12 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.6209487
2015-10-18 18:18:42,008 INFO [IPC Server handler 6 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.6210422
2015-10-18 18:18:42,320 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.6597156
2015-10-18 18:18:42,320 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:42,320 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.62465745
2015-10-18 18:18:42,367 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.6199081
2015-10-18 18:18:42,367 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.620844
2015-10-18 18:18:42,602 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.6597156
2015-10-18 18:18:43,117 INFO [IPC Server handler 0 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.19266446
2015-10-18 18:18:43,352 INFO [IPC Server handler 13 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:43,867 INFO [IPC Server handler 15 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.62465745
2015-10-18 18:18:44,336 INFO [IPC Server handler 13 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:18:44,399 INFO [IPC Server handler 17 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:44,680 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.620844
2015-10-18 18:18:44,899 INFO [IPC Server handler 19 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.6209487
2015-10-18 18:18:44,914 INFO [IPC Server handler 15 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.6210422
2015-10-18 18:18:45,071 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.667
2015-10-18 18:18:45,071 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.667
2015-10-18 18:18:45,367 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.667
2015-10-18 18:18:45,367 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.667
2015-10-18 18:18:45,367 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.667
2015-10-18 18:18:45,367 INFO [IPC Server handler 13 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.6402899
2015-10-18 18:18:45,399 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:46,274 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.6402899
2015-10-18 18:18:46,399 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:46,602 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.19266446
2015-10-18 18:18:47,352 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:18:47,430 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:48,086 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.667
2015-10-18 18:18:48,086 INFO [IPC Server handler 18 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.667
2015-10-18 18:18:48,383 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.667
2015-10-18 18:18:48,383 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.667
2015-10-18 18:18:48,383 INFO [IPC Server handler 13 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.667
2015-10-18 18:18:48,383 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.667
2015-10-18 18:18:48,461 INFO [IPC Server handler 17 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:49,493 INFO [IPC Server handler 20 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:50,164 INFO [IPC Server handler 4 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.19266446
2015-10-18 18:18:50,461 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:18:50,539 INFO [IPC Server handler 17 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:51,086 INFO [IPC Server handler 3 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.667
2015-10-18 18:18:51,086 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.667
2015-10-18 18:18:51,383 INFO [IPC Server handler 0 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.68911904
2015-10-18 18:18:51,383 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.67246544
2015-10-18 18:18:51,399 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.667
2015-10-18 18:18:51,399 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.667
2015-10-18 18:18:51,586 INFO [IPC Server handler 17 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:52,633 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:53,493 INFO [IPC Server handler 13 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:18:53,618 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.19266446
2015-10-18 18:18:53,665 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:54,102 INFO [IPC Server handler 3 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.67615575
2015-10-18 18:18:54,102 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.6760623
2015-10-18 18:18:54,430 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.7123683
2015-10-18 18:18:54,430 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.73187965
2015-10-18 18:18:54,430 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.7069592
2015-10-18 18:18:54,430 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.667
2015-10-18 18:18:54,711 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:55,711 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:56,524 INFO [IPC Server handler 0 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:18:56,758 INFO [IPC Server handler 20 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:57,118 INFO [IPC Server handler 23 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.70688033
2015-10-18 18:18:57,133 INFO [IPC Server handler 3 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.7063663
2015-10-18 18:18:57,274 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.19266446
2015-10-18 18:18:57,462 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.7774037
2015-10-18 18:18:57,462 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.752443
2015-10-18 18:18:57,462 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.68912137
2015-10-18 18:18:57,462 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.7576755
2015-10-18 18:18:57,805 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:58,821 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:18:59,571 INFO [IPC Server handler 12 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:18:59,837 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:19:00,133 INFO [IPC Server handler 23 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.7363647
2015-10-18 18:19:00,133 INFO [IPC Server handler 3 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.7387373
2015-10-18 18:19:00,462 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.7865233
2015-10-18 18:19:00,462 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.71714115
2015-10-18 18:19:00,477 INFO [IPC Server handler 12 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.7917102
2015-10-18 18:19:00,477 INFO [IPC Server handler 6 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.8115912
2015-10-18 18:19:00,852 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.19266446
2015-10-18 18:19:00,852 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:19:01,868 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:19:02,618 INFO [IPC Server handler 0 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:19:02,899 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:19:03,165 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.7677711
2015-10-18 18:19:03,165 INFO [IPC Server handler 3 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.7712802
2015-10-18 18:19:03,493 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.8330109
2015-10-18 18:19:03,493 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.8266064
2015-10-18 18:19:03,493 INFO [IPC Server handler 6 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.7467619
2015-10-18 18:19:03,493 INFO [IPC Server handler 26 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.8521235
2015-10-18 18:19:03,946 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:19:04,290 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.19266446
2015-10-18 18:19:04,977 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:19:05,649 INFO [IPC Server handler 0 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:19:06,009 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:19:06,165 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.811443
2015-10-18 18:19:06,165 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.80666393
2015-10-18 18:19:06,493 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.8847538
2015-10-18 18:19:06,493 INFO [IPC Server handler 18 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.8784516
2015-10-18 18:19:06,509 INFO [IPC Server handler 4 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.90477204
2015-10-18 18:19:06,509 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.78538454
2015-10-18 18:19:07,056 INFO [IPC Server handler 8 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:19:07,602 INFO [IPC Server handler 29 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.19266446
2015-10-18 18:19:08,149 INFO [IPC Server handler 20 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:19:08,727 INFO [IPC Server handler 0 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:19:09,181 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:19:09,228 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.8434408
2015-10-18 18:19:09,228 INFO [IPC Server handler 3 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.85022104
2015-10-18 18:19:09,603 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.92369366
2015-10-18 18:19:09,603 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.943514
2015-10-18 18:19:09,603 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.91777533
2015-10-18 18:19:09,603 INFO [IPC Server handler 18 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.81935644
2015-10-18 18:19:10,259 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:19:11,118 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.2283013
2015-10-18 18:19:11,306 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:19:11,806 INFO [IPC Server handler 13 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:19:12,306 INFO [IPC Server handler 10 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.8770292
2015-10-18 18:19:12,306 INFO [IPC Server handler 20 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.88635576
2015-10-18 18:19:12,368 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:19:12,618 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 0.9636245
2015-10-18 18:19:12,618 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.8513765
2015-10-18 18:19:12,634 INFO [IPC Server handler 18 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 0.9830123
2015-10-18 18:19:12,634 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.9576988
2015-10-18 18:19:13,415 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:19:13,978 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000008_0 is : 1.0
2015-10-18 18:19:13,993 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0024_m_000008_0
2015-10-18 18:19:13,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:19:13,993 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0024_01_000010 taskAttempt attempt_1445144423722_0024_m_000008_0
2015-10-18 18:19:13,993 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0024_m_000008_0
2015-10-18 18:19:14,009 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:19:14,040 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:19:14,040 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0024_01_000014 to attempt_1445144423722_0024_m_000008_1
2015-10-18 18:19:14,040 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-18 18:19:14,040 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:19:14,040 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000008_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:19:14,040 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0024_01_000014 taskAttempt attempt_1445144423722_0024_m_000008_1
2015-10-18 18:19:14,040 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0024_m_000008_1
2015-10-18 18:19:14,040 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:19:14,243 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:19:14,462 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0024_m_000008_0
2015-10-18 18:19:14,462 INFO [IPC Server handler 17 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:19:14,462 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445144423722_0024_m_000008_1
2015-10-18 18:19:14,462 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:19:14,462 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-18 18:19:14,462 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000008_1 TaskAttempt Transitioned from ASSIGNED to KILL_CONTAINER_CLEANUP
2015-10-18 18:19:14,462 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0024_01_000014 taskAttempt attempt_1445144423722_0024_m_000008_1
2015-10-18 18:19:14,493 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0024_m_000008_1 : 13562
2015-10-18 18:19:14,493 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0024_m_000008_1
2015-10-18 18:19:14,493 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:19:14,962 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.13333334
2015-10-18 18:19:14,962 INFO [IPC Server handler 24 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.26768288
2015-10-18 18:19:14,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000008_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 18:19:15,103 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-18 18:19:15,118 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0024: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-20> knownNMs=3
2015-10-18 18:19:15,181 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 18:19:15,228 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/_temporary/attempt_1445144423722_0024_m_000008_1
2015-10-18 18:19:15,228 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000008_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 18:19:15,337 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.9075076
2015-10-18 18:19:15,337 INFO [IPC Server handler 7 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.9199089
2015-10-18 18:19:15,446 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000004_0 is : 1.0
2015-10-18 18:19:15,493 INFO [IPC Server handler 20 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0024_m_000004_0
2015-10-18 18:19:15,493 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:19:15,493 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0024_01_000006 taskAttempt attempt_1445144423722_0024_m_000004_0
2015-10-18 18:19:15,493 INFO [IPC Server handler 10 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:19:15,493 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0024_m_000004_0
2015-10-18 18:19:15,493 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:19:15,853 INFO [IPC Server handler 12 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.88197947
2015-10-18 18:19:15,853 INFO [IPC Server handler 0 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 0.9982022
2015-10-18 18:19:15,884 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:19:15,884 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0024_m_000004_0
2015-10-18 18:19:15,884 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:19:15,884 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-18 18:19:15,900 INFO [IPC Server handler 6 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000007_0 is : 1.0
2015-10-18 18:19:15,931 INFO [IPC Server handler 16 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0024_m_000007_0
2015-10-18 18:19:15,931 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:19:15,931 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0024_01_000009 taskAttempt attempt_1445144423722_0024_m_000007_0
2015-10-18 18:19:15,931 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0024_m_000007_0
2015-10-18 18:19:15,931 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:19:16,165 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-18 18:19:16,196 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:19:16,196 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0024_01_000010
2015-10-18 18:19:16,196 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0024_01_000014
2015-10-18 18:19:16,196 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-18 18:19:16,196 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0024_m_000007_0
2015-10-18 18:19:16,196 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:19:16,196 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0024_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:19:16,196 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0024_m_000008_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:19:16,196 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-18 18:19:16,618 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:19:17,228 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-18 18:19:17,228 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0024_01_000006
2015-10-18 18:19:17,228 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0024_01_000009
2015-10-18 18:19:17,228 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0024_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:19:17,228 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-18 18:19:17,228 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0024_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:19:17,665 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:19:18,009 INFO [IPC Server handler 4 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.23333333
2015-10-18 18:19:18,353 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.9483372
2015-10-18 18:19:18,368 INFO [IPC Server handler 7 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 0.9623647
2015-10-18 18:19:18,400 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.2783809
2015-10-18 18:19:18,697 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:19:18,868 INFO [IPC Server handler 18 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.92299056
2015-10-18 18:19:19,712 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:19:20,775 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:19:21,072 INFO [IPC Server handler 6 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.23333333
2015-10-18 18:19:21,165 INFO [IPC Server handler 15 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_0 is : 1.0
2015-10-18 18:19:21,165 INFO [IPC Server handler 2 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0024_m_000006_0
2015-10-18 18:19:21,165 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:19:21,165 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0024_01_000008 taskAttempt attempt_1445144423722_0024_m_000006_0
2015-10-18 18:19:21,165 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0024_m_000006_0
2015-10-18 18:19:21,165 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:19:21,337 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:19:21,337 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0024_m_000006_0
2015-10-18 18:19:21,337 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445144423722_0024_m_000006_1
2015-10-18 18:19:21,337 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:19:21,337 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-18 18:19:21,337 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000006_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 18:19:21,337 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0024_01_000013 taskAttempt attempt_1445144423722_0024_m_000006_1
2015-10-18 18:19:21,337 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0024_m_000006_1
2015-10-18 18:19:21,337 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:19:21,368 INFO [IPC Server handler 14 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 0.98805845
2015-10-18 18:19:21,728 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0024_m_000006
2015-10-18 18:19:21,728 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:19:21,759 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000006_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 18:19:21,806 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:19:21,869 INFO [IPC Server handler 18 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 0.9599133
2015-10-18 18:19:21,915 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 18:19:21,915 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/_temporary/attempt_1445144423722_0024_m_000006_1
2015-10-18 18:19:21,915 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000006_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 18:19:21,915 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000006_1 is : 0.2783809
2015-10-18 18:19:22,275 INFO [IPC Server handler 13 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000005_0 is : 1.0
2015-10-18 18:19:22,290 INFO [IPC Server handler 5 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0024_m_000005_0
2015-10-18 18:19:22,290 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:19:22,290 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0024_01_000007 taskAttempt attempt_1445144423722_0024_m_000005_0
2015-10-18 18:19:22,290 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0024_m_000005_0
2015-10-18 18:19:22,290 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:19:22,447 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-18 18:19:22,447 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0024_01_000008
2015-10-18 18:19:22,447 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-18 18:19:22,447 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0024_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:19:22,447 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:19:22,447 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0024_m_000005_0
2015-10-18 18:19:22,447 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:19:22,447 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-18 18:19:22,853 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:19:23,072 INFO [Socket Reader #1 for port 57861] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 57861: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 18:19:23,462 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-18 18:19:23,462 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0024_01_000007
2015-10-18 18:19:23,462 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-18 18:19:23,462 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0024_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:19:23,869 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 18:19:24,103 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.3
2015-10-18 18:19:24,478 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0024_01_000013
2015-10-18 18:19:24,478 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-18 18:19:24,478 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0024_m_000006_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:19:24,587 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_m_000003_0 is : 1.0
2015-10-18 18:19:24,603 INFO [IPC Server handler 21 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0024_m_000003_0
2015-10-18 18:19:24,603 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:19:24,603 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0024_01_000005 taskAttempt attempt_1445144423722_0024_m_000003_0
2015-10-18 18:19:24,603 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0024_m_000003_0
2015-10-18 18:19:24,603 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:19:24,681 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:19:24,681 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0024_m_000003_0
2015-10-18 18:19:24,681 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:19:24,681 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-18 18:19:24,900 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0024_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 18:19:25,509 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-18 18:19:25,619 INFO [IPC Server handler 1 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.3
2015-10-18 18:19:25,916 INFO [IPC Server handler 18 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.3
2015-10-18 18:19:26,837 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0024_01_000005
2015-10-18 18:19:26,837 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-18 18:19:26,837 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0024_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:19:27,150 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.6669537
2015-10-18 18:19:30,197 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.6853773
2015-10-18 18:19:33,228 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.6973821
2015-10-18 18:19:36,260 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.7211416
2015-10-18 18:19:39,322 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.7399924
2015-10-18 18:19:42,385 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.7549321
2015-10-18 18:19:45,416 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.7793864
2015-10-18 18:19:48,447 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.8022326
2015-10-18 18:19:51,494 INFO [IPC Server handler 22 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.8153524
2015-10-18 18:19:54,573 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.8371446
2015-10-18 18:19:57,620 INFO [IPC Server handler 27 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.86036706
2015-10-18 18:20:00,635 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.8819187
2015-10-18 18:20:03,682 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.9056941
2015-10-18 18:20:06,745 INFO [IPC Server handler 18 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.9226266
2015-10-18 18:20:09,792 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.94000435
2015-10-18 18:20:12,823 INFO [IPC Server handler 18 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.96154714
2015-10-18 18:20:15,886 INFO [IPC Server handler 11 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 0.9854695
2015-10-18 18:20:18,198 INFO [IPC Server handler 28 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445144423722_0024_r_000000_0
2015-10-18 18:20:18,198 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_r_000000_0 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-18 18:20:18,198 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445144423722_0024_r_000000_0 given a go for committing the task output.
2015-10-18 18:20:18,214 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445144423722_0024_r_000000_0
2015-10-18 18:20:18,214 INFO [IPC Server handler 9 on 57861] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445144423722_0024_r_000000_0:true
2015-10-18 18:20:18,292 INFO [IPC Server handler 12 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0024_r_000000_0 is : 1.0
2015-10-18 18:20:18,292 INFO [IPC Server handler 15 on 57861] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0024_r_000000_0
2015-10-18 18:20:18,292 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_r_000000_0 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:20:18,292 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0024_01_000012 taskAttempt attempt_1445144423722_0024_r_000000_0
2015-10-18 18:20:18,292 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0024_r_000000_0
2015-10-18 18:20:18,292 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:20:18,433 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0024_r_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:20:18,433 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0024_r_000000_0
2015-10-18 18:20:18,433 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0024_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:20:18,433 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-18 18:20:18,433 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445144423722_0024Job Transitioned from RUNNING to COMMITTING
2015-10-18 18:20:18,589 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-18 18:20:18,589 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-18 18:20:19,058 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-18 18:20:19,058 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445144423722_0024Job Transitioned from COMMITTING to SUCCEEDED
2015-10-18 18:20:19,151 INFO [Thread-111] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-18 18:20:19,151 INFO [Thread-111] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-18 18:20:19,151 INFO [Thread-111] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-18 18:20:19,151 INFO [Thread-111] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-18 18:20:19,151 INFO [Thread-111] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-18 18:20:19,151 INFO [Thread-111] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-18 18:20:19,151 INFO [Thread-111] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-18 18:20:19,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0024_01_000012
2015-10-18 18:20:19,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-18 18:20:19,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0024_r_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:20:20,104 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0024/job_1445144423722_0024_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0024-1445162508959-msrabi-pagerank-1445163619058-10-1-SUCCEEDED-default-1445162981270.jhist_tmp
2015-10-18 18:20:21,604 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0024-1445162508959-msrabi-pagerank-1445163619058-10-1-SUCCEEDED-default-1445162981270.jhist_tmp
2015-10-18 18:20:21,636 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0024/job_1445144423722_0024_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0024_conf.xml_tmp
2015-10-18 18:20:22,948 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0024_conf.xml_tmp
2015-10-18 18:20:22,948 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0024.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0024.summary
2015-10-18 18:20:22,964 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0024_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0024_conf.xml
2015-10-18 18:20:22,964 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0024-1445162508959-msrabi-pagerank-1445163619058-10-1-SUCCEEDED-default-1445162981270.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0024-1445162508959-msrabi-pagerank-1445163619058-10-1-SUCCEEDED-default-1445162981270.jhist
2015-10-18 18:20:22,964 INFO [Thread-111] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-18 18:20:22,979 INFO [Thread-111] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-18 18:20:22,979 INFO [Thread-111] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://04DN8IQ.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445144423722_0024
2015-10-18 18:20:22,979 INFO [Thread-111] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-18 18:20:23,995 INFO [Thread-111] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-18 18:20:23,995 INFO [Thread-111] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0024
2015-10-18 18:20:24,026 INFO [Thread-111] org.apache.hadoop.ipc.Server: Stopping server on 57861
2015-10-18 18:20:24,073 INFO [IPC Server listener on 57861] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 57861
2015-10-18 18:20:24,073 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
2015-10-18 18:20:24,073 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
