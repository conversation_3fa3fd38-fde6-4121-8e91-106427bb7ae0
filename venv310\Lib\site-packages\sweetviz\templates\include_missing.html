    <div class="pos-base-stats-in-detail">
        <div class="row-missing">
            <div class="text-label color-normal pos-base-stats__label">MISSING:</div>
            <div class="text-value color-source pos-base-stats__source">
                {% if feature_dict.base_stats.num_missing.number > 0: %}
                    {{ feature_dict.base_stats.num_missing.number|fmt_int_commas }}
                {% else %}
                    ---
                {% endif %}
            </div>
            <div class="text-value color-source pos-base-stats__source-perc" {{ feature_dict.base_stats.num_missing.perc|fmt_div_color_override_missing }}>
                {% if feature_dict.base_stats.num_missing.number > 0: %}
                    {{ feature_dict.base_stats.num_missing.perc|fmt_div_icon_missing }}
                    {{ feature_dict.base_stats.num_missing.perc|fmt_percent_parentheses }}
                {% else %}

                {% endif %}
            </div>
            {% if compare_dict is not none: %}
                <div class="text-value color-compare pos-base-stats__compare">
                    {% if compare_dict.base_stats.num_missing.number > 0: %}
                        {{ compare_dict.base_stats.num_missing.number|fmt_int_commas }}
                    {% else %}
                        ---
                    {% endif %}
                </div>
                <div class="text-value color-compare pos-base-stats__compare-perc" {{ compare_dict.base_stats.num_missing.perc|fmt_div_color_override_missing }}>
                    {% if compare_dict.base_stats.num_missing.number > 0: %}
                        {{ compare_dict.base_stats.num_missing.perc|fmt_div_icon_missing }}
                        {{ compare_dict.base_stats.num_missing.perc|fmt_percent_parentheses }}
                    {% else %}

                    {% endif %}
                </div>
            {% endif %}
        </div>
</div>

