<div class="row sub-item">
    {% if items[0].__str__() == 'HTMLVariableInfo' %}
            <div class="col-sm-12">
                {{ items[0].render() }}
            </div>
        {% for item in items[1:] %}
            <div class="col-sm-{% if loop.length == 3 %}4{% else %}6{% endif %}{% if item.content['classes'] %} {{ item.content['classes'] }}{% endif %}">
                {{ item.render() }}
            </div>
        {% endfor %}
    {% else %}
        {% for item in items %}
            <div class="col-sm-{% if loop.length == 1 %}12{% elif (loop.last and loop.length == 3) or loop.length == 2 %}6{% else %}3{% endif %}{% if item.content['classes'] %} {{ item.content['classes'] }}{% endif %}">
                {{ item.render() }}
            </div>
        {% endfor %}
    {% endif %}
</div>
