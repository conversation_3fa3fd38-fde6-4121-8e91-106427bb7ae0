2015-10-18 18:04:20,947 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-18 18:04:21,103 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-18 18:04:21,103 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-18 18:04:21,135 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-18 18:04:21,135 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445144423722_0023, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@1623b78d)
2015-10-18 18:04:21,307 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-18 18:04:22,072 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445144423722_0023
2015-10-18 18:04:22,529 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-18 18:04:23,373 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-18 18:04:23,389 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@db57326
2015-10-18 18:04:23,779 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/pageinput2.txt:134217728+134217728
2015-10-18 18:04:23,889 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-18 18:04:23,889 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-18 18:04:23,889 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-18 18:04:23,889 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-18 18:04:23,889 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-18 18:04:23,904 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-18 18:04:28,311 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-18 18:04:28,311 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 48254386; bufvoid = 104857600
2015-10-18 18:04:28,311 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 17306472(69225888); length = 8907925/6553600
2015-10-18 18:04:28,311 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 57323122 kvi 14330776(57323104)
2015-10-18 18:04:39,264 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-18 18:04:39,264 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 57323122 kv 14330776(57323104) kvi 12127800(48511200)
2015-10-18 18:04:41,546 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-18 18:04:41,546 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 57323122; bufend = 699496; bufvoid = 104857600
2015-10-18 18:04:41,546 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14330776(57323104); kvend = 5417752(21671008); length = 8913025/6553600
2015-10-18 18:04:41,546 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 9768248 kvi 2442056(9768224)
2015-10-18 18:04:50,359 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-18 18:04:50,359 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 9768248 kv 2442056(9768224) kvi 241544(966176)
2015-10-18 18:04:52,781 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-18 18:04:52,781 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 9768248; bufend = 57981481; bufvoid = 104857600
2015-10-18 18:04:52,781 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 2442056(9768224); kvend = 19738252(78953008); length = 8918205/6553600
2015-10-18 18:04:52,781 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 67050233 kvi 16762552(67050208)
2015-10-18 18:05:01,922 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-18 18:05:01,922 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 67050233 kv 16762552(67050208) kvi 14554320(58217280)
2015-10-18 18:05:03,828 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-18 18:05:03,828 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 67050233; bufend = 10441503; bufvoid = 104857600
2015-10-18 18:05:03,828 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 16762552(67050208); kvend = 7853256(31413024); length = 8909297/6553600
2015-10-18 18:05:03,828 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 19510255 kvi 4877556(19510224)
2015-10-18 18:05:12,641 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-18 18:05:12,641 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 19510255 kv 4877556(19510224) kvi 2674180(10696720)
2015-10-18 18:05:14,625 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-18 18:05:14,625 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 19510255; bufend = 67733940; bufvoid = 104857600
2015-10-18 18:05:14,625 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 4877556(19510224); kvend = 22176360(88705440); length = 8915597/6553600
2015-10-18 18:05:14,625 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 76802676 kvi 19200664(76802656)
2015-10-18 18:05:23,126 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-18 18:05:23,126 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 76802676 kv 19200664(76802656) kvi 17001428(68005712)
2015-10-18 18:05:23,157 INFO [communication thread] org.apache.hadoop.mapred.Task: Communication exception: java.io.IOException: Failed on local exception: java.io.IOException: An existing connection was forcibly closed by the remote host; Host Details : local host is: "MSRA-SA-39/**************"; destination host is: "minint-fnanli5.fareast.corp.microsoft.com":62304; 
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:772)
	at org.apache.hadoop.ipc.Client.call(Client.java:1472)
	at org.apache.hadoop.ipc.Client.call(Client.java:1399)
	at org.apache.hadoop.ipc.WritableRpcEngine$Invoker.invoke(WritableRpcEngine.java:244)
	at com.sun.proxy.$Proxy8.statusUpdate(Unknown Source)
	at org.apache.hadoop.mapred.Task$TaskReporter.run(Task.java:737)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.net.SocketInputStream$Reader.performIO(SocketInputStream.java:57)
	at org.apache.hadoop.net.SocketIOWithTimeout.doIO(SocketIOWithTimeout.java:142)
	at org.apache.hadoop.net.SocketInputStream.read(SocketInputStream.java:161)
	at org.apache.hadoop.net.SocketInputStream.read(SocketInputStream.java:131)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at org.apache.hadoop.ipc.Client$Connection$PingInputStream.read(Client.java:513)
	at java.io.BufferedInputStream.fill(BufferedInputStream.java:246)
	at java.io.BufferedInputStream.read(BufferedInputStream.java:265)
	at java.io.DataInputStream.readInt(DataInputStream.java:387)
	at org.apache.hadoop.ipc.Client$Connection.receiveRpcResponse(Client.java:1071)
	at org.apache.hadoop.ipc.Client$Connection.run(Client.java:966)

2015-10-18 18:05:25,173 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-18 18:05:25,173 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 76802676; bufend = 20189504; bufvoid = 104857600
2015-10-18 18:05:25,173 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 19200664(76802656); kvend = 10290256(41161024); length = 8910409/6553600
2015-10-18 18:05:25,173 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29258256 kvi 7314560(29258240)
2015-10-18 18:05:35,235 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 5
2015-10-18 18:05:35,235 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29258256 kv 7314560(29258240) kvi 5109580(20438320)
2015-10-18 18:05:37,595 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-18 18:05:37,595 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29258256; bufend = 77515779; bufvoid = 104857600
2015-10-18 18:05:37,595 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7314560(29258240); kvend = 24621820(98487280); length = 8907141/6553600
2015-10-18 18:05:37,595 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 86584515 kvi 21646124(86584496)
2015-10-18 18:05:46,158 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 0 time(s); maxRetries=45
2015-10-18 18:05:47,064 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 6
2015-10-18 18:05:47,080 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 86584515 kv 21646124(86584496) kvi 19440748(77762992)
2015-10-18 18:06:06,159 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 1 time(s); maxRetries=45
2015-10-18 18:06:26,159 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 2 time(s); maxRetries=45
2015-10-18 18:06:46,160 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 3 time(s); maxRetries=45
2015-10-18 18:07:06,161 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 4 time(s); maxRetries=45
2015-10-18 18:07:26,162 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 5 time(s); maxRetries=45
2015-10-18 18:07:46,163 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 6 time(s); maxRetries=45
2015-10-18 18:08:06,163 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 7 time(s); maxRetries=45
2015-10-18 18:08:26,164 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 8 time(s); maxRetries=45
2015-10-18 18:08:46,165 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 9 time(s); maxRetries=45
2015-10-18 18:09:06,166 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 10 time(s); maxRetries=45
2015-10-18 18:09:26,167 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 11 time(s); maxRetries=45
2015-10-18 18:09:46,168 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 12 time(s); maxRetries=45
2015-10-18 18:10:06,168 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 13 time(s); maxRetries=45
2015-10-18 18:10:26,169 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 14 time(s); maxRetries=45
2015-10-18 18:10:46,170 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 15 time(s); maxRetries=45
2015-10-18 18:11:06,174 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 16 time(s); maxRetries=45
2015-10-18 18:11:26,175 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 17 time(s); maxRetries=45
2015-10-18 18:11:46,175 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 18 time(s); maxRetries=45
2015-10-18 18:12:06,176 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 19 time(s); maxRetries=45
2015-10-18 18:12:26,177 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 20 time(s); maxRetries=45
2015-10-18 18:12:46,178 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 21 time(s); maxRetries=45
2015-10-18 18:13:06,179 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 22 time(s); maxRetries=45
2015-10-18 18:13:26,183 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 23 time(s); maxRetries=45
2015-10-18 18:13:46,183 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 24 time(s); maxRetries=45
2015-10-18 18:14:06,184 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 25 time(s); maxRetries=45
2015-10-18 18:14:26,185 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 26 time(s); maxRetries=45
2015-10-18 18:14:46,186 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 27 time(s); maxRetries=45
2015-10-18 18:15:06,190 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 28 time(s); maxRetries=45
2015-10-18 18:15:26,190 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 29 time(s); maxRetries=45
2015-10-18 18:15:46,191 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 30 time(s); maxRetries=45
2015-10-18 18:16:06,192 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 31 time(s); maxRetries=45
2015-10-18 18:16:26,193 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 32 time(s); maxRetries=45
2015-10-18 18:16:46,194 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:62304. Already tried 33 time(s); maxRetries=45
