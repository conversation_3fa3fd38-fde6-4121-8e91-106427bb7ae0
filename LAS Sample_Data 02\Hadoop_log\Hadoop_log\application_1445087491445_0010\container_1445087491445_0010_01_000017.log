2015-10-17 22:32:08,180 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 22:32:08,263 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 22:32:08,263 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-17 22:32:08,283 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 22:32:08,283 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445087491445_0010, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@7253580c)
2015-10-17 22:32:08,417 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 22:32:08,725 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445087491445_0010
2015-10-17 22:32:09,245 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 22:32:09,784 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 22:32:09,803 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@56ca138e
2015-10-17 22:32:10,014 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/wordcount2.txt:805306368+134217728
2015-10-17 22:32:10,075 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-17 22:32:10,076 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-17 22:32:10,076 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-17 22:32:10,076 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-17 22:32:10,076 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-17 22:32:10,084 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-17 22:32:11,701 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:32:11,702 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 34172179; bufvoid = 104857600
2015-10-17 22:32:11,702 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 13785924(55143696); length = 12428473/6553600
2015-10-17 22:32:11,702 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 44657929 kvi 11164476(44657904)
2015-10-17 22:32:20,525 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-17 22:32:20,527 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 44657929 kv 11164476(44657904) kvi 8543052(34172208)
2015-10-17 22:32:21,436 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:32:21,436 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 44657929; bufend = 78836928; bufvoid = 104857600
2015-10-17 22:32:21,436 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 11164476(44657904); kvend = 24952116(99808464); length = 12426761/6553600
2015-10-17 22:32:21,436 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 89322688 kvi 22330668(89322672)
2015-10-17 22:32:29,749 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-17 22:32:29,752 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 89322688 kv 22330668(89322672) kvi 19709236(78836944)
2015-10-17 22:32:30,644 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:32:30,644 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 89322688; bufend = 18642505; bufvoid = 104857598
2015-10-17 22:32:30,645 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 22330668(89322672); kvend = 9903508(39614032); length = 12427161/6553600
2015-10-17 22:32:30,645 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29128260 kvi 7282060(29128240)
2015-10-17 22:32:41,815 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-17 22:32:41,820 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29128260 kv 7282060(29128240) kvi 4660632(18642528)
2015-10-17 22:32:43,137 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:32:43,138 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29128260; bufend = 63302794; bufvoid = 104857600
2015-10-17 22:32:43,138 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7282060(29128240); kvend = 21068580(84274320); length = 12427881/6553600
2015-10-17 22:32:43,138 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 73788549 kvi 18447132(73788528)
2015-10-17 22:32:50,615 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-17 22:32:50,618 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 73788549 kv 18447132(73788528) kvi 15825704(63302816)
2015-10-17 22:32:51,512 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:32:51,512 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 73788549; bufend = 3106177; bufvoid = 104857591
2015-10-17 22:32:51,512 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 18447132(73788528); kvend = 6019428(24077712); length = 12427705/6553600
2015-10-17 22:32:51,512 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 13591936 kvi 3397980(13591920)
2015-10-17 22:32:59,071 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-17 22:32:59,073 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 13591936 kv 3397980(13591920) kvi 776552(3106208)
2015-10-17 22:32:59,942 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:32:59,943 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 13591936; bufend = 47766714; bufvoid = 104857600
2015-10-17 22:32:59,943 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 3397980(13591920); kvend = 17184560(68738240); length = 12427821/6553600
2015-10-17 22:32:59,943 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 58252469 kvi 14563112(58252448)
2015-10-17 22:33:00,582 INFO [main] org.apache.hadoop.mapred.MapTask: Starting flush of map output
2015-10-17 22:33:10,628 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 5
2015-10-17 22:33:10,633 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 58252469 kv 14563112(58252448) kvi 12518536(50074144)
2015-10-17 22:33:10,633 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:33:10,634 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 58252469; bufend = 63874262; bufvoid = 104857600
2015-10-17 22:33:10,634 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14563112(58252448); kvend = 12518540(50074160); length = 2044573/6553600
2015-10-17 22:33:12,358 INFO [main] org.apache.hadoop.mapred.MapTask: Finished spill 6
2015-10-17 22:33:12,372 INFO [main] org.apache.hadoop.mapred.Merger: Merging 7 sorted segments
2015-10-17 22:33:12,382 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 7 segments left of total size: 228418410 bytes
2015-10-17 22:33:51,347 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445087491445_0010_m_000006_2 is done. And is in the process of committing
2015-10-17 22:33:51,446 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445087491445_0010_m_000006_2' done.
2015-10-17 22:33:51,548 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Stopping MapTask metrics system...
2015-10-17 22:33:51,548 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system stopped.
2015-10-17 22:33:51,548 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system shutdown complete.
