<div class="isHidden container-feature-detail" id="df-assoc-{{ which }}"Yellow
{% if dataframe.page_layout == 'vertical': %}
    style="display: contents; "
{% endif %}
>
<div class="container-feature-detail__offset"
{% if dataframe.page_layout == 'vertical': %}
    style="position: absolute; top: 137px"
{% endif %}
>
    <span class="bg-tab-detail-wide"></span>
    <span class="text-title-tab pos-text-title-tab__no-icon">Associations</span>
    <div class="text-med  pos-detail-assoc-desc-text" style="font-size: 11px;">
        <span class="color-{{ which }}">[Only including dataset "{{ dataframe.source_name if which == 'source' else dataframe.compare_name }}"]</span><br>
         &#9632; <b>Squares</b> are categorical associations (uncertainty coefficient & correlation ratio) from 0 to 1. The uncertainty coefficient is <b>assymmetrical</b>,
        (i.e. ROW LABEL values indicate how much they PROVIDE INFORMATION to each LABEL at the TOP).
       <br><br>&#8226; <b>Circles</b> are the symmetrical numerical correlations (Pearson's) from -1 to 1. The <b>trivial diagonal</b> is intentionally left blank for clarity.
    </div>
    <span class="association-graph-{{ which }} pos-detail-assoc-graph"></span>
</div>
</div>

