2015-10-17 16:51:11,913 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 16:51:12,054 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 16:51:12,054 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-17 16:51:12,085 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 16:51:12,085 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445062781478_0018, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@1623b78d)
2015-10-17 16:51:12,241 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 16:51:13,007 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445062781478_0018
2015-10-17 16:51:13,429 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 16:51:14,226 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 16:51:14,241 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@db57326
2015-10-17 16:51:14,538 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/pageinput2.txt:1073741824+134217728
2015-10-17 16:51:14,601 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-17 16:51:14,601 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-17 16:51:14,601 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-17 16:51:14,601 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-17 16:51:14,601 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-17 16:51:14,616 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-17 16:51:17,304 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 16:51:17,304 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 48246341; bufvoid = 104857600
2015-10-17 16:51:17,304 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 17304468(69217872); length = 8909929/6553600
2015-10-17 16:51:17,304 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 57315093 kvi 14328768(57315072)
2015-10-17 16:51:26,882 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-17 16:51:26,898 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 57315093 kv 14328768(57315072) kvi 12122788(48491152)
2015-10-17 16:51:28,570 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 16:51:28,570 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 57315093; bufend = 701411; bufvoid = 104857600
2015-10-17 16:51:28,570 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14328768(57315072); kvend = 5418228(21672912); length = 8910541/6553600
2015-10-17 16:51:28,570 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 9770147 kvi 2442532(9770128)
2015-10-17 16:51:36,633 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-17 16:51:36,633 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 9770147 kv 2442532(9770128) kvi 233240(932960)
2015-10-17 16:51:38,680 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 16:51:38,680 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 9770147; bufend = 58023267; bufvoid = 104857600
2015-10-17 16:51:38,680 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 2442532(9770128); kvend = 19748700(78994800); length = 8908233/6553600
2015-10-17 16:51:38,680 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 67092019 kvi 16773000(67092000)
2015-10-17 16:51:47,618 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-17 16:51:47,618 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 67092019 kv 16773000(67092000) kvi 14575980(58303920)
2015-10-17 16:51:49,383 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 16:51:49,383 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 67092019; bufend = 10489855; bufvoid = 104857600
2015-10-17 16:51:49,383 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 16773000(67092000); kvend = 7865344(31461376); length = 8907657/6553600
2015-10-17 16:51:49,383 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 19558607 kvi 4889644(19558576)
2015-10-17 16:51:59,103 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-17 16:51:59,103 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 19558607 kv 4889644(19558576) kvi 2691852(10767408)
2015-10-17 16:52:01,759 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 16:52:01,759 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 19558607; bufend = 67814201; bufvoid = 104857600
2015-10-17 16:52:01,759 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 4889644(19558576); kvend = 22196432(88785728); length = 8907613/6553600
2015-10-17 16:52:01,759 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 76882953 kvi 19220732(76882928)
2015-10-17 16:52:10,587 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-17 16:52:10,603 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 76882953 kv 19220732(76882928) kvi 17013156(68052624)
2015-10-17 16:52:12,166 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 16:52:12,166 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 76882953; bufend = 20214328; bufvoid = 104857600
2015-10-17 16:52:12,166 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 19220732(76882928); kvend = 10296460(41185840); length = 8924273/6553600
2015-10-17 16:52:12,166 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29283080 kvi 7320764(29283056)
2015-10-17 16:52:20,697 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 5
2015-10-17 16:52:20,713 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29283080 kv 7320764(29283056) kvi 5121912(20487648)
2015-10-17 16:52:22,275 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 16:52:22,275 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29283080; bufend = 77555951; bufvoid = 104857600
2015-10-17 16:52:22,275 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7320764(29283056); kvend = 24631868(98527472); length = 8903297/6553600
2015-10-17 16:52:22,275 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 86624703 kvi 21656168(86624672)
2015-10-17 16:52:30,713 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 6
2015-10-17 16:52:30,729 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 86624703 kv 21656168(86624672) kvi 19462536(77850144)
2015-10-17 16:52:31,869 INFO [main] org.apache.hadoop.mapred.MapTask: Starting flush of map output
2015-10-17 16:52:31,869 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 16:52:31,869 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 86624703; bufend = 19663524; bufvoid = 104857600
2015-10-17 16:52:31,869 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 21656168(86624672); kvend = 14655792(58623168); length = 7000377/6553600
2015-10-17 16:52:37,838 INFO [main] org.apache.hadoop.mapred.MapTask: Finished spill 7
2015-10-17 16:52:37,854 INFO [main] org.apache.hadoop.mapred.Merger: Merging 8 sorted segments
2015-10-17 16:52:37,870 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 8 segments left of total size: 288340204 bytes
2015-10-17 16:52:56,074 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445062781478_0018_m_000008_1 is done. And is in the process of committing
2015-10-17 16:52:56,214 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445062781478_0018_m_000008_1' done.
2015-10-17 16:52:56,324 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Stopping MapTask metrics system...
2015-10-17 16:52:56,324 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system stopped.
2015-10-17 16:52:56,324 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system shutdown complete.
