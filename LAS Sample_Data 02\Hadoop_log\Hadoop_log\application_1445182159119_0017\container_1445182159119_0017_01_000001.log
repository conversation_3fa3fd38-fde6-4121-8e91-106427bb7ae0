2015-10-19 17:39:14,195 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445182159119_0017_000001
2015-10-19 17:39:15,086 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-19 17:39:15,086 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 17 cluster_timestamp: 1445182159119 } attemptId: 1 } keyId: 1694045684)
2015-10-19 17:39:15,289 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-19 17:39:16,273 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-19 17:39:16,351 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-19 17:39:16,383 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-19 17:39:16,383 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-19 17:39:16,398 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-19 17:39:16,398 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-19 17:39:16,398 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-19 17:39:16,398 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-19 17:39:16,398 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-19 17:39:16,398 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-19 17:39:16,445 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 17:39:16,476 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 17:39:16,492 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 17:39:16,508 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-19 17:39:16,570 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-19 17:39:16,883 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 17:39:16,945 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 17:39:16,945 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-19 17:39:16,945 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445182159119_0017 to jobTokenSecretManager
2015-10-19 17:39:17,148 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445182159119_0017 because: not enabled; too many maps; too much input;
2015-10-19 17:39:17,164 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445182159119_0017 = 1256521728. Number of splits = 10
2015-10-19 17:39:17,164 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445182159119_0017 = 1
2015-10-19 17:39:17,164 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0017Job Transitioned from NEW to INITED
2015-10-19 17:39:17,164 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445182159119_0017.
2015-10-19 17:39:17,211 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 17:39:17,226 INFO [Socket Reader #1 for port 64400] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 64400
2015-10-19 17:39:17,242 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-19 17:39:17,258 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 17:39:17,258 INFO [IPC Server listener on 64400] org.apache.hadoop.ipc.Server: IPC Server listener on 64400: starting
2015-10-19 17:39:17,258 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MININT-FNANLI5.fareast.corp.microsoft.com/*************:64400
2015-10-19 17:39:17,336 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-19 17:39:17,336 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-19 17:39:17,352 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-19 17:39:17,352 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-19 17:39:17,352 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-19 17:39:17,352 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-19 17:39:17,352 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-19 17:39:17,367 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 64407
2015-10-19 17:39:17,367 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-19 17:39:17,445 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\Jetty_0_0_0_0_64407_mapreduce____bp6z30\webapp
2015-10-19 17:39:17,617 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:64407
2015-10-19 17:39:17,617 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 64407
2015-10-19 17:39:18,039 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-19 17:39:18,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445182159119_0017
2015-10-19 17:39:18,055 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 17:39:18,055 INFO [Socket Reader #1 for port 64410] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 64410
2015-10-19 17:39:18,070 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 17:39:18,070 INFO [IPC Server listener on 64410] org.apache.hadoop.ipc.Server: IPC Server listener on 64410: starting
2015-10-19 17:39:18,086 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-19 17:39:18,086 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-19 17:39:18,086 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-19 17:39:18,148 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-19 17:39:18,258 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-19 17:39:18,258 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-19 17:39:18,258 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-19 17:39:18,258 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-19 17:39:18,273 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0017Job Transitioned from INITED to SETUP
2015-10-19 17:39:18,289 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-19 17:39:18,305 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0017Job Transitioned from SETUP to RUNNING
2015-10-19 17:39:18,336 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:18,352 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:39:18,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:39:18,383 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-19 17:39:18,383 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-19 17:39:18,383 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445182159119_0017, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0017/job_1445182159119_0017_1.jhist
2015-10-19 17:39:19,274 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-19 17:39:19,320 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0017: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:24576, vCores:-3> knownNMs=4
2015-10-19 17:39:19,336 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:24576, vCores:-3>
2015-10-19 17:39:19,336 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:39:20,367 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 10
2015-10-19 17:39:20,383 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0017_01_000002 to attempt_1445182159119_0017_m_000000_0
2015-10-19 17:39:20,383 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0017_01_000003 to attempt_1445182159119_0017_m_000001_0
2015-10-19 17:39:20,383 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0017_01_000004 to attempt_1445182159119_0017_m_000002_0
2015-10-19 17:39:20,383 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0017_01_000005 to attempt_1445182159119_0017_m_000003_0
2015-10-19 17:39:20,383 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0017_01_000006 to attempt_1445182159119_0017_m_000004_0
2015-10-19 17:39:20,383 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0017_01_000007 to attempt_1445182159119_0017_m_000005_0
2015-10-19 17:39:20,383 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0017_01_000008 to attempt_1445182159119_0017_m_000006_0
2015-10-19 17:39:20,383 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0017_01_000009 to attempt_1445182159119_0017_m_000007_0
2015-10-19 17:39:20,383 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0017_01_000010 to attempt_1445182159119_0017_m_000008_0
2015-10-19 17:39:20,383 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0017_01_000011 to attempt_1445182159119_0017_m_000009_0
2015-10-19 17:39:20,383 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:14336, vCores:-13>
2015-10-19 17:39:20,383 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:39:20,383 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-19 17:39:20,461 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:20,492 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0017/job.jar
2015-10-19 17:39:20,492 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0017/job.xml
2015-10-19 17:39:20,492 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-19 17:39:20,492 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-19 17:39:20,508 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-19 17:39:20,555 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:39:20,555 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:20,555 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:39:20,555 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:20,555 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:39:20,555 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:20,555 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:39:20,570 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:20,570 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:39:20,570 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:20,570 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:39:20,570 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:20,570 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:39:20,570 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:20,570 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:39:20,570 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:20,570 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:39:20,570 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:39:20,570 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:39:20,570 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0017_01_000002 taskAttempt attempt_1445182159119_0017_m_000000_0
2015-10-19 17:39:20,570 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0017_01_000003 taskAttempt attempt_1445182159119_0017_m_000001_0
2015-10-19 17:39:20,570 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0017_01_000004 taskAttempt attempt_1445182159119_0017_m_000002_0
2015-10-19 17:39:20,570 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0017_01_000011 taskAttempt attempt_1445182159119_0017_m_000009_0
2015-10-19 17:39:20,570 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0017_01_000010 taskAttempt attempt_1445182159119_0017_m_000008_0
2015-10-19 17:39:20,570 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0017_01_000008 taskAttempt attempt_1445182159119_0017_m_000006_0
2015-10-19 17:39:20,570 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0017_01_000009 taskAttempt attempt_1445182159119_0017_m_000007_0
2015-10-19 17:39:20,570 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0017_01_000007 taskAttempt attempt_1445182159119_0017_m_000005_0
2015-10-19 17:39:20,570 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0017_01_000006 taskAttempt attempt_1445182159119_0017_m_000004_0
2015-10-19 17:39:20,570 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0017_01_000005 taskAttempt attempt_1445182159119_0017_m_000003_0
2015-10-19 17:39:20,570 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0017_m_000001_0
2015-10-19 17:39:20,570 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0017_m_000003_0
2015-10-19 17:39:20,570 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0017_m_000008_0
2015-10-19 17:39:20,570 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0017_m_000009_0
2015-10-19 17:39:20,570 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0017_m_000000_0
2015-10-19 17:39:20,570 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0017_m_000004_0
2015-10-19 17:39:20,570 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0017_m_000002_0
2015-10-19 17:39:20,570 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:39:20,570 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0017_m_000007_0
2015-10-19 17:39:20,570 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0017_m_000006_0
2015-10-19 17:39:20,570 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0017_m_000005_0
2015-10-19 17:39:20,602 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:39:20,602 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:39:20,602 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:39:20,602 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:39:20,602 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:39:20,602 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:39:20,602 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:39:20,602 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 17:39:20,602 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 17:39:20,695 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0017_m_000005_0 : 13562
2015-10-19 17:39:20,695 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0017_m_000007_0 : 13562
2015-10-19 17:39:20,695 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0017_m_000004_0 : 13562
2015-10-19 17:39:20,695 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0017_m_000008_0 : 13562
2015-10-19 17:39:20,695 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0017_m_000001_0 : 13562
2015-10-19 17:39:20,695 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0017_m_000009_0 : 13562
2015-10-19 17:39:20,695 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0017_m_000005_0] using containerId: [container_1445182159119_0017_01_000007 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:39:20,695 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0017_m_000006_0 : 13562
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0017_m_000008_0] using containerId: [container_1445182159119_0017_01_000010 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0017_m_000001_0] using containerId: [container_1445182159119_0017_01_000003 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0017_m_000007_0] using containerId: [container_1445182159119_0017_01_000009 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0017_m_000004_0] using containerId: [container_1445182159119_0017_01_000006 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0017_m_000009_0] using containerId: [container_1445182159119_0017_01_000011 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0017_m_000006_0] using containerId: [container_1445182159119_0017_01_000008 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0017_m_000005
2015-10-19 17:39:20,711 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0017_m_000003_0 : 13562
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0017_m_000008
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0017_m_000001
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0017_m_000007
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0017_m_000004
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0017_m_000009
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0017_m_000006
2015-10-19 17:39:20,711 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0017_m_000002_0 : 13562
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0017_m_000003_0] using containerId: [container_1445182159119_0017_01_000005 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0017_m_000002_0] using containerId: [container_1445182159119_0017_01_000004 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0017_m_000003
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0017_m_000002
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:39:20,711 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0017_m_000000_0 : 13562
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0017_m_000000_0] using containerId: [container_1445182159119_0017_01_000002 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0017_m_000000
2015-10-19 17:39:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:39:21,383 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0017: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:10240, vCores:-17> knownNMs=4
2015-10-19 17:39:21,383 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:10240, vCores:-17>
2015-10-19 17:39:21,383 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:39:24,149 INFO [Socket Reader #1 for port 64410] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0017 (auth:SIMPLE)
2015-10-19 17:39:24,149 INFO [Socket Reader #1 for port 64410] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0017 (auth:SIMPLE)
2015-10-19 17:39:24,149 INFO [Socket Reader #1 for port 64410] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0017 (auth:SIMPLE)
2015-10-19 17:39:24,164 INFO [Socket Reader #1 for port 64410] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0017 (auth:SIMPLE)
2015-10-19 17:39:24,180 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0017_m_000009 asked for a task
2015-10-19 17:39:24,180 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0017_m_000009 given task: attempt_1445182159119_0017_m_000007_0
2015-10-19 17:39:24,180 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0017_m_000007 asked for a task
2015-10-19 17:39:24,180 INFO [IPC Server handler 11 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0017_m_000003 asked for a task
2015-10-19 17:39:24,180 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0017_m_000007 given task: attempt_1445182159119_0017_m_000005_0
2015-10-19 17:39:24,180 INFO [IPC Server handler 11 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0017_m_000003 given task: attempt_1445182159119_0017_m_000001_0
2015-10-19 17:39:24,180 INFO [IPC Server handler 7 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0017_m_000008 asked for a task
2015-10-19 17:39:24,180 INFO [IPC Server handler 7 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0017_m_000008 given task: attempt_1445182159119_0017_m_000006_0
2015-10-19 17:39:24,383 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-18>
2015-10-19 17:39:24,383 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:39:24,758 INFO [Socket Reader #1 for port 64410] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0017 (auth:SIMPLE)
2015-10-19 17:39:24,774 INFO [Socket Reader #1 for port 64410] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0017 (auth:SIMPLE)
2015-10-19 17:39:24,789 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0017_m_000011 asked for a task
2015-10-19 17:39:24,789 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0017_m_000011 given task: attempt_1445182159119_0017_m_000009_0
2015-10-19 17:39:24,805 INFO [IPC Server handler 9 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0017_m_000010 asked for a task
2015-10-19 17:39:24,805 INFO [IPC Server handler 9 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0017_m_000010 given task: attempt_1445182159119_0017_m_000008_0
2015-10-19 17:39:25,227 INFO [Socket Reader #1 for port 64410] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0017 (auth:SIMPLE)
2015-10-19 17:39:25,227 INFO [Socket Reader #1 for port 64410] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0017 (auth:SIMPLE)
2015-10-19 17:39:25,258 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0017_m_000005 asked for a task
2015-10-19 17:39:25,258 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0017_m_000005 given task: attempt_1445182159119_0017_m_000003_0
2015-10-19 17:39:25,258 INFO [IPC Server handler 9 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0017_m_000006 asked for a task
2015-10-19 17:39:25,258 INFO [IPC Server handler 9 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0017_m_000006 given task: attempt_1445182159119_0017_m_000004_0
2015-10-19 17:39:25,274 INFO [Socket Reader #1 for port 64410] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0017 (auth:SIMPLE)
2015-10-19 17:39:25,289 INFO [Socket Reader #1 for port 64410] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0017 (auth:SIMPLE)
2015-10-19 17:39:25,305 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0017_m_000004 asked for a task
2015-10-19 17:39:25,305 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0017_m_000004 given task: attempt_1445182159119_0017_m_000002_0
2015-10-19 17:39:25,321 INFO [IPC Server handler 0 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0017_m_000002 asked for a task
2015-10-19 17:39:25,321 INFO [IPC Server handler 0 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0017_m_000002 given task: attempt_1445182159119_0017_m_000000_0
2015-10-19 17:39:25,399 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-19>
2015-10-19 17:39:25,399 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:39:26,399 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-20>
2015-10-19 17:39:26,399 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:39:27,446 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-21>
2015-10-19 17:39:27,446 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:39:28,571 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-23>
2015-10-19 17:39:28,571 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:39:29,602 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 17:39:29,602 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:39:32,571 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000009_0 is : 0.29013196
2015-10-19 17:39:32,587 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.10153207
2015-10-19 17:39:32,899 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.08765187
2015-10-19 17:39:32,915 INFO [IPC Server handler 7 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.089254946
2015-10-19 17:39:32,915 INFO [IPC Server handler 7 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.08904717
2015-10-19 17:39:32,930 INFO [IPC Server handler 11 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.09064071
2015-10-19 17:39:33,290 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.10680563
2015-10-19 17:39:33,290 INFO [IPC Server handler 28 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.10635664
2015-10-19 17:39:33,305 INFO [IPC Server handler 4 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.10660437
2015-10-19 17:39:33,368 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.106493875
2015-10-19 17:39:33,852 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-19 17:39:33,852 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:39:34,884 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:39:34,884 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:39:35,602 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000009_0 is : 0.295472
2015-10-19 17:39:35,602 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.106881365
2015-10-19 17:39:35,977 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.10681946
2015-10-19 17:39:35,977 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.1066108
2015-10-19 17:39:35,993 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.106964506
2015-10-19 17:39:35,993 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.10685723
2015-10-19 17:39:36,353 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.10635664
2015-10-19 17:39:36,353 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.10660437
2015-10-19 17:39:36,353 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.10680563
2015-10-19 17:39:36,415 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.106493875
2015-10-19 17:39:38,634 INFO [IPC Server handler 28 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.106881365
2015-10-19 17:39:38,634 INFO [IPC Server handler 28 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000009_0 is : 0.295472
2015-10-19 17:39:39,009 INFO [IPC Server handler 0 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.10681946
2015-10-19 17:39:39,025 INFO [IPC Server handler 7 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.1066108
2015-10-19 17:39:39,025 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.10685723
2015-10-19 17:39:39,025 INFO [IPC Server handler 11 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.106964506
2015-10-19 17:39:39,462 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.10635664
2015-10-19 17:39:39,462 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.10680563
2015-10-19 17:39:39,462 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.10660437
2015-10-19 17:39:39,462 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.106493875
2015-10-19 17:39:41,759 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000009_0 is : 0.295472
2015-10-19 17:39:41,759 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.106881365
2015-10-19 17:39:42,150 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.10681946
2015-10-19 17:39:42,150 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.1066108
2015-10-19 17:39:42,150 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.106964506
2015-10-19 17:39:42,150 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.10685723
2015-10-19 17:39:42,493 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.16709755
2015-10-19 17:39:42,493 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.17241576
2015-10-19 17:39:42,493 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.15695193
2015-10-19 17:39:42,493 INFO [IPC Server handler 28 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.16567665
2015-10-19 17:39:44,853 INFO [IPC Server handler 4 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.14021115
2015-10-19 17:39:44,853 INFO [IPC Server handler 4 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000009_0 is : 0.50739825
2015-10-19 17:39:45,337 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.15341023
2015-10-19 17:39:45,353 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.11896488
2015-10-19 17:39:45,353 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.14967197
2015-10-19 17:39:45,353 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.14161831
2015-10-19 17:39:45,619 INFO [IPC Server handler 4 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.19242907
2015-10-19 17:39:45,619 INFO [IPC Server handler 4 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.19212553
2015-10-19 17:39:45,619 INFO [IPC Server handler 4 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.19158794
2015-10-19 17:39:45,650 INFO [IPC Server handler 4 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.19209063
2015-10-19 17:39:47,916 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.19258286
2015-10-19 17:39:47,916 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000009_0 is : 0.5323719
2015-10-19 17:39:48,384 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.19211523
2015-10-19 17:39:48,384 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.19255035
2015-10-19 17:39:48,384 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.19266446
2015-10-19 17:39:48,384 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.19247705
2015-10-19 17:39:48,666 INFO [IPC Server handler 28 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.19242907
2015-10-19 17:39:48,666 INFO [IPC Server handler 28 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.19212553
2015-10-19 17:39:48,697 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.19209063
2015-10-19 17:39:48,697 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.19158794
2015-10-19 17:39:51,010 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000009_0 is : 0.5323719
2015-10-19 17:39:51,010 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.19258286
2015-10-19 17:39:51,869 INFO [IPC Server handler 1 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.19211523
2015-10-19 17:39:51,885 INFO [IPC Server handler 1 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.19255035
2015-10-19 17:39:51,885 INFO [IPC Server handler 1 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.19266446
2015-10-19 17:39:51,885 INFO [IPC Server handler 1 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.19247705
2015-10-19 17:39:51,885 INFO [IPC Server handler 1 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.19746137
2015-10-19 17:39:52,056 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.19571683
2015-10-19 17:39:52,056 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.19158794
2015-10-19 17:39:52,103 INFO [IPC Server handler 1 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.19209063
2015-10-19 17:39:54,228 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000009_0 is : 0.5323719
2015-10-19 17:39:54,228 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.19258286
2015-10-19 17:39:55,010 INFO [IPC Server handler 13 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.19247705
2015-10-19 17:39:55,010 INFO [IPC Server handler 13 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.19211523
2015-10-19 17:39:55,010 INFO [IPC Server handler 13 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.19266446
2015-10-19 17:39:55,041 INFO [IPC Server handler 15 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.19255035
2015-10-19 17:39:55,041 INFO [IPC Server handler 15 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.2672359
2015-10-19 17:39:55,166 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.25419572
2015-10-19 17:39:55,166 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.26821446
2015-10-19 17:39:55,197 INFO [IPC Server handler 1 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.23969008
2015-10-19 17:39:56,463 INFO [IPC Server handler 13 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000009_0 is : 0.5323719
2015-10-19 17:39:57,291 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.21803707
2015-10-19 17:39:57,291 INFO [IPC Server handler 13 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000009_0 is : 0.667
2015-10-19 17:39:58,104 INFO [IPC Server handler 18 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.20011275
2015-10-19 17:39:58,104 INFO [IPC Server handler 18 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.22764254
2015-10-19 17:39:58,104 INFO [IPC Server handler 18 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.2302773
2015-10-19 17:39:58,119 INFO [IPC Server handler 16 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.2781602
2015-10-19 17:39:58,119 INFO [IPC Server handler 16 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.19255035
2015-10-19 17:39:58,244 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.27696857
2015-10-19 17:39:58,260 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.27772525
2015-10-19 17:39:58,260 INFO [IPC Server handler 13 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.27765483
2015-10-19 17:40:00,416 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000009_0 is : 0.667
2015-10-19 17:40:00,416 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.27811313
2015-10-19 17:40:01,198 INFO [IPC Server handler 1 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.2678551
2015-10-19 17:40:01,198 INFO [IPC Server handler 1 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.2783809
2015-10-19 17:40:01,198 INFO [IPC Server handler 1 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.2549979
2015-10-19 17:40:01,213 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.27776006
2015-10-19 17:40:01,213 INFO [IPC Server handler 15 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.2781602
2015-10-19 17:40:01,369 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.27696857
2015-10-19 17:40:01,416 INFO [IPC Server handler 7 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.27765483
2015-10-19 17:40:01,416 INFO [IPC Server handler 0 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.27772525
2015-10-19 17:40:03,479 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000009_0 is : 0.667
2015-10-19 17:40:03,479 INFO [IPC Server handler 17 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.27811313
2015-10-19 17:40:04,323 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.2783809
2015-10-19 17:40:04,323 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.27813601
2015-10-19 17:40:04,323 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.2781602
2015-10-19 17:40:04,323 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.27776006
2015-10-19 17:40:04,417 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.27696857
2015-10-19 17:40:04,510 INFO [IPC Server handler 17 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.27765483
2015-10-19 17:40:04,526 INFO [IPC Server handler 22 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.27772525
2015-10-19 17:40:04,698 INFO [IPC Server handler 19 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.27825075
2015-10-19 17:40:08,276 INFO [IPC Server handler 15 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.27776006
2015-10-19 17:40:08,276 INFO [IPC Server handler 15 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.27813601
2015-10-19 17:40:08,276 INFO [IPC Server handler 15 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.35933742
2015-10-19 17:40:08,339 INFO [IPC Server handler 0 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.2783809
2015-10-19 17:40:08,385 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.3234089
2015-10-19 17:40:08,495 INFO [IPC Server handler 13 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.33654374
2015-10-19 17:40:08,495 INFO [IPC Server handler 13 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.3617536
2015-10-19 17:40:08,682 INFO [IPC Server handler 19 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000009_0 is : 0.70918953
2015-10-19 17:40:08,761 INFO [IPC Server handler 26 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.27825075
2015-10-19 17:40:08,761 INFO [IPC Server handler 25 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.27811313
2015-10-19 17:40:11,323 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.33710006
2015-10-19 17:40:11,339 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.36388028
2015-10-19 17:40:11,386 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.29499802
2015-10-19 17:40:11,401 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.33317554
2015-10-19 17:40:14,401 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.36319977
2015-10-19 17:40:14,464 INFO [IPC Server handler 7 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.3636334
2015-10-19 17:40:14,464 INFO [IPC Server handler 13 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.36388028
2015-10-19 17:40:14,464 INFO [IPC Server handler 7 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.36404583
2015-10-19 17:40:14,730 INFO [IPC Server handler 26 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.3025744
2015-10-19 17:40:17,464 INFO [IPC Server handler 5 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.36319977
2015-10-19 17:40:17,511 INFO [IPC Server handler 22 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.36390656
2015-10-19 17:40:17,511 INFO [IPC Server handler 17 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.41961297
2015-10-19 17:40:17,995 INFO [IPC Server handler 23 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.36404583
2015-10-19 17:40:20,636 INFO [IPC Server handler 26 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.36319977
2015-10-19 17:40:20,636 INFO [IPC Server handler 19 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.44968578
2015-10-19 17:40:20,636 INFO [IPC Server handler 26 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.36390656
2015-10-19 17:40:21,058 INFO [IPC Server handler 28 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.36404583
2015-10-19 17:40:23,683 INFO [IPC Server handler 25 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.44968578
2015-10-19 17:40:23,683 INFO [IPC Server handler 23 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.39019272
2015-10-19 17:40:23,683 INFO [IPC Server handler 25 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.36390656
2015-10-19 17:40:24,168 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.38998252
2015-10-19 17:40:25,308 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000009_0 is : 0.8549595
2015-10-19 17:40:25,590 INFO [IPC Server handler 7 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.36107287
2015-10-19 17:40:25,730 INFO [Socket Reader #1 for port 64410] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0017 (auth:SIMPLE)
2015-10-19 17:40:25,824 INFO [IPC Server handler 28 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000009_0 is : 1.0
2015-10-19 17:40:25,933 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0017_m_000009_0
2015-10-19 17:40:25,933 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:40:25,933 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0017_01_000011 taskAttempt attempt_1445182159119_0017_m_000009_0
2015-10-19 17:40:25,933 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0017_m_000009_0
2015-10-19 17:40:25,933 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 17:40:26,215 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.3624012
2015-10-19 17:40:26,261 INFO [IPC Server handler 15 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.36317363
2015-10-19 17:40:26,277 INFO [IPC Server handler 18 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.36323506
2015-10-19 17:40:26,308 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:40:26,324 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0017_m_000009_0
2015-10-19 17:40:26,324 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:40:26,324 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-19 17:40:26,449 INFO [IPC Server handler 29 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.3638923
2015-10-19 17:40:26,605 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0017_m_000000
2015-10-19 17:40:26,621 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0017_m_000000
2015-10-19 17:40:26,621 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:40:26,621 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:40:26,621 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000000_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:40:26,621 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 17:40:27,074 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.41085702
2015-10-19 17:40:27,074 INFO [IPC Server handler 15 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.45104596
2015-10-19 17:40:27,230 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-19 17:40:27,980 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.448704
2015-10-19 17:40:27,980 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.44980705
2015-10-19 17:40:27,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0017: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 17:40:27,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:40:27,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-19 17:40:27,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:10240, vCores:-17> finalMapResourceLimit:<memory:9216, vCores:-16> finalReduceResourceLimit:<memory:1024, vCores:-1> netScheduledMapResource:<memory:11264, vCores:11> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 17:40:27,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping up 1
2015-10-19 17:40:27,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-19 17:40:28,668 INFO [Socket Reader #1 for port 64410] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0017 (auth:SIMPLE)
2015-10-19 17:40:28,683 INFO [IPC Server handler 19 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.44950172
2015-10-19 17:40:29,027 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0017: ask=1 release= 0 newContainers=1 finishedContainers=1 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 17:40:29,027 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0017_01_000011
2015-10-19 17:40:29,027 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:40:29,027 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0017_01_000012 to attempt_1445182159119_0017_m_000000_1
2015-10-19 17:40:29,027 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:11 RackLocal:0
2015-10-19 17:40:29,027 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0017_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:40:29,027 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:40:29,027 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000000_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:40:29,027 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0017_01_000012 taskAttempt attempt_1445182159119_0017_m_000000_1
2015-10-19 17:40:29,027 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0017_m_000000_1
2015-10-19 17:40:29,027 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 17:40:29,183 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0017_m_000000_1 : 13562
2015-10-19 17:40:29,183 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0017_m_000000_1] using containerId: [container_1445182159119_0017_01_000012 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 17:40:29,183 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000000_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:40:29,183 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0017_m_000000
2015-10-19 17:40:29,371 INFO [Socket Reader #1 for port 64410] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0017 (auth:SIMPLE)
2015-10-19 17:40:29,434 INFO [Socket Reader #1 for port 64410] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0017 (auth:SIMPLE)
2015-10-19 17:40:29,434 INFO [Socket Reader #1 for port 64410] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0017 (auth:SIMPLE)
2015-10-19 17:40:29,434 INFO [IPC Server handler 16 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.44789755
2015-10-19 17:40:29,480 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.49078232
2015-10-19 17:40:29,496 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.47588837
2015-10-19 17:40:29,574 INFO [Socket Reader #1 for port 64410] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0017 (auth:SIMPLE)
2015-10-19 17:40:29,605 INFO [IPC Server handler 17 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.44964966
2015-10-19 17:40:30,043 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0017: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 17:40:30,105 INFO [IPC Server handler 18 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.44950968
2015-10-19 17:40:30,105 INFO [IPC Server handler 18 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.5352028
2015-10-19 17:40:31,027 INFO [IPC Server handler 15 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.44980705
2015-10-19 17:40:31,027 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.448704
2015-10-19 17:40:31,777 INFO [IPC Server handler 28 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.44950172
2015-10-19 17:40:32,199 INFO [Socket Reader #1 for port 64410] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0017 (auth:SIMPLE)
2015-10-19 17:40:32,262 INFO [IPC Server handler 16 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0017_m_000012 asked for a task
2015-10-19 17:40:32,262 INFO [IPC Server handler 16 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0017_m_000012 given task: attempt_1445182159119_0017_m_000000_1
2015-10-19 17:40:33,199 INFO [IPC Server handler 16 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.44950968
2015-10-19 17:40:33,215 INFO [IPC Server handler 21 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.5352028
2015-10-19 17:40:33,371 INFO [IPC Server handler 24 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.53341997
2015-10-19 17:40:33,434 INFO [IPC Server handler 6 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.5343203
2015-10-19 17:40:33,434 INFO [IPC Server handler 6 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.5342037
2015-10-19 17:40:34,106 INFO [IPC Server handler 18 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.44980705
2015-10-19 17:40:34,121 INFO [IPC Server handler 16 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.448704
2015-10-19 17:40:34,762 INFO [IPC Server handler 19 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.44964966
2015-10-19 17:40:34,840 INFO [IPC Server handler 28 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.4856301
2015-10-19 17:40:36,371 INFO [IPC Server handler 24 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.5352028
2015-10-19 17:40:36,371 INFO [IPC Server handler 24 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.44950968
2015-10-19 17:40:36,559 INFO [IPC Server handler 11 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.53341997
2015-10-19 17:40:37,184 INFO [IPC Server handler 21 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.53543663
2015-10-19 17:40:37,200 INFO [IPC Server handler 24 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.53425497
2015-10-19 17:40:37,871 INFO [IPC Server handler 25 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.53255355
2015-10-19 17:40:37,981 INFO [IPC Server handler 28 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.53521925
2015-10-19 17:40:38,746 INFO [IPC Server handler 20 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.5343203
2015-10-19 17:40:38,746 INFO [IPC Server handler 20 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.5342037
2015-10-19 17:40:39,528 INFO [IPC Server handler 6 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_1 is : 0.10635664
2015-10-19 17:40:39,528 INFO [IPC Server handler 6 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.58954155
2015-10-19 17:40:39,590 INFO [IPC Server handler 6 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.5352021
2015-10-19 17:40:39,668 INFO [IPC Server handler 29 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.53341997
2015-10-19 17:40:40,247 INFO [IPC Server handler 16 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.53543663
2015-10-19 17:40:40,247 INFO [IPC Server handler 18 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.53425497
2015-10-19 17:40:41,684 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0017_m_000001
2015-10-19 17:40:41,684 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 17:40:41,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0017_m_000001
2015-10-19 17:40:41,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:40:41,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:40:41,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000001_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:40:42,590 INFO [IPC Server handler 6 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_1 is : 0.10635664
2015-10-19 17:40:42,637 INFO [IPC Server handler 29 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.6208445
2015-10-19 17:40:42,637 INFO [IPC Server handler 29 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.5352021
2015-10-19 17:40:42,715 INFO [IPC Server handler 22 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.58936584
2015-10-19 17:40:43,059 INFO [IPC Server handler 26 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.5352825
2015-10-19 17:40:43,169 INFO [IPC Server handler 23 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.53521925
2015-10-19 17:40:43,294 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.53543663
2015-10-19 17:40:43,419 INFO [IPC Server handler 15 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.53425497
2015-10-19 17:40:43,747 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:11 RackLocal:0
2015-10-19 17:40:45,700 INFO [IPC Server handler 6 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_1 is : 0.10635664
2015-10-19 17:40:45,716 INFO [IPC Server handler 29 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.6208445
2015-10-19 17:40:45,762 INFO [IPC Server handler 22 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.5352021
2015-10-19 17:40:45,872 INFO [IPC Server handler 11 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.61898744
2015-10-19 17:40:46,263 INFO [IPC Server handler 25 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.5601186
2015-10-19 17:40:46,263 INFO [IPC Server handler 26 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.5410951
2015-10-19 17:40:46,356 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.6023213
2015-10-19 17:40:46,372 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0017: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 17:40:46,497 INFO [IPC Server handler 15 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.5903695
2015-10-19 17:40:48,075 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.5994994
2015-10-19 17:40:48,091 INFO [IPC Server handler 4 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.6165186
2015-10-19 17:40:48,747 INFO [IPC Server handler 24 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_1 is : 0.13898641
2015-10-19 17:40:48,856 INFO [IPC Server handler 29 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.6208445
2015-10-19 17:40:48,919 INFO [IPC Server handler 11 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.6209487
2015-10-19 17:40:48,919 INFO [IPC Server handler 11 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.61898744
2015-10-19 17:40:49,310 INFO [IPC Server handler 26 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.6207798
2015-10-19 17:40:49,310 INFO [IPC Server handler 26 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.620844
2015-10-19 17:40:49,403 INFO [IPC Server handler 28 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.6210422
2015-10-19 17:40:49,653 INFO [IPC Server handler 24 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.6197233
2015-10-19 17:40:50,716 INFO [IPC Server handler 15 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.6208445
2015-10-19 17:40:51,185 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.6490534
2015-10-19 17:40:51,185 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.66699797
2015-10-19 17:40:51,185 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.66699797
2015-10-19 17:40:51,544 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.6490534
2015-10-19 17:40:51,825 INFO [IPC Server handler 21 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_1 is : 0.19158794
2015-10-19 17:40:51,935 INFO [IPC Server handler 6 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.667
2015-10-19 17:40:52,091 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.62652963
2015-10-19 17:40:52,091 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.6209487
2015-10-19 17:40:52,419 INFO [IPC Server handler 25 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.6207798
2015-10-19 17:40:52,435 INFO [IPC Server handler 25 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.620844
2015-10-19 17:40:52,528 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.6210422
2015-10-19 17:40:52,716 INFO [IPC Server handler 15 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.6197233
2015-10-19 17:40:52,794 INFO [IPC Server handler 24 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.62652963
2015-10-19 17:40:54,247 INFO [IPC Server handler 4 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.667
2015-10-19 17:40:54,263 INFO [IPC Server handler 1 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.667
2015-10-19 17:40:54,935 INFO [IPC Server handler 16 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_1 is : 0.19158794
2015-10-19 17:40:55,013 INFO [IPC Server handler 21 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.667
2015-10-19 17:40:55,216 INFO [IPC Server handler 22 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.667
2015-10-19 17:40:55,216 INFO [IPC Server handler 22 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.6209487
2015-10-19 17:40:55,591 INFO [IPC Server handler 26 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.6207798
2015-10-19 17:40:55,591 INFO [IPC Server handler 26 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.620844
2015-10-19 17:40:55,638 INFO [IPC Server handler 28 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.6210422
2015-10-19 17:40:55,794 INFO [IPC Server handler 15 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.6197233
2015-10-19 17:40:56,810 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.6210422
2015-10-19 17:40:57,341 INFO [IPC Server handler 22 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.6197233
2015-10-19 17:40:57,341 INFO [IPC Server handler 22 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.667
2015-10-19 17:40:57,419 INFO [IPC Server handler 29 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.667
2015-10-19 17:40:57,669 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.6209487
2015-10-19 17:40:58,248 INFO [IPC Server handler 24 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.620844
2015-10-19 17:40:58,248 INFO [IPC Server handler 21 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_1 is : 0.19158794
2015-10-19 17:40:58,373 INFO [IPC Server handler 6 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.667
2015-10-19 17:40:58,373 INFO [IPC Server handler 6 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.667
2015-10-19 17:40:58,513 INFO [IPC Server handler 22 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.6671301
2015-10-19 17:40:58,623 INFO [IPC Server handler 1 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.657313
2015-10-19 17:40:58,638 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.667
2015-10-19 17:40:58,826 INFO [IPC Server handler 9 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.667
2015-10-19 17:40:58,888 INFO [IPC Server handler 26 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.667
2015-10-19 17:40:59,029 INFO [IPC Server handler 18 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.657313
2015-10-19 17:41:00,466 INFO [IPC Server handler 24 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.68325347
2015-10-19 17:41:00,482 INFO [IPC Server handler 6 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.6753092
2015-10-19 17:41:01,341 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_1 is : 0.26489684
2015-10-19 17:41:01,435 INFO [IPC Server handler 24 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.667
2015-10-19 17:41:01,435 INFO [IPC Server handler 21 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.672317
2015-10-19 17:41:01,560 INFO [IPC Server handler 22 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.7048848
2015-10-19 17:41:02,107 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.667
2015-10-19 17:41:02,326 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.667
2015-10-19 17:41:02,326 INFO [IPC Server handler 28 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.667
2015-10-19 17:41:04,123 INFO [IPC Server handler 4 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.7184695
2015-10-19 17:41:04,123 INFO [IPC Server handler 4 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.71016026
2015-10-19 17:41:04,123 INFO [IPC Server handler 4 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.667
2015-10-19 17:41:04,373 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_1 is : 0.27696857
2015-10-19 17:41:04,560 INFO [IPC Server handler 28 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.70525956
2015-10-19 17:41:04,560 INFO [IPC Server handler 28 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.667
2015-10-19 17:41:04,607 INFO [IPC Server handler 18 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.7389963
2015-10-19 17:41:05,232 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.667
2015-10-19 17:41:05,389 INFO [IPC Server handler 19 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.667
2015-10-19 17:41:05,404 INFO [IPC Server handler 19 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.66771173
2015-10-19 17:41:07,201 INFO [IPC Server handler 29 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.667
2015-10-19 17:41:07,201 INFO [IPC Server handler 11 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.75347465
2015-10-19 17:41:07,201 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.7480757
2015-10-19 17:41:07,404 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_1 is : 0.27696857
2015-10-19 17:41:07,654 INFO [IPC Server handler 28 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.66907316
2015-10-19 17:41:07,654 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.7368959
2015-10-19 17:41:07,654 INFO [IPC Server handler 15 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.76732653
2015-10-19 17:41:08,279 INFO [IPC Server handler 29 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.67575413
2015-10-19 17:41:08,498 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.6855158
2015-10-19 17:41:08,498 INFO [IPC Server handler 1 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.6837343
2015-10-19 17:41:10,311 INFO [IPC Server handler 6 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.6873911
2015-10-19 17:41:10,311 INFO [IPC Server handler 29 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.78634804
2015-10-19 17:41:10,311 INFO [IPC Server handler 22 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.78008157
2015-10-19 17:41:10,498 INFO [IPC Server handler 11 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_1 is : 0.27696857
2015-10-19 17:41:10,701 INFO [IPC Server handler 25 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.6988335
2015-10-19 17:41:10,701 INFO [IPC Server handler 25 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.7684361
2015-10-19 17:41:10,701 INFO [IPC Server handler 25 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.79764163
2015-10-19 17:41:11,405 INFO [IPC Server handler 29 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.70720303
2015-10-19 17:41:11,576 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.7159968
2015-10-19 17:41:11,576 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.7158687
2015-10-19 17:41:13,420 INFO [IPC Server handler 24 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.8104437
2015-10-19 17:41:13,420 INFO [IPC Server handler 6 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.71401864
2015-10-19 17:41:13,420 INFO [IPC Server handler 22 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.8038982
2015-10-19 17:41:13,608 INFO [IPC Server handler 11 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_1 is : 0.290609
2015-10-19 17:41:13,889 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.72702193
2015-10-19 17:41:13,889 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.7927195
2015-10-19 17:41:13,889 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.8224153
2015-10-19 17:41:14,498 INFO [IPC Server handler 29 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.7404376
2015-10-19 17:41:14,655 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.7488544
2015-10-19 17:41:14,655 INFO [IPC Server handler 11 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.74664396
2015-10-19 17:41:16,577 INFO [IPC Server handler 29 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.84169567
2015-10-19 17:41:16,592 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.75248504
2015-10-19 17:41:16,592 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.83442783
2015-10-19 17:41:16,686 INFO [IPC Server handler 4 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_1 is : 0.35481903
2015-10-19 17:41:16,999 INFO [IPC Server handler 1 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.76732457
2015-10-19 17:41:16,999 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.8290426
2015-10-19 17:41:17,030 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.8587228
2015-10-19 17:41:17,561 INFO [IPC Server handler 18 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.7768216
2015-10-19 17:41:17,764 INFO [IPC Server handler 24 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.7909611
2015-10-19 17:41:17,764 INFO [IPC Server handler 22 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.7866444
2015-10-19 17:41:19,702 INFO [IPC Server handler 16 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.7967545
2015-10-19 17:41:19,702 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.86688566
2015-10-19 17:41:19,702 INFO [IPC Server handler 18 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.8740381
2015-10-19 17:41:19,780 INFO [IPC Server handler 6 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_1 is : 0.3624012
2015-10-19 17:41:20,467 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.85892653
2015-10-19 17:41:20,467 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.8078936
2015-10-19 17:41:20,483 INFO [IPC Server handler 16 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.8891823
2015-10-19 17:41:20,639 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.8114544
2015-10-19 17:41:20,858 INFO [IPC Server handler 22 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.820704
2015-10-19 17:41:20,858 INFO [IPC Server handler 24 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.82681715
2015-10-19 17:41:22,827 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.88613355
2015-10-19 17:41:22,827 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.8163487
2015-10-19 17:41:22,827 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.8941883
2015-10-19 17:41:22,983 INFO [IPC Server handler 18 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_1 is : 0.3624012
2015-10-19 17:41:23,608 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.91154087
2015-10-19 17:41:23,608 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.8814548
2015-10-19 17:41:23,608 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.8291917
2015-10-19 17:41:23,733 INFO [IPC Server handler 10 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.8496268
2015-10-19 17:41:23,968 INFO [IPC Server handler 18 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.8473256
2015-10-19 17:41:23,999 INFO [IPC Server handler 24 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.8400922
2015-10-19 17:41:25,905 INFO [IPC Server handler 22 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.9165043
2015-10-19 17:41:25,905 INFO [IPC Server handler 22 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.84817064
2015-10-19 17:41:25,905 INFO [IPC Server handler 6 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.9250035
2015-10-19 17:41:25,999 INFO [IPC Server handler 18 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_1 is : 0.3624012
2015-10-19 17:41:26,733 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.9132367
2015-10-19 17:41:26,733 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.9435808
2015-10-19 17:41:26,733 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.86686647
2015-10-19 17:41:26,890 INFO [IPC Server handler 17 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.8825339
2015-10-19 17:41:27,062 INFO [IPC Server handler 24 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.8875481
2015-10-19 17:41:27,093 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.87827253
2015-10-19 17:41:29,077 INFO [IPC Server handler 17 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.95129097
2015-10-19 17:41:29,077 INFO [IPC Server handler 17 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.88080275
2015-10-19 17:41:29,077 INFO [IPC Server handler 17 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_1 is : 0.4272552
2015-10-19 17:41:29,077 INFO [IPC Server handler 17 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.96232355
2015-10-19 17:41:30,265 INFO [IPC Server handler 22 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.91693044
2015-10-19 17:41:30,468 INFO [IPC Server handler 29 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.90343624
2015-10-19 17:41:30,468 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.9130556
2015-10-19 17:41:30,718 INFO [IPC Server handler 4 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 0.98566747
2015-10-19 17:41:30,718 INFO [IPC Server handler 4 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.89298695
2015-10-19 17:41:30,718 INFO [IPC Server handler 4 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.9564253
2015-10-19 17:41:31,077 INFO [IPC Server handler 19 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000004_0 is : 1.0
2015-10-19 17:41:31,077 INFO [IPC Server handler 7 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0017_m_000004_0
2015-10-19 17:41:31,077 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:41:31,077 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0017_01_000006 taskAttempt attempt_1445182159119_0017_m_000004_0
2015-10-19 17:41:31,077 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0017_m_000004_0
2015-10-19 17:41:31,077 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:41:31,499 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:41:31,499 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0017_m_000004_0
2015-10-19 17:41:31,499 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:41:31,499 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-19 17:41:32,187 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.9122635
2015-10-19 17:41:32,187 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_1 is : 0.44789755
2015-10-19 17:41:32,187 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 0.9966599
2015-10-19 17:41:32,187 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 0.9852836
2015-10-19 17:41:32,578 INFO [IPC Server handler 22 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000002_0 is : 1.0
2015-10-19 17:41:32,578 INFO [IPC Server handler 29 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0017_m_000002_0
2015-10-19 17:41:32,578 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:41:32,578 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0017_01_000004 taskAttempt attempt_1445182159119_0017_m_000002_0
2015-10-19 17:41:32,578 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0017_m_000002_0
2015-10-19 17:41:32,578 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:41:32,984 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:11 RackLocal:0
2015-10-19 17:41:32,999 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:41:32,999 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0017_m_000002_0
2015-10-19 17:41:32,999 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:41:32,999 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-19 17:41:33,312 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.9525984
2015-10-19 17:41:33,499 INFO [IPC Server handler 22 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.946555
2015-10-19 17:41:33,515 INFO [IPC Server handler 6 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.9345983
2015-10-19 17:41:33,749 INFO [IPC Server handler 21 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.9312783
2015-10-19 17:41:33,749 INFO [IPC Server handler 4 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 0.9942942
2015-10-19 17:41:34,078 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:3 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:11 RackLocal:0
2015-10-19 17:41:34,078 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0017_01_000006
2015-10-19 17:41:34,078 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0017_01_000004
2015-10-19 17:41:34,078 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-19 17:41:34,078 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-19 17:41:34,078 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0017_01_000013 to attempt_1445182159119_0017_r_000000_0
2015-10-19 17:41:34,078 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0017_01_000014 to attempt_1445182159119_0017_m_000001_1
2015-10-19 17:41:34,078 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-19 17:41:34,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0017_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:41:34,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0017_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:41:34,093 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:41:34,093 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:41:34,093 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:41:34,093 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000001_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:41:34,109 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0017_01_000013 taskAttempt attempt_1445182159119_0017_r_000000_0
2015-10-19 17:41:34,109 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0017_r_000000_0
2015-10-19 17:41:34,109 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:41:34,109 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0017_01_000014 taskAttempt attempt_1445182159119_0017_m_000001_1
2015-10-19 17:41:34,109 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0017_m_000001_1
2015-10-19 17:41:34,109 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:41:35,062 INFO [IPC Server handler 22 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000003_0 is : 1.0
2015-10-19 17:41:35,062 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0017_m_000001_1 : 13562
2015-10-19 17:41:35,062 INFO [IPC Server handler 4 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_0 is : 1.0
2015-10-19 17:41:35,062 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0017_m_000001_1] using containerId: [container_1445182159119_0017_01_000014 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:41:35,062 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000001_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:41:35,062 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0017_m_000001
2015-10-19 17:41:35,062 INFO [IPC Server handler 29 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0017_m_000000_0
2015-10-19 17:41:35,062 INFO [IPC Server handler 29 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0017_m_000003_0
2015-10-19 17:41:35,062 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:41:35,062 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:41:35,062 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0017_01_000002 taskAttempt attempt_1445182159119_0017_m_000000_0
2015-10-19 17:41:35,062 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0017_01_000005 taskAttempt attempt_1445182159119_0017_m_000003_0
2015-10-19 17:41:35,062 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0017_m_000003_0
2015-10-19 17:41:35,062 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:41:35,062 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0017_m_000000_0
2015-10-19 17:41:35,062 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:41:35,078 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0017_r_000000_0 : 13562
2015-10-19 17:41:35,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0017_r_000000_0] using containerId: [container_1445182159119_0017_01_000013 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:41:35,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:41:35,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0017_r_000000
2015-10-19 17:41:35,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:41:35,421 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000000_1 is : 0.44789755
2015-10-19 17:41:35,421 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.9351693
2015-10-19 17:41:35,750 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0017: ask=5 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 17:41:35,750 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:41:35,750 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0017_m_000000_0
2015-10-19 17:41:35,750 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0017_m_000000_1
2015-10-19 17:41:35,750 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:41:35,750 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-19 17:41:35,750 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000000_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 17:41:35,750 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0017_01_000012 taskAttempt attempt_1445182159119_0017_m_000000_1
2015-10-19 17:41:35,750 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0017_m_000000_1
2015-10-19 17:41:35,750 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 17:41:35,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:41:35,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0017_m_000003_0
2015-10-19 17:41:35,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:41:35,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-19 17:41:36,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000000_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 17:41:36,140 INFO [Socket Reader #1 for port 64410] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 64410: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 17:41:36,312 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 0.98504144
2015-10-19 17:41:36,453 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 17:41:36,875 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out3/_temporary/1/_temporary/attempt_1445182159119_0017_m_000000_1
2015-10-19 17:41:36,875 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000000_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 17:41:36,906 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 0.97060513
2015-10-19 17:41:36,906 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.9571241
2015-10-19 17:41:36,906 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.95465004
2015-10-19 17:41:36,906 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-19 17:41:36,906 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0017_01_000002
2015-10-19 17:41:36,906 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0017_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:41:36,906 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0017_01_000005
2015-10-19 17:41:36,906 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-19 17:41:36,906 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0017_m_000003_0: 
2015-10-19 17:41:37,500 INFO [Socket Reader #1 for port 64410] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0017 (auth:SIMPLE)
2015-10-19 17:41:37,531 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0017_m_000014 asked for a task
2015-10-19 17:41:37,531 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0017_m_000014 given task: attempt_1445182159119_0017_m_000001_1
2015-10-19 17:41:37,765 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000008_0 is : 1.0
2015-10-19 17:41:37,797 INFO [IPC Server handler 7 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0017_m_000008_0
2015-10-19 17:41:37,797 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:41:37,797 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0017_01_000010 taskAttempt attempt_1445182159119_0017_m_000008_0
2015-10-19 17:41:37,812 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0017_m_000008_0
2015-10-19 17:41:37,812 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 17:41:38,187 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0017_01_000012
2015-10-19 17:41:38,187 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-19 17:41:38,187 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0017_m_000000_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:41:38,187 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:41:38,187 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0017_m_000008_0
2015-10-19 17:41:38,187 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:41:38,187 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-19 17:41:38,281 INFO [Socket Reader #1 for port 64410] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0017 (auth:SIMPLE)
2015-10-19 17:41:38,328 INFO [IPC Server handler 21 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0017_r_000013 asked for a task
2015-10-19 17:41:38,328 INFO [IPC Server handler 21 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0017_r_000013 given task: attempt_1445182159119_0017_r_000000_0
2015-10-19 17:41:38,484 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.96996653
2015-10-19 17:41:39,250 INFO [IPC Server handler 11 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000001_0 is : 1.0
2015-10-19 17:41:39,250 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-19 17:41:39,281 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0017_01_000010
2015-10-19 17:41:39,281 INFO [IPC Server handler 29 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0017_m_000001_0
2015-10-19 17:41:39,281 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-19 17:41:39,281 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0017_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:41:39,281 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:41:39,281 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0017_01_000003 taskAttempt attempt_1445182159119_0017_m_000001_0
2015-10-19 17:41:39,281 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0017_m_000001_0
2015-10-19 17:41:39,281 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:41:39,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:41:39,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0017_m_000001_0
2015-10-19 17:41:39,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0017_m_000001_1
2015-10-19 17:41:39,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:41:39,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-19 17:41:39,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000001_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 17:41:39,765 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0017_01_000014 taskAttempt attempt_1445182159119_0017_m_000001_1
2015-10-19 17:41:39,765 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0017_m_000001_1
2015-10-19 17:41:39,765 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:41:39,797 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0017_r_000000_0. startIndex 0 maxEvents 10000
2015-10-19 17:41:40,312 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000001_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 17:41:40,359 INFO [IPC Server handler 11 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 0.98901415
2015-10-19 17:41:40,359 INFO [IPC Server handler 11 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 0.99300534
2015-10-19 17:41:40,375 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-19 17:41:40,390 INFO [Socket Reader #1 for port 64410] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 64410: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 17:41:40,437 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0017_01_000003
2015-10-19 17:41:40,437 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-19 17:41:40,437 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0017_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:41:40,609 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 17:41:40,656 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out3/_temporary/1/_temporary/attempt_1445182159119_0017_m_000001_1
2015-10-19 17:41:40,656 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000001_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 17:41:40,859 INFO [IPC Server handler 7 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0017_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 17:41:41,094 INFO [IPC Server handler 11 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000006_0 is : 1.0
2015-10-19 17:41:41,172 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0017_m_000006_0
2015-10-19 17:41:41,172 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:41:41,172 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0017_01_000008 taskAttempt attempt_1445182159119_0017_m_000006_0
2015-10-19 17:41:41,172 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0017_m_000006_0
2015-10-19 17:41:41,172 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:41:41,625 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000005_0 is : 1.0
2015-10-19 17:41:41,625 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 0.9975213
2015-10-19 17:41:41,703 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0017_01_000014
2015-10-19 17:41:41,703 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-19 17:41:41,703 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0017_m_000005_0
2015-10-19 17:41:41,703 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0017_m_000001_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:41:41,703 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:41:41,703 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:41:41,703 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0017_m_000006_0
2015-10-19 17:41:41,703 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:41:41,703 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-19 17:41:41,734 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0017_01_000007 taskAttempt attempt_1445182159119_0017_m_000005_0
2015-10-19 17:41:41,750 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0017_m_000005_0
2015-10-19 17:41:41,750 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:41:41,812 INFO [IPC Server handler 7 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_m_000007_0 is : 1.0
2015-10-19 17:41:41,875 INFO [IPC Server handler 11 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0017_m_000007_0
2015-10-19 17:41:41,875 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:41:41,875 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0017_01_000009 taskAttempt attempt_1445182159119_0017_m_000007_0
2015-10-19 17:41:41,875 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0017_m_000007_0
2015-10-19 17:41:41,875 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:41:41,922 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0017_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 17:41:42,453 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:41:42,453 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0017_m_000005_0
2015-10-19 17:41:42,453 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:41:42,453 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-19 17:41:42,547 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:41:42,547 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0017_m_000007_0
2015-10-19 17:41:42,547 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:41:42,547 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-19 17:41:42,781 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-19 17:41:42,859 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0017_01_000008
2015-10-19 17:41:42,859 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0017_01_000007
2015-10-19 17:41:42,859 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-19 17:41:42,859 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0017_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:41:42,859 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0017_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:41:43,000 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0017_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 17:41:44,031 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0017_01_000009
2015-10-19 17:41:44,031 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-19 17:41:44,031 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0017_m_000007_0: Container killed by the ApplicationMaster.

2015-10-19 17:41:44,063 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0017_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:41:44,719 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.0
2015-10-19 17:41:44,891 INFO [IPC Server handler 16 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.0
2015-10-19 17:41:45,891 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.6667899
2015-10-19 17:41:48,938 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.67876804
2015-10-19 17:41:51,985 INFO [IPC Server handler 12 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.6949384
2015-10-19 17:41:55,110 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.7144856
2015-10-19 17:41:58,220 INFO [IPC Server handler 16 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.7298837
2015-10-19 17:42:01,267 INFO [IPC Server handler 16 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.74556094
2015-10-19 17:42:04,314 INFO [IPC Server handler 16 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.7571835
2015-10-19 17:42:07,345 INFO [IPC Server handler 16 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.76933897
2015-10-19 17:42:10,470 INFO [IPC Server handler 17 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.78597313
2015-10-19 17:42:13,502 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.80286986
2015-10-19 17:42:16,549 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.81663156
2015-10-19 17:42:19,611 INFO [IPC Server handler 8 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.82868063
2015-10-19 17:42:22,658 INFO [IPC Server handler 16 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.84537363
2015-10-19 17:42:25,705 INFO [IPC Server handler 28 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.8548373
2015-10-19 17:42:29,096 INFO [IPC Server handler 15 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.86882865
2015-10-19 17:42:32,221 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.8869854
2015-10-19 17:42:35,300 INFO [IPC Server handler 28 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.89837253
2015-10-19 17:42:38,331 INFO [IPC Server handler 28 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.91850513
2015-10-19 17:42:41,378 INFO [IPC Server handler 15 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.935179
2015-10-19 17:42:44,410 INFO [IPC Server handler 15 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.9517594
2015-10-19 17:42:47,472 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.9641489
2015-10-19 17:42:50,504 INFO [IPC Server handler 2 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.9831822
2015-10-19 17:42:53,551 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 0.9956752
2015-10-19 17:42:54,332 INFO [IPC Server handler 15 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445182159119_0017_r_000000_0
2015-10-19 17:42:54,332 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_r_000000_0 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-19 17:42:54,332 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445182159119_0017_r_000000_0 given a go for committing the task output.
2015-10-19 17:42:54,348 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445182159119_0017_r_000000_0
2015-10-19 17:42:54,348 INFO [IPC Server handler 27 on 64410] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445182159119_0017_r_000000_0:true
2015-10-19 17:42:54,394 INFO [IPC Server handler 11 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0017_r_000000_0 is : 1.0
2015-10-19 17:42:54,394 INFO [IPC Server handler 3 on 64410] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0017_r_000000_0
2015-10-19 17:42:54,394 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_r_000000_0 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:42:54,394 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0017_01_000013 taskAttempt attempt_1445182159119_0017_r_000000_0
2015-10-19 17:42:54,394 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0017_r_000000_0
2015-10-19 17:42:54,394 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:42:54,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0017_r_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:42:54,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0017_r_000000_0
2015-10-19 17:42:54,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0017_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:42:54,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-19 17:42:54,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0017Job Transitioned from RUNNING to COMMITTING
2015-10-19 17:42:55,066 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-19 17:42:55,691 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-19 17:42:56,270 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0017_01_000013
2015-10-19 17:42:56,270 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-19 17:42:56,270 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0017_r_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:42:56,598 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-19 17:42:56,598 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0017Job Transitioned from COMMITTING to SUCCEEDED
2015-10-19 17:42:56,941 INFO [Thread-97] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-19 17:42:56,941 INFO [Thread-97] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-19 17:42:56,941 INFO [Thread-97] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-19 17:42:56,941 INFO [Thread-97] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-19 17:42:56,941 INFO [Thread-97] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-19 17:42:56,941 INFO [Thread-97] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-19 17:42:56,941 INFO [Thread-97] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-19 17:42:58,973 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0017/job_1445182159119_0017_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0017-1445247551183-msrabi-pagerank-1445247776598-10-1-SUCCEEDED-default-1445247558273.jhist_tmp
2015-10-19 17:43:01,379 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0017-1445247551183-msrabi-pagerank-1445247776598-10-1-SUCCEEDED-default-1445247558273.jhist_tmp
2015-10-19 17:43:01,582 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0017/job_1445182159119_0017_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0017_conf.xml_tmp
2015-10-19 17:43:05,786 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0017_conf.xml_tmp
2015-10-19 17:43:05,879 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0017.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0017.summary
2015-10-19 17:43:05,926 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0017_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0017_conf.xml
2015-10-19 17:43:05,989 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0017-1445247551183-msrabi-pagerank-1445247776598-10-1-SUCCEEDED-default-1445247558273.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0017-1445247551183-msrabi-pagerank-1445247776598-10-1-SUCCEEDED-default-1445247558273.jhist
2015-10-19 17:43:05,989 INFO [Thread-97] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-19 17:43:06,004 INFO [Thread-97] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-19 17:43:06,004 INFO [Thread-97] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MININT-FNANLI5.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445182159119_0017
2015-10-19 17:43:06,036 INFO [Thread-97] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-19 17:43:07,145 INFO [Thread-97] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-19 17:43:07,145 INFO [Thread-97] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0017
2015-10-19 17:43:07,239 INFO [Thread-97] org.apache.hadoop.ipc.Server: Stopping server on 64410
2015-10-19 17:43:07,348 INFO [IPC Server listener on 64410] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 64410
2015-10-19 17:43:07,348 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-19 17:43:07,348 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
