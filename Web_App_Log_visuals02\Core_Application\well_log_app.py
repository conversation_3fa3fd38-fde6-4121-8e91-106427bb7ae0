"""
Streamlit Web App for Well Log Data Visualization and Petrophysical Analysis
Designed for Oil & Gas domain experts - Fully offline, responsive, and interactive
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import io
import base64
from datetime import datetime

# Page configuration
st.set_page_config(
    page_title="Well Log Analyzer - ONGC",
    # page_icon="🛢️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for Oil & Gas styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f4e79;
        text-align: center;
        margin-bottom: 1rem;
        font-weight: bold;
    }
    .sub-header {
        font-size: 1.5rem;
        color: #2c5aa0;
        margin-bottom: 1rem;
    }
    .metric-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #1f4e79;
        margin-bottom: 1rem;
    }
    .upload-section {
        background-color: #e8f4fd;
        padding: 2rem;
        border-radius: 15px;
        border: 2px dashed #1f4e79;
        text-align: center;
        margin-bottom: 2rem;
    }
    .stAlert > div {
        background-color: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }
    .track-title {
        font-size: 1.2rem;
        font-weight: bold;
        color: #1f4e79;
        text-align: center;
        margin-bottom: 0.5rem;
    }
</style>
""", unsafe_allow_html=True)

# Required columns for well log analysis
REQUIRED_COLUMNS = ['DEPTH_MD', 'GR', 'RDEP', 'RHOB', 'NPHI', 'CALI', 'DTC', 'PEF']

# Optional columns for enhanced analysis (Xeek dataset)
OPTIONAL_COLUMNS = ['WELL', 'GROUP', 'FORMATION', 'LITH']

# Comprehensive column mapping dictionary for intelligent data handling
COLUMN_MAPPING = {
    # Depth columns
    'DEPTH_MD': ['DEPTH_MD', 'DEPTH', 'MD', 'MEASURED_DEPTH', 'TVD', 'DEPT', 'Z_LOC'],

    # Gamma Ray columns
    'GR': ['GR', 'GAMMA_RAY', 'GAMMA', 'SGR', 'GRC', 'GRCFM'],

    # Resistivity columns
    'RDEP': ['RDEP', 'RDEEP', 'RT', 'RES_DEEP', 'RESISTIVITY', 'RMED', 'RM', 'RXO'],

    # Density columns
    'RHOB': ['RHOB', 'DENSITY', 'DEN', 'BULK_DENSITY', 'RHOZ', 'ZDEN'],

    # Neutron Porosity columns
    'NPHI': ['NPHI', 'NEUTRON', 'NEU', 'NEUTRON_POROSITY', 'PHIN', 'CNPOR'],

    # Caliper columns
    'CALI': ['CALI', 'CALIPER', 'CAL', 'BS', 'BIT_SIZE', 'HOLE_SIZE'],

    # Delta Time Compressional columns
    'DTC': ['DTC', 'DELTA_TIME', 'AC', 'ACOUSTIC', 'TRANSIT_TIME', 'DT'],

    # Photoelectric Factor columns
    'PEF': ['PEF', 'PHOTOELECTRIC', 'PE', 'PEFZ', 'U'],

    # Optional columns
    'WELL': ['WELL', 'WELL_NAME', 'WELLNAME', 'UWI', 'API'],
    'GROUP': ['GROUP', 'GEOLOGICAL_GROUP', 'GEO_GROUP'],
    'FORMATION': ['FORMATION', 'FORM', 'FM', 'GEOLOGICAL_FORMATION'],
    'LITH': ['LITH', 'LITHOLOGY', 'ROCK_TYPE', 'FACIES', 'FORCE_2020_LITHOFACIES_LITHOLOGY']
}

# Reverse mapping for quick lookup
REVERSE_COLUMN_MAPPING = {}
for standard_name, variations in COLUMN_MAPPING.items():
    for variation in variations:
        REVERSE_COLUMN_MAPPING[variation.upper()] = standard_name

# Lithology color mapping for consistent visualization
LITHOLOGY_COLORS = {
    'Shale': '#8B4513',           # Brown
    'Sandstone': '#F4A460',       # Sandy Brown
    'Limestone': '#D3D3D3',       # Light Gray
    'Sandstone/Shale': '#DEB887', # Burlywood
    'Chalk': '#F5F5DC',           # Beige
    'Marl': '#BC8F8F',            # Rosy Brown
    'Halite': '#E0E0E0',          # Light Gray
    'Tuff': '#A0522D',            # Sienna
    'Anhydrite': '#DCDCDC',       # Gainsboro
    'Dolomite': '#C0C0C0',        # Silver
    'Coal': '#2F4F4F'             # Dark Slate Gray
}

def detect_separator(file_content):
    """Detect the separator used in CSV file"""
    # Check first few lines to determine separator
    first_lines = file_content.split('\n')[:5]
    separators = [',', ';', '\t', '|']

    separator_counts = {}
    for sep in separators:
        count = sum(line.count(sep) for line in first_lines)
        separator_counts[sep] = count

    # Return the separator with highest count
    best_separator = max(separator_counts, key=separator_counts.get)
    return best_separator if separator_counts[best_separator] > 0 else ','

def smart_load_csv(uploaded_file):
    """Intelligently load CSV with automatic separator detection"""
    try:
        # Read file content as string first
        file_content = uploaded_file.getvalue().decode('utf-8')

        # Detect separator
        separator = detect_separator(file_content)

        # Reset file pointer
        uploaded_file.seek(0)

        # Load with detected separator
        df = pd.read_csv(uploaded_file, sep=separator)

        return df, separator, None

    except Exception as e:
        return None, None, str(e)

def map_columns_intelligently(df):
    """Intelligently map column names to standard format"""
    original_columns = df.columns.tolist()
    mapped_columns = {}
    unmapped_columns = []
    used_standard_names = set()

    # Create a copy of the dataframe for mapping
    df_mapped = df.copy()

    # Process each column and find the best mapping
    for col in original_columns:
        col_upper = col.upper().strip()
        found_mapping = None

        # First try direct match
        if col_upper in REVERSE_COLUMN_MAPPING:
            standard_name = REVERSE_COLUMN_MAPPING[col_upper]
            if standard_name not in used_standard_names:
                found_mapping = standard_name

        # If no direct match, try fuzzy matching
        if not found_mapping:
            for standard_name, variations in COLUMN_MAPPING.items():
                if standard_name not in used_standard_names:
                    for variation in variations:
                        if variation.upper() in col_upper or col_upper in variation.upper():
                            found_mapping = standard_name
                            break
                    if found_mapping:
                        break

        # Record the mapping or mark as unmapped
        if found_mapping:
            mapped_columns[col] = found_mapping
            used_standard_names.add(found_mapping)
        else:
            unmapped_columns.append(col)

    # Create final mapped dataframe
    final_columns = {}
    columns_to_drop = []

    for col in original_columns:
        if col in mapped_columns:
            standard_name = mapped_columns[col]
            if standard_name not in final_columns:
                final_columns[standard_name] = col
            else:
                # Duplicate mapping - drop this column
                columns_to_drop.append(col)
                unmapped_columns.append(col)
        else:
            # Unmapped column - drop it
            columns_to_drop.append(col)

    # Drop unmapped and duplicate columns
    df_mapped = df_mapped.drop(columns=columns_to_drop)

    # Rename columns to standard names
    rename_dict = {orig_col: standard_name for standard_name, orig_col in final_columns.items()}
    df_mapped = df_mapped.rename(columns=rename_dict)

    # Update mapped_columns to reflect what was actually mapped
    final_mapped_columns = {orig_col: standard_name for standard_name, orig_col in final_columns.items()}

    return df_mapped, final_mapped_columns, unmapped_columns

def create_synthetic_columns(df, missing_columns):
    """Create synthetic data for missing required columns"""
    synthetic_info = {}

    for col in missing_columns:
        if col == 'DEPTH_MD' and 'DEPTH_MD' not in df.columns:
            # Create synthetic depth if completely missing
            if len(df) > 0:
                df['DEPTH_MD'] = np.arange(1000, 1000 + len(df) * 0.5, 0.5)[:len(df)]
                synthetic_info[col] = "Generated sequential depth values"

        elif col in ['GR', 'RDEP', 'RHOB', 'NPHI', 'CALI', 'DTC', 'PEF']:
            # Create realistic synthetic values based on typical ranges
            ranges = {
                'GR': (20, 150),      # API units
                'RDEP': (0.5, 100),   # ohm.m
                'RHOB': (1.8, 2.8),   # g/cc
                'NPHI': (0.05, 0.45), # v/v
                'CALI': (6, 16),      # inches
                'DTC': (50, 120),     # us/ft
                'PEF': (1.5, 5.0)     # barns/electron
            }

            if col in ranges:
                min_val, max_val = ranges[col]
                np.random.seed(42)  # For reproducible results

                if col == 'RDEP':
                    # Log-normal distribution for resistivity
                    synthetic_values = np.random.lognormal(
                        mean=np.log(5), sigma=1, size=len(df)
                    )
                    synthetic_values = np.clip(synthetic_values, min_val, max_val)
                else:
                    # Normal distribution for other parameters
                    mean_val = (min_val + max_val) / 2
                    std_val = (max_val - min_val) / 6
                    synthetic_values = np.random.normal(mean_val, std_val, len(df))
                    synthetic_values = np.clip(synthetic_values, min_val, max_val)

                df[col] = synthetic_values
                synthetic_info[col] = f"Generated synthetic values (range: {min_val}-{max_val})"

    return df, synthetic_info

def validate_and_process_data(df):
    """Enhanced data validation and processing"""
    processing_info = {
        'original_shape': df.shape,
        'mapped_columns': {},
        'missing_columns': [],
        'synthetic_columns': {},
        'optional_columns': [],
        'data_quality': {},
        'warnings': []
    }

    # Step 1: Intelligent column mapping
    df_processed, mapped_columns, unmapped_columns = map_columns_intelligently(df)
    processing_info['mapped_columns'] = mapped_columns
    processing_info['unmapped_columns'] = unmapped_columns

    # Step 2: Check for missing required columns
    missing_required = [col for col in REQUIRED_COLUMNS if col not in df_processed.columns]
    processing_info['missing_columns'] = missing_required

    # Step 3: Create synthetic data for missing columns
    if missing_required:
        df_processed, synthetic_info = create_synthetic_columns(df_processed, missing_required)
        processing_info['synthetic_columns'] = synthetic_info

    # Step 4: Check for optional columns
    optional_present = [col for col in OPTIONAL_COLUMNS if col in df_processed.columns]
    processing_info['optional_columns'] = optional_present

    # Step 5: Data type conversion and cleaning
    numeric_columns = REQUIRED_COLUMNS
    for col in numeric_columns:
        if col in df_processed.columns:
            if not pd.api.types.is_numeric_dtype(df_processed[col]):
                try:
                    df_processed[col] = pd.to_numeric(df_processed[col], errors='coerce')
                except:
                    processing_info['warnings'].append(f"{col} contains non-numeric data")

    # Step 6: Handle missing values
    original_length = len(df_processed)

    # Remove rows with missing critical data (only if we have real data, not synthetic)
    critical_cols = ['DEPTH_MD']
    if not any(col in processing_info['synthetic_columns'] for col in critical_cols):
        # Only drop rows if DEPTH_MD has missing values
        if 'DEPTH_MD' in df_processed.columns:
            initial_length = len(df_processed)
            df_processed = df_processed.dropna(subset=['DEPTH_MD'])
            if len(df_processed) < initial_length:
                processing_info['warnings'].append(f"Removed {initial_length - len(df_processed)} rows with missing depth data")

    # Fill remaining missing values with interpolation
    for col in REQUIRED_COLUMNS:
        if col in df_processed.columns:
            missing_count = df_processed[col].isna().sum()
            if missing_count > 0:
                missing_pct = (missing_count / len(df_processed)) * 100

                if missing_pct < 50:  # Only interpolate if less than 50% missing
                    df_processed[col] = df_processed[col].interpolate(method='linear').bfill().ffill()
                    processing_info['data_quality'][col] = f"Interpolated {missing_count} missing values ({missing_pct:.1f}%)"
                else:
                    processing_info['warnings'].append(f"{col} has {missing_pct:.1f}% missing data")

    processing_info['final_shape'] = df_processed.shape
    processing_info['rows_removed'] = original_length - len(df_processed)

    return df_processed, processing_info

def create_gamma_ray_track(df, color_by_lithology=False):
    """Create Gamma Ray track plot with optional lithology coloring"""
    fig = go.Figure()

    if color_by_lithology and 'LITH' in df.columns:
        # Group by lithology and plot each separately
        for lith in df['LITH'].unique():
            if pd.isna(lith):
                continue
            lith_data = df[df['LITH'] == lith]
            color = LITHOLOGY_COLORS.get(lith, '#1f77b4')

            fig.add_trace(go.Scatter(
                x=lith_data['GR'],
                y=lith_data['DEPTH_MD'],
                mode='markers',
                name=lith,
                marker=dict(color=color, size=3),
                hovertemplate='<b>Depth:</b> %{y:.2f} m<br>' +
                              '<b>GR:</b> %{x:.2f} API<br>' +
                              '<b>Lithology:</b> ' + lith + '<br>' +
                              '<extra></extra>'
            ))
    else:
        # Standard line plot
        fig.add_trace(go.Scatter(
            x=df['GR'],
            y=df['DEPTH_MD'],
            mode='lines',
            name='Gamma Ray',
            line=dict(color='green', width=1.5),
            hovertemplate='<b>Depth:</b> %{y:.2f} m<br>' +
                          '<b>GR:</b> %{x:.2f} API<br>' +
                          '<extra></extra>'
        ))

    fig.update_layout(
        title='Gamma Ray (GR) Log',
        xaxis_title='Gamma Ray (API)',
        yaxis_title='Depth (m)',
        yaxis=dict(autorange='reversed', showgrid=True, gridwidth=1, gridcolor='lightgray'),
        xaxis=dict(showgrid=True, gridwidth=1, gridcolor='lightgray'),
        height=600,
        showlegend=color_by_lithology,
        plot_bgcolor='white',
        paper_bgcolor='white'
    )

    return fig

def create_resistivity_track(df, color_by_lithology=False):
    """Create Deep Resistivity track plot with log scale and optional lithology coloring"""
    fig = go.Figure()

    if color_by_lithology and 'LITH' in df.columns:
        # Group by lithology and plot each separately
        for lith in df['LITH'].unique():
            if pd.isna(lith):
                continue
            lith_data = df[df['LITH'] == lith]
            color = LITHOLOGY_COLORS.get(lith, '#1f77b4')

            fig.add_trace(go.Scatter(
                x=lith_data['RDEP'],
                y=lith_data['DEPTH_MD'],
                mode='markers',
                name=lith,
                marker=dict(color=color, size=3),
                hovertemplate='<b>Depth:</b> %{y:.2f} m<br>' +
                              '<b>RDEP:</b> %{x:.2f} ohm.m<br>' +
                              '<b>Lithology:</b> ' + lith + '<br>' +
                              '<extra></extra>'
            ))
    else:
        # Standard line plot
        fig.add_trace(go.Scatter(
            x=df['RDEP'],
            y=df['DEPTH_MD'],
            mode='lines',
            name='Deep Resistivity',
            line=dict(color='red', width=1.5),
            hovertemplate='<b>Depth:</b> %{y:.2f} m<br>' +
                          '<b>RDEP:</b> %{x:.2f} ohm.m<br>' +
                          '<extra></extra>'
        ))

    fig.update_layout(
        title='Deep Resistivity (RDEP) Log',
        xaxis_title='Deep Resistivity (ohm.m)',
        yaxis_title='Depth (m)',
        xaxis=dict(type='log', showgrid=True, gridwidth=1, gridcolor='lightgray'),
        yaxis=dict(autorange='reversed', showgrid=True, gridwidth=1, gridcolor='lightgray'),
        height=600,
        showlegend=color_by_lithology,
        plot_bgcolor='white',
        paper_bgcolor='white'
    )

    return fig

def create_density_neutron_track(df):
    """Create combined Density and Neutron Porosity track"""
    # Create subplots with two y-axes
    fig = make_subplots(
        rows=1, cols=2,
        subplot_titles=('Density (RHOB)', 'Neutron Porosity (NPHI)'),
        shared_yaxes=True,
        horizontal_spacing=0.1
    )

    # Density curve
    fig.add_trace(
        go.Scatter(
            x=df['RHOB'],
            y=df['DEPTH_MD'],
            mode='lines',
            name='Density (RHOB)',
            line=dict(color='blue', width=1.5),
            hovertemplate='<b>Depth:</b> %{y:.2f} m<br>' +
                          '<b>RHOB:</b> %{x:.2f} g/cc<br>' +
                          '<extra></extra>'
        ),
        row=1, col=1
    )

    # Neutron curve
    fig.add_trace(
        go.Scatter(
            x=df['NPHI'],
            y=df['DEPTH_MD'],
            mode='lines',
            name='Neutron (NPHI)',
            line=dict(color='orange', width=1.5),
            hovertemplate='<b>Depth:</b> %{y:.2f} m<br>' +
                          '<b>NPHI:</b> %{x:.2f} v/v<br>' +
                          '<extra></extra>'
        ),
        row=1, col=2
    )

    # Update layout
    fig.update_layout(
        title='Density-Neutron Log',
        height=600,
        plot_bgcolor='white',
        paper_bgcolor='white'
    )

    # Update axes
    fig.update_xaxes(title_text="Density (g/cc)", row=1, col=1)
    fig.update_xaxes(title_text="Neutron Porosity (v/v)", row=1, col=2)
    fig.update_yaxes(title_text="Depth (m)", row=1, col=1)
    fig.update_yaxes(autorange='reversed', row=1, col=1)
    fig.update_yaxes(autorange='reversed', row=1, col=2)

    # Update grid settings
    fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='lightgray')
    fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='lightgray')

    return fig

def create_density_neutron_crossplot(df, color_by='GR'):
    """Create Density-Neutron crossplot with GR color scale or lithology"""
    fig = go.Figure()

    if color_by == 'LITH' and 'LITH' in df.columns:
        # Color by lithology
        for lith in df['LITH'].unique():
            if pd.isna(lith):
                continue
            lith_data = df[df['LITH'] == lith]
            color = LITHOLOGY_COLORS.get(lith, '#1f77b4')

            fig.add_trace(go.Scatter(
                x=lith_data['NPHI'],
                y=lith_data['RHOB'],
                mode='markers',
                name=lith,
                marker=dict(color=color, size=6),
                text=lith_data['DEPTH_MD'],
                hovertemplate='<b>NPHI:</b> %{x:.3f} v/v<br>' +
                              '<b>RHOB:</b> %{y:.3f} g/cc<br>' +
                              '<b>Lithology:</b> ' + lith + '<br>' +
                              '<b>Depth:</b> %{text:.2f} m<br>' +
                              '<extra></extra>'
            ))

        title = 'Density-Neutron Crossplot (Color = Lithology)'
        showlegend = True
    else:
        # Color by Gamma Ray (default)
        fig.add_trace(go.Scatter(
            x=df['NPHI'],
            y=df['RHOB'],
            mode='markers',
            marker=dict(
                size=6,
                color=df['GR'],
                colorscale='Viridis',
                colorbar=dict(title="Gamma Ray (API)"),
                showscale=True
            ),
            text=df['DEPTH_MD'],
            hovertemplate='<b>NPHI:</b> %{x:.3f} v/v<br>' +
                          '<b>RHOB:</b> %{y:.3f} g/cc<br>' +
                          '<b>GR:</b> %{marker.color:.1f} API<br>' +
                          '<b>Depth:</b> %{text:.2f} m<br>' +
                          '<extra></extra>',
            name='Data Points'
        ))

        title = 'Density-Neutron Crossplot (Color = Gamma Ray)'
        showlegend = False

    fig.update_layout(
        title=title,
        xaxis_title='Neutron Porosity (NPHI) [v/v]',
        yaxis_title='Bulk Density (RHOB) [g/cc]',
        xaxis=dict(showgrid=True, gridwidth=1, gridcolor='lightgray'),
        yaxis=dict(showgrid=True, gridwidth=1, gridcolor='lightgray'),
        height=600,
        showlegend=showlegend,
        plot_bgcolor='white',
        paper_bgcolor='white'
    )

    return fig

def display_processing_info(processing_info, separator):
    """Display intelligent data processing information"""
    st.subheader("🔧 Intelligent Data Processing")

    # File format detection
    sep_names = {',': 'Comma', ';': 'Semicolon', '\t': 'Tab', '|': 'Pipe'}
    st.info(f"📄 **File Format Detected**: {sep_names.get(separator, 'Unknown')} separated values")

    # Column mapping information
    if processing_info['mapped_columns']:
        st.success("✅ **Column Mapping Successful**")

        with st.expander("🔄 Column Mapping Details"):
            col1, col2 = st.columns(2)

            with col1:
                st.write("**Original → Standard**")
                for orig, standard in processing_info['mapped_columns'].items():
                    st.write(f"📍 `{orig}` → `{standard}`")

            with col2:
                if processing_info['unmapped_columns']:
                    st.write("**Unmapped Columns**")
                    for col in processing_info['unmapped_columns']:
                        st.write(f"⚪ `{col}` (ignored)")

    # Missing columns and synthetic data
    if processing_info['synthetic_columns']:
        st.warning("⚠️ **Synthetic Data Generated**")

        with st.expander("🧪 Synthetic Data Details"):
            for col, description in processing_info['synthetic_columns'].items():
                st.write(f"🔬 **{col}**: {description}")

            st.info("💡 Synthetic data allows visualization even with incomplete datasets")

    # Data quality information
    if processing_info['data_quality']:
        with st.expander("📊 Data Quality Information"):
            for col, info in processing_info['data_quality'].items():
                st.write(f"🔍 **{col}**: {info}")

    # Warnings
    if processing_info['warnings']:
        st.warning("⚠️ **Data Quality Warnings**")
        for warning in processing_info['warnings']:
            st.write(f"⚠️ {warning}")

    # Processing summary
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("Original Rows", f"{processing_info['original_shape'][0]:,}")
        st.metric("Original Columns", processing_info['original_shape'][1])

    with col2:
        st.metric("Final Rows", f"{processing_info['final_shape'][0]:,}")
        st.metric("Mapped Columns", len(processing_info['mapped_columns']))

    with col3:
        if processing_info['rows_removed'] > 0:
            st.metric("Rows Removed", processing_info['rows_removed'])
        st.metric("Synthetic Columns", len(processing_info['synthetic_columns']))

def display_data_summary(df, processing_info=None, optional_columns=None):
    """Display summary statistics of the well log data"""
    st.subheader("📊 Data Summary")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Total Data Points", len(df))
        if processing_info and processing_info['rows_removed'] > 0:
            st.metric("Data Cleaned", f"{processing_info['rows_removed']} rows removed")
        st.metric("Depth Range", f"{df['DEPTH_MD'].min():.1f} - {df['DEPTH_MD'].max():.1f} m")

        # Well information if available
        if 'WELL' in df.columns:
            st.metric("Number of Wells", df['WELL'].nunique())

    with col2:
        st.metric("GR Range", f"{df['GR'].min():.1f} - {df['GR'].max():.1f} API")
        st.metric("RDEP Range", f"{df['RDEP'].min():.2f} - {df['RDEP'].max():.2f} ohm.m")

    with col3:
        st.metric("RHOB Range", f"{df['RHOB'].min():.2f} - {df['RHOB'].max():.2f} g/cc")
        st.metric("NPHI Range", f"{df['NPHI'].min():.3f} - {df['NPHI'].max():.3f} v/v")

    with col4:
        st.metric("CALI Range", f"{df['CALI'].min():.2f} - {df['CALI'].max():.2f} in")
        st.metric("DTC Range", f"{df['DTC'].min():.1f} - {df['DTC'].max():.1f} us/ft")

    # Enhanced information for Xeek-style datasets
    if optional_columns and len(optional_columns) > 0:
        st.subheader("🌍 Geological Information")
        geo_col1, geo_col2 = st.columns(2)

        with geo_col1:
            if 'LITH' in df.columns:
                st.write("**Lithology Distribution:**")
                lith_counts = df['LITH'].value_counts()
                for lith, count in lith_counts.head(5).items():
                    pct = (count / len(df)) * 100
                    color = LITHOLOGY_COLORS.get(lith, '#1f77b4')
                    st.write(f"🔸 {lith}: {count:,} ({pct:.1f}%)")

        with geo_col2:
            if 'GROUP' in df.columns:
                st.write("**Geological Groups:**")
                groups = df['GROUP'].value_counts()
                for group, count in groups.head(3).items():
                    if pd.notna(group):
                        st.write(f"📍 {group}: {count:,} points")

            if 'FORMATION' in df.columns:
                formations = df['FORMATION'].nunique()
                st.write(f"**Formations:** {formations} unique")

    # Data quality indicators
    st.subheader("🔍 Data Quality")
    quality_col1, quality_col2 = st.columns(2)

    with quality_col1:
        # Check for data completeness
        completeness = {}
        for col in REQUIRED_COLUMNS:
            completeness[col] = (1 - df[col].isna().sum() / len(df)) * 100

        st.write("**Data Completeness:**")
        for col, pct in completeness.items():
            color = "🟢" if pct == 100 else "🟡" if pct > 95 else "🔴"
            st.write(f"{color} {col}: {pct:.1f}%")

    with quality_col2:
        # Statistical summary
        st.write("**Key Statistics:**")
        if len(df) > 1:
            depth_interval = abs(df['DEPTH_MD'].iloc[1] - df['DEPTH_MD'].iloc[0])
            st.write(f"📏 Depth Interval: {depth_interval:.2f} m")
        st.write(f"📊 Total Depth: {(df['DEPTH_MD'].max() - df['DEPTH_MD'].min()):.1f} m")

        # Check for potential data issues
        if df['CALI'].max() > 20:
            st.warning("⚠️ Large caliper readings detected (>20 in)")
        if df['GR'].max() > 200:
            st.warning("⚠️ Very high gamma ray readings detected (>200 API)")
        if df['RDEP'].min() < 0.1:
            st.warning("⚠️ Very low resistivity readings detected (<0.1 ohm.m)")

def main():
    """Main Streamlit application"""

    # Header
    st.markdown('<h1 class="main-header">Well Log Analyzer</h1>', unsafe_allow_html=True)
    st.markdown('<p style="text-align: center; font-size: 1.2rem; color: #666;">Petrophysical Analysis Tool for Oil & Gas Professionals</p>', unsafe_allow_html=True)

    # Sidebar
    st.sidebar.header("⚙️ Intelligent Well Log Analyzer")
    st.sidebar.markdown("""
    **🧠 Smart Features:**
    - 🔄 Auto-detects file separators
    - 🎯 Intelligent column mapping
    - 🧪 Synthetic data generation
    - 📊 Interactive visualizations
    - 🪨 Lithology analysis
    - 🏗️ Multi-well support
    - 💾 Export capabilities

    **📁 Supported Formats:**
    - CSV (comma, semicolon, tab separated)
    - Various column naming conventions
    - Missing data handling
    - Flexible data structures
    """)

    # File upload section
    st.markdown('<div class="upload-section">', unsafe_allow_html=True)
    st.markdown("### 📁 Upload Any Well Log Data")
    st.markdown("Upload any CSV file - our intelligent system will automatically adapt to your data format!")

    uploaded_file = st.file_uploader(
        "Choose a CSV file",
        type=['csv'],
        help="Supports any CSV format with well log data. Column names will be automatically mapped!"
    )

    # Show supported column variations
    with st.expander("🔍 Supported Column Names (Auto-Detected)"):
        col1, col2 = st.columns(2)

        with col1:
            st.write("**Depth**: DEPTH_MD, DEPTH, MD, TVD, Z_LOC")
            st.write("**Gamma Ray**: GR, GAMMA_RAY, SGR, GAMMA")
            st.write("**Resistivity**: RDEP, RDEEP, RT, RMED, RXO")
            st.write("**Density**: RHOB, DENSITY, DEN, RHOZ")

        with col2:
            st.write("**Neutron**: NPHI, NEUTRON, NEU, PHIN")
            st.write("**Caliper**: CALI, CALIPER, BS, CAL")
            st.write("**Acoustic**: DTC, AC, DELTA_TIME, DT")
            st.write("**Photoelectric**: PEF, PE, PHOTOELECTRIC")

    st.markdown('</div>', unsafe_allow_html=True)

    if uploaded_file is not None:
        try:
            # Step 1: Intelligent CSV loading with separator detection
            df_raw, separator, load_error = smart_load_csv(uploaded_file)

            if load_error:
                st.error(f"❌ Error loading file: {load_error}")
                st.info("Please ensure your file is a valid CSV format")
                return

            # Store original length for comparison
            original_length = len(df_raw)

            # Step 2: Intelligent data processing and column mapping
            df, processing_info = validate_and_process_data(df_raw)

            st.success("✅ Data loaded and processed successfully!")

            # Step 3: Display processing information
            display_processing_info(processing_info, separator)

            # Well selection sidebar (if WELL column exists)
            selected_wells = None
            if 'WELL' in df.columns:
                st.sidebar.subheader("🏗️ Well Selection")
                available_wells = sorted(df['WELL'].unique())
                selected_wells = st.sidebar.multiselect(
                    "Select Wells to Analyze",
                    available_wells,
                    default=available_wells[:3] if len(available_wells) > 3 else available_wells,
                    help="Select one or more wells for analysis"
                )

                if selected_wells:
                    df = df[df['WELL'].isin(selected_wells)]
                    st.sidebar.success(f"✅ Analyzing {len(selected_wells)} well(s)")
                    st.sidebar.write(f"📊 Data points: {len(df):,}")

            # Visualization options
            st.sidebar.subheader("🎨 Visualization Options")
            color_by_lithology = False
            crossplot_color = 'GR'

            if 'LITH' in df.columns:
                color_by_lithology = st.sidebar.checkbox(
                    "Color by Lithology",
                    value=False,
                    help="Color track plots by lithology type"
                )

                crossplot_color = st.sidebar.selectbox(
                    "Crossplot Color Scale",
                    ['GR', 'LITH'],
                    help="Choose color scale for density-neutron crossplot"
                )

            # Display data summary
            display_data_summary(df, processing_info, processing_info['optional_columns'])

            # Create tabs for different visualizations
            tabs = ["🟢 Gamma Ray", "🔴 Resistivity", "🔵 Density-Neutron", "📊 Crossplot", "📋 Data Table"]
            if 'LITH' in df.columns:
                tabs.append("🪨 Lithology")

            tab_objects = st.tabs(tabs)

            with tab_objects[0]:  # Gamma Ray
                st.markdown('<div class="track-title">Gamma Ray Track</div>', unsafe_allow_html=True)
                fig_gr = create_gamma_ray_track(df, color_by_lithology)
                st.plotly_chart(fig_gr, use_container_width=True)

            with tab_objects[1]:  # Resistivity
                st.markdown('<div class="track-title">Deep Resistivity Track (Log Scale)</div>', unsafe_allow_html=True)
                fig_res = create_resistivity_track(df, color_by_lithology)
                st.plotly_chart(fig_res, use_container_width=True)

            with tab_objects[2]:  # Density-Neutron
                st.markdown('<div class="track-title">Density-Neutron Track</div>', unsafe_allow_html=True)
                fig_dn = create_density_neutron_track(df)
                st.plotly_chart(fig_dn, use_container_width=True)

            with tab_objects[3]:  # Crossplot
                st.markdown('<div class="track-title">Density-Neutron Crossplot</div>', unsafe_allow_html=True)
                fig_cross = create_density_neutron_crossplot(df, crossplot_color)
                st.plotly_chart(fig_cross, use_container_width=True)

            with tab_objects[4]:  # Data Table
                st.subheader("📋 Raw Data Table")

                # Show column information
                if 'WELL' in df.columns:
                    col1, col2 = st.columns(2)
                    with col1:
                        st.write(f"**Wells in dataset:** {', '.join(df['WELL'].unique())}")
                    with col2:
                        if 'LITH' in df.columns:
                            st.write(f"**Lithologies:** {df['LITH'].nunique()} types")

                st.dataframe(df, use_container_width=True)

                # Download button for processed data
                csv = df.to_csv(index=False)
                st.download_button(
                    label="📥 Download Processed Data",
                    data=csv,
                    file_name=f"processed_well_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )

            # Lithology analysis tab (if available)
            if 'LITH' in df.columns and len(tab_objects) > 5:
                with tab_objects[5]:  # Lithology
                    st.subheader("🪨 Lithology Analysis")

                    # Lithology distribution
                    col1, col2 = st.columns(2)

                    with col1:
                        st.write("**Lithology Distribution:**")
                        lith_counts = df['LITH'].value_counts()

                        # Create pie chart
                        fig_pie = go.Figure(data=[go.Pie(
                            labels=lith_counts.index,
                            values=lith_counts.values,
                            marker_colors=[LITHOLOGY_COLORS.get(lith, '#1f77b4') for lith in lith_counts.index]
                        )])
                        fig_pie.update_layout(title="Lithology Distribution", height=400)
                        st.plotly_chart(fig_pie, use_container_width=True)

                    with col2:
                        st.write("**Lithology Statistics:**")
                        for lith, count in lith_counts.items():
                            pct = (count / len(df)) * 100
                            st.write(f"🔸 **{lith}**: {count:,} points ({pct:.1f}%)")

                        # Depth range by lithology
                        st.write("**Depth Ranges by Lithology:**")
                        for lith in lith_counts.index[:5]:  # Top 5 lithologies
                            lith_data = df[df['LITH'] == lith]
                            depth_min = lith_data['DEPTH_MD'].min()
                            depth_max = lith_data['DEPTH_MD'].max()
                            st.write(f"📏 {lith}: {depth_min:.1f} - {depth_max:.1f} m")

        except Exception as e:
            st.error(f"❌ Error processing file: {str(e)}")
            st.info("Please ensure your CSV file is properly formatted with the required columns.")

    else:
        # Show sample data format
        st.info("👆 Please upload a CSV file to begin analysis")

        with st.expander("📋 Sample Data Format"):
            sample_data = {
                'DEPTH_MD': [1500.0, 1500.5, 1501.0],
                'GR': [45.2, 52.1, 38.9],
                'RDEP': [12.5, 8.3, 25.7],
                'RHOB': [2.35, 2.42, 2.28],
                'NPHI': [0.15, 0.18, 0.12],
                'CALI': [8.5, 8.7, 8.3],
                'DTC': [65.2, 68.1, 62.8],
                'PEF': [2.8, 3.1, 2.6]
            }
            sample_df = pd.DataFrame(sample_data)
            st.dataframe(sample_df)

if __name__ == "__main__":
    main()
