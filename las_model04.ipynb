{"cells": [{"cell_type": "markdown", "id": "e3dcafed", "metadata": {}, "source": ["# Scikit Learn ANN for Predicting Continuous Well Measurements"]}, {"cell_type": "markdown", "id": "33025840", "metadata": {}, "source": ["## Importing Libraries and Loading Data"]}, {"cell_type": "code", "execution_count": 1, "id": "731ac85c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "id": "db7159a5-126b-4ade-bfc5-82701c5f08e4", "metadata": {}, "source": ["med_tutorial_scikit_learn_1.py"]}, {"cell_type": "code", "execution_count": 2, "id": "c2fb85af", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(\"C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/CSV Data/volve_wells.csv\", usecols=['WELL', 'DEPTH', 'RHOB', 'GR', 'NPHI', 'PEF', 'DT'])"]}, {"cell_type": "code", "execution_count": 3, "id": "affa6ba8-c0db-409b-9af4-27c1ad175e98", "metadata": {}, "outputs": [], "source": ["df = df.dropna()"]}, {"cell_type": "markdown", "id": "1c86690f", "metadata": {}, "source": ["## Create Training, Testing and Validation Datasets"]}, {"cell_type": "markdown", "id": "01aade55", "metadata": {}, "source": ["Our dataset should have 4 wells within it. We can confirm this by calling upon the `unique()` function"]}, {"cell_type": "code", "execution_count": 4, "id": "7c6c58be", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DEPTH</th>\n", "      <th>DT</th>\n", "      <th>GR</th>\n", "      <th>NPHI</th>\n", "      <th>PEF</th>\n", "      <th>RHOB</th>\n", "      <th>WELL</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>69703</th>\n", "      <td>2577.0</td>\n", "      <td>109.3850</td>\n", "      <td>74.6970</td>\n", "      <td>0.4030</td>\n", "      <td>8.1360</td>\n", "      <td>2.3470</td>\n", "      <td>15/9-F-11 A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69704</th>\n", "      <td>2577.1</td>\n", "      <td>110.1320</td>\n", "      <td>72.7970</td>\n", "      <td>0.3990</td>\n", "      <td>7.9250</td>\n", "      <td>2.3570</td>\n", "      <td>15/9-F-11 A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69705</th>\n", "      <td>2577.2</td>\n", "      <td>110.8430</td>\n", "      <td>71.6290</td>\n", "      <td>0.3900</td>\n", "      <td>7.7750</td>\n", "      <td>2.3600</td>\n", "      <td>15/9-F-11 A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69706</th>\n", "      <td>2577.3</td>\n", "      <td>111.0100</td>\n", "      <td>69.8990</td>\n", "      <td>0.3850</td>\n", "      <td>7.5920</td>\n", "      <td>2.3610</td>\n", "      <td>15/9-F-11 A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69707</th>\n", "      <td>2577.4</td>\n", "      <td>110.7970</td>\n", "      <td>69.0410</td>\n", "      <td>0.3820</td>\n", "      <td>7.3800</td>\n", "      <td>2.3630</td>\n", "      <td>15/9-F-11 A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>149702</th>\n", "      <td>3641.7</td>\n", "      <td>74.4395</td>\n", "      <td>120.4854</td>\n", "      <td>0.1973</td>\n", "      <td>7.0911</td>\n", "      <td>2.6337</td>\n", "      <td>15/9-F-1 A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>149703</th>\n", "      <td>3641.8</td>\n", "      <td>74.5584</td>\n", "      <td>120.2856</td>\n", "      <td>0.1930</td>\n", "      <td>7.0064</td>\n", "      <td>2.6295</td>\n", "      <td>15/9-F-1 A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>149704</th>\n", "      <td>3641.9</td>\n", "      <td>74.6748</td>\n", "      <td>119.9561</td>\n", "      <td>0.1879</td>\n", "      <td>6.8993</td>\n", "      <td>2.6240</td>\n", "      <td>15/9-F-1 A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>149705</th>\n", "      <td>3642.0</td>\n", "      <td>74.7656</td>\n", "      <td>122.5272</td>\n", "      <td>0.1825</td>\n", "      <td>6.8016</td>\n", "      <td>2.6194</td>\n", "      <td>15/9-F-1 A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>149706</th>\n", "      <td>3642.1</td>\n", "      <td>74.8246</td>\n", "      <td>122.0503</td>\n", "      <td>0.1774</td>\n", "      <td>6.7421</td>\n", "      <td>2.6169</td>\n", "      <td>15/9-F-1 A</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>24950 rows × 7 columns</p>\n", "</div>"], "text/plain": ["         DEPTH        DT        GR    NPHI     PEF    RHOB         WELL\n", "69703   2577.0  109.3850   74.6970  0.4030  8.1360  2.3470  15/9-F-11 A\n", "69704   2577.1  110.1320   72.7970  0.3990  7.9250  2.3570  15/9-F-11 A\n", "69705   2577.2  110.8430   71.6290  0.3900  7.7750  2.3600  15/9-F-11 A\n", "69706   2577.3  111.0100   69.8990  0.3850  7.5920  2.3610  15/9-F-11 A\n", "69707   2577.4  110.7970   69.0410  0.3820  7.3800  2.3630  15/9-F-11 A\n", "...        ...       ...       ...     ...     ...     ...          ...\n", "149702  3641.7   74.4395  120.4854  0.1973  7.0911  2.6337   15/9-F-1 A\n", "149703  3641.8   74.5584  120.2856  0.1930  7.0064  2.6295   15/9-F-1 A\n", "149704  3641.9   74.6748  119.9561  0.1879  6.8993  2.6240   15/9-F-1 A\n", "149705  3642.0   74.7656  122.5272  0.1825  6.8016  2.6194   15/9-F-1 A\n", "149706  3642.1   74.8246  122.0503  0.1774  6.7421  2.6169   15/9-F-1 A\n", "\n", "[24950 rows x 7 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "markdown", "id": "0607c9d1", "metadata": {}, "source": ["As we are using measurements taken from multiple wells, one way to split our data into training and testing is to set aside a single well (blind test well) which will be used to see how our model performs on unseen data."]}, {"cell_type": "code", "execution_count": 5, "id": "c7114040", "metadata": {}, "outputs": [], "source": ["# Training Wells\n", "training_wells = ['15/9-F-11 A', '15/9-F-1 A']\n", "\n", "# Test Well\n", "test_well = ['15/9-F-1 B']"]}, {"cell_type": "markdown", "id": "dfd658cc", "metadata": {}, "source": ["\"Extract\" the data from the main dataframe using the well lists above"]}, {"cell_type": "code", "execution_count": 6, "id": "6320823a", "metadata": {}, "outputs": [], "source": ["train_val_df = df[df['WELL'].isin(training_wells)].copy()\n", "test_df = df[df['WELL'].isin(test_well)].copy()"]}, {"cell_type": "code", "execution_count": 7, "id": "37d705af", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DEPTH</th>\n", "      <th>DT</th>\n", "      <th>GR</th>\n", "      <th>NPHI</th>\n", "      <th>PEF</th>\n", "      <th>RHOB</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>21688.000000</td>\n", "      <td>21688.000000</td>\n", "      <td>21688.000000</td>\n", "      <td>21688.000000</td>\n", "      <td>21688.000000</td>\n", "      <td>21688.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>3141.098875</td>\n", "      <td>77.235857</td>\n", "      <td>39.803246</td>\n", "      <td>0.166648</td>\n", "      <td>7.093603</td>\n", "      <td>2.475232</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>314.723749</td>\n", "      <td>14.336048</td>\n", "      <td>57.907158</td>\n", "      <td>0.099200</td>\n", "      <td>1.188313</td>\n", "      <td>0.147635</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>2577.000000</td>\n", "      <td>53.165000</td>\n", "      <td>0.852000</td>\n", "      <td>0.010000</td>\n", "      <td>4.297800</td>\n", "      <td>1.980600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>2869.475000</td>\n", "      <td>66.849300</td>\n", "      <td>9.416350</td>\n", "      <td>0.096000</td>\n", "      <td>6.218475</td>\n", "      <td>2.379000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>3140.550000</td>\n", "      <td>72.720750</td>\n", "      <td>27.552000</td>\n", "      <td>0.136000</td>\n", "      <td>7.487700</td>\n", "      <td>2.533000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>3411.625000</td>\n", "      <td>86.093800</td>\n", "      <td>44.877425</td>\n", "      <td>0.217200</td>\n", "      <td>8.001000</td>\n", "      <td>2.581400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>3723.300000</td>\n", "      <td>126.827000</td>\n", "      <td>1124.403000</td>\n", "      <td>0.593200</td>\n", "      <td>13.841000</td>\n", "      <td>3.025000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              DEPTH            DT            GR          NPHI           PEF  \\\n", "count  21688.000000  21688.000000  21688.000000  21688.000000  21688.000000   \n", "mean    3141.098875     77.235857     39.803246      0.166648      7.093603   \n", "std      314.723749     14.336048     57.907158      0.099200      1.188313   \n", "min     2577.000000     53.165000      0.852000      0.010000      4.297800   \n", "25%     2869.475000     66.849300      9.416350      0.096000      6.218475   \n", "50%     3140.550000     72.720750     27.552000      0.136000      7.487700   \n", "75%     3411.625000     86.093800     44.877425      0.217200      8.001000   \n", "max     3723.300000    126.827000   1124.403000      0.593200     13.841000   \n", "\n", "               RHOB  \n", "count  21688.000000  \n", "mean       2.475232  \n", "std        0.147635  \n", "min        1.980600  \n", "25%        2.379000  \n", "50%        2.533000  \n", "75%        2.581400  \n", "max        3.025000  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["train_val_df.describe()"]}, {"cell_type": "code", "execution_count": 8, "id": "fc094c52", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DEPTH</th>\n", "      <th>DT</th>\n", "      <th>GR</th>\n", "      <th>NPHI</th>\n", "      <th>PEF</th>\n", "      <th>RHOB</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>3262.000000</td>\n", "      <td>3262.000000</td>\n", "      <td>3262.000000</td>\n", "      <td>3262.000000</td>\n", "      <td>3262.000000</td>\n", "      <td>3262.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>3261.550000</td>\n", "      <td>84.236378</td>\n", "      <td>72.114743</td>\n", "      <td>0.204948</td>\n", "      <td>6.448994</td>\n", "      <td>2.452392</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>94.180262</td>\n", "      <td>14.488623</td>\n", "      <td>61.620077</td>\n", "      <td>0.097875</td>\n", "      <td>0.830665</td>\n", "      <td>0.133218</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>3098.500000</td>\n", "      <td>58.631800</td>\n", "      <td>8.001500</td>\n", "      <td>0.059500</td>\n", "      <td>4.729900</td>\n", "      <td>2.111800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>3180.025000</td>\n", "      <td>74.450700</td>\n", "      <td>38.552300</td>\n", "      <td>0.146300</td>\n", "      <td>5.917400</td>\n", "      <td>2.364200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>3261.550000</td>\n", "      <td>81.407800</td>\n", "      <td>50.796200</td>\n", "      <td>0.172400</td>\n", "      <td>6.345200</td>\n", "      <td>2.483300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>3343.075000</td>\n", "      <td>88.037350</td>\n", "      <td>64.980325</td>\n", "      <td>0.225450</td>\n", "      <td>6.910000</td>\n", "      <td>2.539875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>3424.600000</td>\n", "      <td>125.982700</td>\n", "      <td>297.767300</td>\n", "      <td>0.557600</td>\n", "      <td>10.987600</td>\n", "      <td>3.051700</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             DEPTH           DT           GR         NPHI          PEF  \\\n", "count  3262.000000  3262.000000  3262.000000  3262.000000  3262.000000   \n", "mean   3261.550000    84.236378    72.114743     0.204948     6.448994   \n", "std      94.180262    14.488623    61.620077     0.097875     0.830665   \n", "min    3098.500000    58.631800     8.001500     0.059500     4.729900   \n", "25%    3180.025000    74.450700    38.552300     0.146300     5.917400   \n", "50%    3261.550000    81.407800    50.796200     0.172400     6.345200   \n", "75%    3343.075000    88.037350    64.980325     0.225450     6.910000   \n", "max    3424.600000   125.982700   297.767300     0.557600    10.987600   \n", "\n", "              RHOB  \n", "count  3262.000000  \n", "mean      2.452392  \n", "std       0.133218  \n", "min       2.111800  \n", "25%       2.364200  \n", "50%       2.483300  \n", "75%       2.539875  \n", "max       3.051700  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["test_df.describe()"]}, {"cell_type": "markdown", "id": "495f5c4b-25ee-4f22-837a-1977f5abd3df", "metadata": {}, "source": ["## Proprocessing"]}, {"cell_type": "markdown", "id": "4e55a73d", "metadata": {}, "source": ["## Implementing the Random Forest Model"]}, {"cell_type": "code", "execution_count": 9, "id": "fa08e1cc", "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split\n", "from sklearn.neural_network import MLPRegressor\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn import metrics"]}, {"cell_type": "markdown", "id": "b18c90d6", "metadata": {}, "source": ["### Selecting Training and Target Features"]}, {"cell_type": "code", "execution_count": 10, "id": "fd8dc2b5", "metadata": {}, "outputs": [], "source": ["X = train_val_df[['RHOB', 'GR', 'NPHI', 'PEF']]\n", "y = train_val_df['DT']"]}, {"cell_type": "markdown", "id": "51ec1cf5", "metadata": {}, "source": ["Note that the name test used here is commonly used within machine learning. In this case the variables X_test and y_test are our validation data. In other words it is used to help tune our model. "]}, {"cell_type": "code", "execution_count": 11, "id": "fb9b92c0", "metadata": {}, "outputs": [], "source": ["X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2)"]}, {"cell_type": "markdown", "id": "76c0fe11", "metadata": {}, "source": ["Checking the shapes of X_<PERSON> and X_test to make sure they have been split correctly."]}, {"cell_type": "markdown", "id": "76569ed7-1f86-480b-911a-e98e7df1ea3f", "metadata": {"tags": []}, "source": ["### Applying Standard Scaler"]}, {"cell_type": "code", "execution_count": 12, "id": "3cb77c5f-7b63-4e1f-a708-7b7b56cc282c", "metadata": {}, "outputs": [], "source": ["scaler = StandardScaler()"]}, {"cell_type": "code", "execution_count": 13, "id": "aa6757ea-dd22-44c4-af8b-0d9ef6af6344", "metadata": {}, "outputs": [], "source": ["X_train = scaler.fit_transform(X_train)"]}, {"cell_type": "code", "execution_count": 14, "id": "d309579a-0122-4a20-b107-8e38c1b6deaf", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 0.17418632, -0.63874246, -0.4304835 ,  0.6815211 ],\n", "       [ 0.97270046,  1.0939581 , -0.2012031 , -0.48756989],\n", "       [ 0.91304915, -0.26838162, -0.69511549,  0.91505348],\n", "       ...,\n", "       [ 0.98693543,  1.14625115, -0.20221315, -0.49824613],\n", "       [ 0.27586469, -0.55116564, -0.37190084,  1.09999633],\n", "       [-0.14440591,  0.40852272,  0.67854678,  0.13409027]])"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train"]}, {"cell_type": "code", "execution_count": 15, "id": "4334161f-a44a-4b21-ab10-62fcca63640e", "metadata": {}, "outputs": [], "source": ["X_val = scaler.transform(X_val)"]}, {"cell_type": "code", "execution_count": 16, "id": "65f4c412-19f3-4aa1-813c-c8b53dd3dda4", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 0.66902106, -0.56774202, -0.74561778,  0.96212984],\n", "       [-2.09663063, -0.20047474,  1.20377059, -1.69936586],\n", "       [ 0.04539372, -0.19134189, -0.67491458, -0.3803871 ],\n", "       ...,\n", "       [ 0.64868538, -0.49877932, -0.73551732,  0.80156582],\n", "       [ 1.09607022, -0.62713195, -1.32134388,  1.20507749],\n", "       [-0.09288887,  0.08461097, -0.55875931, -0.93345028]])"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["X_val"]}, {"cell_type": "markdown", "id": "a5cf39ac-6e08-4d5f-b905-4b597ce3e122", "metadata": {}, "source": ["from sklearn.preprocessing import StandardScaler\n", "sc = StandardScaler()\n", "X_train = sc.fit_transform(X_train)\n", "X_test = sc.transform (X_test)"]}, {"cell_type": "markdown", "id": "6508af88", "metadata": {}, "source": ["### Building the Model"]}, {"cell_type": "code", "execution_count": 17, "id": "4d6904f6", "metadata": {}, "outputs": [], "source": ["model = MLPRegressor(hidden_layer_sizes=(64, 64,64), \n", "                     activation=\"relu\" ,\n", "                     random_state=42, max_iter=2000)"]}, {"cell_type": "code", "execution_count": 18, "id": "4a37dd73", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-1 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-1 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-1 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-1 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-1 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-1 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-1 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-1 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-1 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-1 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-1 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-1 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-1 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-1 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-1 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-1 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-1 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>MLPRegressor(hidden_layer_sizes=(64, 64, 64), max_iter=2000, random_state=42)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;MLPRegressor<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.neural_network.MLPRegressor.html\">?<span>Documentation for MLPRegressor</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>MLPRegressor(hidden_layer_sizes=(64, 64, 64), max_iter=2000, random_state=42)</pre></div> </div></div></div></div>"], "text/plain": ["MLPRegressor(hidden_layer_sizes=(64, 64, 64), max_iter=2000, random_state=42)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["model.fit(X_train, y_train)"]}, {"cell_type": "code", "execution_count": 19, "id": "8736e39b", "metadata": {}, "outputs": [], "source": ["y_pred = model.predict(X_val)"]}, {"cell_type": "markdown", "id": "f4443b15", "metadata": {}, "source": ["### Check the Prediction Results"]}, {"cell_type": "code", "execution_count": 20, "id": "f11706f0", "metadata": {}, "outputs": [], "source": ["mae = metrics.mean_absolute_error(y_val, y_pred)"]}, {"cell_type": "code", "execution_count": 21, "id": "b225f0c1", "metadata": {}, "outputs": [], "source": ["mse = metrics.mean_squared_error(y_val, y_pred)\n", "rmse = mse**0.5 "]}, {"cell_type": "code", "execution_count": 22, "id": "7387a999", "metadata": {}, "outputs": [{"data": {"text/plain": ["3.504222416138832"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["rmse"]}, {"cell_type": "code", "execution_count": 23, "id": "8c236ab1-e77d-4b3b-b11b-be1948b1ad7b", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.9416009112877678"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["r2 = metrics.r2_score(y_val, y_pred)\n", "r2"]}, {"cell_type": "code", "execution_count": 24, "id": "8d1797f0-a04c-4c80-83a4-e6d9419ff0da", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "MAE: \t2.18\n", "RMSE: \t3.50\n", "r2: \t0.94\n", "\n"]}], "source": ["print(f\"\"\"\n", "MAE: \\t{mae:.2f}\n", "RMSE: \\t{rmse:.2f}\n", "r2: \\t{r2:.2f}\n", "\"\"\")"]}, {"cell_type": "markdown", "id": "328fca04", "metadata": {}, "source": ["Simple metrics like above are a nice way to see how a model has performed, but you should always check the actual data. \n", "\n", "In the plot below, we are comparing the real data against the predicted data."]}, {"cell_type": "code", "execution_count": 25, "id": "6c5639e7", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.scatter(y_val, y_pred)\n", "plt.xlim(40, 140)\n", "plt.ylim(40, 140)\n", "plt.ylabel('Predicted DT')\n", "plt.xlabel('Actual DT')\n", "plt.plot([40,140], [40,140], 'black') #1 to 1 line\n", "plt.show()"]}, {"cell_type": "markdown", "id": "742e3edd", "metadata": {}, "source": ["## Test Well Prediction\n", "Once the model has been fine tuned, we can apply it to our blind test well and see how it performs."]}, {"cell_type": "code", "execution_count": 26, "id": "9ffe8a16", "metadata": {}, "outputs": [], "source": ["test_well_x = test_df[['RHOB', 'GR', 'NPHI', 'PEF']]"]}, {"cell_type": "code", "execution_count": 27, "id": "4c27a1c9-5728-46f9-a706-563f10d734ea", "metadata": {}, "outputs": [], "source": ["test_well_x = scaler.transform(test_well_x)"]}, {"cell_type": "code", "execution_count": 28, "id": "af0d83da", "metadata": {}, "outputs": [], "source": ["test_df['TEST_DT'] = model.predict(test_well_x)"]}, {"cell_type": "code", "execution_count": 29, "id": "71e6109f", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAkgAAAG2CAYAAACEbnlbAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAABv60lEQVR4nO3dd1xV9f8H8Ne9bEEuS7mQC01T3Htgaoq5NbMMVzhy75XaNzNtKFbu0jRzryy3iT9XoYmgAo7o60RcIMKFy4bLvef3B19usu+FO+H1fDx4POKcc895w03vy88UCYIggIiIiIjUxMYugIiIiMjUMCARERERFcCARERERFQAAxIRERFRAQxIRERERAUwIBEREREVwIBEREREVAADEhEREVEBDEhEREREBTAgERERERVg1IAUFBSEAQMGwNPTEyKRCEeOHCn22kmTJkEkEmHNmjX5jstkMowYMQKOjo5wcnLCuHHjkJqaqt/CiYiIqEIzakBKS0tD8+bN8f3335d43eHDh3HlyhV4enoWOjdixAj8/fffOHPmDE6cOIGgoCBMmDBBXyUTERFRJWBpzIf36dMHffr0KfGaZ8+eYfr06Th9+jT69euX79w///yDwMBAXL16FW3atAEArF+/Hn379sW3335bZKAiIiIiKo1RA1JpVCoVRo0ahfnz56Nx48aFzgcHB8PJyUkdjgDA19cXYrEYISEhGDx4cJH3zcrKQlZWVr7nyGQyuLq6QiQS6f4HISIiIp0TBAEpKSnw9PSEWKzbTjGTDkgBAQGwtLTEjBkzijwfGxuL6tWr5ztmaWkJFxcXxMbGFnvf5cuXY+nSpTqtlYiIiIzjyZMnqFGjhk7vabIB6fr161i7di3CwsJ03qqzaNEizJkzR/29XC5HrVq18OTJEzg6Our0WURERKQbDx48wOjRo3Hz5k0AuRO4Nm3ahKpVq+r8WSYbkC5evIi4uDjUqlVLfUypVGLu3LlYs2YNHj16BKlUiri4uHyvy8nJgUwmg1QqLfbeNjY2sLGxKXTc0dGRAYmIiMgE7d+/HxMmTEBKSgpcXV2xc+dOdO7cGZs2bdLL8BiTDUijRo2Cr69vvmO9evXCqFGjMGbMGABAx44dkZSUhOvXr6N169YAgPPnz0OlUqF9+/YGr5mIiIh0KyMjA7NmzcLmzZsBAG+++Sb27t2LGjVqIDk5WW/PNWpASk1Nxf3799XfR0VFISIiAi4uLqhVqxZcXV3zXW9lZQWpVIo33ngDANCoUSP07t0b48ePx6ZNm6BQKDBt2jT4+flxBhsREZGZu3PnDoYOHYqbN29CJBLhk08+weeffw5LS/3HF6MGpGvXruGtt95Sf583Lsjf3x/bt2/X6B579uzBtGnT0KNHD4jFYgwZMgTr1q3TR7lERERkILt378akSZOQlpaG6tWrY/fu3ejZs6fBni8SBEEw2NNMVHJyMiQSCeRyOccgERERGVF6ejqmTZuGbdu2AQDeeust7NmzBx4eHoWu1efnN/diIyIiIpPw999/o23btti2bRtEIhE+//xznDlzpshwpG8mO0ibiIiIKgdBELB9+3ZMnToVGRkZkEql2Lt3b75hOIbGgERERERGk5qaiilTpmDXrl0AgJ49e2L37t2FFoI2NHaxERERkVHcvHkTbdq0wa5duyAWi/HVV18hMDDQ6OEIYAsSERERGZggCNiyZQtmzJiBrKwsvPbaa9i3bx/efPNNY5emxoBEREREBpOcnIyJEydi//79AIA+ffpg586dcHNzM3Jl+bGLjYiIiAwiPDwcrVu3xv79+2FhYYGVK1fixIkTJheOALYgERERkZ4JgoAffvgBc+bMQXZ2NmrVqoX9+/ejY8eOxi6tWAxIREREpDdJSUn46KOP8NtvvwEABg4ciG3btsHFxcXIlZWMXWxERESkF1evXkWrVq3w22+/wcrKCqtXr8aRI0dMPhwBbEEiIiIiHRMEAWvXrsXHH38MhUIBLy8vHDhwAG3btjV2aRpjQCIiIiKdkclkGDNmDI4dOwYAGDJkCH766Sc4OTkZtzAtsYuNiIiIdCI4OBgtW7bEsWPHYG1tjQ0bNuDgwYNmF44ABiQiIiIqJ5VKhW+++QZdunTB48ePUa9ePQQHB2Pq1KkQiUTGLq9M2MVGREREZRYfHw9/f3/8/vvvAIAPPvgAmzdvhqOjo5ErKx+2IBEREVGZXLx4ES1atMDvv/8OGxsb/Pjjj9i3b5/ZhyOAAYmIiIi0pFKp8PXXX+Ott97Cs2fP8MYbbyA0NBQTJkww2y61gtjFRkRERBqLi4vDyJEjcebMGQDAyJEjsXHjRjg4OBi5Mt1iQCIiIiKNXLhwAcOHD0dsbCzs7Ozw/fffY/To0RWm1ehV7GIjIiKiEimVSixduhS+vr6IjY2Ft7c3rl69ijFjxlTIcASwBYmIiIhKEBMTg5EjR+L8+fMAgDFjxmD9+vWwt7c3cmX6xYBERERERTpz5gxGjhyJuLg42NvbY+PGjRg1apSxyzIIdrERERFRPjk5Ofj000/Rq1cvxMXFoWnTprh27VqlCUcAW5CIiIjoFU+fPsXw4cNx8eJFAMDEiROxevVq2NnZGbkyw2JAIiIiIgDAqVOnMGrUKCQkJKBq1arYvHkz/Pz8jF2WUbCLjYiIqJJTKBRYsGAB+vbti4SEBLRs2RLXr1+vtOEIYAsSERFRpfb48WP4+fkhODgYADB16lR8++23sLW1NXJlxsWAREREVEkdO3YMo0ePRmJiIiQSCbZu3YohQ4YYuyyTwC42IiKiSiY7Oxtz5szBoEGDkJiYiLZt2yIsLIzh6BVsQSIiIqpEoqKi4Ofnh9DQUADArFmzEBAQAGtrayNXZloYkIiIiCqJQ4cOYezYsZDL5XB2dsb27dsxcOBAY5dlktjFRkREVMFlZWVh+vTpGDJkCORyOTp06IDw8HCGoxIwIBEREVVg9+/fR6dOnbBhwwYAwMcff4ygoCDUrl3byJWZNnaxERERVVAHDhzA+PHjkZKSAldXV+zcuRN9+/Y1dllmgS1IREREFUxGRgYmTZoEPz8/pKSkoHPnzoiIiGA40gIDEhERUQVy584ddOjQAT/++CNEIhE++eQTXLhwATVq1DB2aWaFXWxEREQVxO7duzFp0iSkpaWhWrVq2L17N95++21jl2WW2IJERERk5tLT0zFu3DiMGjUKaWlp6NatG27cuMFwVA4MSERERGYsMjIS7dq1w88//wyRSIQlS5bg7Nmz8PDwMHZpZo1dbERERGZq+/btmDJlCjIyMiCVSrFnzx50797d2GVVCGxBIiIiMjOpqanw9/fHmDFjkJGRAV9fX0RERDAc6RADEhERkRm5desW2rZti507d0IsFuPLL7/E6dOn4e7ubuzSKhR2sREREZkBQRDw008/YcaMGcjMzISnpyf27duHLl26GLu0CokBiYiIyMQlJydj4sSJ2L9/PwCgT58+2LFjB6pVq2bkyioudrERERGZsPDwcLRu3Rr79++HhYUFAgICcOLECYYjPWMLEhERkQkSBAEbN27E7NmzkZ2djZo1a2L//v3o1KmTsUurFBiQiIiITIxcLsdHH32EX3/9FQAwYMAAbN++HS4uLkaurPJgFxsREZEJuXr1Klq2bIlff/0VVlZWWLVqFY4ePcpwZGBsQSIiIjIBgiBg3bp1mD9/PhQKBerUqYMDBw6gXbt2xi6tUmJAIiIiMjKZTIaxY8fi6NGjAIB3330XW7duhZOTk3ELq8TYxUZERGREV65cQcuWLXH06FFYW1tj/fr1+PXXXxmOjIwBiYiIyAhUKhW+/fZbvPnmm3j8+DHq1auH4OBgTJs2DSKRyNjlVXrsYiMiIjKw+Ph4jB49GidPngQAfPDBB9i8eTMcHR2NXBnlYUAiIiIyoEuXLsHPzw/Pnj2DjY0N1q5diwkTJrDVyMSwi42IiMgAVCoVli9fjm7duuHZs2do0KABQkJCMHHiRIYjE8QWJCIiIj2Li4vDqFGj8H//938AgJEjR2Ljxo1wcHAwcmVUHAYkIiIiPfrjjz8wfPhwxMTEwM7ODhs2bMCYMWPYamTi2MVGRESkB0qlEsuWLUOPHj0QExODRo0a4erVqxg7dizDkRlgCxIREZGOxcbGYsSIETh//jwAYMyYMVi/fj3s7e2NXBlpigGJiIhIh86ePYsRI0YgLi4O9vb22LhxI0aNGmXsskhL7GIjIiLSgZycHCxevBhvv/024uLi0LRpU1y7do3hyEyxBYmIiKicnj17huHDhyMoKAgAMGHCBKxZswZ2dnZGrozKigGJiIioHAIDAzFq1CjEx8fDwcEBW7ZsgZ+fn7HLonIyahdbUFAQBgwYAE9PT4hEIhw5ckR9TqFQYMGCBWjatCns7e3h6emJDz/8EM+fP893D5lMhhEjRsDR0RFOTk4YN24cUlNTDfyTEBFRZaNQKLBw4UL06dMH8fHxaNGiBcLCwhiOKgijBqS0tDQ0b94c33//faFz6enpCAsLw+LFixEWFoZDhw7hzp07GDhwYL7rRowYgb///htnzpzBiRMnEBQUhAkTJhjqRyAiokro8ePH6NatGwICAgAAU6dORXBwMOrXr2/kykhXRIIgCMYuAgBEIhEOHz6Md955p9hrrl69inbt2iE6Ohq1atXCP//8A29vb1y9ehVt2rQBkNvU2bdvXzx9+hSenp4aPTs5ORkSiQRyuZwbBRIRUYmOHz+O0aNHQyaTwdHREVu3bsV7771n7LIqJX1+fpvVLDa5XA6RSAQnJycAQHBwMJycnNThCAB8fX0hFosREhJS7H2ysrKQnJyc74uIiKgk2dnZmDt3LgYOHAiZTIY2bdogPDyc4aiCMpuAlJmZiQULFmDYsGHqlBgbG4vq1avnu87S0hIuLi6IjY0t9l7Lly+HRCJRf9WsWVOvtRMRkXmLiorCm2++iVWrVgEAZs2ahUuXLqFu3bpGroz0xSwCkkKhwNChQyEIAjZu3Fju+y1atAhyuVz99eTJEx1USUREFdHhw4fRsmVLhIaGwsnJCUeOHMHq1athY2Nj7NJIj0x+mn9eOIqOjsb58+fz9TFKpVLExcXluz4nJwcymQxSqbTYe9rY2PB/bCIiKlFWVhbmz5+P9evXAwA6dOiA/fv3o3bt2kaujAzBpFuQ8sLRvXv3cPbsWbi6uuY737FjRyQlJeH69evqY+fPn4dKpUL79u0NXS4REVUQDx48gI+PjzoczZ8/H0FBQQxHlYhRW5BSU1Nx//599fdRUVGIiIiAi4sLPDw88N577yEsLAwnTpyAUqlUjytycXGBtbU1GjVqhN69e2P8+PHYtGkTFAoFpk2bBj8/P41nsBEREb3ql19+wUcffYSUlBS4urpix44d6Nevn7HLIgMz6jT/P/74A2+99Vah4/7+/vj888/h5eVV5OsuXLiAbt26AchdKHLatGk4fvw4xGIxhgwZgnXr1sHBwUHjOjjNn4iIMjIyMGfOHGzatAkA0LlzZ+zbtw81atQwcmVUHH1+fpvMOkjGxIBERFS53blzB0OHDsXNmzcB5E7mWbZsGSwtjdfRolQJCI2SIS4lE9Wr2qKdlwssxCKj1WOK9Pn5bfKDtImIiPRpz549mDhxItLS0lCtWjXs2rULvXr10uoeug4zgbdjsPR4JGLkmepjHhJbLBngjd5NPMp8X9IcAxIREVVK6enpmDFjBrZu3QoA6NatG/bs2VPqGNaCYSgxLRtfnNRdmAm8HYPJu8NQsHsnVp6JybvDsHFkK4YkA2BAIiKiSicyMhJDhw7F33//DZFIhMWLF+Ozzz6DhYVFia8rqmWnKGUNM0qVgKXHIwuFIwAQAIgALD0eiZ7eUna36ZlJT/MnIiLSte3bt6Nt27b4+++/4e7ujrNnz2Lp0qUahaPJu8NKDUcA1AFn6fFIKFWaD/UNjZKVeH8BQIw8E6FRMo3vSWXDgERERJVCamoq/P39MWbMGKSnp8PX1xc3btxA9+7dS31tSS07xckLM1ceJGj8mriU0sMXAJyNLH47LdINBiQiIqrwbt26hbZt22Lnzp0Qi8X48ssvERgYCHd3dwC5ASj4QQKORjxD8IOEQq0+pbXslGTq3jAE3o7R6NrqVW01uu5wxDOtWqZIexyDREREFZYgCNi6dSumT5+OzMxMeHp6Yt++fejSpYv6Gk1mjGnaslOUpAyFxuOREtOyIRIBpS3AI0tTIDRKho71XEu+kMqMLUhERFQhpaSkYMSIERg/fjwyMzPRu3dvREREFApHRY0ryhtkndfyo2nLTklKG48UeDsGU/eGlRqO8pQntFHpGJCIiKjCCQ8PR6tWrbBv3z5YWFhgxYoVOHnyJKpVq6a+prQZY8C/oaadlws8JLYo67yx0gZXl2WMky5CGxWPAYmIiCoMQRDwww8/oGPHjrh//z5q1qyJoKAgLFiwAGJx/o88bWaMWYhFWDLAGwDKHJKA4lt9tBnjJEJuF2A7L5dyVEKlYUAiIqIKQS6XY+jQoZg6dSqysrIwYMAAhIeHo1OnTkVer2kXVd51vZt4YOPIVpBK8rfceEhsMavH6xrdq7hWH226ywQAA5t7cB0kPeMgbSIiMnvXrl3DBx98gIcPH8LS0hIBAQGYPXs2RKLiQ4SmXVSvXte7iQd6eksLbSsCANuDo5GUrijyHiIA0hJafbTtLtscFIWWtZy5orYeMSAREZHZEgQB69atw/z586FQKFC7dm0cOHAA7du3L/W1eeOKYuWZRY79KS7UWIhFhWaPBd6OKTYcAbmtPksGeBfb6lNaLUXhitr6xS42IiIyS4mJiXj33Xcxa9YsKBQKDB48GOHh4RqFIwAljisSITfUDG1TA6vP3MG3p+/gr/vxRc5CyxtgXRLnKlbo6S0tUy1F4Yra+scWJCIiMjshISH44IMPEB0dDWtra6z85hu07TscQdHpqC5ToZ2XS4ktK0qVgCsPE3D7WTJ6NXbHpfsvkZqlUp+3s7aAUiVg7bn76mMbLtyHg40lVg5phr7N/u3a0mSAdWJ66esW5Y1x0mSvtzyc6q8/DEhERGQ2VCoVVq9ejYULFyInJwd169bFrK9/wN6Hllj9U4j6uoILPb4q8HYMFh66VWKXWHq2ssjjqVk5mLI3DOOfeOE//XJbfDQNKaf+t6bSq+FNqRLyjWfq6S1FlkKFmQciNLonp/rrDwMSERGZhYSEBPj7++PkyZMAgKFDh2LorGWYd/Q+BOTkuzZvoceCq1cH3o7BpN1h5a5ly8Uo5KhUWDKgicYhZWdwNHYGR6vDG4BCrUUONpZIzcop7haFtK7trF3hpDEGJCIiMnl//fUX/Pz88PTpU9jY2GDNmjX4aPwEvLnyQrELPYqQfyCzUiXg82MljxXSxra/onHpbjx8XndTj1nSRIw8s9iQpk04AoCrj2Twed1Nq9eQZjhIm4iITJZKpcLy5cvRtWtXPH36FA0aNEBISAgmTZqEq48SNV7oEcgdKxSbrNsxO/depmF7cLRWK2DrUvCDBCM9ueJjCxIREZmkuLg4fPjhhzh9+jQAYMSIEdi4cSOqVq2ae17LhR5j5Rn6KdSojBXNKj4GJCIiMjl//vknhg0bhpiYGNjZ2WH9+vUYO3ZsvoUftV3oUZaWrZdajaljXXav6Qu72IiIyGQolUosW7YM3bt3R0xMDBo1aoTQ0FCMGzeu0KrYpW0gW3DPMhcHG/0Wb2D2NhboUMKyAVQ+DEhERGQSYmNj0atXLyxZsgQqlQqjR4/G1atX0aRJkyKvL22hRyD/6tVSx4o1JT5gcFOuoq1HDEhERGR0586dQ4sWLXDu3DlUqVIFO3bswLZt22Bvb1/i64rbQFYqsS00xb+dlwuc7Kz0Ur8xuHINJL3iGCQiIjKanJwcLFu2DF9++SUEQUCTJk3wyy+/oFGjRhrfo7gNZAu2rliIRRjjUwerz97T9Y9hFFxFW78YkIiIyCieP3+OYcOGISgoCAAwfvx4rF27FnZ2dlrfq6gNZIsyrXt9bLv8qMRVtM0FV9HWL3axERGRwQUGBqJ58+YICgqCbRV7fL76R2zc9GOZwpE2LMQirHi3qV6foW8FB5+TfjAgERGRwSgUCixatAh9+vRBfHw8rKrXhcuIVdgW+xo6B5xH4P/2K9On3k08sGlkKzhVMb/xSEUNPif9EAmCUOlXmUpOToZEIoFcLoejo6OxyyEiqpCePHkCPz8/XL58GQDg0LIfXLqPg8jSGsC/H/4FB1fri1IlYMP5+9j2VxSSMsyjy62kTXgrI31+frMFiYiI9O7EiRNo0aIFLl++DAubKnAbtBCub09WhyPg3zWhlx6PhFKl/3+7W4hFmOlbH6H/8YW1hXl8HLJNw3DM4/8IIiIyS9nZ2Zg7dy4GDBgAmUyGhk2aw91/Lewbdi7y+oL7pxnC9ehEZCtVBnteebxIzsLk3WEG6Yqs7BiQiIhILx49eoQuXbpg1apVAICZM2fii62HYOVceveQIaewn4mMNdizysvQrWyVGQMSERHp3JEjR9CyZUuEhITAyckJhw8fxpo1a/Caq0Sj1xtqCnvg7Rj8/NcjgzxLV4zRylYZMSAREZHOZGVlYebMmRg8eDCSkpLQvn17hIeH45133gGg/f5p+qRUCVh6PFLvz9EXLhSpXwxIRESkEw8ePICPjw/WrVsHAJg3bx4uXryIOnXqqK/Rdv80fQqNkiFGbr4hgwtF6hcDEhERldvBgwfRqlUrXL9+HS4uLjhx4gS++eYbWFkVXmtIm/3T9MlcW2C4UKRhcKsRIiIqs8zMTMyZMwcbN24EAPj4+GDfvn2oWbNmia/TdP80fXoUn26wZ2lDBEBSxQry/22HIhQ4B3ChSENgQCIiojK5e/cuhg4dihs3bgAAFi1ahGXLlsHSUrOPFk33T9OWUiWUGryUKgF7Q6J1/uzyyqtyxbtNEf44EVsuRuHVpY9EImD8m15cKNIAGJCIiEhre/fuxcSJE5Gamopq1aph165d6NWrl7HLQuDtGCw9HplvbFFRq0+HRsnwIiXLGCWWSPq/WgFgc1AUCk7kVwm5x1vWcmZI0jMGJCIiKtGrLTKOlirsWbMUW7duBQB07doVe/fuhaenp5GrzA1Hk3eHFQoVsfJMTN4dlm98kymOP3KwscCf89+ChViEzgHnC/0cr1p6PBI9vaXsZtMjBiQiIirWqy0yivgneHl0BRTx0RCJRFi8eDEWL16scZeaPuVN2S8qVAjI7bp6NVS42dsYuMLSpWYpcT06EQBKnF336jpI+uiipFzG/7+aiIhM0qstMqm3zkF25gcIiiyI7Z1Qrf88dHx/rEmEI6D0KfuFQoWJNrzEyjNw+5lco2tNsRWsIjGN/7OJiMik5LXIKLMzITuzEWm3zwEAbGs3h1v/ebB0cDapbh5Nw0LedXEmOP4IAOJTs3A44plG13IdJP1iQCIiokJCo2SIvv9fvDyyAjmyp4BIDInPMEg6DoVIbGFy3TyahoW8614mm2brS0JqNmRpilKvE4uA1rWdDVBR5cWARERE+QiCgD07fkbszk8h5GTDwsEFbgPmw7ZW00LX6rqbR5Mp+kXJ28IkVp5Z5DgkEXJniOUtrhj5XLNuLEO7rWFdKgG4Hp1oEuG0omJAIiIitZSUFEyaNAl79+4FANh6tYZb/zmwqFL0JrO67ObRdIp+UfK2MJm8OwwilL64YrpCpbO6dcnWSvMNLjgGSb+41QgREQEAIiIi0KZNG+zduxcWFhao2esjuL+/pMhwpOvtLvIGhBccaJ03RT/wdkyp99BmC5O2dUyze8rd0RYu9oW3ZykKxyDpF1uQiIgqOUEQsGnTJsyePRtZWVmoUaMG9u/fjxRJXUzeHZZ7zSvX63q7C22n6JdE0y1M/Dt54avf/1vu2nWtVS0XVLG2wJaLj0q8jnux6R9bkIiIKjG5XI4PPvgAU6ZMQVZWFvr374+IiAj4+PgYbFNZbaboayJvC5NBLV5Dx3quRYYqa0sx+jd1L2vJeiN1tMWJm7GlXre4H/di0ze2IBERVVLXrl3DBx98gIcPH8LS0hIBAQGYPXs2RKJ/P3gNsamstlP0dWXVB61w4tYpnd6zPDwktoCo5EUi8zjbWxugosqNAYmIqJIRBAHr16/HvHnzoFAoULt2bRw4cADt27cv8np9bSqbR9sp+rqSt2q1KRAht8syPlWz9Zk4QFv/2MVGRFSJJCYmYsiQIZg5cyYUCgXeeecdhIeHFxuODCFvin5xbVK6HhCex1RChscrXZbGCotUGFuQiIgqiZCQEHzwwQeIjo6GlZUVvv32W0yfPj1fl5oxaDtFX1eMGTJc7K2wuH9jSB3zd1kmppXegsQB2obBFiQiogpOEAR899136Ny5M6Kjo1G3bl1cvnwZM2bMMHo4ymOoAeGval3bWS9bsmlyzy8HNcHglvkHkStVAr44+U+pr+UAbcNgCxIRUQWWkJCA0aNH48SJEwCA999/H1u2bIFEUvTCj9oq68rXRTHEgPBXXY9OLHJpgfLS5J4Su8KDrEubzZeHA7QNgwGJiKiC+uuvvzBs2DA8efIENjY2WLNmDSZOnKizVqPyrHxdHH0PCH+VMccgTd0bhhVDmub7PZ2NLH16P2A6Y6cqOnaxERFVAEqVgOAHCTga8Qx/3XuJr5cvR9euXfHkyRPUr18fV65cwaRJk3Qajsq78rWxGXMMUlKGIt/vSakScDjimUav5QBtw2ALEhGRmXu1JUeZLkf8iVXIjLoOABg+fDg2bdqEqlWr6ux5ulz52pjaeblA6miD2GTNptZrQupoA0CEF8lFb5r7KgHA58f+VncrytIUpd7f1d6aA7QNhC1IRERm7NWWnMzHtxCzbToyo65DZGkN194zMHLhNzoNR4DuV742FguxCJ8PbKyz+4kAfD6wMT4f6K3xa2KTs7Dh/H2Nu9cGtfA06dBZkTAgERGZqbyWHJVKiaS/9uHF/v9AmSqDpUsNSD9charN38ayE/9AqdLtUGRjrXytD72beGDTyFZwqqLZBrHFeXUto7wZeU52mt1z9dm72BPyWKNre3pLy1MmaYFdbEREZio0Soanz2IQf+JbZEbfAADYN+kBl56TIba2zdeSo8uBzxVtMcO82XNXHiQg+GE87r5Iwf9FxpX6umlv1UN996pFzrbr3cQDVW2tMOKnEI1qyMxRlXoNu9cMS+OAZGFhgZiYGFSvXl2f9RARkYbOnD2L59unQ5WWBJGVDVx6ToFD0x6FrtN1S07eytex8qLH2YiQu36ROX2YW4hF8KnvBp/6bgh+kKBRQPJ5vVqJwbNDXVd4SGw1mrqvCXavGZbGXWyCoI/VIoiISFtKpRJLlizB55OHQZWWBCu32pB+uLrIcAToviUnb+VroPCiiPpc+dpQdLX1yau/J11g95phGXUMUlBQEAYMGABPT0+IRCIcOXIk33lBEPDZZ5/Bw8MDdnZ28PX1xb179/JdI5PJMGLECDg6OsLJyQnjxo1DamqqAX8KIiLDef78OXr06IFly5ZBEARUa9MH0g+/g7VbrSKv19e2FMZY+dpQdBkAezfxwGzf+uWuycnOyqxa5CoCrcYg/fTTT3BwcCjxmhkzZmh8v7S0NDRv3hxjx47Fu+++W+j8ypUrsW7dOuzYsQNeXl5YvHgxevXqhcjISNja5v6hHDFiBGJiYnDmzBkoFAqMGTMGEyZMwN69e7X50YiITN7p06cxatQovHz5Eg4ODpjy6QqczqqPpPTip4cPbO6ht5YcQ698bUh5AbDgQpjSMiyEOa17fewLfYLY5LJ3tfk2ql4hfq/mRCRo2HcmFotRo0YNWFhYFH8zkQgPHz4sWyEiEQ4fPox33nkHQG7rkaenJ+bOnYt58+YBAORyOdzd3bF9+3b4+fnhn3/+gbe3N65evYo2bdoAAAIDA9G3b188ffoUnp6eGj07OTkZEokEcrkcjo6OZaqfiEgflCoBl+/FYW3AF/ht2/cAgObNm2Palxvw1SV5qWvtiACzb9ExJl1tpRJ4OwaTdoeVuY7VQ5tjcKsaZX59RaXPz2+tWpCuXbtmsEHaUVFRiI2Nha+vr/qYRCJB+/btERwcDD8/PwQHB8PJyUkdjgDA19cXYrEYISEhGDx4cJH3zsrKQlbWvwuDJScn6+8HISIqo8DbMfhk1x+I3PMlsp5FAgDc2w/A4nVr8NXp+xrt+SUA+M/h2+je0B3WllzZRVu62vqkdxMP/DC8JabuDS/T/m9SiV25ayDtaPynxdA7PsfG5i6a5e7unu+4u7u7+lxsbGyhwGZpaQkXFxf1NUVZvnw5JBKJ+qtmzZo6rp6IqHwCb8fAf+mPuLFuIrKeRUJkXQVuAxfAtttEzD30j0arLudJSMtGh+VnEXg7Jt+WJMEPEnS+RhIVr28zT6z3a6n16xxsLDn+yAg0bkGqSLPYFi1ahDlz5qi/T05OZkgiIpORmZWNMZNnIO7SrwAAa+nrcBu4AFbOZe8mk6UpMGl3GJyqWOUbs1TezWVJO/1beOLW8yT8GBSl8WverO/K8UdGoHEL0pIlS0odoK1LUmnudMYXL17kO/7ixQv1OalUiri4/GtV5OTkQCaTqa8pio2NDRwdHfN9ERGZgkePHqFhi3aI/V84qtp6AKQjvilXOHpVwQHd5rS5bEWxqK83NmjRkjSyfR39FUPF0rgFacmSJQCAe/fu4ejRo3j06BFEIhG8vLzwzjvvoG7dujotzMvLC1KpFOfOnUOLFi0A5Lb0hISEYPLkyQCAjh07IikpCdevX0fr1q0BAOfPn4dKpUL79u11Wg8Rkb4dOXIEoz4cjdQUOcQ29nDtOxNVGnTS6zPNaXPZiqR/i9xJRNP2h5d4nVMVK3TQ4SropDmtBmkvX74cn332GVQqFapXrw5BEPDy5UssXLgQX3/9tXq2maZSU1Nx//599fdRUVGIiIiAi4sLatWqhVmzZuHLL79E/fr11dP8PT091TPdGjVqhN69e2P8+PHYtGkTFAoFpk2bBj8/P41nsBERGVtWVhYWLFiAtWvXAgCsPRqg2qAFsJS4l/JK3dDXliRUMk2621a825Sh1Ug0DkgXLlzAp59+isWLF2PmzJlwdnYGkLtQ45o1a7Bw4UK0a9cOXbp00fjh165dw1tvvaX+Pm9ckL+/P7Zv346PP/4YaWlpmDBhApKSktC5c2cEBgaq10ACgD179mDatGno0aMHxGIxhgwZgnXr1mlcAxGRoRQ1ZTz6URSGDh2K69evAwAc2w6GU9cPIbIo3+apZWEOm8tWNIv6eqN5DWf858gtJL7S/Sl1tMHnAxtzbJgRabwO0gcffAAnJyf8+OOPRZ6fMGECUlJSsG/fPp0WaAhcB4mI9C3wdkyhRQetH4fi2fHVSE9NgYuLCyZ99h32xFQr8zNc7K2hUimRlKEs0+v3je/AFiQj0dV6S5WNSayDFBoail27dhV7ftSoUfjwww91UhQRUUUSeDsGk3eHqde/EXKyITu/FanhJwEA3i3bIvDob5h5PBqAvEzPEAH4enATAMDk/y1IqOncY3PcXLai0dV6S6Q7Gs9ie/HiBerUqVPseS8vrxLXHiIiqoyUKgFLj0eqw4pC9gwxu+apw5Gkw3twePcLfH4uFmGPyxaOXOyt1KtlF7dHmoNN0f8ergibyxLpg8YtSJmZmbC2ti72vJWVFbKzs3VSFBFRRaBUCdj+V5S6Wy0t8k8knN4AITsDYjtHuPWfC7u6rfEiNQcv/htXyt2Kt7h//rEqr+6RdjYyFocjnhW7sGRZ9hYjqgx0tlltSkqKTgoiIjJXr44jeRSfhn2hjxGbnAWVIguJZ39E6s3/AwDY1GwCtwHzYFnVTSfPlTraFjpmIRZBnpGNn/96VGxX22zfBpjW/XW2HBEVQeOAVKtWLWzZsqXUa4iIKqOiBmEDgCL+CV4eXQFFfDQAESSdPoDEZxhE4uI3/taGk51VkWOHCnbtFSQCsP/qY0zr/rpO6iCqaDQOSI8ePdJjGURE5qPgjKPEtKwiNyFNvX0Osv/7AYIiC2J7J7j1nwe7Oi10WssYnzpFtgCFRskKhbVXce0jopJp1cVGRFTZFdVSJBblnzGmys6E7MwmpN0+CwCwrd0Mbv3nw8LBWae1OFexwrTu9Ys8p+maRlz7iKhoDEhEVGEVt7ZMWdacUaoEbDh/D6vP3it0TvVKOsp++QjxRwOgSHgCiMSQ+AyDpONQnXWpvWp5CassV69aeFxSea4jqmwYkIioQiqqpcdDYouBzT1w7EZMvuNSR1sMa1cLddyqFBmYAm/H4PNjkYhNLqHLShCQevMMEs/+CCEnCxYOLnAbMA+2tZrp5edzsrNET+/iN+Vu5+UCD4ktYuWZRY5D4tpHRCXTeCXtiowraRNVLAUXZtSWxytT3zW5lyorHbL/+wFpkX8AAGy9WsGt3xxY2DuVsQLNlLbydV7tQP4uwLzol7d2EpG50ufnt8YLRRIRmYPSZm9pIkaeicm7w/D7zZhS75Ud9xAxO2fnhiORGE5d/VH9/c/1Ho4AIFaeUeL54haNlEpsGY6ISqFRF1tycrLGN2QLDBEZ2qtjiuJTskqcvaUpAcCcXyKQmaMq+rwgIDXiFGTntgBKBSyqusFt4HzY1mhc7mdrSpZW+uK8Pb2lqGpjheCH8QByt7PoUNeVax8RlUKjgOTk5ASRSLM/TEpl2TZJJCLKo80g6t9vxuCTwzeRlJGj8zqKC0eqrHQkBK5H+n8vAgDs6rWFa7/ZsLAz7D8QXRxsSjxf1Dis38KecuVsIg1oFJAuXLig/u9Hjx5h4cKFGD16NDp27AgACA4Oxo4dO7B8+XL9VElElUZxg6uL+lD/6mQktlyMMmh9WbH3EX80ADlJMYDYAs5d/VG17TsQiQw/YqGoFbTzFDd2KvZ/3YfsYiMqmdaDtHv06IGPPvoIw4YNy3d879692Lx5M/744w9d1mcQHKRNZBpKGhAtQv5BxV+d/BtbLj4yWG2CICAl7AQSL2wFlDmwcKyOagM/hs1rDSECyjXmqSw8JLa4tKB7kS1rSpWAzgHni+1qzJvBVtzricyFSQ3SDg4ORps2bQodb9OmDUJDQ3VSFBFVPqUNrhYALDp0C0qVgN9vPjdoOFJmpuLlka+RePZHQJkDu/od4DFmHWxeawgrseHDEQAs7teo2HCjzSraRFQ0rQNSzZo1i9yT7aeffkLNmjV1UhQRVT6lfagDQGK6AmvO3sGcgzcMVBWQ9fwOYrbPRMbdYEBsCeceE1Bt8H9gYZu7cbei6GFKenf3RWqx57iKNlH5ab1Q5OrVqzFkyBCcOnUK7du3BwCEhobi3r17+O2333ReIBFVDmciYzW6bv35B3quJJcgCEi5egSJf24HVEpYOknhNnABbDyK3trD0Naeu4cG7g7o28yz0Dmuok1Uflq3IPXt2xd3797FgAEDIJPJIJPJMGDAANy9exd9+/bVR41EVMEF3o7Bz389MnYZasqMFLw89EXueCOVElXe8IHH6LUmE46A3G6yKXvDEXg7ptC5vFW0Sxpd5GRnBZVKgFJV6dcKJioSV9IGB2kTGVLBKfytazuj6zcXdLJ2kS5kPv0H8cdWQpnyErCwgkuP8XBo0UfjpU4MzamKFa5/2rPQeKTiVtEuqLgZgkTmQJ+f32UKSBcvXsSPP/6Ihw8f4uDBg3jttdewa9cueHl5oXPnzjot0BAYkIgMo6gp/C721hoteKhvgqBCcsghJAXtBAQVLJ09UW3QQli71zV2aaXaM649fOq7FToeeDsGCw/dQlK6osTXF5whSGQuTGoW22+//YZevXrBzs4OYWFhyMrKAgDI5XJ8/fXXOi2OiCqOvBaNgi1FphCOlOlyxP26FEl/bgcEFao06goP/zVmEY4A/G+V7MJUKkBeSjjKs/R4JLvbiF6hdUD68ssvsWnTJmzZsgVWVlbq4z4+PggLC9NpcURUMehifzR9yXxyGzHbpiPz4XWILK3h0ns63AbMg9imirFL01hRv9fA2zGYslezDXs57Z+oMK1nsd25cwddunQpdFwikSApKUkXNRFRBaPJFH5DE1RKyK8chPzS3twuNZcaqPbOQlhXq2Ps0rTmZGed7/u8QKqtv+6/1Gh7F6LKQOuAJJVKcf/+fdSpUyff8UuXLqFuXfNojiYi3dFk37TSdp03NGVaIuKPf4fM6AgAgH2T7nDpORliazvjFlZGbg75A1JZA+mGC/8uoeBib40vBzVB32Ycl0SVk9YBafz48Zg5cyZ+/vlniEQiPH/+HMHBwZg3bx4WL16sjxqJyET9fjMGnx69nW8cUcFZUYG3Y/DFyX+MVWIhGdE3EH/8G6jSkiCysoFLz8lwaOpr7LLKRSrJH+x0sQCkLC0bU/aGYeJTLyzq613u+xGZG60D0sKFC6FSqdCjRw+kp6ejS5cusLGxwbx58zB9+nR91EhEJmj575H4MajwRrExr2yGCqDYvdUMTVApIf9rP+SX9wMQYOVWC26DFsLarZaxSysXWysx2nm55DumywUgfwyKQvMaTkUuSElUkZV5HaTs7Gzcv38fqamp8Pb2hoODg65rMxhO8yfSzu83n2PK3vASr/GQ2EIQBMQmZxmoquLlpCQg/sS3yHp8CwDg0OxtOPtOgNiqYqwkfffLPrC2/HfOjVIloPnS/0NqVo5O7u9ib4Wr/ym81hKRsZnUNP+xY8ciJSUF1tbW8Pb2Rrt27eDg4IC0tDSMHTtWp8URkelRqgR8evR2qdfFyDNNIhxlRIUhZvsMZD2+BZGVLVz7z4VrnxkVJhwBwI7Ljwody1bqbpM4WZqCM9yo0tE6IO3YsQMZGYUHXGZkZGDnzp06KYqITFdolAyyNM3W1jEmQaVE4p87EPfLZ1Cly2FV3Qseo9fCofFbxi5N51aduZNvy5ErDxOQnaPbXXS5sS1VNhqPQUpOToYgCLkbOKakwNb23399KZVK/P7776hevbpeiiQi02FqM9KKkpMcj/jjK5H1NHequ0OLPnDpMR4iS+tSXqkdEUrexsNQMhQqTNodhk3/Ww07+EGCzp/h5mCj83sSmTKNA5KTkxNEIhFEIhEaNGhQ6LxIJMLSpUt1WhwR6Y8m0/OLYgorX5ck48FVxJ9cDVVGMkTWdnDtPQP2jd7Uy7NMIRy9aub+CEQuk0IvlZnaD0ukZxoHpAsXLkAQBHTv3h2//fYbXFz+nTVhbW2N2rVrw9OTsxyIzEFRe6Jpummpi4m2JAjKHCQF7URy6CEAgLV7PbgNWggrZ/2u4zPOpw5+vx1rEgthZuWocOnuS3Ss65ZvTSNdiE8z/ngyIkPSOCB17doVABAVFYVatWqZ7M7WRFSyvD3RCjYIxMgzMWl3GH4Y3qrExQEfJ6Trt8AyyJHH4eWxAGQ/vwMAqNp6AJy7jYXI0qqUV5afr7cUn/Tzxs+XHuKr3/+r9+eVZvPFh9g5rj2qWFsgPVups/vqcukAInOg9TpI58+fh4ODA95///18xw8ePIj09HT4+/vrrDgiKr9Xu9LcHGyw5OjfJfaWTNsXhg1oWeS6N0qVgH2hj/VXbBmk37uChJOrocpKg8jGHm59ZqLKG530/lwRAKnEFq1rOyM0SoZL94veMNbQ7sWlAACsLcU6C0geEttCay0RVXRaB6Tly5fjxx9/LHS8evXqmDBhAgMSkQkpqiutNCoBmLI3HOOiE+HrLc03Nik0SobYZON3JQGAoFQg8cI2pFw/BgCw9mgAt4Efw8pJarAaBjb3QNdvLphE91oeWZoCVx4mIClddzMN/drW4hpIVOloHZAeP34MLy+vQsdr166Nx49N61+WRJVZcV1pmtr61yNs/esRPCS2WNzPG8721jj1ylRyY1IkxSL+aACyY+8BAKq2fQfOXf0hstB/lxqQ26IysLkHNgdFmdzY5RyVoPNZbHXcquj0fkTmQOuAVL16ddy8ebPQZrU3btyAq6urruoionLI281dFx/eMfJMTNkbpoM76Ubafy8h4dQ6CNnpENs6wLXfbFR5vb3Bnj+kpSc61XPDV6f+a3LhKM/F+y91ej+OP6LKSOuANGzYMMyYMQNVq1ZFly5dAAB//vknZs6cCT8/P50XSETaK+tu7qZMyMmG7PxWpIafBADYvNYIbgPnw9LRsOuv/Rb+HL+FPzfoM7V144lcJ/fJG2fF8UdUGWkdkL744gs8evQIPXr0gKVl7stVKhU+/PBDfP311zovkIi0V9FWPVbIniH+2Epkv8iduu7Y/j04vTkSIgut/wojLQgAlgzw5vgjqpS0/tvF2toaBw4cwBdffIEbN27Azs4OTZs2Re3atfVRHxGVQUXqEkmL/BMJpzdAyM6A2M4Rbv3nwq5ua2OXVSmM8alT6rpYRBVVmf/51aBBgyJX1CYi/dJkBex2Xi7wkNgiVp5psuNkSqNSZCHx3Bak3ggEANjUbAK3AfNgWdXNyJXph1OV3AHmupx9Vl6eEjtjl0BkNBoFpDlz5uCLL76Avb095syZU+K1q1at0klhRJSfUiVgw/n72PZXFJIy/v0QdbKzwhifOpjWvb46KFmIRVgywBuTd5vO4GptKBKe4OXRAChePgIggqTjUEg6D4dIbGHs0nRKBMC/U204V7HBmrN3TS7MJqZz9WyqvDQKSOHh4VAoFOr/Lg5X1ybSj8DbMVh46FaRrQtJGQqsPnsP2y4/wop3m+brEpFUsTKpFglNpN4+B9n//QBBkQVxFSe4DZgHuzotjF2WXnw/vBV6NZGic8B5g4YjawtAozUkBf6dTpWXRgHpwoULRf43Eelf4O0YTNKgJSgpXYHJu8Pw/fBWuBeXgtVn7xmgOt1RZWdCdmYT0m6fBQDY1m4G1/7zYOlg+jOoXOytsLh/YzyIS9FoDzQRgAldvNC3mQeCHyQYfMahABE02X02OdO8wjWRLnEKCJEJy1vPSFMCcrcKUZlaX00psl9GI/5oABQJjwGRGBKfYZB0HGryXWp57StfD85tufvrXrxGAUkAsDkoCi1rOSMrR6XXGouiUGr4PwgbkKgS0yggvfvuuxrf8NChQ2UuhojyK8t6RuYUjgRBQNqtM5Cd+RFCThYsHFzgNmAebGs1M3ZpGpFKbLFkgDd6N/FA4O0YfH5M8zALAEuPR+Lb95vrqbry83K1N3YJREajUUCSSCTq/xYEAYcPH4ZEIkGbNm0AANevX0dSUpJWQYqISlfR1jN6lSo7A7LT3yMt8g8AgG2dlnDrPxcW9k5Gras0096qh/ruVfPNICzLti4CclcphwCTnHEoEgGjOtYxdhlERqNRQNq2bZv6vxcsWIChQ4di06ZNsLDIbf5WKpWYMmUKHB0d9VMlUSVVkdYzelV23EO8PBqAHNkzQCSG05sj4djhPYhEYmOXViqf16uhY71/t1Uq77Yu8WlZWDLAW6NxZrogsbOEnZVlqZsOf9TZC9aWpv9+EOmL1mOQfv75Z1y6dEkdjgDAwsICc+bMQadOnfDNN9/otECiyqx1bWeIRebVbVYSQRCQeiMQsrObAaUCFg6ucBv0MWxrNDZ2aRrxKGLbjfJu61K9qi3kGdnlLU1jY33q4g2pQ4ktXj29q+M//bwNVhORKdI6IOXk5OC///0v3njjjXzH//vf/0KlMvxgQ6KK7Hp0YoUJR6qsdCQErkf6fy8CAOzqtYVr31mwqCIp5ZWmQYSit90oTzeoh8QWrWs7o+s3hpkd7FTFCtO6vw4LsQgbR7bC0uOR+cKdo60lvn6nKfq38DRIPUSmTOuANGbMGIwbNw4PHjxAu3btAAAhISFYsWIFxowZo/MCiSqzijIGKSv2PuKPBiAnKQYQW8Cpiz8c271jFl1qQG6QyRuMXVB5ukEX9/PG9ehEg03zX/FuU3XA693EAz29paWuyk5UWWkdkL799ltIpVJ89913iImJAQB4eHhg/vz5mDt3rs4LJKrMzH0MkiAISAk7gcQLWwFlDiwcq6HawAWwea2hsUvT2LS36mF2zzeKDQ7tvFzgVMYFOZ3trQ0Sgp2rWGF5gUVEgdwV118dT0VE/9I6IInFYnz88cf4+OOPkZycDAAcnE2kJ+28XGBvbYE0jZY9Ni2qzFQknFqH9LuXAQB29TvAtc9MWNhVNXJl2vF5vVqJrSpnImPLvFp5XsuNvhS1DQ0RaaZMC0Xm5OTgjz/+wIMHDzB8+HAAwPPnz+Ho6AgHBwedFkhUma04FWmW4Sjr+R28PLYSSvkLQGwJ57fGoGrrgWa3HZFYlDtQvjjaLuRZUF63lpOdVb799YrTo2E1XHkoK/X/CUuxCDvGtkOHuq4MRkRlpPUAgOjoaDRt2hSDBg3C1KlT8fLlSwBAQEAA5s2bp/MCiSqr32/GYMvFR8YuQyuCICD56hHE7lkApfwFLCXukI5cCcc2g8wuHAG5swevRycWe748M9jyZsRZiEUY41NHo9d89GY93Py8F/o1dS/xug3DW8LndTeGI6Jy0DogzZw5E23atEFiYiLs7OzUxwcPHoxz587ptDiiykqpEvDxbzeNXYZWlBkpeHnoCySe/wlQ5aBKg07wGL0WNh4NjF1auZQ0RuhMZGyZ7/vqjLhp3evDqYpVide/Gqi+H9EGPwxvCecCr/GQ2GLTyFZFDiYnIu1o3cV28eJFXL58GdbW1vmO16lTB8+ePdNZYUSV2ZWHCUjNyjF2GRrLfPoP4o+thDLlJWBhCZfu4+HQsq9ZthoV5OZgU+RxpUrAkYjnZbrnbN8G+UKMhViEFe82LXJtorzfYMElBvo280SvJh6chUakJ1oHJJVKBaWycP/306dPUbWqeQ2+JDJVwQ8SjF2CRgRBheTQQ0j6cycgqGDp7IFqgxbC2r2esUvTnWLWoQqNkkGWpv0Cj1JHG0zr/nqh472beBS5NpG0hCUGOAuNSH+0Dkhvv/021qxZg82bNwMARCIRUlNTsWTJEvTt21fnBRJVTqa/OqQyXY74k6uQ+fA6AKBKo65w7TUVYpsqRq5Mt+JSs4o+Xobp+SIAnw9sXGwrD9cmIjIdZVoHqXfv3vD29kZmZiaGDx+Oe/fuwc3NDfv27dNHjUSVzsOXqcYuoUSZT27ndqmlyiCytIZzjwlwaN6rQnSpFSQrJiBpOz3fwcYS377frNTxQWwVIjINWgekmjVr4saNGzhw4ABu3LiB1NRUjBs3DiNGjMg3aJuIyub4jef4/fYLY5dRJEFQITn4IJIu7cntUnOpgWqDFsC6upexS9MbF3vrIo+383KBi70VZGmlT8+3tRIjbHFPbv5KZEa0CkgKhQINGzbEiRMnMGLECIwYMUJfdRFVSoG3YzB9X7ixyyiSMi0R8SdWIfNRbn32jd+Cy9tTILau2P8wkkqK/vksxCJ8OagJpuwt/f1a9X4LhiMiM6PVn1grKytkZhpubyilUonFixfDy8sLdnZ2qFevHr744gsIwr/jMwRBwGeffQYPDw/Y2dnB19cX9+7dM1iNRLqiVAmYuT/C2GUUKSP6BmK2zUDmo3CILG3g2ncW3PrPrfDhKG9qfXH6NvPExC4lt55N7OKFvs047Z7I3Gj9T5qpU6ciICAAOTn6n4IcEBCAjRs3YsOGDfjnn38QEBCAlStXYv369eprVq5ciXXr1mHTpk0ICQmBvb09evXqZdAgR6QLl+69RFaOythl5COolEi6tAdx+z+FMi0RVm61IPVfDYemvsYuzSAKTq0vyqK+3vhheKtCXXEu9lb4YXhLLOrrrc8SiUhPRMKrzTEayFsQ0sHBAU2bNoW9vX2+84cOHdJZcf3794e7uzu2bt2qPjZkyBDY2dlh9+7dEAQBnp6emDt3rnoVb7lcDnd3d2zfvh1+fn4aPSc5ORkSiQRyuZz7ypHRjNgSjL8eyIxdhlpOqgzxx79B1uNbAAD7pj3h0nMixFbmvYGuJorb3LUkSpXA2WdEBqbPz2+tB2k7OTlhyJAhOi2iOJ06dcLmzZtx9+5dNGjQADdu3MClS5ewatUqAEBUVBRiY2Ph6/vvv2YlEgnat2+P4ODgYgNSVlYWsrL+nZmSt+kukTGFPU4ydglqGVFhiD/xHVTpcoisbOHSayocGr9l7LL0qndjd7xevSo61nMt0x5mnH1GVLFoHZC2bdumjzqKtHDhQiQnJ6Nhw4awsLCAUqnEV199pR4cHhubu8y/u3v+fYnc3d3V54qyfPlyLF26VH+FE2npq5ORyFAYv3str0stOfggAAFW1eqg2qCFsHKtYezS9KYsrUVEVPFpHJBUKhW++eYbHDt2DNnZ2ejRoweWLFmi16n9v/zyC/bs2YO9e/eicePGiIiIwKxZs+Dp6Ql/f/8y33fRokWYM2eO+vvk5GTUrFlTFyUTaS07R4WfLkYZuwzkJMfndqk9/RsA4NCiN5y7j4fYquitNsyZraUYbzWsjpEdanPHeyIqksYB6auvvsLnn38OX19f2NnZYe3atYiLi8PPP/+st+Lmz5+PhQsXqrvKmjZtiujoaCxfvhz+/v6QSqUAgBcvXsDD499//b148QItWrQo9r42Njawsal4f+mTeVp06IbR183OeHAV8SdXQ5WRDJG1HVx7T4d9oy5Grkp/bK3E2DC8FYMRERVL41lsO3fuxA8//IDTp0/jyJEjOH78OPbs2QOVSn/dAunp6RCL85doYWGhfqaXlxekUinOnTunPp+cnIyQkBB07NhRb3UR6crvN5/jt7CybXiqC4IyB4kXfkbcr0uhykiGtXs9eIxeW6HDEQAkZeTgykPz2O+OiIxD4xakx48f59trzdfXFyKRCM+fP0eNGvoZnzBgwAB89dVXqFWrFho3bozw8HCsWrUKY8eOBZC7D9ysWbPw5Zdfon79+vDy8sLixYvh6emJd955Ry81EenK7zdjMFWDRQb1JSc5DvFHVyLr+X8BAFVbD4Bzt7EQWVoZrSZDuvwgHj6vuxm7DCIyURoHpJycHNja5p/ea2VlBYWi9GX2y2r9+vVYvHgxpkyZgri4OHh6emLixIn47LPP1Nd8/PHHSEtLw4QJE5CUlITOnTsjMDCwUK1EpuT3mzGYsjfMaM9PvxeChN9XQ5WZCpGNPdz6zESVNzoZrR5jeCJLN3YJRGTCNF4HSSwWo0+fPvnG7hw/fhzdu3fPtxaSLtdBMhSug0SGolQJWHfuLtadu2+UcUeCUoHEP7Yj5dpRAIC1R324DVwAKyepEaoxrk51XbB3ArviicyZSayDVNSssZEjR+q0GKKKLPB2DBYeuoWkdP21upZEkRSL+GMByI7J3YqnaptBcO42GiKLytGlVpC9jdarnBBRJaLx3xCGXP+IqKIJvB2DybvDjDZbLe3OX0g4tQ5CVhrEtg5w7TsbVeq3N1I1pqGdFxd1JKLi8Z9QRHqmVAlYejzSOF1qOdlIvLAVKWEnAQA2ng3hNuhjWDpWN0I1psW/Ux1jl0BEJowBiUjPQqNkiJEbfvNkReJzxB8NQPaLBwAAx/ZD4PTmKIgs+Me+Q10XWFtqvVc3EVUi/JuSSM9i5RkGf2Za5J9IOL0BQnYGxHaOcOs3B3b12hi8DlMkArBzbOXuXiSi0jEgEenZpXvxBnuWSpGFxHNbkHojEABgU6Mx3AbOh2VVrveTZ0IXL7YeEVGpGJCI9EipEnDqdoxBnqVIeIqXR1dA8fIRABEkHYdC0nk4RGILgzzfHPRv6oFFfb2NXQYRmQEGJCI92nD+PtIV+tuOJ0/q3xcgO/09BEUmxFWc4NZ/Luy8Wur9ueZEYmuBtcP4OyEizTAgEemBUiVgw/l7WH32nl6fo8rOhOzsJqTdOgsAsKnVDG4D5sHSwUWvz9U3GwsRspS6nfcX8F5zbk5LRBpjQCLSseM3nuOTw7eQkpmj1+dkv4xG/NEAKBIeAxBB4jMMkk4fVIgutfKEIxGQb0kFD4ktlgzwRu8mHuWui4gqDwYkIh0av/MqzkTG6fUZgiAg7dZZyM5sgpCTBQt7Z7gNmA/b2s30+lxz8J++DeHfyQvXoxMRl5KJ6lVt0c7LhS1HRKQ1BiQiHfnq5N96D0eq7AzI/u8HpP19AQBgW6cl3PrPhYW9k16fay6qO9rC2lKMjvW4SjYRlQ8DEpEOZOeosOXiI/0+Iy4KL48GIEf2FBCJ4fTmSDh2eA8iEaes56le1dbYJRBRBcGARKQDnxy6pbd7C4KA1BunITv7I6BUwMLBFW4D58O2ZhO9PdPciABIJbndaUREusCARFROSpWA32/pZ60jVVY6Ek5vQPo/QQAAu7pt4NpvNiyqSPTyPHOUN7poyQBvjjUiIp1hQCIqglIlIDRKptFA39AoGdIVSp3XkP3iAV4eXYGcxBhAbAGnLh/Csd1gdqkVIOUsNSLSAwYkogICb8fg82ORiE3+d4NZqaMtPh9Y9IdwXIpuN6IVBAGp4SchO/8ToMyBhWM1VBv4MWxea6TT55grqaMNhrWrhTpu9pylRkR6w4BE9IrA2zGYtDus0PHY5ExM2h2GTSNbFQpJbvY2Onu+KjMVCafWIf3uZQCA3evt4dp3FizsqursGebEQ2KLxf0awdnehtP2icigGJCI/kepErCwlMHWiw7dQk9vaf4PaB19VmfF3EX80QDkyF8AYks4dxuDqm0GQiSqPGGArUNEZCoYkIj+58rDBCSlK0q8JjFdgSsPE+Dzupv62NnI2HI9VxAEpFw7hsQ/tgGqHFhK3OE2aAFsPBqU677mwrmKFT4b0BhSRwYiIjIdDEhk9rQZUF2S4AcJGl+XF5C+OhmJbZejtX5WHmVGChJ+X4OM+yEAgCoNOsG1zwyIbR3KfE9zIgKw/N2mHGBNRCaHAYnMWuDtGCw9HokY+b8Dpcu699al+y81vFKAUiVgxr5wnCzH9P6sZ//g5bGVUCa/BCws4dL9Izi07GcyXWpdG7jhz7vxeru/cxUrhiMiMlkMSGS2Am/HYPLuMBTc1jRWnonJu8OwsYgB1UVRqgSsOXsXEU/kGj1XLBKh6ZJTSFeUbUNVQVAhOfQwkoJ2AiolLJ08crvUpK+X6X76Mqnr62hVyxmrz97T6X2d7KwwxqcOpnWvz+40IjJZDEhkdpQqAVceJmDhb7cKhSPg353c/3P4Nro3dIe1ZfHrBgXejsHCQ7dKHXuURywC1p2/r33R/6NMlyPh5GpkPLwGAKjS8E249p4OsU2VMt9T115dlbqdlwt2BEdDlpZd7vtO6VoPbzaoxnFGRGQWGJDIrBTVpVachLRsdFh+Fl8PLtyNo1QJ2HD+PlafvavV81VlazQCAGQ+uY34Y99AmZoAkaU1nHtMgEPzXibTpQYUvSr1l4OaYMrewksfaMO5ihXm9nqDwYiIzAaX5CWzkdelpkk4yiNLU2Dy7jAE3v53rFDg7Rj4rDindTgqK0FQQR78C17s+wTK1ARYutSAdNR3qNqit0mFIyC35ahg12TfZh6Y2MWrxNdVsbYo8fzyd5syHBGRWWELEpkFpUrA0uORRXapaWLp8Uj09JbiTGRskeOW9EWZloT4E98h81E4AMC+8VtweXsKxNZ2BqpAMx92rI0+TTyK7f5a1NcbzWs44dOjtyFL+7c70qmKFcZ08sK07q/jTGRsoRXIyzpgnojI2BiQyCyERsm0ajl6lQAgRp6JKw8SyhWytJUZfRPxx7+BMi0RIksbuPScBPumvibXagQAfZp4oGM91xKv6dvME72aeBS7pELvJh7o6S3VyZILRETGxoBEZkEX+50dvP6kzCFLG4JKCfnlA5Bf3g8IKli51oLboAWwrlZb78/W1qsDsjVhIRaVGKRKO09EZC4YkMgsVK9qW+57HIl4roNKSpaTKkP88W+R9fgmAMC+aU+49JwIsVX569eXVwdkExFRLgYkMgvtvFzgIbFFrDzTYF1k2sqICkf8ie+gSk+CyMoWLm9PgUOT7sYuq1gu9lZFzvAjIiLOYiMzYSEWYckAbwCF94Y1dtuHoFIiMWgX4n75DKr0JFhVqwMP/9UmHY4AYHH/xgxHRETFYEAik6ZUCQh+kICjEc8gsbPG98NbQirJ310lldhitq9xNnbNSY7Hi32fIDn4AAABDi16QzrqO1i51jRKPdqQOpputx8RkbGxi41MVnH7rC3u5w1ne+t8M6VO3NT/+KKCMh5cQ/zJVVBlJENkbQfXXtNg793V4HWUhYcWA7OJiCojBiQySSXtszZ1b+4+a4NavKY+7mZvY7DaBGUOki7uQnLIbwAAa/d6cBu0AFbOngaroTxE4MBsIqLSMCCRySlpUUgBuR/weQs/ArlrJP11X3+7zr8qJzkO8UdXIuv5fwEAVVv1h/NbYyGytDbI8zXRrYErJnatj4SULHx2PP/Cjly4kYhIMwxIZHJKWxQyb+HHDefvY//VxwZZ2wgA0u+FIOH31VBlpkJkYw/XPjNg/4aPQZ6tjZvPktULNPZpVvzCjkREVDwGJDI5mi4KabC91JQKJP6xHSnXjgIArD3qw23gAlg5SQ3yfG3J0hTY/lcURvt4ceFGIqIy4iw2Mjm6WBRSVxRJsYjd87E6HFVtMwjSEStNNhzl+eLkP+gccD7fJr1ERKQ5BiQyOXmLQhq7Iyj9zmXEbJ+J7Jh7ENs6oNq7i+HSYzxEFlZGrkwzsfJMTN4dxpBERFQGDEhkMvLWPDpx8zn82tZUD8jWFXtrC42uE3IUkJ3ZhJdHvoaQlQZrzzfgMXodqtRvr8Nq9C9vkPvS45FQqkx1/XEiItPEMUhkEopa88ipSm5LTVK6oriXacSlihW+frcpQqNk+PmvRyVeq0h8jvijAch+8QAA4Nh+CJzeHAWRhf7/qIgAnW+jkjegPTRKxrFIRERaYEAioytuzSP5/4LRbN8GUCiV2HDhQZnuv7h/7rR2iZ11iQEp7Z8gJASuh5CdAbGdI9z6zYZdvbZlemZZ6LONR9OB70RElIsBiYxKkzWP9l99jN6Nyz4oWiqxA5A7tslCBCgLPEylyELi+S1IjQgEANjU8IbbgI9h6ehW5meaGlMa+E5EZA4YkMholCoB2/+K0mjNo1/Dnmp9fxFy92nL21LDQiyCq4M14lKy1dcoEp7i5dEVULx8BEAEx45D4dR5OERizcYrmbqCvwMiItIMAxIZRVFjjkqSkpmj1f3zBncX3FLDt5E79oY+AQCk/n0BstPfQ1BkQlxFArf+82Dn1VKr55iy4n4HRERUOgYkMrjixhzpkqOdJQKGNCu0pcbi/o2x+697kJ35EWm3zgAAbGo1g9uAebB0qFitLFJuK0JEVGYMSGRQJY05Ko6dlRgZCpVWz5FnFN3iFHX/DlIPfIy0Zw8BiCDx8YOkk59ZdKk52VnhzfquOH4zttCMt7z2oVm+DVDHrQq3FSEiKicGJDKo0vZZK0qGQgWRCBC0bHJadOgWenpLYSEWQRAEbN++HVOnTkVGRgbsJK6o2mcO7Go31+6mOiAWAa8uS+RcxQqJ6Ypip/mP86kDX2+pOvD0a1a4e5KtRUREusWARAYVeKtsqzprG44AIDFdgfXn7uGjjp6YMmUKdu3aBQDo2bMndu3aBXuJK1p+8X9QFJzWpmcbhrWCs711vg1kz0TGFgo9HsWEnt5NPNDTW8pNaImI9EgkCGX56KlYkpOTIZFIIJfL4ejoaOxyKqzxO6/iTGScQZ+piIuC+I+1eBp1H2KxGMuWLcOiRYsgFucuIh94OwaTdocZrJ5xPnWweEDjIs8pVQJDDxGRFvT5+c0WJDKIr05GGjQcCYKA1BunkXhuM4ScbLhWk2LZ2s14rVErhEQlqsNH7yYeWD+sJabvCzdIXb7exa/nZCEWcbVrIiITwYBEepedo8JPl6IM9jxVVjoSTm9A+j9BAADbuq1Rpf8crLwhBm5EAABc7K0wuMVr8PWWwtHaMH8MPLgeERGR2WBAIr3bFfyoTGOIyiL7xQO8PLoCOYkxgEgMp67+cGw3GCJR/n2ZZWkKbP3rEbb+9Qi2VobZs5nrERERmQ8GJNK7aFm63p8hCAJSw3+H7PwWQJkDi6rVUG3Qx7B5rVGpr83UcgmBPLaWYnR7oxou3U9AalbxC1mKRcCGYS05w4yIyIwwIJHe1XapovG149/0wk8Xc7vjNG10UmWlIeHUOqTf+QsAYPd6e7j2nQULu6ralqqR5jUc8XHvRuhQ1xUWYlGpA703DGuFvs0YjoiIzIlh+haoUhvVsQ406VlaN7QF/tPPGxtHtoJUotnmqlkxdxGzbUZuOBJbwrn7R6j27qd6C0c9GlbD0Wlvwud1N3V3We8mHtg0shU8CtTsIbHFppEMR0RE5ojT/MFp/oaw/PdI/BhU/EDtnt7VseXDturvs3NU6LD8HGRp2UVeLwgCUq4dQ+If2wBVDiwk7qg28GPYeL6h89rz+Daqhp/82xV7ntP0iYgMi9P8yewt6usNANhyMSrfKtJiETCusxf+08873/XXoxOLDUfKjBQknFqLjHtXAABVGnSCa58ZENs66KV2KwsRVr/fHP1bvFbidZymT0RUcTAgkcEs6uuNuW83xK7gR4iWpaO2SxWM6lgH1paFe3rjUorejiTr2X/x8lgAlMkvAQtLOL81DlVb9YdIpLuWmnZ1nOFUxRr21hZ4t1UNdHqlO42IiCoHBiQyKGtLMca9WbfU66pXzT+eRxBUSA49jKSgnYBKCUsnD7gNWgAb6es6r/HByzSE/qcjQxERUSXGgEQmqXVtZ/Wmrsp0ORJ+X4OMB1cBAFUavgnX3tMhttF8dpw2EtKyERolY3cZEVElxoBEJul6dCJUApD59G/EH10JZWoCYGEFF9+JcGjeq8gutder2aN3EyksxWLsv/oEsclFd9NpojyvJSIi82fy0/yfPXuGkSNHwtXVFXZ2dmjatCmuXbumPi8IAj777DN4eHjAzs4Ovr6+uHfvnhErJl14mpAKefAveLF3EZSpCbB0eQ0eH65C1Ra9ix1vdP9lGjZceIA15+4BEDCgmRQS27L9G0CWmlWO6omIyNyZdEBKTEyEj48PrKyscOrUKURGRuK7776Ds7Oz+pqVK1di3bp12LRpE0JCQmBvb49evXohM5MtAOYqLi4OCycOzx1vJKhg3/gtePivgXV1L43vEZucheM3YyHPLH6F65I42VmV6XVERFQxmHQXW0BAAGrWrIlt27apj3l5/fshKQgC1qxZg08//RSDBg0CAOzcuRPu7u44cuQI/Pz8DF4zlc8ff/yB4cOHIyYmBiJLG7j0nAj7pj11OktNE7L0opcYICKiysGkW5COHTuGNm3a4P3330f16tXRsmVLbNmyRX0+KioKsbGx8PX1VR+TSCRo3749goODi71vVlYWkpOT832RcSmVSixbtgw9evRATEwMPOvUh/TDVXBo9rbBwxEAJGUoDP5MIiIyHSYdkB4+fIiNGzeifv36OH36NCZPnowZM2Zgx44dAIDY2FgAgLu7e77Xubu7q88VZfny5ZBIJOqvmjVr6u+HoFLFxsbi7bffxpIlS6BSqTBmzBjcjrgO2+q1jVaTCJziT0RUmZl0QFKpVGjVqhW+/vprtGzZEhMmTMD48eOxadOmct130aJFkMvl6q8nT57oqGLS1tmzZ9G8eXOcP38e9vb22LlzJ37++Wc4S6pi/JuajznSNU7xJyKq3Ew6IHl4eMDbO/8WFI0aNcLjx48BAFKpFADw4sWLfNe8ePFCfa4oNjY2cHR0zPdFhpWTk4NPP/0Ub7/9NuLi4tSzE0eNGqW+ZlFfb0zs4qXRRre65FzFCh3qMiAREVVmJj1I28fHB3fu3Ml37O7du6hdO7frxcvLC1KpFOfOnUOLFi0A5G5cFxISgsmTJxu6XNLQs2fPMHz4cAQFBQEAJkyYgDVr1sDOzq7QtUVtT+Je1QZfnfovYuTaz1QUAShtd+bl7zblKtpERJWcSQek2bNno1OnTvj6668xdOhQhIaGYvPmzdi8eTMAQCQSYdasWfjyyy9Rv359eHl5YfHixfD09MQ777xj3OKpSIGBgRg1ahTi4+Ph4OCALVu2lDrbsKjtSfo080RolAxxKZl4FJ+GfaGPEZv879pFHhJbDGzugWM3YvIFKanEFksG5LZKfn4sMt+CkB7/O9e7iYcuflQiIjJjIkEQSvsHtVGdOHECixYtwr179+Dl5YU5c+Zg/Pjx6vOCIGDJkiXYvHkzkpKS0LlzZ/zwww9o0KCBxs9ITk6GRCKBXC5nd5ueKBQKLF68GAEBAQCAFi1a4JdffkH9+vV1cn+lSlAHpupVbdHOywUWYlGxx0t6DRERmQd9fn6bfEAyBAYk/Xr8+DGGDRuGy5cvAwCmTp2Kb7/9Fra2tqW8koiIqHj6/Pw26S42Mn/Hjx/H6NGjIZPJ4OjoiK1bt+K9994zdllEREQlMulZbGS+srOzMXfuXAwcOBAymQxt2rRBeHg4wxEREZkFtiCRzkVFRcHPzw+hoaEAgFmzZiEgIADW1tZGroyIiEgzDEikU4cOHcLYsWMhl8vh5OSE7du3q/fJIyIiMhfsYiOdyMrKwvTp0zFkyBDI5XJ06NABERERDEdERGSWGJCo3O7fv49OnTphw4YNAID58+cjKChIvaAnERGRuWEXG5XLL7/8go8++ggpKSlwdXXFjh070K9fP2OXRUREVC5sQaIyycjIwOTJk/HBBx8gJSUFnTt3RkREBMMRERFVCAxIpLU7d+6gQ4cO2LRpE0QiET755BNcuHABNWrUMHZpREREOsEuNtLKnj17MHHiRKSlpaFatWrYvXs33n77bWOXRUREpFNsQSKNpKen46OPPsLIkSORlpaGbt26ISIiguGIiIgqJAYkKlVkZCTatWuHrVu3QiQSYcmSJTh79iw8PT2NXRoREZFesIuNSrR9+3ZMnToV6enpkEql2LNnD7p3727ssoiIiPSKLUhUpNTUVPj7+2PMmDFIT0+Hr68vIiIiGI6IiKhSYECiQm7duoW2bdti586dEIvF+PLLLxEYGAh3d3djl0ZERGQQ7GIjNUEQ8NNPP2HGjBnIzMyEp6cn9u3bhy5duhi7NCIiIoNiQCIAQEpKCiZOnIh9+/YBAHr37o2dO3eiWrVqRq6MiIjI8NjFRggPD0erVq2wb98+WFhYYMWKFTh58iTDERERVVpsQarEBEHAxo0bMWfOHGRlZaFmzZrYv38/OnXqZOzSiIiIjIoBqZKSy+X46KOP8OuvvwIABgwYgG3btsHV1dXIlRERERkfu9gqoWvXrqFVq1b49ddfYWlpiVWrVuHo0aMMR0RERP/DFqRKRBAErFu3DvPnz4dCoUCdOnVw4MABtGvXztilERERmRQGpEoiMTERY8eOxZEjRwAAgwcPxs8//wwnJyej1kVERGSK2MVWCVy5cgUtW7bEkSNHYG1tjfXr1+O3335jOCIiIioGA1IFplKp8O233+LNN99EdHQ06tWrh8uXL2PatGkQiUTGLo+IiMhksYutgkpISIC/vz9OnjwJABg6dCg2b94MiURi5MqIiIhMH1uQKqBLly6hRYsWOHnyJGxsbLBx40bs37+f4YiIiEhDDEgViEqlwvLly9GtWzc8ffoUDRo0QEhICCZNmsQuNSIiIi2wi62CiIuLw4cffojTp08DAEaMGIGNGzeiatWqRq6MiIjI/DAgVQB//vknhg0bhpiYGNjZ2WHDhg0YM2YMW42IiIjKiF1sZkypVGLZsmXo3r07YmJi0KhRI4SGhmLs2LEMR0REROXAFiQzFRsbi5EjR+LcuXMAgNGjR2PDhg2wt7c3cmVERETmjwHJDJ09exYjR47EixcvUKVKFWzcuBEffvihscsiIiKqMNjFZkZycnKwePFivP3223jx4gWaNGmC69evMxwRERHpGFuQzMSzZ88wfPhwBAUFAQDGjx+PtWvXws7OzsiVERERVTwMSGYgMDAQo0aNQnx8PBwcHLB582YMGzbM2GURERFVWOxiM2EKhQKLFi1Cnz59EB8fjxYtWuD69esMR0RERHrGFiQT9eTJE/j5+eHy5csAgClTpuC7776Dra2tkSsjIiKq+BiQTNCJEyfg7+8PmUwGR0dHbN26Fe+9956xyyIiIqo02MVmQrKzszF37lwMGDAAMpkMbdq0QXh4OMMRERGRgbEFyUQ8evQIfn5+CAkJAQDMnDkTAQEBsLGxMXJlRERElQ8Dkgk4cuQIxowZg6SkJDg5OWHbtm145513jF0WERFRpcUuNiPKysrCzJkzMXjwYCQlJaF9+/aIiIhgOCIiIjIyBiQjefDgAXx8fLBu3ToAwLx583Dx4kXUrl3byJURERERu9iM4ODBg/joo4+QnJwMFxcX7Ny5E/369TN2WURERPQ/bEEyoMzMTEyZMgVDhw5FcnIyfHx8EBERwXBERERkYhiQDOTu3bvo0KEDNm7cCABYtGgR/vjjD9SsWdPIlREREVFB7GIzgL1792LixIlITU1FtWrVsGvXLvTq1cvYZREREVEx2IKkR+np6Rg/fjxGjBiB1NRUdOvWDREREQxHREREJo4BSU/++ecftG/fHj/99BNEIhE+++wznD17Fp6ensYujYiIiErBLjY92LFjB6ZMmYL09HS4u7tjz5496NGjh7HLIiIiIg2xBUmH0tLSMHr0aIwePRrp6eno0aMHIiIiGI6IiIjMDAOSjty6dQtt2rTBjh07IBaL8cUXX+D06dOQSqXGLo2IiIi0xC62chIEAVu3bsX06dORmZkJT09P7N27F127djV2aURERFRGDEjlkJKSgkmTJmHv3r0AgN69e2Pnzp2oVq2akSsjIiKi8mAXWxlFRESgdevW2Lt3LywsLLBixQqcPHmS4YiIiKgCYAuSlgRBwKZNmzB79mxkZWWhRo0a2L9/P3x8fIxdGhEREekIA5IW5HI5xo8fj4MHDwIA+vfvj+3bt8PV1dXIlREREZEusYtNQ9euXUOrVq1w8OBBWFpa4rvvvsOxY8cYjoiIiCogtiCVQhAErF+/HvPmzYNCoUDt2rVx4MABtG/f3tilERERkZ4wIJUgMTER48aNw+HDhwEA77zzDn7++Wc4OzsbuTIiIiLSJ3axFSMkJAQtW7bE4cOHYW1tjXXr1uHQoUMMR0RERJUAA1IBgiDgu+++Q+fOnREdHY26devi8uXLmD59OkQikbHLIyIiIgMwq4C0YsUKiEQizJo1S30sMzMTU6dOhaurKxwcHDBkyBC8ePGiTPeXyWQYOHAg5s2bh5ycHLz//vsICwtD69atdfQTEBERkTkwm4B09epV/Pjjj2jWrFm+47Nnz8bx48dx8OBB/Pnnn3j+/DnefffdMj3Dx8cHJ06cgI2NDTZu3IgDBw5AIpHoonwiIiIyI2YRkFJTUzFixAhs2bIl3xgguVyOrVu3YtWqVejevTtat26Nbdu24fLly7hy5YrWz3n+/Dnq16+PK1euYNKkSexSIyIiqqTMYhbb1KlT0a9fP/j6+uLLL79UH79+/ToUCgV8fX3Vxxo2bIhatWohODgYHTp0KPJ+WVlZyMrKUn8vl8sB5M5S27BhA6pWrYrk5GQ9/TRERESkC3mf1YIg6PzeJh+Q9u/fj7CwMFy9erXQudjYWFhbW8PJySnfcXd3d8TGxhZ7z+XLl2Pp0qWFjh85cgRHjhwpb8lERERkQAkJCTofEmPSAenJkyeYOXMmzpw5A1tbW53dd9GiRZgzZ476+6SkJNSuXRuPHz/mmCMjS05ORs2aNfHkyRM4Ojoau5xKje+F6eB7YTr4XpgWuVyOWrVqwcXFRef3NumAdP36dcTFxaFVq1bqY0qlEkFBQdiwYQNOnz6N7OxsJCUl5WtFevHiBaRSabH3tbGxgY2NTaHjEomE/8ObCEdHR74XJoLvhenge2E6+F6YFrFY90OqTTog9ejRA7du3cp3bMyYMWjYsCEWLFiAmjVrwsrKCufOncOQIUMAAHfu3MHjx4/RsWNHY5RMREREFYBJB6SqVauiSZMm+Y7Z29vD1dVVfXzcuHGYM2cOXFxc4OjoiOnTp6Njx47FDtAmIiIiKo1JByRNrF69GmKxGEOGDEFWVhZ69eqFH374Qat72NjYYMmSJUV2u5Fh8b0wHXwvTAffC9PB98K06PP9EAn6mBtHREREZMbMYqFIIiIiIkNiQCIiIiIqgAGJiIiIqAAGJCIiIqICKm1AWrFiBUQiEWbNmqU+lpmZialTp8LV1RUODg4YMmQIXrx4YbwiK7Bnz55h5MiRcHV1hZ2dHZo2bYpr166pzwuCgM8++wweHh6ws7ODr68v7t27Z8SKKyalUonFixfDy8sLdnZ2qFevHr744ot8+xrxvdCfoKAgDBgwAJ6enhCJRIW2OtLkdy+TyTBixAg4OjrCyckJ48aNQ2pqqgF/ioqhpPdCoVBgwYIFaNq0Kezt7eHp6YkPP/wQz58/z3cPvhe6Udqfi1flbSy/Zs2afMd18V5UyoB09epV/Pjjj2jWrFm+47Nnz8bx48dx8OBB/Pnnn3j+/DneffddI1VZcSUmJsLHxwdWVlY4deoUIiMj8d1338HZ2Vl9zcqVK7Fu3Tps2rQJISEhsLe3R69evZCZmWnEyiuegIAAbNy4ERs2bMA///yDgIAArFy5EuvXr1dfw/dCf9LS0tC8eXN8//33RZ7X5Hc/YsQI/P333zhz5gxOnDiBoKAgTJgwwVA/QoVR0nuRnp6OsLAwLF68GGFhYTh06BDu3LmDgQMH5ruO74VulPbnIs/hw4dx5coVeHp6Fjqnk/dCqGRSUlKE+vXrC2fOnBG6du0qzJw5UxAEQUhKShKsrKyEgwcPqq/9559/BABCcHCwkaqtmBYsWCB07ty52PMqlUqQSqXCN998oz6WlJQk2NjYCPv27TNEiZVGv379hLFjx+Y79u677wojRowQBIHvhSEBEA4fPqz+XpPffWRkpABAuHr1qvqaU6dOCSKRSHj27JnBaq9oCr4XRQkNDRUACNHR0YIg8L3Ql+Lei6dPnwqvvfaacPv2baF27drC6tWr1ed09V5UuhakqVOnol+/fvD19c13/Pr161AoFPmON2zYELVq1UJwcLChy6zQjh07hjZt2uD9999H9erV0bJlS2zZskV9PioqCrGxsfneC4lEgvbt2/O90LFOnTrh3LlzuHv3LgDgxo0buHTpEvr06QOA74UxafK7Dw4OhpOTE9q0aaO+xtfXF2KxGCEhIQavuTKRy+UQiUTqfUD5XhiOSqXCqFGjMH/+fDRu3LjQeV29F2a/krY29u/fj7CwMFy9erXQudjYWFhbW+fb9BYA3N3dERsba6AKK4eHDx9i48aNmDNnDj755BNcvXoVM2bMgLW1Nfz9/dW/b3d393yv43uhewsXLkRycjIaNmwICwsLKJVKfPXVVxgxYgQA8L0wIk1+97GxsahevXq+85aWlnBxceH7o0eZmZlYsGABhg0bpt6wlu+F4QQEBMDS0hIzZswo8ryu3otKE5CePHmCmTNn4syZM7C1tTV2OZWaSqVCmzZt8PXXXwMAWrZsidu3b2PTpk3w9/c3cnWVyy+//II9e/Zg7969aNy4MSIiIjBr1ix4enryvSAqgkKhwNChQyEIAjZu3Gjsciqd69evY+3atQgLC4NIJNLrsypNF9v169cRFxeHVq1awdLSEpaWlvjzzz+xbt06WFpawt3dHdnZ2UhKSsr3uhcvXkAqlRqn6ArKw8MD3t7e+Y41atQIjx8/BgD177vgDEK+F7o3f/58LFy4EH5+fmjatClGjRqF2bNnY/ny5QD4XhiTJr97qVSKuLi4fOdzcnIgk8n4/uhBXjiKjo7GmTNn1K1HAN8LQ7l48SLi4uJQq1Yt9Wd5dHQ05s6dizp16gDQ3XtRaQJSjx49cOvWLURERKi/2rRpgxEjRqj/28rKCufOnVO/5s6dO3j8+DE6duxoxMorHh8fH9y5cyffsbt376J27doAAC8vL0il0nzvRXJyMkJCQvhe6Fh6ejrE4vx/DVhYWEClUgHge2FMmvzuO3bsiKSkJFy/fl19zfnz56FSqdC+fXuD11yR5YWje/fu4ezZs3B1dc13nu+FYYwaNQo3b97M91nu6emJ+fPn4/Tp0wB0+F6UY3C52Xt1FpsgCMKkSZOEWrVqCefPnxeuXbsmdOzYUejYsaPxCqygQkNDBUtLS+Grr74S7t27J+zZs0eoUqWKsHv3bvU1K1asEJycnISjR48KN2/eFAYNGiR4eXkJGRkZRqy84vH39xdee+014cSJE0JUVJRw6NAhwc3NTfj444/V1/C90J+UlBQhPDxcCA8PFwAIq1atEsLDw9UzozT53ffu3Vto2bKlEBISIly6dEmoX7++MGzYMGP9SGarpPciOztbGDhwoFCjRg0hIiJCiImJUX9lZWWp78H3QjdK+3NRUMFZbIKgm/eCAemVgJSRkSFMmTJFcHZ2FqpUqSIMHjxYiImJMV6BFdjx48eFJk2aCDY2NkLDhg2FzZs35zuvUqmExYsXC+7u7oKNjY3Qo0cP4c6dO0aqtuJKTk4WZs6cKdSqVUuwtbUV6tatK/znP//J95c+3wv9uXDhggCg0Je/v78gCJr97hMSEoRhw4YJDg4OgqOjozBmzBghJSXFCD+NeSvpvYiKiiryHADhwoUL6nvwvdCN0v5cFFRUQNLFeyEShFeWzCUiIiKiyjMGiYiIiEhTDEhEREREBTAgERERERXAgERERERUAAMSERERUQEMSEREREQFMCARERERFcCARESVmkgkwpEjR4xdBhGZGAYkIjKI4OBgWFhYoF+/flq/tk6dOlizZo3ui9LA6NGjIRKJIBKJYGVlBXd3d/Ts2RM///yzes+6P/74Q31NcV9//PGHUeonorJhQCIig9i6dSumT5+OoKAgPH/+3NjlaKV3796IiYnBo0ePcOrUKbz11luYOXMm+vfvj5ycHHTq1AkxMTHqr6FDh6pfk/fVqVMnY/8YRKQFBiQi0rvU1FQcOHAAkydPRr9+/bB9+/ZC1xw/fhxt27aFra0t3NzcMHjwYABAt27dEB0djdmzZ6tbYwDg888/R4sWLfLdY82aNahTp476+6tXr6Jnz55wc3ODRCJB165dERYWpnX9NjY2kEqleO2119CqVSt88sknOHr0KE6dOoXt27fD2toaUqlU/WVnZ6d+Td6XtbW11s8lIuNhQCIivfvll1/QsGFDvPHGGxg5ciR+/vlnvLoN5MmTJzF48GD07dsX4eHhOHfuHNq1awcAOHToEGrUqIFly5apW2M0lZKSAn9/f1y6dAlXrlxB/fr10bdvX6SkpJT7Z+revTuaN2+OQ4cOlfteRGR6LI1dABFVfFu3bsXIkSMB5HZXyeVy/Pnnn+jWrRsA4KuvvoKfnx+WLl2qfk3z5s0BAC4uLrCwsEDVqlUhlUq1em737t3zfb9582Y4OTnhzz//RP/+/cvxE+Vq2LAhbt68We77EJHpYQsSEenVnTt3EBoaimHDhgEALC0t8cEHH2Dr1q3qayIiItCjRw+dP/vFixcYP3486tevD4lEAkdHR6SmpuLx48c6ub8gCOouPyKqWNiCRER6tXXrVuTk5MDT01N9TBAE2NjYYMOGDZBIJLCzs9P6vmKxOF83HQAoFIp83/v7+yMhIQFr165F7dq1YWNjg44dOyI7O7tsP0wB//zzD7y8vHRyLyIyLWxBIiK9ycnJwc6dO/Hdd98hIiJC/XXjxg14enpi3759AIBmzZrh3Llzxd7H2toaSqUy37Fq1aohNjY2X0iKiIjId81ff/2FGTNmoG/fvmjcuDFsbGwQHx+vk5/t/PnzuHXrFoYMGaKT+xGRaWELEhHpzYkTJ5CYmIhx48ZBIpHkOzdkyBBs3boVkyZNwpIlS9CjRw/Uq1cPfn5+yMnJwe+//44FCxYAyF0HKSgoCH5+frCxsYGbmxu6deuGly9fYuXKlXjvvfcQGBiIU6dOwdHRUf2M+vXrY9euXWjTpg2Sk5Mxf/78MrVWZWVlITY2FkqlEi9evEBgYCCWL1+O/v3748MPPyzfL4mITBJbkIhIb7Zu3QpfX99C4QjIDUjXrl3DzZs30a1bNxw8eBDHjh1DixYt0L17d4SGhqqvXbZsGR49eoR69eqhWrVqAIBGjRrhhx9+wPfff4/mzZsjNDQU8+bNK/T8xMREtGrVCqNGjcKMGTNQvXp1rX+OwMBAeHh4oE6dOujduzcuXLiAdevW4ejRo7CwsND6fkRk+kRCwU58IiIiokqOLUhEREREBTAgERERERXAgERERERUAAMSERERUQEMSEREREQFMCARERERFcCARERERFQAAxIRERFRAQxIRERERAUwIBEREREVwIBEREREVAADEhEREVEB/w8dePEK1i/wugAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.scatter(test_df['DT'], test_df['TEST_DT'])\n", "plt.xlim(40, 140)\n", "plt.ylim(40, 140)\n", "plt.ylabel('Predicted DT')\n", "plt.xlabel('Actual DT')\n", "plt.plot([40,140], [40,140], 'black') #1 to 1 line\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 30, "id": "ccb99db5", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(12, 4))\n", "plt.plot(test_df['DEPTH'], test_df['DT'], label='Actual DT')\n", "plt.plot(test_df['DEPTH'], test_df['TEST_DT'], label='Predicted DT')\n", "\n", "plt.xlabel('Depth (m)', fontsize=14, fontweight='bold')\n", "plt.ylabel('DT', fontsize=14,fontweight='bold')\n", "\n", "plt.ylim(40, 140)\n", "plt.legend(fontsize=14)\n", "plt.grid()"]}, {"cell_type": "code", "execution_count": null, "id": "1859d8ec", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}