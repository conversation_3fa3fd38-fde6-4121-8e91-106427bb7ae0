2015-10-19 15:50:23,654 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 15:50:23,825 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 15:50:23,825 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-19 15:50:23,841 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-19 15:50:23,841 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445182159119_0011, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@11628d93)
2015-10-19 15:50:24,404 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-19 15:50:25,638 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445182159119_0011
2015-10-19 15:50:27,201 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-19 15:50:29,185 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-19 15:50:29,654 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@23ce7dc3
2015-10-19 15:50:34,654 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/pageinput2.txt:671088640+134217728
2015-10-19 15:50:35,435 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-19 15:50:35,435 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-19 15:50:35,435 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-19 15:50:35,435 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-19 15:50:35,435 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-19 15:50:35,670 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-19 15:51:01,109 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 15:51:01,109 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 48241717; bufvoid = 104857600
2015-10-19 15:51:01,109 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 17303312(69213248); length = 8911085/6553600
2015-10-19 15:51:01,109 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 57310469 kvi 14327612(57310448)
2015-10-19 15:52:01,284 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-19 15:52:01,753 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 57310469 kv 14327612(57310448) kvi 12124056(48496224)
2015-10-19 15:52:10,988 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 15:52:10,988 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 57310469; bufend = 677160; bufvoid = 104857600
2015-10-19 15:52:10,988 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14327612(57310448); kvend = 5412172(21648688); length = 8915441/6553600
2015-10-19 15:52:10,988 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 9745912 kvi 2436472(9745888)
2015-10-19 15:53:09,866 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-19 15:53:10,038 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 9745912 kv 2436472(9745888) kvi 243380(973520)
2015-10-19 15:53:18,960 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 15:53:18,960 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 9745912; bufend = 58001423; bufvoid = 104857600
2015-10-19 15:53:18,960 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 2436472(9745888); kvend = 19743236(78972944); length = 8907637/6553600
2015-10-19 15:53:18,960 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 67070175 kvi 16767536(67070144)
2015-10-19 15:54:16,979 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-19 15:54:17,166 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 67070175 kv 16767536(67070144) kvi 14572400(58289600)
2015-10-19 15:54:26,010 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 15:54:26,010 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 67070175; bufend = 10445315; bufvoid = 104857600
2015-10-19 15:54:26,010 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 16767536(67070144); kvend = 7854204(31416816); length = 8913333/6553600
2015-10-19 15:54:26,010 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 19514051 kvi 4878508(19514032)
