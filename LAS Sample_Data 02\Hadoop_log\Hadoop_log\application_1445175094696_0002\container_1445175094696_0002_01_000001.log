2015-10-18 21:33:02,464 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445175094696_0002_000001
2015-10-18 21:33:02,886 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-18 21:33:02,886 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 2 cluster_timestamp: 1445175094696 } attemptId: 1 } keyId: -2054027300)
2015-10-18 21:33:03,152 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-18 21:33:04,543 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-18 21:33:04,668 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-18 21:33:04,714 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-18 21:33:04,730 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-18 21:33:04,730 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-18 21:33:04,730 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-18 21:33:04,730 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-18 21:33:04,746 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-18 21:33:04,746 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-18 21:33:04,746 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-18 21:33:04,824 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 21:33:04,871 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 21:33:04,918 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 21:33:04,933 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-18 21:33:05,027 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-18 21:33:05,433 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-18 21:33:05,543 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-18 21:33:05,543 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-18 21:33:05,558 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445175094696_0002 to jobTokenSecretManager
2015-10-18 21:33:06,355 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445175094696_0002 because: not enabled; too many maps; too much input;
2015-10-18 21:33:06,386 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445175094696_0002 = 1313861632. Number of splits = 10
2015-10-18 21:33:06,386 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445175094696_0002 = 1
2015-10-18 21:33:06,386 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445175094696_0002Job Transitioned from NEW to INITED
2015-10-18 21:33:06,386 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445175094696_0002.
2015-10-18 21:33:06,449 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-18 21:33:06,480 INFO [Socket Reader #1 for port 20314] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 20314
2015-10-18 21:33:06,527 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-18 21:33:06,527 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-18 21:33:06,527 INFO [IPC Server listener on 20314] org.apache.hadoop.ipc.Server: IPC Server listener on 20314: starting
2015-10-18 21:33:06,527 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MSRA-SA-39.fareast.corp.microsoft.com/**************:20314
2015-10-18 21:33:06,636 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-18 21:33:06,652 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-18 21:33:06,668 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-18 21:33:06,683 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-18 21:33:06,683 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-18 21:33:06,683 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-18 21:33:06,683 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-18 21:33:06,699 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 20321
2015-10-18 21:33:06,699 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-18 21:33:06,746 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\2\Jetty_0_0_0_0_20321_mapreduce____pkr7ut\webapp
2015-10-18 21:33:07,027 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:20321
2015-10-18 21:33:07,027 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 20321
2015-10-18 21:33:07,496 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-18 21:33:07,496 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445175094696_0002
2015-10-18 21:33:07,496 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-18 21:33:07,496 INFO [Socket Reader #1 for port 20324] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 20324
2015-10-18 21:33:07,511 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-18 21:33:07,511 INFO [IPC Server listener on 20324] org.apache.hadoop.ipc.Server: IPC Server listener on 20324: starting
2015-10-18 21:33:07,527 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-18 21:33:07,543 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-18 21:33:07,543 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-18 21:33:07,590 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-18 21:33:07,683 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-18 21:33:07,683 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-18 21:33:07,683 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-18 21:33:07,683 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-18 21:33:07,699 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445175094696_0002Job Transitioned from INITED to SETUP
2015-10-18 21:33:07,699 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-18 21:33:07,715 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445175094696_0002Job Transitioned from SETUP to RUNNING
2015-10-18 21:33:07,746 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:07,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:07,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:07,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:07,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:07,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:07,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:07,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:07,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:07,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:07,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:07,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:07,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:07,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:07,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:07,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:07,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:07,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:07,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:07,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:07,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:07,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:07,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:07,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:07,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:07,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:07,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:07,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:07,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:07,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:07,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:07,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:07,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:07,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:07,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:07,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:07,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:07,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:07,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:07,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:07,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:07,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:07,777 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-18 21:33:07,793 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-18 21:33:07,824 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445175094696_0002, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445175094696_0002/job_1445175094696_0002_1.jhist
2015-10-18 21:33:08,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-18 21:33:08,746 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0002: ask=6 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:14336, vCores:-13> knownNMs=4
2015-10-18 21:33:08,746 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:14336, vCores:-13>
2015-10-18 21:33:08,746 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:09,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 4
2015-10-18 21:33:09,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0002_01_000002 to attempt_1445175094696_0002_m_000002_0
2015-10-18 21:33:09,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0002_01_000003 to attempt_1445175094696_0002_m_000004_0
2015-10-18 21:33:09,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0002_01_000004 to attempt_1445175094696_0002_m_000006_0
2015-10-18 21:33:09,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0002_01_000005 to attempt_1445175094696_0002_m_000000_0
2015-10-18 21:33:09,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-23>
2015-10-18 21:33:09,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:09,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:6 ScheduledReds:0 AssignedMaps:4 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:4 ContRel:0 HostLocal:4 RackLocal:0
2015-10-18 21:33:09,874 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:09,905 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445175094696_0002/job.jar
2015-10-18 21:33:09,905 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445175094696_0002/job.xml
2015-10-18 21:33:09,905 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-18 21:33:09,905 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-18 21:33:09,905 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-18 21:33:09,952 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:33:09,968 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:09,968 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:33:09,968 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:09,968 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:33:09,968 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:09,968 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:33:09,968 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0002_01_000002 taskAttempt attempt_1445175094696_0002_m_000002_0
2015-10-18 21:33:09,968 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0002_01_000003 taskAttempt attempt_1445175094696_0002_m_000004_0
2015-10-18 21:33:09,968 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0002_01_000004 taskAttempt attempt_1445175094696_0002_m_000006_0
2015-10-18 21:33:09,968 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0002_01_000005 taskAttempt attempt_1445175094696_0002_m_000000_0
2015-10-18 21:33:09,983 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0002_m_000002_0
2015-10-18 21:33:09,983 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0002_m_000000_0
2015-10-18 21:33:09,983 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0002_m_000006_0
2015-10-18 21:33:09,983 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0002_m_000004_0
2015-10-18 21:33:09,983 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 21:33:10,015 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 21:33:10,015 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 21:33:10,015 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-18 21:33:10,124 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0002_m_000000_0 : 13562
2015-10-18 21:33:10,124 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0002_m_000002_0 : 13562
2015-10-18 21:33:10,124 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0002_m_000006_0 : 13562
2015-10-18 21:33:10,140 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0002_m_000000_0] using containerId: [container_1445175094696_0002_01_000005 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:52368]
2015-10-18 21:33:10,140 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0002_m_000004_0 : 13562
2015-10-18 21:33:10,140 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:33:10,140 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0002_m_000006_0] using containerId: [container_1445175094696_0002_01_000004 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 21:33:10,140 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:33:10,140 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0002_m_000002_0] using containerId: [container_1445175094696_0002_01_000002 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 21:33:10,140 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:33:10,140 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0002_m_000004_0] using containerId: [container_1445175094696_0002_01_000003 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 21:33:10,140 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:33:10,140 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0002_m_000000
2015-10-18 21:33:10,140 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:33:10,140 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0002_m_000006
2015-10-18 21:33:10,140 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:33:10,140 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0002_m_000002
2015-10-18 21:33:10,140 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:33:10,140 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0002_m_000004
2015-10-18 21:33:10,140 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:33:10,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0002: ask=5 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:4096, vCores:-23> knownNMs=4
2015-10-18 21:33:10,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-23>
2015-10-18 21:33:10,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:11,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-23>
2015-10-18 21:33:11,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:12,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-24>
2015-10-18 21:33:12,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:13,771 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:33:13,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 21:33:13,786 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:13,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0002_01_000006 to attempt_1445175094696_0002_m_000001_0
2015-10-18 21:33:13,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-18 21:33:13,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:13,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:5 ContRel:0 HostLocal:4 RackLocal:1
2015-10-18 21:33:13,786 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:13,802 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:33:13,802 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0002_01_000006 taskAttempt attempt_1445175094696_0002_m_000001_0
2015-10-18 21:33:13,802 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0002_m_000001_0
2015-10-18 21:33:13,802 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-18 21:33:13,818 INFO [IPC Server handler 28 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0002_m_000005 asked for a task
2015-10-18 21:33:13,818 INFO [IPC Server handler 28 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0002_m_000005 given task: attempt_1445175094696_0002_m_000000_0
2015-10-18 21:33:14,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-25> knownNMs=4
2015-10-18 21:33:14,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-18 21:33:14,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:14,849 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0002_m_000001_0 : 13562
2015-10-18 21:33:14,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0002_m_000001_0] using containerId: [container_1445175094696_0002_01_000006 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:52368]
2015-10-18 21:33:14,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:33:14,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0002_m_000001
2015-10-18 21:33:14,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:33:15,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-18 21:33:15,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:16,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:16,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:17,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:17,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:18,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:18,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:19,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:19,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:20,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:20,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:21,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:21,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:22,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:22,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:22,896 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:33:23,037 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0002_m_000006 asked for a task
2015-10-18 21:33:23,037 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0002_m_000006 given task: attempt_1445175094696_0002_m_000001_0
2015-10-18 21:33:23,162 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:33:23,256 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0002_m_000004 asked for a task
2015-10-18 21:33:23,256 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0002_m_000004 given task: attempt_1445175094696_0002_m_000006_0
2015-10-18 21:33:23,474 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:33:23,521 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:33:23,584 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0002_m_000003 asked for a task
2015-10-18 21:33:23,584 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0002_m_000003 given task: attempt_1445175094696_0002_m_000004_0
2015-10-18 21:33:23,584 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0002_m_000002 asked for a task
2015-10-18 21:33:23,584 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0002_m_000002 given task: attempt_1445175094696_0002_m_000002_0
2015-10-18 21:33:23,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:23,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:24,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:24,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:25,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:25,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:26,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:26,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:27,115 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.013024487
2015-10-18 21:33:27,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:27,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:28,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:28,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:29,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:29,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:30,600 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.054467633
2015-10-18 21:33:30,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:30,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:31,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:31,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:32,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:32,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:33,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:33,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:34,553 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.11933475
2015-10-18 21:33:34,615 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.04090628
2015-10-18 21:33:34,647 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.04067718
2015-10-18 21:33:34,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:34,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:34,990 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.06226415
2015-10-18 21:33:35,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:35,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:36,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:36,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:37,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:37,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:37,850 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.13101934
2015-10-18 21:33:38,444 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.10151355
2015-10-18 21:33:38,491 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.09717255
2015-10-18 21:33:38,756 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.12107881
2015-10-18 21:33:38,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:38,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:39,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:39,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:40,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:40,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:41,022 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.13101934
2015-10-18 21:33:41,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:41,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:41,959 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.131014
2015-10-18 21:33:42,022 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.13104042
2015-10-18 21:33:42,303 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.13101135
2015-10-18 21:33:42,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:42,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:43,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:43,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:44,366 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.13101934
2015-10-18 21:33:44,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:44,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:45,335 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.131014
2015-10-18 21:33:45,460 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.13104042
2015-10-18 21:33:45,616 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.0863023
2015-10-18 21:33:45,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:45,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:45,866 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.13101135
2015-10-18 21:33:46,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:46,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:47,710 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.13101934
2015-10-18 21:33:47,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:47,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:48,772 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.131014
2015-10-18 21:33:48,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:48,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:49,085 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.13104042
2015-10-18 21:33:49,116 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.1234297
2015-10-18 21:33:49,350 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.13101135
2015-10-18 21:33:49,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:49,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:50,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:50,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:51,366 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.13101934
2015-10-18 21:33:51,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:51,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:52,288 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.131014
2015-10-18 21:33:52,522 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.13104042
2015-10-18 21:33:52,757 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.13101135
2015-10-18 21:33:52,757 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.13102192
2015-10-18 21:33:52,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:52,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:53,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:53,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:54,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:54,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:54,960 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.13101934
2015-10-18 21:33:55,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:55,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:55,913 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.131014
2015-10-18 21:33:56,148 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.13104042
2015-10-18 21:33:56,491 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.13101135
2015-10-18 21:33:56,523 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.13102192
2015-10-18 21:33:56,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:56,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:57,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:57,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:58,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:58,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:58,835 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.13101934
2015-10-18 21:33:59,773 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.131014
2015-10-18 21:33:59,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:59,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:59,945 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.13104042
2015-10-18 21:34:00,382 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.13102192
2015-10-18 21:34:00,538 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.13101135
2015-10-18 21:34:00,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:00,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:01,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:01,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:02,538 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.13101934
2015-10-18 21:34:02,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:02,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:03,226 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.131014
2015-10-18 21:34:03,460 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.13104042
2015-10-18 21:34:03,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:03,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:03,945 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.13101135
2015-10-18 21:34:04,585 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.13102192
2015-10-18 21:34:04,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:04,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:05,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:05,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:06,148 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.16642053
2015-10-18 21:34:06,757 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.131014
2015-10-18 21:34:06,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:06,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:07,070 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.13104042
2015-10-18 21:34:07,398 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.13101135
2015-10-18 21:34:07,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:07,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:08,476 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.13102192
2015-10-18 21:34:08,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:08,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:09,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:09,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:09,804 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.23002757
2015-10-18 21:34:10,070 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.14439775
2015-10-18 21:34:10,382 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.1597923
2015-10-18 21:34:10,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:10,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:11,179 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.17421043
2015-10-18 21:34:11,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:11,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:12,351 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.13102192
2015-10-18 21:34:12,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:12,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:13,492 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.23921585
2015-10-18 21:34:13,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:13,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:14,054 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.21170057
2015-10-18 21:34:14,211 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.22582409
2015-10-18 21:34:14,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:14,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:15,211 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.23232105
2015-10-18 21:34:15,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:15,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:16,289 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.13102192
2015-10-18 21:34:16,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:16,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:17,398 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.23921585
2015-10-18 21:34:17,539 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.23919508
2015-10-18 21:34:17,727 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.23924798
2015-10-18 21:34:17,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:17,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:18,711 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.23923388
2015-10-18 21:34:18,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:18,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:19,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:19,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:20,039 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.13102192
2015-10-18 21:34:20,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:20,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:20,961 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.23919508
2015-10-18 21:34:21,195 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.23921585
2015-10-18 21:34:21,211 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.23924798
2015-10-18 21:34:21,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:21,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:22,242 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.23923388
2015-10-18 21:34:22,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:22,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:23,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:23,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:23,821 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.13102192
2015-10-18 21:34:24,446 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.23919508
2015-10-18 21:34:24,711 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.23924798
2015-10-18 21:34:24,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:24,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:25,102 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.23921585
2015-10-18 21:34:25,617 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.23923388
2015-10-18 21:34:25,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:25,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:26,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:26,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:27,493 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.13102192
2015-10-18 21:34:27,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:27,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:27,899 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.23919508
2015-10-18 21:34:28,227 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.23924798
2015-10-18 21:34:28,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:28,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:28,883 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.23921585
2015-10-18 21:34:29,102 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.23923388
2015-10-18 21:34:29,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:29,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:30,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:30,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:31,133 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.1830323
2015-10-18 21:34:31,508 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.23919508
2015-10-18 21:34:31,743 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.23924798
2015-10-18 21:34:31,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:31,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:32,618 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.23921585
2015-10-18 21:34:32,649 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.23923388
2015-10-18 21:34:32,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:32,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:33,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-18 21:34:33,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0002_01_000007 to attempt_1445175094696_0002_m_000003_0
2015-10-18 21:34:33,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0002_01_000008 to attempt_1445175094696_0002_m_000005_0
2015-10-18 21:34:33,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:33,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:33,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:6 RackLocal:1
2015-10-18 21:34:33,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:34:33,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:34:33,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:34:33,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:34:33,805 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0002_01_000007 taskAttempt attempt_1445175094696_0002_m_000003_0
2015-10-18 21:34:33,805 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0002_m_000003_0
2015-10-18 21:34:33,805 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0002_01_000008 taskAttempt attempt_1445175094696_0002_m_000005_0
2015-10-18 21:34:33,805 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0002_m_000005_0
2015-10-18 21:34:33,805 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:34:33,805 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:34:33,837 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0002_m_000003_0 : 13562
2015-10-18 21:34:33,837 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0002_m_000003_0] using containerId: [container_1445175094696_0002_01_000007 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:25649]
2015-10-18 21:34:33,837 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:34:33,837 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0002_m_000003
2015-10-18 21:34:33,837 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:34:33,837 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0002_m_000005_0 : 13562
2015-10-18 21:34:33,837 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0002_m_000005_0] using containerId: [container_1445175094696_0002_01_000008 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:25649]
2015-10-18 21:34:33,837 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:34:33,837 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0002_m_000005
2015-10-18 21:34:33,837 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:34:34,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:34:34,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:34,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:34,899 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.23919508
2015-10-18 21:34:35,118 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.23921879
2015-10-18 21:34:35,212 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.23924798
2015-10-18 21:34:35,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 21:34:35,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0002_01_000009 to attempt_1445175094696_0002_m_000007_0
2015-10-18 21:34:35,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:35,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:35,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:8 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:8 ContRel:0 HostLocal:7 RackLocal:1
2015-10-18 21:34:35,790 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:34:35,790 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:34:35,790 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0002_01_000009 taskAttempt attempt_1445175094696_0002_m_000007_0
2015-10-18 21:34:35,790 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0002_m_000007_0
2015-10-18 21:34:35,790 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:34:35,805 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0002_m_000007_0 : 13562
2015-10-18 21:34:35,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0002_m_000007_0] using containerId: [container_1445175094696_0002_01_000009 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:25649]
2015-10-18 21:34:35,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:34:35,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0002_m_000007
2015-10-18 21:34:35,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:34:36,087 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.23923388
2015-10-18 21:34:36,571 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:34:36,571 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0002_m_000007 asked for a task
2015-10-18 21:34:36,571 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0002_m_000007 given task: attempt_1445175094696_0002_m_000003_0
2015-10-18 21:34:36,618 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.23921585
2015-10-18 21:34:36,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:34:36,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:36,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:37,680 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:34:37,696 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0002_m_000008 asked for a task
2015-10-18 21:34:37,696 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0002_m_000008 given task: attempt_1445175094696_0002_m_000005_0
2015-10-18 21:34:37,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 21:34:37,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0002_01_000010 to attempt_1445175094696_0002_m_000008_0
2015-10-18 21:34:37,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:37,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:37,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:8 RackLocal:1
2015-10-18 21:34:37,790 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:34:37,790 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:34:37,790 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0002_01_000010 taskAttempt attempt_1445175094696_0002_m_000008_0
2015-10-18 21:34:37,790 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0002_m_000008_0
2015-10-18 21:34:37,790 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:34:37,805 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0002_m_000008_0 : 13562
2015-10-18 21:34:37,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0002_m_000008_0] using containerId: [container_1445175094696_0002_01_000010 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:25649]
2015-10-18 21:34:37,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:34:37,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0002_m_000008
2015-10-18 21:34:37,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:34:38,274 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:34:38,290 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0002_m_000009 asked for a task
2015-10-18 21:34:38,290 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0002_m_000009 given task: attempt_1445175094696_0002_m_000007_0
2015-10-18 21:34:38,368 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.23919508
2015-10-18 21:34:38,680 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.23924798
2015-10-18 21:34:38,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0002: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:34:38,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 21:34:38,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0002_01_000011 to attempt_1445175094696_0002_m_000009_0
2015-10-18 21:34:38,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:38,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:38,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-18 21:34:38,790 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:34:38,790 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:34:38,790 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0002_01_000011 taskAttempt attempt_1445175094696_0002_m_000009_0
2015-10-18 21:34:38,790 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0002_m_000009_0
2015-10-18 21:34:38,790 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:34:38,806 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.23921879
2015-10-18 21:34:38,806 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0002_m_000009_0 : 13562
2015-10-18 21:34:38,806 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0002_m_000009_0] using containerId: [container_1445175094696_0002_01_000011 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:25649]
2015-10-18 21:34:38,806 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:34:38,806 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0002_m_000009
2015-10-18 21:34:38,806 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:34:39,618 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.23923388
2015-10-18 21:34:39,743 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:34:39,759 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0002_m_000010 asked for a task
2015-10-18 21:34:39,759 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0002_m_000010 given task: attempt_1445175094696_0002_m_000008_0
2015-10-18 21:34:39,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:34:40,024 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.23921585
2015-10-18 21:34:41,290 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:34:41,321 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0002_m_000011 asked for a task
2015-10-18 21:34:41,321 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0002_m_000011 given task: attempt_1445175094696_0002_m_000009_0
2015-10-18 21:34:41,837 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.23919508
2015-10-18 21:34:42,040 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.24768941
2015-10-18 21:34:42,681 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.23921879
2015-10-18 21:34:43,040 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.26206142
2015-10-18 21:34:43,712 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.23921585
2015-10-18 21:34:43,931 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.13102706
2015-10-18 21:34:45,087 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.13104132
2015-10-18 21:34:45,275 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.2548426
2015-10-18 21:34:45,462 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.3112834
2015-10-18 21:34:45,634 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.13103712
2015-10-18 21:34:46,446 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.23921879
2015-10-18 21:34:46,634 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.32611543
2015-10-18 21:34:46,946 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.13102706
2015-10-18 21:34:47,321 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.2515394
2015-10-18 21:34:47,665 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.13102318
2015-10-18 21:34:48,103 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.13104132
2015-10-18 21:34:48,665 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.13103712
2015-10-18 21:34:48,915 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.31265363
2015-10-18 21:34:49,134 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.34742972
2015-10-18 21:34:49,822 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000009_0 is : 0.16604526
2015-10-18 21:34:49,978 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.15806048
2015-10-18 21:34:50,290 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.23921879
2015-10-18 21:34:50,337 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.34743717
2015-10-18 21:34:50,681 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.13102318
2015-10-18 21:34:51,337 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.13104132
2015-10-18 21:34:51,384 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.32350096
2015-10-18 21:34:51,681 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.13103712
2015-10-18 21:34:52,415 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.3474061
2015-10-18 21:34:52,603 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.34742972
2015-10-18 21:34:52,837 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000009_0 is : 0.16604526
2015-10-18 21:34:52,994 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.23922287
2015-10-18 21:34:53,712 INFO [IPC Server handler 28 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.18254368
2015-10-18 21:34:53,853 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.34743717
2015-10-18 21:34:54,119 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.23921879
2015-10-18 21:34:54,353 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.23922269
2015-10-18 21:34:54,712 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.23924637
2015-10-18 21:34:55,041 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.3474062
2015-10-18 21:34:55,869 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000009_0 is : 0.3031575
2015-10-18 21:34:55,947 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.3474061
2015-10-18 21:34:56,009 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.23922287
2015-10-18 21:34:56,103 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.34742972
2015-10-18 21:34:56,728 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.23921506
2015-10-18 21:34:56,791 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-18 21:34:56,791 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:57,323 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.34743717
2015-10-18 21:34:57,369 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.23922269
2015-10-18 21:34:57,744 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.23924637
2015-10-18 21:34:57,791 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:57,791 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:57,994 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.23921879
2015-10-18 21:34:58,892 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000009_0 is : 0.3031575
2015-10-18 21:34:58,970 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.3474062
2015-10-18 21:34:59,033 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.26270497
2015-10-18 21:34:59,486 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.3474061
2015-10-18 21:34:59,595 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.34742972
2015-10-18 21:34:59,752 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.23921506
2015-10-18 21:35:00,408 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.32368255
2015-10-18 21:35:00,752 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.23924637
2015-10-18 21:35:00,783 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.34743717
2015-10-18 21:35:01,580 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.23921879
2015-10-18 21:35:01,924 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000009_0 is : 0.3031575
2015-10-18 21:35:02,064 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.3473985
2015-10-18 21:35:02,627 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.3474062
2015-10-18 21:35:02,783 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.3411936
2015-10-18 21:35:02,986 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.3474061
2015-10-18 21:35:03,142 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.34742972
2015-10-18 21:35:03,424 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.3474054
2015-10-18 21:35:03,799 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.34743145
2015-10-18 21:35:04,283 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.34743717
2015-10-18 21:35:04,939 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000009_0 is : 0.4402952
2015-10-18 21:35:05,096 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.3473985
2015-10-18 21:35:05,236 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.23921879
2015-10-18 21:35:05,801 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.3474145
2015-10-18 21:35:06,348 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.3474062
2015-10-18 21:35:06,442 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.3474054
2015-10-18 21:35:06,614 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.3474061
2015-10-18 21:35:06,817 INFO [IPC Server handler 28 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.34743145
2015-10-18 21:35:06,879 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.34742972
2015-10-18 21:35:07,911 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.34743717
2015-10-18 21:35:07,957 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000009_0 is : 0.4402952
2015-10-18 21:35:08,114 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.3473985
2015-10-18 21:35:08,817 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.3474145
2015-10-18 21:35:09,020 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.23921879
2015-10-18 21:35:09,458 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.45560944
2015-10-18 21:35:09,848 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.38470346
2015-10-18 21:35:10,083 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.3474062
2015-10-18 21:35:10,129 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.3474061
2015-10-18 21:35:10,270 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.34742972
2015-10-18 21:35:10,989 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000009_0 is : 0.5542076
2015-10-18 21:35:11,129 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.45559394
2015-10-18 21:35:11,598 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.34743717
2015-10-18 21:35:11,833 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.45562187
2015-10-18 21:35:12,473 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.45560944
2015-10-18 21:35:12,723 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.23921879
2015-10-18 21:35:12,864 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.4556257
2015-10-18 21:35:13,426 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.3474061
2015-10-18 21:35:13,739 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.34742972
2015-10-18 21:35:13,786 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.3474062
2015-10-18 21:35:14,005 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000009_0 is : 0.5773621
2015-10-18 21:35:14,145 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.45559394
2015-10-18 21:35:14,848 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.45562187
2015-10-18 21:35:14,989 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.34743717
2015-10-18 21:35:15,505 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.45560944
2015-10-18 21:35:15,880 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.4556257
2015-10-18 21:35:16,271 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.29734668
2015-10-18 21:35:17,021 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000009_0 is : 0.5773621
2015-10-18 21:35:17,130 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.3474061
2015-10-18 21:35:17,177 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.3913633
2015-10-18 21:35:17,177 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.5560148
2015-10-18 21:35:17,427 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.3474062
2015-10-18 21:35:17,866 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.45562187
2015-10-18 21:35:18,491 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.40588784
2015-10-18 21:35:18,523 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.56381226
2015-10-18 21:35:18,898 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000009_0 is : 0.5773621
2015-10-18 21:35:18,898 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.5555592
2015-10-18 21:35:20,039 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000009_0 is : 0.667
2015-10-18 21:35:20,195 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.5637838
2015-10-18 21:35:20,336 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.34743196
2015-10-18 21:35:20,885 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.56384325
2015-10-18 21:35:31,183 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.3829783
2015-10-18 21:35:31,183 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.3474062
2015-10-18 21:35:31,183 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.56380385
2015-10-18 21:35:31,183 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.5637838
2015-10-18 21:35:31,183 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000009_0 is : 0.667
2015-10-18 21:35:31,183 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.45549887
2015-10-18 21:35:31,183 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.56381226
2015-10-18 21:35:31,183 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.56384325
2015-10-18 21:35:31,183 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.455643
2015-10-18 21:35:31,183 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.34743196
2015-10-18 21:35:31,183 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.56380385
2015-10-18 21:35:31,199 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.5637838
2015-10-18 21:35:31,199 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.56381226
2015-10-18 21:35:31,199 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.56384325
2015-10-18 21:35:34,209 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.6672887
2015-10-18 21:35:34,209 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.6711207
2015-10-18 21:35:34,209 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.667
2015-10-18 21:35:34,209 INFO [IPC Server handler 28 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.667
2015-10-18 21:35:34,209 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:35:34,225 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000009_0 is : 0.8651817
2015-10-18 21:35:34,803 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:35:34,803 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:35:34,865 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:35:34,881 INFO [IPC Server handler 24 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.455643
2015-10-18 21:35:34,881 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.45561612
2015-10-18 21:35:34,928 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.45565325
2015-10-18 21:35:35,022 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:35:35,287 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.40369835
2015-10-18 21:35:35,350 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:35:35,568 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.34743196
2015-10-18 21:35:37,228 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.71369624
2015-10-18 21:35:37,228 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.7209331
2015-10-18 21:35:37,228 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.6889859
2015-10-18 21:35:37,228 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.6975346
2015-10-18 21:35:37,243 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000009_0 is : 0.93116605
2015-10-18 21:35:38,478 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.45565325
2015-10-18 21:35:38,525 INFO [IPC Server handler 24 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.45561612
2015-10-18 21:35:38,556 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.455643
2015-10-18 21:35:39,181 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.45563135
2015-10-18 21:35:39,650 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.34743196
2015-10-18 21:35:40,244 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.76206326
2015-10-18 21:35:40,244 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.77151644
2015-10-18 21:35:40,244 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.7394527
2015-10-18 21:35:40,244 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.7464837
2015-10-18 21:35:40,259 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000009_0 is : 0.9942296
2015-10-18 21:35:40,603 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000009_0 is : 1.0
2015-10-18 21:35:40,603 INFO [IPC Server handler 26 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0002_m_000009_0
2015-10-18 21:35:40,603 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:35:40,603 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0002_01_000011 taskAttempt attempt_1445175094696_0002_m_000009_0
2015-10-18 21:35:40,603 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0002_m_000009_0
2015-10-18 21:35:40,603 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:35:40,634 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:35:40,650 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0002_m_000009_0
2015-10-18 21:35:40,665 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:35:40,665 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-18 21:35:40,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-18 21:35:40,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:35:40,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-18 21:35:40,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-18 21:35:40,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-18 21:35:41,244 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445175094696_0002_m_000001
2015-10-18 21:35:41,244 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 21:35:41,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445175094696_0002_m_000001
2015-10-18 21:35:41,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:35:41,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:35:41,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000001_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:35:41,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-18 21:35:41,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0002: ask=5 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:35:41,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0002_01_000011
2015-10-18 21:35:41,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-18 21:35:41,822 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0002_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:35:42,087 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.45561612
2015-10-18 21:35:42,087 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.45565325
2015-10-18 21:35:42,119 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.455643
2015-10-18 21:35:42,884 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.45563135
2015-10-18 21:35:43,259 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.8059077
2015-10-18 21:35:43,259 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.78297865
2015-10-18 21:35:43,259 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.7867498
2015-10-18 21:35:43,259 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.8025804
2015-10-18 21:35:43,494 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.34743196
2015-10-18 21:35:45,650 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.45565325
2015-10-18 21:35:45,697 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.455643
2015-10-18 21:35:45,744 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.45561612
2015-10-18 21:35:46,291 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.85097337
2015-10-18 21:35:46,291 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.83371943
2015-10-18 21:35:46,291 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.8317226
2015-10-18 21:35:46,306 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.85262156
2015-10-18 21:35:46,494 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.45563135
2015-10-18 21:35:47,275 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.34743196
2015-10-18 21:35:49,010 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.45870516
2015-10-18 21:35:49,056 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.4667006
2015-10-18 21:35:49,056 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.45561612
2015-10-18 21:35:49,306 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.890393
2015-10-18 21:35:49,322 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.8758823
2015-10-18 21:35:49,322 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.8946563
2015-10-18 21:35:49,322 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.87608373
2015-10-18 21:35:50,291 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.45563135
2015-10-18 21:35:51,010 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.34743196
2015-10-18 21:35:52,338 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.9374653
2015-10-18 21:35:52,338 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.9127245
2015-10-18 21:35:52,338 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.9323423
2015-10-18 21:35:52,338 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.9130715
2015-10-18 21:35:52,447 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.52309453
2015-10-18 21:35:52,525 INFO [IPC Server handler 28 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.533104
2015-10-18 21:35:52,713 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.45561612
2015-10-18 21:35:54,072 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.45563135
2015-10-18 21:35:54,697 INFO [IPC Server handler 26 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.34743196
2015-10-18 21:35:55,354 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 0.96310616
2015-10-18 21:35:55,369 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 0.974282
2015-10-18 21:35:55,369 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 0.9593872
2015-10-18 21:35:55,369 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 0.979121
2015-10-18 21:35:56,150 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.5638328
2015-10-18 21:35:56,229 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.5638263
2015-10-18 21:35:56,557 INFO [IPC Server handler 28 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.50715476
2015-10-18 21:35:56,744 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000005_0 is : 1.0
2015-10-18 21:35:56,760 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0002_m_000005_0
2015-10-18 21:35:56,760 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:35:56,760 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0002_01_000008 taskAttempt attempt_1445175094696_0002_m_000005_0
2015-10-18 21:35:56,760 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0002_m_000005_0
2015-10-18 21:35:56,760 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:35:56,807 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:35:56,807 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0002_m_000005_0
2015-10-18 21:35:56,807 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:35:56,807 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-18 21:35:56,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-18 21:35:57,791 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.45563135
2015-10-18 21:35:57,807 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000008_0 is : 1.0
2015-10-18 21:35:57,807 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0002_m_000008_0
2015-10-18 21:35:57,807 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:35:57,807 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0002_01_000010 taskAttempt attempt_1445175094696_0002_m_000008_0
2015-10-18 21:35:57,807 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0002_m_000008_0
2015-10-18 21:35:57,807 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:35:57,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0002_01_000008
2015-10-18 21:35:57,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:8 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-18 21:35:57,822 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0002_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:35:57,822 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:35:57,822 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0002_m_000008_0
2015-10-18 21:35:57,822 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:35:57,822 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-18 21:35:58,401 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 1.0
2015-10-18 21:35:58,401 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 1.0
2015-10-18 21:35:58,479 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.3696459
2015-10-18 21:35:58,479 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000003_0 is : 1.0
2015-10-18 21:35:58,494 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0002_m_000003_0
2015-10-18 21:35:58,494 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:35:58,494 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0002_01_000007 taskAttempt attempt_1445175094696_0002_m_000003_0
2015-10-18 21:35:58,494 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0002_m_000003_0
2015-10-18 21:35:58,494 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:35:58,510 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:35:58,510 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0002_m_000003_0
2015-10-18 21:35:58,510 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:35:58,510 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-18 21:35:58,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:8 AssignedReds:0 CompletedMaps:4 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-18 21:35:58,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0002_01_000010
2015-10-18 21:35:58,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 21:35:58,822 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0002_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:35:58,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-18 21:35:58,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0002_01_000012 to attempt_1445175094696_0002_r_000000_0
2015-10-18 21:35:58,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-18 21:35:58,822 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:35:58,822 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:35:58,822 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0002_01_000012 taskAttempt attempt_1445175094696_0002_r_000000_0
2015-10-18 21:35:58,822 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0002_r_000000_0
2015-10-18 21:35:58,822 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:35:58,854 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0002_r_000000_0 : 13562
2015-10-18 21:35:58,854 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0002_r_000000_0] using containerId: [container_1445175094696_0002_01_000012 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:25649]
2015-10-18 21:35:58,854 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:35:58,854 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0002_r_000000
2015-10-18 21:35:58,854 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:35:59,666 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.5638328
2015-10-18 21:35:59,776 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.5638263
2015-10-18 21:35:59,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0002: ask=1 release= 0 newContainers=1 finishedContainers=1 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:35:59,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0002_01_000007
2015-10-18 21:35:59,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 21:35:59,823 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0002_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:35:59,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0002_01_000013 to attempt_1445175094696_0002_m_000001_1
2015-10-18 21:35:59,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-18 21:35:59,823 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:35:59,823 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000001_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:35:59,823 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0002_01_000013 taskAttempt attempt_1445175094696_0002_m_000001_1
2015-10-18 21:35:59,823 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0002_m_000001_1
2015-10-18 21:35:59,823 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:35:59,838 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0002_m_000001_1 : 13562
2015-10-18 21:35:59,838 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0002_m_000001_1] using containerId: [container_1445175094696_0002_01_000013 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:25649]
2015-10-18 21:35:59,838 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000001_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:35:59,838 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0002_m_000001
2015-10-18 21:35:59,994 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000007_0 is : 1.0
2015-10-18 21:35:59,994 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0002_m_000007_0
2015-10-18 21:35:59,994 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:35:59,994 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0002_01_000009 taskAttempt attempt_1445175094696_0002_m_000007_0
2015-10-18 21:35:59,994 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0002_m_000007_0
2015-10-18 21:35:59,994 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:36:00,010 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:36:00,010 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0002_m_000007_0
2015-10-18 21:36:00,010 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:36:00,010 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-18 21:36:00,151 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.56381613
2015-10-18 21:36:00,291 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445175094696_0002_m_000000
2015-10-18 21:36:00,291 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 21:36:00,291 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445175094696_0002_m_000000
2015-10-18 21:36:00,291 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:36:00,291 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:36:00,291 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000000_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:36:00,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-18 21:36:00,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0002: ask=5 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:36:01,057 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:36:01,073 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0002_r_000012 asked for a task
2015-10-18 21:36:01,073 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0002_r_000012 given task: attempt_1445175094696_0002_r_000000_0
2015-10-18 21:36:01,573 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:36:01,588 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0002_m_000013 asked for a task
2015-10-18 21:36:01,588 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0002_m_000013 given task: attempt_1445175094696_0002_m_000001_1
2015-10-18 21:36:01,713 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.45563135
2015-10-18 21:36:01,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0002_01_000009
2015-10-18 21:36:01,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 21:36:01,823 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0002_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:36:01,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0002_01_000014 to attempt_1445175094696_0002_m_000000_1
2015-10-18 21:36:01,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-18 21:36:01,823 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:36:01,823 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000000_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:36:01,823 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0002_01_000014 taskAttempt attempt_1445175094696_0002_m_000000_1
2015-10-18 21:36:01,823 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0002_m_000000_1
2015-10-18 21:36:01,823 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:36:01,838 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0002_m_000000_1 : 13562
2015-10-18 21:36:01,838 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0002_m_000000_1] using containerId: [container_1445175094696_0002_01_000014 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:25649]
2015-10-18 21:36:01,838 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000000_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:36:01,838 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0002_m_000000
2015-10-18 21:36:02,229 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 0 maxEvents 10000
2015-10-18 21:36:02,573 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.44869938
2015-10-18 21:36:02,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:36:03,120 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.5638328
2015-10-18 21:36:03,245 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:03,338 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.5638263
2015-10-18 21:36:03,620 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:36:03,635 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0002_m_000014 asked for a task
2015-10-18 21:36:03,635 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0002_m_000014 given task: attempt_1445175094696_0002_m_000000_1
2015-10-18 21:36:03,838 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.56381613
2015-10-18 21:36:04,245 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:05,245 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:05,323 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.45563135
2015-10-18 21:36:06,245 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:06,276 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.455629
2015-10-18 21:36:06,604 INFO [IPC Server handler 28 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.5638328
2015-10-18 21:36:06,917 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.5638263
2015-10-18 21:36:07,245 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:07,323 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.56381613
2015-10-18 21:36:08,182 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.13333334
2015-10-18 21:36:08,245 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:09,245 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.13102192
2015-10-18 21:36:09,245 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.45563135
2015-10-18 21:36:09,260 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:10,073 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.5638328
2015-10-18 21:36:10,260 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:10,339 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.455629
2015-10-18 21:36:10,464 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.5638263
2015-10-18 21:36:10,729 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.13101934
2015-10-18 21:36:10,823 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.56381613
2015-10-18 21:36:11,229 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:36:11,260 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:12,261 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:12,276 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.13102192
2015-10-18 21:36:12,854 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.45563135
2015-10-18 21:36:13,261 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:13,495 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.5638328
2015-10-18 21:36:13,745 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.13101934
2015-10-18 21:36:13,839 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.5638263
2015-10-18 21:36:13,839 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.455629
2015-10-18 21:36:14,245 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:36:14,261 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:14,339 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.56381613
2015-10-18 21:36:15,276 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:15,308 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445175094696_0002_m_000002
2015-10-18 21:36:15,308 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 21:36:15,308 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445175094696_0002_m_000002
2015-10-18 21:36:15,308 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:36:15,308 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:36:15,308 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000002_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:36:15,323 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.23921879
2015-10-18 21:36:16,120 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-18 21:36:16,120 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:36:16,276 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:16,354 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.45563135
2015-10-18 21:36:16,776 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.23921585
2015-10-18 21:36:16,979 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.5638328
2015-10-18 21:36:17,261 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:36:17,276 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:17,511 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.5638263
2015-10-18 21:36:17,620 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.455629
2015-10-18 21:36:17,823 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.56381613
2015-10-18 21:36:18,276 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:18,355 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.23921879
2015-10-18 21:36:19,276 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:19,808 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.23921585
2015-10-18 21:36:20,026 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.49959418
2015-10-18 21:36:20,276 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:20,339 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.5638328
2015-10-18 21:36:20,292 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:36:20,901 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.5638263
2015-10-18 21:36:21,167 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.56381613
2015-10-18 21:36:21,386 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.23921879
2015-10-18 21:36:21,433 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:21,464 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.455629
2015-10-18 21:36:22,433 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:22,839 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.24505454
2015-10-18 21:36:23,433 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:23,448 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:36:23,917 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.5499841
2015-10-18 21:36:23,933 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.5781666
2015-10-18 21:36:24,308 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.5978219
2015-10-18 21:36:24,402 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.34743196
2015-10-18 21:36:24,433 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:24,730 INFO [IPC Server handler 28 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.56381613
2015-10-18 21:36:25,402 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.455629
2015-10-18 21:36:25,433 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:25,870 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.3474062
2015-10-18 21:36:26,433 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:26,480 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:36:27,292 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.6464796
2015-10-18 21:36:27,433 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:27,433 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.34743196
2015-10-18 21:36:27,574 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.56380075
2015-10-18 21:36:27,933 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.6619421
2015-10-18 21:36:28,402 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.5681269
2015-10-18 21:36:28,433 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:28,902 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.3474062
2015-10-18 21:36:29,105 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.455629
2015-10-18 21:36:29,433 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:29,511 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:36:29,777 INFO [IPC Server handler 24 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.6619421
2015-10-18 21:36:30,433 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:30,464 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.44518307
2015-10-18 21:36:30,746 INFO [IPC Server handler 28 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.667
2015-10-18 21:36:30,746 INFO [IPC Server handler 24 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.667
2015-10-18 21:36:31,246 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.56380075
2015-10-18 21:36:31,402 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.667
2015-10-18 21:36:31,433 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:31,839 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.63969547
2015-10-18 21:36:31,918 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.44241127
2015-10-18 21:36:32,433 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:32,543 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:36:32,871 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.455629
2015-10-18 21:36:33,449 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:33,496 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.455629
2015-10-18 21:36:33,949 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.63969547
2015-10-18 21:36:34,215 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.667
2015-10-18 21:36:34,449 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:34,777 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.667
2015-10-18 21:36:34,886 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.56380075
2015-10-18 21:36:34,949 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.45563135
2015-10-18 21:36:35,308 INFO [IPC Server handler 26 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.667
2015-10-18 21:36:35,449 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:35,558 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:36:36,449 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:36,511 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.455629
2015-10-18 21:36:36,590 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.455629
2015-10-18 21:36:37,449 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:37,746 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.667
2015-10-18 21:36:37,980 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.45563135
2015-10-18 21:36:38,230 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.667
2015-10-18 21:36:38,449 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:38,590 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:36:38,699 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.56380075
2015-10-18 21:36:38,777 INFO [IPC Server handler 24 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.667
2015-10-18 21:36:39,449 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:39,543 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.5628149
2015-10-18 21:36:40,371 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.455629
2015-10-18 21:36:40,449 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:40,996 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.56380075
2015-10-18 21:36:41,262 INFO [IPC Server handler 26 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.667
2015-10-18 21:36:41,449 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:41,605 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:36:41,699 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.667
2015-10-18 21:36:42,277 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.667
2015-10-18 21:36:42,309 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.56380075
2015-10-18 21:36:42,449 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:42,559 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.5638294
2015-10-18 21:36:43,449 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:44,027 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.56380075
2015-10-18 21:36:44,074 INFO [IPC Server handler 26 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.50448316
2015-10-18 21:36:44,449 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:44,637 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.667
2015-10-18 21:36:44,637 INFO [IPC Server handler 24 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:36:45,324 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.667
2015-10-18 21:36:45,449 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:45,590 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.5638294
2015-10-18 21:36:45,793 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.667
2015-10-18 21:36:46,074 INFO [IPC Server handler 26 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.56380075
2015-10-18 21:36:46,465 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:47,059 INFO [IPC Server handler 26 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.57695436
2015-10-18 21:36:47,465 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:47,653 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:36:47,715 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.5638294
2015-10-18 21:36:48,043 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.667
2015-10-18 21:36:48,059 INFO [IPC Server handler 26 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.5638294
2015-10-18 21:36:48,262 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.57695436
2015-10-18 21:36:48,465 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:48,621 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.667
2015-10-18 21:36:48,793 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.667
2015-10-18 21:36:49,340 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.667
2015-10-18 21:36:49,465 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:49,778 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.56380075
2015-10-18 21:36:50,075 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.667
2015-10-18 21:36:50,465 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:50,684 INFO [IPC Server handler 24 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:36:51,465 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:51,496 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.667
2015-10-18 21:36:51,653 INFO [IPC Server handler 24 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.667
2015-10-18 21:36:51,700 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.5638294
2015-10-18 21:36:52,247 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.667
2015-10-18 21:36:52,465 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:52,762 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.667
2015-10-18 21:36:53,090 INFO [IPC Server handler 26 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.667
2015-10-18 21:36:53,465 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:53,543 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.56380075
2015-10-18 21:36:53,700 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:36:54,465 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:54,668 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.667
2015-10-18 21:36:54,872 INFO [IPC Server handler 28 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.667
2015-10-18 21:36:55,450 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.5638294
2015-10-18 21:36:55,465 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:55,715 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.667
2015-10-18 21:36:56,122 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.667
2015-10-18 21:36:56,356 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.667
2015-10-18 21:36:56,465 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:56,731 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:36:57,231 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.56380075
2015-10-18 21:36:57,465 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:57,700 INFO [IPC Server handler 24 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.6814365
2015-10-18 21:36:58,309 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.667
2015-10-18 21:36:58,481 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:59,012 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.667
2015-10-18 21:36:59,153 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.7096003
2015-10-18 21:36:59,481 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:36:59,700 INFO [IPC Server handler 24 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.5638294
2015-10-18 21:36:59,762 INFO [IPC Server handler 28 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:36:59,966 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.667
2015-10-18 21:37:00,481 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:37:00,716 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.7328679
2015-10-18 21:37:01,106 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.56380075
2015-10-18 21:37:01,481 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:37:01,809 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.6670493
2015-10-18 21:37:02,184 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.76076657
2015-10-18 21:37:02,481 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:37:02,591 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.6685757
2015-10-18 21:37:02,794 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:37:03,481 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.5638294
2015-10-18 21:37:03,481 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:37:03,559 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.667
2015-10-18 21:37:03,747 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.77283156
2015-10-18 21:37:04,481 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:37:04,841 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.60153997
2015-10-18 21:37:05,216 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.8017249
2015-10-18 21:37:05,356 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.67701817
2015-10-18 21:37:05,481 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:37:05,825 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:37:05,981 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.6800207
2015-10-18 21:37:06,122 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 21:37:06,122 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0002_01_000015 to attempt_1445175094696_0002_m_000002_1
2015-10-18 21:37:06,122 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:12 RackLocal:1
2015-10-18 21:37:06,122 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:37:06,122 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000002_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:37:06,122 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0002_01_000015 taskAttempt attempt_1445175094696_0002_m_000002_1
2015-10-18 21:37:06,122 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0002_m_000002_1
2015-10-18 21:37:06,122 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:37:06,138 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0002_m_000002_1 : 13562
2015-10-18 21:37:06,138 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0002_m_000002_1] using containerId: [container_1445175094696_0002_01_000015 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:25649]
2015-10-18 21:37:06,138 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000002_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:37:06,138 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0002_m_000002
2015-10-18 21:37:06,481 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:37:06,716 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445175094696_0002_m_000004
2015-10-18 21:37:06,716 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 21:37:06,716 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445175094696_0002_m_000004
2015-10-18 21:37:06,716 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:37:06,716 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:37:06,716 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000004_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:37:06,763 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.8167378
2015-10-18 21:37:06,935 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.667
2015-10-18 21:37:07,122 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:12 RackLocal:1
2015-10-18 21:37:07,122 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:37:07,153 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.5638294
2015-10-18 21:37:07,497 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:37:07,716 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:37:07,732 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0002_m_000015 asked for a task
2015-10-18 21:37:07,732 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0002_m_000015 given task: attempt_1445175094696_0002_m_000002_1
2015-10-18 21:37:08,122 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 21:37:08,122 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0002_01_000016 to attempt_1445175094696_0002_m_000004_1
2015-10-18 21:37:08,122 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:13 RackLocal:1
2015-10-18 21:37:08,122 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:37:08,122 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000004_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:37:08,122 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0002_01_000016 taskAttempt attempt_1445175094696_0002_m_000004_1
2015-10-18 21:37:08,122 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0002_m_000004_1
2015-10-18 21:37:08,122 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 21:37:08,232 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.851284
2015-10-18 21:37:08,497 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:37:08,638 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.66271627
2015-10-18 21:37:08,732 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0002_m_000004_1 : 13562
2015-10-18 21:37:08,732 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0002_m_000004_1] using containerId: [container_1445175094696_0002_01_000016 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 21:37:08,732 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000004_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:37:08,732 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0002_m_000004
2015-10-18 21:37:08,778 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.69025385
2015-10-18 21:37:08,857 INFO [IPC Server handler 28 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:37:09,122 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:37:09,294 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.66271627
2015-10-18 21:37:09,482 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.69386524
2015-10-18 21:37:09,497 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:37:09,794 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.8665519
2015-10-18 21:37:10,232 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.6781081
2015-10-18 21:37:10,497 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:37:10,700 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.5638294
2015-10-18 21:37:11,263 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.90218014
2015-10-18 21:37:11,497 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:37:11,888 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:37:12,169 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.7040358
2015-10-18 21:37:12,419 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.667
2015-10-18 21:37:12,497 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:37:12,810 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.707885
2015-10-18 21:37:12,825 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.9178256
2015-10-18 21:37:13,435 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.69131106
2015-10-18 21:37:13,497 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:37:14,294 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 0.9541751
2015-10-18 21:37:14,294 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.5638294
2015-10-18 21:37:14,497 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:37:14,794 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.131014
2015-10-18 21:37:14,904 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:37:15,497 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:37:15,732 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.71785927
2015-10-18 21:37:15,857 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 0.9689735
2015-10-18 21:37:15,997 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_0 is : 0.667
2015-10-18 21:37:16,263 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.7219291
2015-10-18 21:37:16,497 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:37:17,044 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000000_1 is : 1.0
2015-10-18 21:37:17,044 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0002_m_000000_1
2015-10-18 21:37:17,060 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000000_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:37:17,060 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0002_01_000014 taskAttempt attempt_1445175094696_0002_m_000000_1
2015-10-18 21:37:17,060 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0002_m_000000_1
2015-10-18 21:37:17,060 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:37:17,060 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.7052062
2015-10-18 21:37:17,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000000_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:37:17,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0002_m_000000_1
2015-10-18 21:37:17,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445175094696_0002_m_000000_0
2015-10-18 21:37:17,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:37:17,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-18 21:37:17,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000000_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 21:37:17,076 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0002_01_000005 taskAttempt attempt_1445175094696_0002_m_000000_0
2015-10-18 21:37:17,076 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0002_m_000000_0
2015-10-18 21:37:17,076 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-18 21:37:17,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:13 RackLocal:1
2015-10-18 21:37:17,513 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 21:37:17,794 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_1 is : 1.0
2015-10-18 21:37:17,794 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0002_m_000001_1
2015-10-18 21:37:17,794 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000001_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:37:17,794 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0002_01_000013 taskAttempt attempt_1445175094696_0002_m_000001_1
2015-10-18 21:37:17,794 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0002_m_000001_1
2015-10-18 21:37:17,794 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:37:17,810 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000001_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:37:17,810 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0002_m_000001_1
2015-10-18 21:37:17,810 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445175094696_0002_m_000001_0
2015-10-18 21:37:17,810 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:37:17,810 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-18 21:37:17,810 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000001_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 21:37:17,810 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0002_01_000006 taskAttempt attempt_1445175094696_0002_m_000001_0
2015-10-18 21:37:17,810 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0002_m_000001_0
2015-10-18 21:37:17,810 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-18 21:37:17,826 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.131014
2015-10-18 21:37:17,857 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000000_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 21:37:17,857 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 21:37:17,873 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445175094696_0002_m_000000_0
2015-10-18 21:37:17,873 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000000_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 21:37:17,935 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.16666667
2015-10-18 21:37:17,966 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000001_0 is : 0.5638294
2015-10-18 21:37:18,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:13 RackLocal:1
2015-10-18 21:37:18,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0002_01_000014
2015-10-18 21:37:18,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0002_01_000013
2015-10-18 21:37:18,123 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0002_m_000000_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:37:18,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:13 RackLocal:1
2015-10-18 21:37:18,123 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0002_m_000001_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:37:18,513 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:37:18,951 INFO [Socket Reader #1 for port 20324] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 20324: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 21:37:19,216 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000001_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 21:37:19,216 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 21:37:19,216 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445175094696_0002_m_000001_0
2015-10-18 21:37:19,216 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000001_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 21:37:19,248 INFO [IPC Server handler 24 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.7287604
2015-10-18 21:37:19,513 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:19,904 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.7335602
2015-10-18 21:37:20,326 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.7149345
2015-10-18 21:37:20,513 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:20,841 INFO [IPC Server handler 24 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.23919508
2015-10-18 21:37:20,966 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:37:21,388 INFO [Socket Reader #1 for port 20324] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 20324: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 21:37:21,513 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:21,732 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445175094696_0002_m_000006
2015-10-18 21:37:21,732 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 21:37:21,732 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445175094696_0002_m_000006
2015-10-18 21:37:21,732 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:37:21,732 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:37:21,732 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000006_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:37:21,920 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:37:22,107 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0002_m_000016 asked for a task
2015-10-18 21:37:22,107 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0002_m_000016 given task: attempt_1445175094696_0002_m_000004_1
2015-10-18 21:37:22,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:13 RackLocal:1
2015-10-18 21:37:22,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:37:22,513 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:23,373 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.74178284
2015-10-18 21:37:23,513 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:23,873 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.23919508
2015-10-18 21:37:23,998 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.74592406
2015-10-18 21:37:23,998 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:37:24,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0002_01_000005
2015-10-18 21:37:24,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0002_01_000006
2015-10-18 21:37:24,123 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0002_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:37:24,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:13 RackLocal:1
2015-10-18 21:37:24,123 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0002_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:37:24,295 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.726963
2015-10-18 21:37:24,513 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:25,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 21:37:25,123 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:37:25,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0002_01_000017 to attempt_1445175094696_0002_m_000006_1
2015-10-18 21:37:25,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-18 21:37:25,123 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:37:25,123 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000006_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:37:25,123 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0002_01_000017 taskAttempt attempt_1445175094696_0002_m_000006_1
2015-10-18 21:37:25,123 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0002_m_000006_1
2015-10-18 21:37:25,123 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-18 21:37:25,529 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:25,592 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0002_m_000006_1 : 13562
2015-10-18 21:37:25,592 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0002_m_000006_1] using containerId: [container_1445175094696_0002_01_000017 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:52368]
2015-10-18 21:37:25,592 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000006_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:37:25,592 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0002_m_000006
2015-10-18 21:37:26,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-25> knownNMs=4
2015-10-18 21:37:26,529 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:26,701 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.75279534
2015-10-18 21:37:26,889 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.23919508
2015-10-18 21:37:27,014 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:37:27,435 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.758782
2015-10-18 21:37:27,529 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:27,810 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.7410317
2015-10-18 21:37:28,529 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:29,529 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:29,904 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.3474061
2015-10-18 21:37:30,029 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:37:30,357 INFO [IPC Server handler 26 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.7671426
2015-10-18 21:37:30,529 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:30,889 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.77246857
2015-10-18 21:37:31,264 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.75445485
2015-10-18 21:37:31,529 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:32,529 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:32,920 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.3474061
2015-10-18 21:37:33,045 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:37:33,529 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:33,764 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.7782061
2015-10-18 21:37:34,342 INFO [Socket Reader #1 for port 20324] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0002 (auth:SIMPLE)
2015-10-18 21:37:34,358 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.78527355
2015-10-18 21:37:34,529 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0002_m_000017 asked for a task
2015-10-18 21:37:34,529 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0002_m_000017 given task: attempt_1445175094696_0002_m_000006_1
2015-10-18 21:37:34,529 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:34,764 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.76618594
2015-10-18 21:37:35,530 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:35,795 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_1 is : 0.102018714
2015-10-18 21:37:35,951 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.3928864
2015-10-18 21:37:36,061 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:37:36,530 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:37,264 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.7908682
2015-10-18 21:37:37,545 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:37,826 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.79856706
2015-10-18 21:37:38,405 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.78005165
2015-10-18 21:37:38,545 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:38,967 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.45561612
2015-10-18 21:37:39,077 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:37:39,342 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_1 is : 0.13104042
2015-10-18 21:37:39,545 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:40,545 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:40,623 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.80209684
2015-10-18 21:37:41,202 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.81046724
2015-10-18 21:37:41,545 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:41,889 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.79174286
2015-10-18 21:37:41,999 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.45561612
2015-10-18 21:37:42,124 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:37:42,545 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:42,892 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_1 is : 0.13104042
2015-10-18 21:37:43,549 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:44,267 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.8137694
2015-10-18 21:37:44,549 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:44,830 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.8231833
2015-10-18 21:37:45,033 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.45561612
2015-10-18 21:37:45,142 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:37:45,486 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.80375934
2015-10-18 21:37:45,549 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:46,549 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:46,564 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_1 is : 0.13104042
2015-10-18 21:37:47,549 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:47,721 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.8255102
2015-10-18 21:37:48,049 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.56381613
2015-10-18 21:37:48,158 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.8354976
2015-10-18 21:37:48,174 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:37:48,549 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:48,783 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.81598955
2015-10-18 21:37:49,549 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:50,002 INFO [IPC Server handler 24 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_1 is : 0.13104042
2015-10-18 21:37:50,549 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:51,018 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.83743066
2015-10-18 21:37:51,080 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.56381613
2015-10-18 21:37:51,205 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:37:51,471 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.8486023
2015-10-18 21:37:51,564 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:52,346 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.82893676
2015-10-18 21:37:52,565 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:53,518 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_1 is : 0.13104042
2015-10-18 21:37:53,565 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:54,100 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.56381613
2015-10-18 21:37:54,225 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:37:54,522 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.8491365
2015-10-18 21:37:54,569 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:54,991 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.86089224
2015-10-18 21:37:55,569 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:55,944 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.84133345
2015-10-18 21:37:56,569 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:56,928 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_1 is : 0.13104042
2015-10-18 21:37:57,147 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.63529134
2015-10-18 21:37:57,272 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:37:57,569 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:57,975 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.86168456
2015-10-18 21:37:58,459 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.8741382
2015-10-18 21:37:58,569 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:58,741 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.63529134
2015-10-18 21:37:59,444 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.8537489
2015-10-18 21:37:59,569 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:00,178 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.667
2015-10-18 21:38:00,303 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:38:00,491 INFO [IPC Server handler 26 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_1 is : 0.13104042
2015-10-18 21:38:00,569 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:00,913 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_1 is : 0.10388676
2015-10-18 21:38:01,569 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:01,663 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.8729842
2015-10-18 21:38:02,022 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.88545245
2015-10-18 21:38:02,569 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:03,210 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.667
2015-10-18 21:38:03,319 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.8641984
2015-10-18 21:38:03,335 INFO [IPC Server handler 26 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:38:03,569 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:04,116 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_1 is : 0.13104042
2015-10-18 21:38:04,569 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:04,772 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_1 is : 0.13101135
2015-10-18 21:38:05,338 INFO [IPC Server handler 26 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.88088185
2015-10-18 21:38:05,510 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.89393455
2015-10-18 21:38:05,572 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:06,228 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.667
2015-10-18 21:38:06,369 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:38:06,572 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:06,635 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.8735426
2015-10-18 21:38:07,588 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:08,228 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_1 is : 0.13104042
2015-10-18 21:38:08,228 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_1 is : 0.13101135
2015-10-18 21:38:08,588 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:08,947 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.891131
2015-10-18 21:38:09,010 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.9042283
2015-10-18 21:38:09,275 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.68190265
2015-10-18 21:38:09,400 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:38:09,588 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:10,104 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.883405
2015-10-18 21:38:10,588 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:11,557 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_1 is : 0.16516009
2015-10-18 21:38:11,588 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:11,916 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_1 is : 0.13101135
2015-10-18 21:38:12,291 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.719055
2015-10-18 21:38:12,322 INFO [IPC Server handler 26 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.9026865
2015-10-18 21:38:12,432 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:38:12,432 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.9165634
2015-10-18 21:38:12,588 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:13,322 INFO [IPC Server handler 26 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.8956123
2015-10-18 21:38:13,588 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:14,588 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:14,901 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_1 is : 0.23924798
2015-10-18 21:38:15,338 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.770311
2015-10-18 21:38:15,463 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:38:15,479 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_1 is : 0.13101135
2015-10-18 21:38:15,510 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.9172529
2015-10-18 21:38:15,588 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:15,619 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.9330723
2015-10-18 21:38:16,588 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:16,776 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.91157854
2015-10-18 21:38:17,588 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:18,151 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_1 is : 0.23924798
2015-10-18 21:38:18,448 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.8229517
2015-10-18 21:38:18,495 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:38:18,588 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:18,713 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.9327289
2015-10-18 21:38:18,932 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.9494506
2015-10-18 21:38:19,026 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_1 is : 0.13101135
2015-10-18 21:38:19,604 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:20,060 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.92916906
2015-10-18 21:38:20,591 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:21,357 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_1 is : 0.23924798
2015-10-18 21:38:21,466 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.869256
2015-10-18 21:38:21,513 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:38:21,591 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:22,029 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.9504135
2015-10-18 21:38:22,185 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.968536
2015-10-18 21:38:22,498 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_1 is : 0.13101135
2015-10-18 21:38:22,607 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:23,201 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.94695044
2015-10-18 21:38:23,607 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:24,498 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.9160745
2015-10-18 21:38:24,545 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:38:24,607 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:24,842 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_1 is : 0.23924798
2015-10-18 21:38:25,404 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.9657056
2015-10-18 21:38:25,607 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:25,670 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.984
2015-10-18 21:38:26,123 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_1 is : 0.13101135
2015-10-18 21:38:26,607 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:26,623 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.9601371
2015-10-18 21:38:27,514 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 0.9607223
2015-10-18 21:38:27,560 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:38:27,607 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:28,310 INFO [IPC Server handler 24 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_1 is : 0.23924798
2015-10-18 21:38:30,826 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:30,826 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.97678137
2015-10-18 21:38:30,826 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 0.99566346
2015-10-18 21:38:30,826 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_1 is : 0.13101135
2015-10-18 21:38:30,826 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_0 is : 0.9709585
2015-10-18 21:38:30,826 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000002_1 is : 1.0
2015-10-18 21:38:30,826 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.23333333
2015-10-18 21:38:30,826 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0002_m_000002_1
2015-10-18 21:38:30,826 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000002_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:38:30,826 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0002_01_000015 taskAttempt attempt_1445175094696_0002_m_000002_1
2015-10-18 21:38:30,826 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0002_m_000002_1
2015-10-18 21:38:30,826 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:38:30,842 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000002_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:38:30,842 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0002_m_000002_1
2015-10-18 21:38:30,842 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445175094696_0002_m_000002_0
2015-10-18 21:38:30,842 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:38:30,842 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-18 21:38:30,842 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000002_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 21:38:30,842 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0002_01_000002 taskAttempt attempt_1445175094696_0002_m_000002_0
2015-10-18 21:38:30,842 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0002_m_000002_0
2015-10-18 21:38:30,842 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 21:38:31,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-18 21:38:31,170 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000006_0 is : 1.0
2015-10-18 21:38:31,201 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000002_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 21:38:31,201 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 21:38:31,217 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0002_m_000006_0
2015-10-18 21:38:31,217 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:38:31,217 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445175094696_0002_m_000002_0
2015-10-18 21:38:31,217 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0002_01_000004 taskAttempt attempt_1445175094696_0002_m_000006_0
2015-10-18 21:38:31,217 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000002_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 21:38:31,217 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0002_m_000006_0
2015-10-18 21:38:31,217 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 21:38:31,686 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_1 is : 0.23924798
2015-10-18 21:38:31,826 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:38:31,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:38:31,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0002_m_000006_0
2015-10-18 21:38:31,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445175094696_0002_m_000006_1
2015-10-18 21:38:31,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:38:31,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-18 21:38:31,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000006_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 21:38:31,904 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0002_01_000017 taskAttempt attempt_1445175094696_0002_m_000006_1
2015-10-18 21:38:31,904 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0002_m_000006_1
2015-10-18 21:38:31,904 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-18 21:38:32,076 INFO [Socket Reader #1 for port 20324] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 20324: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 21:38:32,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-18 21:38:32,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0002_01_000015
2015-10-18 21:38:32,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-18 21:38:32,139 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0002_m_000002_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:38:32,357 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445175094696_0002_m_000006
2015-10-18 21:38:32,357 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 21:38:32,826 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:38:33,186 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000006_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 21:38:33,389 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 21:38:33,514 WARN [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445175094696_0002_m_000006_1
2015-10-18 21:38:33,514 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000006_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 21:38:33,826 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:33,858 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.26666668
2015-10-18 21:38:34,373 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 0.9944206
2015-10-18 21:38:34,420 INFO [Socket Reader #1 for port 20324] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 20324: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 21:38:34,826 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:35,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0002_01_000002
2015-10-18 21:38:35,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0002_01_000004
2015-10-18 21:38:35,139 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0002_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:38:35,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-18 21:38:35,139 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0002_m_000006_0: Container killed by the ApplicationMaster.

2015-10-18 21:38:35,342 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_1 is : 0.23924798
2015-10-18 21:38:35,826 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:36,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0002_01_000017
2015-10-18 21:38:36,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-18 21:38:36,139 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0002_m_000006_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:38:36,842 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:36,873 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.26666668
2015-10-18 21:38:37,373 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_0 is : 1.0
2015-10-18 21:38:37,639 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0002_m_000004_0
2015-10-18 21:38:37,639 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:38:37,639 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0002_01_000003 taskAttempt attempt_1445175094696_0002_m_000004_0
2015-10-18 21:38:37,639 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0002_m_000004_0
2015-10-18 21:38:37,639 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 21:38:37,842 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:38,842 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:38,905 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_m_000004_1 is : 0.23924798
2015-10-18 21:38:39,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:38:39,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0002_m_000004_0
2015-10-18 21:38:39,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445175094696_0002_m_000004_1
2015-10-18 21:38:39,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:38:39,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-18 21:38:39,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000004_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 21:38:39,170 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0002_01_000016 taskAttempt attempt_1445175094696_0002_m_000004_1
2015-10-18 21:38:39,170 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0002_m_000004_1
2015-10-18 21:38:39,170 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 21:38:39,842 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:39,905 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.26666668
2015-10-18 21:38:40,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-18 21:38:40,842 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:41,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0002_01_000003
2015-10-18 21:38:41,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-18 21:38:41,139 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0002_m_000004_0: Container killed by the ApplicationMaster.

2015-10-18 21:38:41,764 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000004_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 21:38:41,764 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 21:38:41,764 WARN [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445175094696_0002_m_000004_1
2015-10-18 21:38:41,764 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_m_000004_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 21:38:41,842 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:41,983 INFO [Socket Reader #1 for port 20324] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 20324: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 21:38:42,842 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:42,952 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.26666668
2015-10-18 21:38:43,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0002_01_000016
2015-10-18 21:38:43,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-18 21:38:43,139 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0002_m_000004_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:38:43,842 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:44,842 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:45,842 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:45,967 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.26666668
2015-10-18 21:38:46,842 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:47,858 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:48,858 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:48,999 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.26666668
2015-10-18 21:38:49,858 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:50,858 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:51,858 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:52,030 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.26666668
2015-10-18 21:38:52,858 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:53,858 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:54,858 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:55,046 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.26666668
2015-10-18 21:38:55,858 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:56,858 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:57,859 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:58,077 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.3
2015-10-18 21:38:58,859 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:59,874 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:00,874 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:01,109 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.3
2015-10-18 21:39:01,874 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:02,874 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:03,874 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:04,140 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.3
2015-10-18 21:39:04,874 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:05,874 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:06,875 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:07,171 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.3
2015-10-18 21:39:07,890 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:08,890 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:09,890 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:10,203 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.3
2015-10-18 21:39:10,890 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:11,890 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:12,890 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:13,234 INFO [IPC Server handler 26 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.3
2015-10-18 21:39:13,890 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:14,890 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:15,891 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:16,281 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.3
2015-10-18 21:39:16,891 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:17,906 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:18,906 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:19,313 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.3
2015-10-18 21:39:19,906 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:20,906 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:21,906 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:22,344 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.3
2015-10-18 21:39:22,906 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:23,906 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:24,906 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:25,375 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.3
2015-10-18 21:39:25,907 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:26,907 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:27,907 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:28,391 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.3
2015-10-18 21:39:28,907 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:29,907 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:30,907 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:31,438 INFO [IPC Server handler 24 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.3
2015-10-18 21:39:31,922 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:32,922 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:33,922 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:34,485 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.3
2015-10-18 21:39:34,923 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:35,923 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:36,923 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:39:37,516 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.3
2015-10-18 21:39:37,845 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.3
2015-10-18 21:39:37,876 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.3
2015-10-18 21:39:40,548 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.67040473
2015-10-18 21:39:43,579 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.6745059
2015-10-18 21:39:46,626 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.6787817
2015-10-18 21:39:49,658 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.6830403
2015-10-18 21:39:52,689 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.6869967
2015-10-18 21:39:55,720 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.6923748
2015-10-18 21:39:58,752 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.69608366
2015-10-18 21:40:00,767 WARN [ResponseProcessor for block BP-1347369012-**************-1444972147527:blk_1073743655_2878] org.apache.hadoop.hdfs.DFSClient: Slow ReadProcessor read fields took 48957ms (threshold=30000ms); ack: seqno: -2 status: SUCCESS status: ERROR downstreamAckTimeNanos: 0, targets: [**************:50010, *************:50010]
2015-10-18 21:40:00,767 WARN [ResponseProcessor for block BP-1347369012-**************-1444972147527:blk_1073743655_2878] org.apache.hadoop.hdfs.DFSClient: DFSOutputStream ResponseProcessor exception  for block BP-1347369012-**************-1444972147527:blk_1073743655_2878
java.io.IOException: Bad response ERROR for block BP-1347369012-**************-1444972147527:blk_1073743655_2878 from datanode *************:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer$ResponseProcessor.run(DFSOutputStream.java:898)
2015-10-18 21:40:00,767 WARN [DataStreamer for file /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445175094696_0002/job_1445175094696_0002_1.jhist block BP-1347369012-**************-1444972147527:blk_1073743655_2878] org.apache.hadoop.hdfs.DFSClient: Error Recovery for block BP-1347369012-**************-1444972147527:blk_1073743655_2878 in pipeline **************:50010, *************:50010: bad datanode *************:50010
2015-10-18 21:40:01,799 INFO [IPC Server handler 28 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.699806
2015-10-18 21:40:04,830 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.70433986
2015-10-18 21:40:07,861 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.7087207
2015-10-18 21:40:10,893 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.7128209
2015-10-18 21:40:13,940 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.7150202
2015-10-18 21:40:16,955 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.7186186
2015-10-18 21:40:19,987 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.72355247
2015-10-18 21:40:23,018 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.7271572
2015-10-18 21:40:26,065 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.72994816
2015-10-18 21:40:29,097 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.732655
2015-10-18 21:40:32,128 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.7330603
2015-10-18 21:40:35,175 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.7330603
2015-10-18 21:40:53,223 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.73697615
2015-10-18 21:40:56,254 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.74124587
2015-10-18 21:40:59,285 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.7447204
2015-10-18 21:41:02,332 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.7483722
2015-10-18 21:41:05,364 INFO [IPC Server handler 28 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.7520785
2015-10-18 21:41:08,395 INFO [IPC Server handler 28 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.75608796
2015-10-18 21:41:11,442 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.7607141
2015-10-18 21:41:14,473 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.7641019
2015-10-18 21:41:17,495 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.766775
2015-10-18 21:41:20,527 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.7705393
2015-10-18 21:41:23,558 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.77546287
2015-10-18 21:41:26,590 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.77995455
2015-10-18 21:41:29,623 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.7849144
2015-10-18 21:41:32,656 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.7886894
2015-10-18 21:41:35,703 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.7908781
2015-10-18 21:41:38,734 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.79461664
2015-10-18 21:41:41,765 INFO [IPC Server handler 26 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.79917246
2015-10-18 21:41:44,797 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.802456
2015-10-18 21:41:47,828 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.8058039
2015-10-18 21:41:50,860 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.8101599
2015-10-18 21:41:53,907 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.81258285
2015-10-18 21:41:56,938 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.815429
2015-10-18 21:41:59,985 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.81874216
2015-10-18 21:42:03,016 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.8226534
2015-10-18 21:42:06,048 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.82587254
2015-10-18 21:42:09,095 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.8303286
2015-10-18 21:42:12,126 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.83426785
2015-10-18 21:42:15,157 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.838685
2015-10-18 21:42:18,189 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.8420572
2015-10-18 21:42:21,236 INFO [IPC Server handler 28 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.8466581
2015-10-18 21:42:24,267 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.848887
2015-10-18 21:42:27,314 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.8521352
2015-10-18 21:42:30,346 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.85561854
2015-10-18 21:42:33,377 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.85963714
2015-10-18 21:42:36,408 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.8630679
2015-10-18 21:42:39,440 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.86672145
2015-10-18 21:42:42,487 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.870558
2015-10-18 21:42:45,518 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.8744003
2015-10-18 21:42:48,565 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.8788212
2015-10-18 21:42:51,596 INFO [IPC Server handler 26 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.8826169
2015-10-18 21:42:54,628 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.8862143
2015-10-18 21:42:57,659 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.89060605
2015-10-18 21:43:00,706 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.8940128
2015-10-18 21:43:03,738 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.8976714
2015-10-18 21:43:06,753 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9009332
2015-10-18 21:43:09,785 INFO [IPC Server handler 26 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9053479
2015-10-18 21:43:12,816 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9096421
2015-10-18 21:43:15,847 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.91298056
2015-10-18 21:43:18,894 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.91541576
2015-10-18 21:43:21,926 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9186959
2015-10-18 21:43:24,957 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.922374
2015-10-18 21:43:27,989 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9256211
2015-10-18 21:43:31,036 INFO [IPC Server handler 24 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9282993
2015-10-18 21:43:34,067 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.930333
2015-10-18 21:43:37,114 INFO [IPC Server handler 19 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.93169916
2015-10-18 21:43:40,145 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.93319803
2015-10-18 21:43:43,192 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9346241
2015-10-18 21:43:46,239 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.93610704
2015-10-18 21:43:49,271 INFO [IPC Server handler 21 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9375266
2015-10-18 21:43:52,318 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.93909997
2015-10-18 21:43:55,349 INFO [IPC Server handler 29 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9406339
2015-10-18 21:43:58,396 INFO [IPC Server handler 0 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9422091
2015-10-18 21:44:01,427 INFO [IPC Server handler 1 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.94380784
2015-10-18 21:44:04,474 INFO [IPC Server handler 25 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.94541407
2015-10-18 21:44:07,506 INFO [IPC Server handler 13 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.94699013
2015-10-18 21:44:10,553 INFO [IPC Server handler 4 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9481709
2015-10-18 21:44:13,600 INFO [IPC Server handler 9 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9495144
2015-10-18 21:44:16,632 INFO [IPC Server handler 17 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9510075
2015-10-18 21:44:19,679 INFO [IPC Server handler 28 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9527033
2015-10-18 21:44:22,710 INFO [IPC Server handler 10 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9544821
2015-10-18 21:44:25,757 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.957431
2015-10-18 21:44:28,788 INFO [IPC Server handler 16 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9588567
2015-10-18 21:44:31,820 INFO [IPC Server handler 23 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9614584
2015-10-18 21:44:34,851 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.96362066
2015-10-18 21:44:37,883 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9664773
2015-10-18 21:44:40,914 INFO [IPC Server handler 6 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9690905
2015-10-18 21:44:43,961 INFO [IPC Server handler 18 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.97105724
2015-10-18 21:44:46,992 INFO [IPC Server handler 12 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9732123
2015-10-18 21:44:50,039 INFO [IPC Server handler 3 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.97568583
2015-10-18 21:44:53,071 INFO [IPC Server handler 14 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.97821456
2015-10-18 21:44:56,118 INFO [IPC Server handler 7 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.98044544
2015-10-18 21:44:59,165 INFO [IPC Server handler 20 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9833456
2015-10-18 21:45:02,196 INFO [IPC Server handler 15 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9857366
2015-10-18 21:45:05,227 INFO [IPC Server handler 2 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9885617
2015-10-18 21:45:08,259 INFO [IPC Server handler 26 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.99280363
2015-10-18 21:45:11,290 INFO [IPC Server handler 22 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 0.9967867
2015-10-18 21:45:14,322 INFO [IPC Server handler 11 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 1.0
2015-10-18 21:45:14,540 INFO [IPC Server handler 8 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445175094696_0002_r_000000_0
2015-10-18 21:45:14,540 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_r_000000_0 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-18 21:45:14,540 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445175094696_0002_r_000000_0 given a go for committing the task output.
2015-10-18 21:45:14,540 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445175094696_0002_r_000000_0
2015-10-18 21:45:14,540 INFO [IPC Server handler 27 on 20324] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445175094696_0002_r_000000_0:true
2015-10-18 21:45:14,587 INFO [IPC Server handler 24 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0002_r_000000_0 is : 1.0
2015-10-18 21:45:14,587 INFO [IPC Server handler 5 on 20324] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0002_r_000000_0
2015-10-18 21:45:14,587 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_r_000000_0 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:45:14,587 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0002_01_000012 taskAttempt attempt_1445175094696_0002_r_000000_0
2015-10-18 21:45:14,587 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0002_r_000000_0
2015-10-18 21:45:14,603 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:45:14,618 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0002_r_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:45:14,618 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0002_r_000000_0
2015-10-18 21:45:14,618 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0002_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:45:14,618 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-18 21:45:14,618 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445175094696_0002Job Transitioned from RUNNING to COMMITTING
2015-10-18 21:45:14,618 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-18 21:45:14,697 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-18 21:45:14,697 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445175094696_0002Job Transitioned from COMMITTING to SUCCEEDED
2015-10-18 21:45:14,697 INFO [Thread-120] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-18 21:45:14,697 INFO [Thread-120] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-18 21:45:14,697 INFO [Thread-120] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-18 21:45:14,697 INFO [Thread-120] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-18 21:45:14,697 INFO [Thread-120] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-18 21:45:14,697 INFO [Thread-120] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-18 21:45:14,697 INFO [Thread-120] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-18 21:45:14,806 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445175094696_0002/job_1445175094696_0002_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0002-1445175179003-msrabi-word+count-1445175914697-10-1-SUCCEEDED-default-1445175187699.jhist_tmp
2015-10-18 21:45:15,165 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-18 21:45:16,165 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0002_01_000012
2015-10-18 21:45:16,165 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-18 21:45:16,165 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0002_r_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:45:36,041 INFO [Thread-123] org.apache.hadoop.hdfs.DFSClient: Exception in createBlockOutputStream
java.io.IOException: Bad connect ack with firstBadLink as *************:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.createBlockOutputStream(DFSOutputStream.java:1461)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.nextBlockOutputStream(DFSOutputStream.java:1362)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.run(DFSOutputStream.java:589)
2015-10-18 21:45:36,041 INFO [Thread-123] org.apache.hadoop.hdfs.DFSClient: Abandoning BP-1347369012-**************-1444972147527:blk_1073743699_2924
2015-10-18 21:45:36,057 INFO [Thread-123] org.apache.hadoop.hdfs.DFSClient: Excluding datanode *************:50010
2015-10-18 21:45:36,119 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0002-1445175179003-msrabi-word+count-1445175914697-10-1-SUCCEEDED-default-1445175187699.jhist_tmp
2015-10-18 21:45:36,119 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445175094696_0002/job_1445175094696_0002_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0002_conf.xml_tmp
2015-10-18 21:45:36,244 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0002_conf.xml_tmp
2015-10-18 21:45:36,244 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0002.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0002.summary
2015-10-18 21:45:36,244 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0002_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0002_conf.xml
2015-10-18 21:45:36,260 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0002-1445175179003-msrabi-word+count-1445175914697-10-1-SUCCEEDED-default-1445175187699.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0002-1445175179003-msrabi-word+count-1445175914697-10-1-SUCCEEDED-default-1445175187699.jhist
2015-10-18 21:45:36,260 INFO [Thread-120] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-18 21:45:36,276 INFO [Thread-120] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-18 21:45:36,276 INFO [Thread-120] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MSRA-SA-39.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445175094696_0002
2015-10-18 21:45:36,307 INFO [Thread-120] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-18 21:45:37,307 INFO [Thread-120] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-18 21:45:37,307 INFO [Thread-120] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445175094696_0002
2015-10-18 21:45:37,307 INFO [Thread-120] org.apache.hadoop.ipc.Server: Stopping server on 20324
2015-10-18 21:45:37,322 INFO [IPC Server listener on 20324] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 20324
2015-10-18 21:45:37,322 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-18 21:45:37,322 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
