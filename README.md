# 🔍 ONGC Knowledge Management System
## Offline AI-Powered Semantic Search for Technical Knowledge

This application provides an **offline semantic search engine** specifically designed for technical knowledge management in the oil and gas industry. It uses advanced AI techniques to understand the meaning of your queries, not just keywords, making it perfect for searching through technical documentation, research papers, and expert knowledge.

## 🌟 Key Features

- **🔍 Semantic Search**: Uses SBERT (Sentence-BERT) to understand meaning, not just keywords
- **⚡ Lightning Fast**: Optional FAISS integration for ultra-fast similarity search
- **🌐 Completely Offline**: No internet connection required after initial setup
- **📊 Analytics Dashboard**: Insights into your knowledge base
- **🎯 Relevance Scoring**: Shows similarity scores for each result
- **💻 Multiple Interfaces**: Both web UI (Streamlit) and command-line interface
- **🏷️ Smart Categorization**: Organized by technical domains (Drilling, Production, etc.)

## 🛠️ Technology Stack

- **SBERT**: Sentence-BERT for high-quality text embeddings
- **FAISS**: Facebook AI Similarity Search for fast vector operations
- **Streamlit**: Modern web interface
- **Click**: Command-line interface
- **Cosine Similarity**: For measuring semantic similarity
- **PyTorch**: Deep learning framework

## 📋 Requirements

- Python 3.8 or higher
- 4GB+ RAM (for model loading)
- 2GB+ disk space (for models and data)

## 🚀 Quick Start

### 1. Clone and Setup
```bash
# Navigate to your project directory
cd /path/to/your/project

# Install dependencies and setup
python setup.py
```

### 2. Run the Application

#### Option A: Web Interface (Recommended)
```bash
streamlit run streamlit_app.py
```
Then open your browser to `http://localhost:8501`

#### Option B: Command Line Interface
```bash
# Interactive mode
python cli_app.py search --interactive

# Direct search
python cli_app.py search "drilling fluid optimization"

# View statistics
python cli_app.py stats
```

## 📚 Sample Queries

Try these example queries to see the semantic search in action:

- `"drilling fluid optimization high temperature"`
- `"reservoir characterization well logs"`
- `"corrosion prevention offshore production"`
- `"seismic interpretation structural analysis"`
- `"enhanced oil recovery techniques"`
- `"wellbore stability shale formations"`

## 🏗️ Project Structure

```
├── streamlit_app.py          # Web interface
├── cli_app.py               # Command-line interface
├── semantic_search_engine.py # Core search engine
├── data_generator.py        # Sample data creation
├── config.py               # Configuration settings
├── setup.py                # Setup script
├── requirements.txt        # Python dependencies
├── data/                   # Knowledge articles
├── embeddings/            # Generated embeddings
├── models/               # Downloaded models
└── results/             # Search results and logs
```

## 🔧 Configuration

Edit `config.py` to customize:

- **Model Selection**: Choose different SBERT models
- **Search Parameters**: Adjust similarity thresholds
- **File Paths**: Customize data locations
- **UI Settings**: Modify interface appearance

### Available SBERT Models

- `all-MiniLM-L6-v2` (Default): Fast, good quality, 384 dimensions
- `all-mpnet-base-v2`: Better quality, larger size, 768 dimensions
- `paraphrase-MiniLM-L6-v2`: Optimized for paraphrase detection

## 📊 How It Works

1. **Text Preprocessing**: Articles are cleaned and prepared
2. **Embedding Generation**: SBERT converts text to numerical vectors
3. **Index Building**: FAISS creates an efficient search index
4. **Query Processing**: User queries are converted to embeddings
5. **Similarity Search**: Cosine similarity finds the most relevant articles
6. **Result Ranking**: Results are ranked by relevance score

## 🎯 Use Cases

Perfect for:
- **Technical Documentation Search**: Find specific procedures and guidelines
- **Research Paper Discovery**: Locate relevant academic papers
- **Expert Knowledge Retrieval**: Access lessons learned and best practices
- **Troubleshooting**: Find solutions to technical problems
- **Training Materials**: Discover educational content

## 📈 Performance

- **Search Speed**: < 100ms for typical queries (with FAISS)
- **Accuracy**: High semantic relevance due to SBERT embeddings
- **Scalability**: Handles thousands of documents efficiently
- **Memory Usage**: ~2GB for model + embeddings

## 🔍 Advanced Features

### FAISS Integration
For large datasets (1000+ articles), FAISS provides:
- 10x faster search performance
- Efficient memory usage
- Scalable to millions of documents

### Custom Datasets
Replace the sample data with your own:
1. Create JSON file with article structure
2. Update `SAMPLE_DATASET_FILE` in config.py
3. Run setup again to generate new embeddings

### Model Fine-tuning
For domain-specific improvements:
1. Collect domain-specific text pairs
2. Fine-tune SBERT model
3. Update model path in config.py

## 🐛 Troubleshooting

### Common Issues

**Model Download Fails**
- Check internet connection during first run
- Models are cached locally after first download

**Out of Memory**
- Reduce batch size in embedding generation
- Use smaller SBERT model (MiniLM instead of MPNet)

**Slow Search**
- Enable FAISS: `pip install faiss-cpu`
- Reduce number of articles for testing

**No Results Found**
- Lower similarity threshold in config
- Try different query phrasings
- Check if articles are loaded correctly

## 📝 Adding Your Own Data

Create a JSON file with this structure:
```json
[
  {
    "id": 1,
    "title": "Your Article Title",
    "content": "Full article content...",
    "category": "Technical Category",
    "tags": ["tag1", "tag2"],
    "author": "Author Name",
    "date": "2023-01-01"
  }
]
```

## 🤝 Contributing

This is a learning project for ONGC knowledge management. Feel free to:
- Add more technical articles
- Improve the search algorithms
- Enhance the user interface
- Add new features

## 📄 License

This project is for educational and internal use within ONGC.

## 🙏 Acknowledgments

- **Sentence-BERT**: For excellent text embeddings
- **FAISS**: For fast similarity search
- **Streamlit**: For the beautiful web interface
- **ONGC**: For the opportunity to build this knowledge management system

---

**Built with ❤️ for ONGC Knowledge Management**
