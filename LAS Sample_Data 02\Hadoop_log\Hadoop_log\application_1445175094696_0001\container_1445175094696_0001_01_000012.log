2015-10-18 21:35:00,845 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-18 21:35:00,986 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-18 21:35:00,986 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: ReduceTask metrics system started
2015-10-18 21:35:01,002 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-18 21:35:01,002 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445175094696_0001, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@7f0eb4b4)
2015-10-18 21:35:01,158 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-18 21:35:02,111 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445175094696_0001
2015-10-18 21:35:02,517 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-18 21:35:03,361 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-18 21:35:03,377 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@6ad3381f
2015-10-18 21:35:03,408 INFO [main] org.apache.hadoop.mapred.ReduceTask: Using ShuffleConsumerPlugin: org.apache.hadoop.mapreduce.task.reduce.Shuffle@4fad9bb2
2015-10-18 21:35:03,439 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: MergerManager: memoryLimit=130652568, maxSingleShuffleLimit=32663142, mergeThreshold=86230696, ioSortFactor=10, memToMemMergeOutputsThreshold=10
2015-10-18 21:35:03,455 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0001_r_000000_0 Thread started: EventFetcher for fetching Map Completion Events
2015-10-18 21:35:03,471 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 21:35:03,471 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 21:35:03,471 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0001_r_000000_0: Got 2 new map-outputs
2015-10-18 21:35:05,567 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0001&reduce=0&map=attempt_1445175094696_0001_m_000003_0 sent hash and received reply
2015-10-18 21:35:05,567 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0001_m_000003_0: Shuffling to disk since 216972750 is greater than maxSingleShuffleLimit (32663142)
2015-10-18 21:35:05,582 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0001_m_000003_0 decomp: 216972750 len: 216972754 to DISK
2015-10-18 21:35:07,989 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216972754 bytes from map-output for attempt_1445175094696_0001_m_000003_0
2015-10-18 21:35:21,714 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0001_r_000000_0: Got 1 new map-outputs
2015-10-18 21:35:53,791 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0001_r_000000_0: Got 1 new map-outputs
2015-10-18 21:35:53,791 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 50327ms
2015-10-18 21:35:53,791 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 3 to fetcher#5
2015-10-18 21:35:53,791 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 3 of 3 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 21:35:53,807 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0001&reduce=0&map=attempt_1445175094696_0001_m_000001_0,attempt_1445175094696_0001_m_000005_0,attempt_1445175094696_0001_m_000007_0 sent hash and received reply
2015-10-18 21:35:53,807 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0001_m_000001_0: Shuffling to disk since 217009502 is greater than maxSingleShuffleLimit (32663142)
2015-10-18 21:35:53,807 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0001_m_000001_0 decomp: 217009502 len: 217009506 to DISK
2015-10-18 21:35:54,869 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0001_r_000000_0: Got 2 new map-outputs
2015-10-18 21:35:54,869 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#4
2015-10-18 21:35:54,869 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#4
2015-10-18 21:35:54,885 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0001&reduce=0&map=attempt_1445175094696_0001_m_000009_0 sent hash and received reply
2015-10-18 21:35:54,885 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0001_m_000009_0: Shuffling to disk since 172334804 is greater than maxSingleShuffleLimit (32663142)
2015-10-18 21:35:54,916 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#4 about to shuffle output of map attempt_1445175094696_0001_m_000009_0 decomp: 172334804 len: 172334808 to DISK
2015-10-18 21:35:56,166 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217009506 bytes from map-output for attempt_1445175094696_0001_m_000001_0
2015-10-18 21:35:56,166 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0001_m_000005_0: Shuffling to disk since 216990140 is greater than maxSingleShuffleLimit (32663142)
2015-10-18 21:35:56,182 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0001_m_000005_0 decomp: 216990140 len: 216990144 to DISK
2015-10-18 21:35:57,932 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 172334808 bytes from map-output for attempt_1445175094696_0001_m_000009_0
2015-10-18 21:35:57,947 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#4 in 3077ms
2015-10-18 21:35:58,057 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216990144 bytes from map-output for attempt_1445175094696_0001_m_000005_0
2015-10-18 21:35:58,072 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0001_m_000007_0: Shuffling to disk since 216976206 is greater than maxSingleShuffleLimit (32663142)
2015-10-18 21:35:58,072 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0001_m_000007_0 decomp: 216976206 len: 216976210 to DISK
2015-10-18 21:35:59,604 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216976210 bytes from map-output for attempt_1445175094696_0001_m_000007_0
2015-10-18 21:35:59,604 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 5814ms
2015-10-18 21:35:59,604 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 21:35:59,604 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 21:35:59,619 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0001&reduce=0&map=attempt_1445175094696_0001_m_000008_0 sent hash and received reply
2015-10-18 21:35:59,619 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0001_m_000008_0: Shuffling to disk since 217015228 is greater than maxSingleShuffleLimit (32663142)
2015-10-18 21:35:59,619 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0001_m_000008_0 decomp: 217015228 len: 217015232 to DISK
2015-10-18 21:36:00,916 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217015232 bytes from map-output for attempt_1445175094696_0001_m_000008_0
2015-10-18 21:36:00,932 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 1322ms
2015-10-18 21:37:03,184 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0001_r_000000_0: Got 1 new map-outputs
2015-10-18 21:37:03,184 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 21:37:03,184 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 21:37:03,184 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0001&reduce=0&map=attempt_1445175094696_0001_m_000002_1 sent hash and received reply
2015-10-18 21:37:03,200 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0001_m_000002_1: Shuffling to disk since 216991624 is greater than maxSingleShuffleLimit (32663142)
2015-10-18 21:37:03,200 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0001_m_000002_1 decomp: 216991624 len: 216991628 to DISK
2015-10-18 21:37:07,997 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216991628 bytes from map-output for attempt_1445175094696_0001_m_000002_1
2015-10-18 21:37:08,013 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 4838ms
2015-10-18 21:37:18,466 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0001_r_000000_0: Got 1 new map-outputs
2015-10-18 21:37:18,466 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 21:37:18,466 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 21:37:18,466 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0001&reduce=0&map=attempt_1445175094696_0001_m_000004_1 sent hash and received reply
2015-10-18 21:37:18,466 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0001_m_000004_1: Shuffling to disk since 216999709 is greater than maxSingleShuffleLimit (32663142)
2015-10-18 21:37:18,482 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0001_m_000004_1 decomp: 216999709 len: 216999713 to DISK
2015-10-18 21:37:21,826 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216999713 bytes from map-output for attempt_1445175094696_0001_m_000004_1
2015-10-18 21:37:48,252 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0001_r_000000_0: Got 1 new map-outputs
2015-10-18 21:37:48,252 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#3
2015-10-18 21:37:48,252 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#3
2015-10-18 21:37:48,252 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 29784ms
2015-10-18 21:37:57,553 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0001&reduce=0&map=attempt_1445175094696_0001_m_000006_1 sent hash and received reply
2015-10-18 21:37:57,569 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0001_m_000006_1: Shuffling to disk since 217011663 is greater than maxSingleShuffleLimit (32663142)
2015-10-18 21:37:57,569 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445175094696_0001_m_000006_1 decomp: 217011663 len: 217011667 to DISK
2015-10-18 21:37:59,084 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217011667 bytes from map-output for attempt_1445175094696_0001_m_000006_1
2015-10-18 21:37:59,100 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#3 in 10851ms
2015-10-18 21:38:28,279 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0001_r_000000_0: Got 1 new map-outputs
2015-10-18 21:38:28,279 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#3
2015-10-18 21:38:28,279 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#3
2015-10-18 21:38:28,295 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0001&reduce=0&map=attempt_1445175094696_0001_m_000000_1 sent hash and received reply
2015-10-18 21:38:28,295 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0001_m_000000_1: Shuffling to disk since 216988123 is greater than maxSingleShuffleLimit (32663142)
2015-10-18 21:38:28,295 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445175094696_0001_m_000000_1 decomp: 216988123 len: 216988127 to DISK
2015-10-18 21:38:31,123 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216988127 bytes from map-output for attempt_1445175094696_0001_m_000000_1
2015-10-18 21:38:34,670 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#3 in 6401ms
2015-10-18 21:38:35,139 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: finalMerge called with 0 in-memory map-outputs and 10 on-disk map-outputs
2015-10-18 21:38:43,467 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 10 files, 2125289789 bytes from disk
2015-10-18 21:38:43,467 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 0 segments, 0 bytes from memory into reduce
2015-10-18 21:38:43,467 INFO [main] org.apache.hadoop.mapred.Merger: Merging 10 sorted segments
2015-10-18 21:38:44,827 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 10 segments left of total size: 2125289689 bytes
2015-10-18 21:38:45,280 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: mapred.skip.on is deprecated. Instead, use mapreduce.job.skiprecords
2015-10-18 21:42:17,798 INFO [DataStreamer for file /out/out1/_temporary/1/_temporary/attempt_1445175094696_0001_r_000000_0/part-r-00000] org.apache.hadoop.hdfs.DFSClient: Exception in createBlockOutputStream
java.io.IOException: Bad connect ack with firstBadLink as *************:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.createBlockOutputStream(DFSOutputStream.java:1461)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.nextBlockOutputStream(DFSOutputStream.java:1362)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.run(DFSOutputStream.java:589)
2015-10-18 21:42:17,798 INFO [DataStreamer for file /out/out1/_temporary/1/_temporary/attempt_1445175094696_0001_r_000000_0/part-r-00000] org.apache.hadoop.hdfs.DFSClient: Abandoning BP-1347369012-**************-1444972147527:blk_1073743674_2899
2015-10-18 21:42:17,814 INFO [DataStreamer for file /out/out1/_temporary/1/_temporary/attempt_1445175094696_0001_r_000000_0/part-r-00000] org.apache.hadoop.hdfs.DFSClient: Excluding datanode *************:50010
2015-10-18 21:45:29,760 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445175094696_0001_r_000000_0 is done. And is in the process of committing
2015-10-18 21:45:29,838 INFO [main] org.apache.hadoop.mapred.Task: Task attempt_1445175094696_0001_r_000000_0 is allowed to commit now
2015-10-18 21:45:29,853 INFO [main] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Saved output of task 'attempt_1445175094696_0001_r_000000_0' to hdfs://msra-sa-41:9000/out/out1/_temporary/1/task_1445175094696_0001_r_000000
2015-10-18 21:45:29,885 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445175094696_0001_r_000000_0' done.
2015-10-18 21:45:29,994 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Stopping ReduceTask metrics system...
2015-10-18 21:45:29,994 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: ReduceTask metrics system stopped.
2015-10-18 21:45:29,994 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: ReduceTask metrics system shutdown complete.
