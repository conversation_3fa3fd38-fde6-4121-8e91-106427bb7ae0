"""
Streamlit Web Application for K-Means Electrofacies Clustering
============================================================

Interactive web application for performing K-Means clustering on well log data
to classify electrofacies based on petrophysical properties.

Author: AI Assistant
Date: 2025-07-22
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import silhouette_score
import io
import base64
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# Simplified clustering class for Streamlit (non-blocking)
class StreamlitKMeans:
    """Simplified K-Means clustering for Streamlit app"""

    def __init__(self, n_clusters=5, random_state=42):
        self.n_clusters = n_clusters
        self.random_state = random_state
        self.kmeans = None
        self.scaler = None
        self.feature_columns = ['GR', 'RDEP', 'RHOB', 'NPHI', 'PEF', 'DTC', 'CALI']
        self.data = None
        self.scaled_data = None
        self.cluster_labels = None

    def preprocess_data(self, data, selected_features):
        """Preprocess data for clustering"""
        # Filter available features
        available_features = [col for col in selected_features if col in data.columns]

        if not available_features:
            raise ValueError("No selected features found in dataset")

        # Extract features
        features_df = data[available_features].copy()

        # Remove rows with too many missing values
        threshold = len(available_features) * 0.5
        features_df = features_df.dropna(thresh=threshold)

        # Fill remaining missing values with median
        for col in available_features:
            if features_df[col].isnull().sum() > 0:
                median_val = features_df[col].median()
                features_df[col].fillna(median_val, inplace=True)

        # Normalize features
        self.scaler = StandardScaler()
        self.scaled_data = self.scaler.fit_transform(features_df)

        # Update data
        self.data = data.loc[features_df.index].copy()
        self.feature_columns = available_features

        return len(features_df)

    def find_optimal_clusters(self, max_clusters=10):
        """Find optimal clusters efficiently"""
        silhouette_scores = []
        cluster_range = range(2, max_clusters + 1)

        # Choose algorithm based on dataset size
        use_mini_batch = len(self.scaled_data) > 100000

        for k in cluster_range:
            if use_mini_batch:
                # Use MiniBatchKMeans for very large datasets
                from sklearn.cluster import MiniBatchKMeans
                kmeans = MiniBatchKMeans(
                    n_clusters=k,
                    random_state=self.random_state,
                    n_init=3,
                    batch_size=min(2000, len(self.scaled_data) // 20)
                )
            else:
                # Use regular KMeans for smaller datasets
                kmeans = KMeans(
                    n_clusters=k,
                    random_state=self.random_state,
                    n_init=10 if len(self.scaled_data) < 50000 else 5
                )

            labels = kmeans.fit_predict(self.scaled_data)

            # Calculate silhouette score efficiently
            if len(self.scaled_data) > 15000:
                # Sample for silhouette calculation to speed up
                sample_size = min(15000, len(self.scaled_data))
                sample_indices = np.random.choice(
                    len(self.scaled_data),
                    size=sample_size,
                    replace=False
                )
                score = silhouette_score(
                    self.scaled_data[sample_indices],
                    labels[sample_indices]
                )
            else:
                score = silhouette_score(self.scaled_data, labels)

            silhouette_scores.append(score)

        # Find optimal k
        optimal_idx = np.argmax(silhouette_scores)
        optimal_k = cluster_range[optimal_idx]

        return optimal_k, silhouette_scores, cluster_range

    def fit_kmeans(self):
        """Fit K-Means model with smart algorithm selection"""
        # Choose algorithm based on dataset size for optimal performance
        if len(self.scaled_data) > 100000:
            # Use MiniBatchKMeans for very large datasets (>100K)
            from sklearn.cluster import MiniBatchKMeans
            self.kmeans = MiniBatchKMeans(
                n_clusters=self.n_clusters,
                random_state=self.random_state,
                n_init=3,
                max_iter=100,
                batch_size=min(2000, len(self.scaled_data) // 20)
            )
        else:
            # Use regular KMeans for smaller datasets
            n_init_value = 10 if len(self.scaled_data) < 50000 else 5
            self.kmeans = KMeans(
                n_clusters=self.n_clusters,
                random_state=self.random_state,
                n_init=n_init_value,
                max_iter=300
            )

        # Fit the model
        self.cluster_labels = self.kmeans.fit_predict(self.scaled_data)
        self.data['Cluster'] = self.cluster_labels

        # Calculate silhouette score efficiently
        if len(self.scaled_data) > 15000:
            # Use sampling for large datasets to speed up calculation
            sample_size = min(15000, len(self.scaled_data))
            sample_indices = np.random.choice(
                len(self.scaled_data),
                size=sample_size,
                replace=False
            )
            silhouette_avg = silhouette_score(
                self.scaled_data[sample_indices],
                self.cluster_labels[sample_indices]
            )
        else:
            silhouette_avg = silhouette_score(self.scaled_data, self.cluster_labels)

        return silhouette_avg

    def get_cluster_summary(self):
        """Get cluster statistics"""
        cluster_summary = []

        for cluster_id in range(self.n_clusters):
            cluster_data = self.data[self.data['Cluster'] == cluster_id]
            cluster_size = len(cluster_data)
            cluster_percentage = (cluster_size / len(self.data)) * 100

            cluster_stats = {}
            for feature in self.feature_columns:
                if feature in cluster_data.columns:
                    mean_val = cluster_data[feature].mean()
                    std_val = cluster_data[feature].std()
                    cluster_stats[feature] = {'mean': mean_val, 'std': std_val}

            cluster_summary.append({
                'cluster': cluster_id,
                'size': cluster_size,
                'percentage': cluster_percentage,
                'stats': cluster_stats
            })

        return cluster_summary

# Page configuration
st.set_page_config(
    page_title="Electrofacies K-Means Clustering",
    page_icon="🔬",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .sub-header {
        font-size: 1.5rem;
        color: #ff7f0e;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
    .metric-container {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
</style>
""", unsafe_allow_html=True)

def main():
    """Main Streamlit application"""

    # Title and description
    st.markdown('<h1 class="main-header">🔬 Electrofacies K-Means Clustering</h1>', unsafe_allow_html=True)
    st.markdown("""
    This application performs K-Means clustering on well log data to classify electrofacies
    based on petrophysical properties. Upload your well log data and explore different clustering configurations.
    """)

    # Sidebar for parameters
    st.sidebar.header("📊 Clustering Parameters")

    # File upload
    uploaded_file = st.sidebar.file_uploader(
        "Upload Well Log CSV File",
        type=['csv'],
        help="Upload a CSV file containing well log data with columns: DEPTH_MD, GR, RDEP, RHOB, NPHI, PEF, DTC, CALI"
    )

    # Default file option
    use_default = st.sidebar.checkbox("Use Default Dataset (train.csv)", value=True)

    if uploaded_file is not None or use_default:

        # Load data
        if uploaded_file is not None:
            # Try different delimiters
            delimiter = st.sidebar.selectbox("CSV Delimiter", [';', ',', '\t'], index=0)
            try:
                data = pd.read_csv(uploaded_file, sep=delimiter)
                st.sidebar.success(f"✅ File uploaded successfully! Shape: {data.shape}")
            except Exception as e:
                st.sidebar.error(f"❌ Error loading file: {str(e)}")
                return
        else:
            # Use default dataset
            try:
                data = pd.read_csv('train.csv', sep=';')
                st.sidebar.success(f"✅ Default dataset loaded! Shape: {data.shape}")
            except Exception as e:
                st.sidebar.error(f"❌ Error loading default dataset: {str(e)}")
                return

        # Display data info
        st.markdown('<h2 class="sub-header">📋 Dataset Overview</h2>', unsafe_allow_html=True)

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total Rows", f"{data.shape[0]:,}")
        with col2:
            st.metric("Total Columns", data.shape[1])
        with col3:
            st.metric("Memory Usage", f"{data.memory_usage(deep=True).sum() / 1024**2:.1f} MB")

        # Show data preview
        with st.expander("🔍 Data Preview", expanded=False):
            st.dataframe(data.head(10))

        # Feature selection
        st.sidebar.subheader("🎯 Feature Selection")
        available_features = ['GR', 'RDEP', 'RHOB', 'NPHI', 'PEF', 'DTC', 'CALI']
        available_features = [col for col in available_features if col in data.columns]

        selected_features = st.sidebar.multiselect(
            "Select Features for Clustering",
            available_features,
            default=available_features,
            help="Select the petrophysical features to use for clustering"
        )

        if not selected_features:
            st.sidebar.error("❌ Please select at least one feature!")
            return

        # Clustering parameters
        st.sidebar.subheader("⚙️ Clustering Settings")

        auto_clusters = st.sidebar.checkbox("Auto-determine optimal clusters", value=True)

        if auto_clusters:
            max_clusters = st.sidebar.slider("Maximum clusters to test", 2, 15, 10)
            n_clusters = None
        else:
            n_clusters = st.sidebar.slider("Number of clusters", 2, 15, 5)

        # Random State explanation and input
        st.sidebar.markdown("**🎲 Random State:**")
        random_state = st.sidebar.number_input(
            "Random State",
            value=42,
            help="Controls randomness for reproducible results. Same number = identical clustering every time"
        )

        with st.sidebar.expander("ℹ️ What is Random State?"):
            st.markdown("""
            **Random State** ensures reproducible results by controlling:
            - Initial cluster center placement
            - Data sampling randomness
            - Algorithm convergence paths

            **Examples:**
            - `42` (recommended): Standard seed
            - `123`: Alternative seed
            - Change it to explore different cluster arrangements
            """)

        # Dataset size and sampling
        st.sidebar.markdown("**📊 Dataset Processing:**")

        # Intelligent sample size recommendation
        if len(data) <= 10000:
            recommended_size = len(data)
            mode_suggestion = "Use full dataset"
        elif len(data) <= 50000:
            recommended_size = min(25000, len(data))
            mode_suggestion = "Balanced processing"
        elif len(data) <= 200000:
            recommended_size = min(50000, len(data))
            mode_suggestion = "High-quality sampling"
        else:
            recommended_size = min(100000, len(data))
            mode_suggestion = "Large dataset optimization"

        st.sidebar.info(f"💡 **Recommendation:** {mode_suggestion}")

        sample_size = st.sidebar.slider(
            "Sample Size",
            min_value=1000,
            max_value=min(500000, len(data)),  # Increased maximum to 500K
            value=recommended_size,
            help=f"Dataset has {len(data):,} rows. Larger samples = better quality but slower processing"
        )

        # Show processing estimates
        processing_time = sample_size / 8000  # Optimized estimate
        if processing_time > 60:
            time_str = f"{processing_time/60:.1f} minutes"
            color = "warning"
        else:
            time_str = f"{processing_time:.0f} seconds"
            color = "info"

        if color == "warning":
            st.sidebar.warning(f"⏱️ Estimated time: {time_str}")
        else:
            st.sidebar.info(f"⏱️ Estimated time: {time_str}")

        # Smart recommendations based on dataset size
        if len(data) > 200000:
            st.sidebar.warning("🔥 Very large dataset! Recommended sample: 50K-100K rows")
        elif len(data) > 50000:
            st.sidebar.info("📊 Large dataset. Recommended sample: 25K-50K rows")
        else:
            st.sidebar.success("✅ Dataset size is optimal for full processing")

        # Run clustering button
        if st.sidebar.button("🚀 Run K-Means Clustering", type="primary"):

            # Initialize progress bar
            progress_bar = st.progress(0)
            status_text = st.empty()

            try:
                # Smart data sampling
                if len(data) > sample_size:
                    status_text.text(f"Sampling {sample_size:,} rows from {len(data):,} total rows...")

                    # Use depth-based stratified sampling for better geological representation
                    if 'DEPTH_MD' in data.columns and len(data) > 10000:
                        try:
                            # Create depth bins and sample proportionally
                            depth_bins = pd.qcut(data['DEPTH_MD'], q=min(10, len(data)//1000), duplicates='drop')
                            sampled_data = data.groupby(depth_bins, group_keys=False).apply(
                                lambda x: x.sample(min(len(x), max(1, sample_size // 10)), random_state=random_state)
                            )
                            # Ensure we get the right sample size
                            if len(sampled_data) > sample_size:
                                sampled_data = sampled_data.sample(n=sample_size, random_state=random_state)
                            st.info(f"📊 Used depth-stratified sampling for better geological representation")
                        except:
                            # Fallback to regular sampling if stratified fails
                            sampled_data = data.sample(n=sample_size, random_state=random_state)
                            st.info(f"📊 Used random sampling")
                    else:
                        # Regular random sampling for smaller datasets or no depth column
                        sampled_data = data.sample(n=sample_size, random_state=random_state)
                        st.info(f"📊 Used random sampling")
                else:
                    sampled_data = data.copy()
                    st.info(f"📊 Using complete dataset ({len(data):,} rows)")

                # Initialize clustering class
                status_text.text("Initializing K-Means analyzer...")
                progress_bar.progress(10)

                kmeans_analyzer = StreamlitKMeans(
                    n_clusters=n_clusters if not auto_clusters else 5,
                    random_state=random_state
                )

                # Preprocess data
                status_text.text("Preprocessing data...")
                progress_bar.progress(20)

                clean_points = kmeans_analyzer.preprocess_data(sampled_data, selected_features)
                st.info(f"📊 Processing {clean_points:,} clean data points")

                # Find optimal clusters if requested
                if auto_clusters:
                    status_text.text("Finding optimal number of clusters...")
                    progress_bar.progress(40)

                    optimal_k, scores, cluster_range = kmeans_analyzer.find_optimal_clusters(max_clusters)
                    kmeans_analyzer.n_clusters = optimal_k

                    st.success(f"🎯 Optimal number of clusters: {optimal_k}")

                    # Show optimization plot
                    fig_opt = go.Figure()
                    fig_opt.add_trace(go.Scatter(
                        x=list(cluster_range),
                        y=scores,
                        mode='lines+markers',
                        name='Silhouette Score'
                    ))
                    fig_opt.update_layout(
                        title="Cluster Optimization",
                        xaxis_title="Number of Clusters",
                        yaxis_title="Silhouette Score"
                    )
                    st.plotly_chart(fig_opt, use_container_width=True)

                # Fit K-Means
                status_text.text("Fitting K-Means model...")
                progress_bar.progress(60)

                silhouette_avg = kmeans_analyzer.fit_kmeans()

                # Analyze clusters
                status_text.text("Analyzing clusters...")
                progress_bar.progress(80)

                cluster_summary = kmeans_analyzer.get_cluster_summary()

                # Complete
                status_text.text("Analysis completed!")
                progress_bar.progress(100)

                # Store results in session state
                st.session_state['kmeans_analyzer'] = kmeans_analyzer
                st.session_state['cluster_summary'] = cluster_summary
                st.session_state['silhouette_score'] = silhouette_avg
                st.session_state['analysis_complete'] = True

                st.success("✅ K-Means clustering completed successfully!")

            except Exception as e:
                st.error(f"❌ Error during clustering: {str(e)}")
                st.error("Please check your data format and try again.")
                return

        # Display results if analysis is complete
        if st.session_state.get('analysis_complete', False):

            kmeans_analyzer = st.session_state['kmeans_analyzer']
            cluster_summary = st.session_state['cluster_summary']

            # Cluster summary
            st.markdown('<h2 class="sub-header">📊 Cluster Analysis Results</h2>', unsafe_allow_html=True)

            # Metrics
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Number of Clusters", kmeans_analyzer.n_clusters)
            with col2:
                silhouette_avg = st.session_state.get('silhouette_score', 0)
                st.metric("Silhouette Score", f"{silhouette_avg:.3f}")
            with col3:
                st.metric("Data Points", len(kmeans_analyzer.data))
            with col4:
                st.metric("Features Used", len(kmeans_analyzer.feature_columns))

            # Cluster statistics table
            st.subheader("📈 Cluster Statistics")

            stats_data = []
            for summary in cluster_summary:
                row = {
                    'Cluster': summary['cluster'],
                    'Size': summary['size'],
                    'Percentage': f"{summary['percentage']:.1f}%"
                }
                for feature in kmeans_analyzer.feature_columns:
                    if feature in summary['stats']:
                        mean_val = summary['stats'][feature]['mean']
                        std_val = summary['stats'][feature]['std']
                        row[f'{feature}_mean'] = f"{mean_val:.2f}"
                        row[f'{feature}_std'] = f"{std_val:.2f}"
                stats_data.append(row)

            stats_df = pd.DataFrame(stats_data)
            st.dataframe(stats_df, use_container_width=True)

            # Visualization tabs
            st.markdown('<h2 class="sub-header">📈 Interactive Visualizations</h2>', unsafe_allow_html=True)

            tab1, tab2, tab3 = st.tabs(["📊 Log Curves", "🎯 Crossplots", "📏 Depth Track"])

            with tab1:
                st.subheader("Well Log Curves with Cluster Overlays")

                # Create log curves plot
                colors = px.colors.qualitative.Set1[:kmeans_analyzer.n_clusters]

                # Select curves to display
                available_curves = kmeans_analyzer.feature_columns
                display_curves = st.multiselect(
                    "Select log curves to display:",
                    available_curves,
                    default=available_curves[:4] if len(available_curves) >= 4 else available_curves
                )

                if display_curves:
                    fig = make_subplots(
                        rows=1, cols=len(display_curves),
                        subplot_titles=display_curves,
                        shared_yaxes=True,
                        horizontal_spacing=0.05
                    )

                    for i, curve in enumerate(display_curves, 1):
                        for cluster_id in range(kmeans_analyzer.n_clusters):
                            cluster_data = kmeans_analyzer.data[kmeans_analyzer.data['Cluster'] == cluster_id]

                            fig.add_trace(
                                go.Scatter(
                                    x=cluster_data[curve],
                                    y=cluster_data['DEPTH_MD'],
                                    mode='markers',
                                    marker=dict(
                                        color=colors[cluster_id],
                                        size=2,
                                        opacity=0.6
                                    ),
                                    name=f'Cluster {cluster_id}' if i == 1 else None,
                                    showlegend=True if i == 1 else False,
                                    legendgroup=f'cluster_{cluster_id}'
                                ),
                                row=1, col=i
                            )

                    fig.update_layout(height=800, title="Well Log Curves with K-Means Clusters")
                    fig.update_yaxes(autorange='reversed', title_text='Depth (MD)')

                    st.plotly_chart(fig, use_container_width=True)

            with tab2:
                st.subheader("Petrophysical Crossplots")

                # Crossplot selection
                available_features = kmeans_analyzer.feature_columns
                col1, col2 = st.columns(2)
                with col1:
                    x_feature = st.selectbox("X-axis feature:", available_features, index=0)
                with col2:
                    y_feature = st.selectbox("Y-axis feature:", available_features, index=1 if len(available_features) > 1 else 0)

                if x_feature != y_feature:
                    fig = go.Figure()

                    for cluster_id in range(kmeans_analyzer.n_clusters):
                        cluster_data = kmeans_analyzer.data[kmeans_analyzer.data['Cluster'] == cluster_id]

                        fig.add_trace(
                            go.Scatter(
                                x=cluster_data[x_feature],
                                y=cluster_data[y_feature],
                                mode='markers',
                                marker=dict(
                                    color=colors[cluster_id],
                                    size=4,
                                    opacity=0.6
                                ),
                                name=f'Cluster {cluster_id}'
                            )
                        )

                    fig.update_layout(
                        title=f'{y_feature} vs {x_feature}',
                        xaxis_title=x_feature,
                        yaxis_title=y_feature,
                        height=600
                    )

                    st.plotly_chart(fig, use_container_width=True)

            with tab3:
                st.subheader("Cluster vs Depth Track")

                fig = go.Figure()

                for cluster_id in range(kmeans_analyzer.n_clusters):
                    cluster_data = kmeans_analyzer.data[kmeans_analyzer.data['Cluster'] == cluster_id]

                    fig.add_trace(
                        go.Scatter(
                            x=[cluster_id] * len(cluster_data),
                            y=cluster_data['DEPTH_MD'],
                            mode='markers',
                            marker=dict(
                                color=colors[cluster_id],
                                size=3,
                                opacity=0.8
                            ),
                            name=f'Cluster {cluster_id}',
                            hovertemplate=f'Cluster {cluster_id}<br>Depth: %{{y:.2f}} m<extra></extra>'
                        )
                    )

                fig.update_layout(
                    title='Electrofacies Clusters vs Depth',
                    xaxis_title='Cluster ID',
                    yaxis_title='Depth (MD)',
                    yaxis=dict(autorange='reversed'),
                    height=800
                )

                st.plotly_chart(fig, use_container_width=True)

            # Download results
            st.markdown('<h2 class="sub-header">💾 Download Results</h2>', unsafe_allow_html=True)

            # Prepare download data
            clustered_data = kmeans_analyzer.data.copy()

            # Convert to CSV
            csv_buffer = io.StringIO()
            clustered_data.to_csv(csv_buffer, index=False, sep=';')
            csv_data = csv_buffer.getvalue()

            st.download_button(
                label="📥 Download Clustered Dataset (CSV)",
                data=csv_data,
                file_name=f"clustered_electrofacies_{kmeans_analyzer.n_clusters}clusters.csv",
                mime="text/csv",
                help="Download the dataset with cluster labels added"
            )

    else:
        st.info("👆 Please upload a CSV file or use the default dataset to get started!")

if __name__ == "__main__":
    # Initialize session state
    if 'analysis_complete' not in st.session_state:
        st.session_state['analysis_complete'] = False

    main()
