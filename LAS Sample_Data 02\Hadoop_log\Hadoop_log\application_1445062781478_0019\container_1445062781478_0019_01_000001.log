2015-10-17 17:06:25,314 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445062781478_0019_000001
2015-10-17 17:06:26,141 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 17:06:26,141 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 19 cluster_timestamp: 1445062781478 } attemptId: 1 } keyId: 471522253)
2015-10-17 17:06:26,408 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 17:06:27,284 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 17:06:27,331 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 17:06:27,357 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 17:06:27,358 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 17:06:27,359 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 17:06:27,360 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 17:06:27,361 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 17:06:27,366 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 17:06:27,367 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 17:06:27,368 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 17:06:27,404 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 17:06:27,424 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 17:06:27,443 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 17:06:27,452 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 17:06:27,494 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 17:06:27,709 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 17:06:27,756 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 17:06:27,756 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 17:06:27,763 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445062781478_0019 to jobTokenSecretManager
2015-10-17 17:06:27,898 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445062781478_0019 because: not enabled; too many maps; too much input;
2015-10-17 17:06:27,942 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445062781478_0019 = 1256521728. Number of splits = 10
2015-10-17 17:06:27,945 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445062781478_0019 = 1
2015-10-17 17:06:27,945 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0019Job Transitioned from NEW to INITED
2015-10-17 17:06:27,948 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445062781478_0019.
2015-10-17 17:06:28,027 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 17:06:28,050 INFO [Socket Reader #1 for port 19657] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 19657
2015-10-17 17:06:28,076 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 17:06:28,077 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 17:06:28,077 INFO [IPC Server listener on 19657] org.apache.hadoop.ipc.Server: IPC Server listener on 19657: starting
2015-10-17 17:06:28,079 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MSRA-SA-41.fareast.corp.microsoft.com/**************:19657
2015-10-17 17:06:28,290 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 17:06:28,300 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 17:06:28,324 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 17:06:28,335 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 17:06:28,336 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 17:06:28,343 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 17:06:28,343 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 17:06:28,365 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 19664
2015-10-17 17:06:28,366 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 17:06:28,439 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\2\Jetty_0_0_0_0_19664_mapreduce____8otwx9\webapp
2015-10-17 17:06:28,796 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:19664
2015-10-17 17:06:28,796 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 19664
2015-10-17 17:06:29,451 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 17:06:29,457 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445062781478_0019
2015-10-17 17:06:29,459 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 17:06:29,464 INFO [Socket Reader #1 for port 19667] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 19667
2015-10-17 17:06:29,473 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 17:06:29,474 INFO [IPC Server listener on 19667] org.apache.hadoop.ipc.Server: IPC Server listener on 19667: starting
2015-10-17 17:06:29,506 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 17:06:29,506 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 17:06:29,506 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 17:06:29,581 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at MSRA-SA-41/**************:8030
2015-10-17 17:06:29,643 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 17:06:29,643 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 17:06:29,646 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 17:06:29,648 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 17:06:29,653 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0019Job Transitioned from INITED to SETUP
2015-10-17 17:06:29,654 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 17:06:29,661 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0019Job Transitioned from SETUP to RUNNING
2015-10-17 17:06:29,675 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:29,676 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:29,678 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:06:29,679 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:29,679 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:29,679 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:06:29,679 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:29,680 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:29,680 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:06:29,682 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:29,682 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:29,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:06:29,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:29,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:29,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:06:29,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:29,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:29,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:06:29,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:29,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:29,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:06:29,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:29,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:29,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:06:29,685 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:29,685 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:29,685 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:06:29,685 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:29,685 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:29,685 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:06:29,686 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:06:29,687 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:06:29,687 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:06:29,687 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:06:29,687 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:06:29,688 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:06:29,688 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:06:29,688 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:06:29,688 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:06:29,688 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:06:29,688 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:06:29,688 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:06:29,689 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 17:06:29,696 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 17:06:29,726 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445062781478_0019, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0019/job_1445062781478_0019_1.jhist
2015-10-17 17:06:30,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 17:06:30,720 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0019: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:23552, vCores:-4> knownNMs=4
2015-10-17 17:06:30,728 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:23552, vCores:-4>
2015-10-17 17:06:30,728 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 17:06:31,772 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 10
2015-10-17 17:06:31,775 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0019_01_000002 to attempt_1445062781478_0019_m_000000_0
2015-10-17 17:06:31,778 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0019_01_000003 to attempt_1445062781478_0019_m_000001_0
2015-10-17 17:06:31,779 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0019_01_000004 to attempt_1445062781478_0019_m_000002_0
2015-10-17 17:06:31,779 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0019_01_000005 to attempt_1445062781478_0019_m_000003_0
2015-10-17 17:06:31,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0019_01_000006 to attempt_1445062781478_0019_m_000004_0
2015-10-17 17:06:31,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0019_01_000007 to attempt_1445062781478_0019_m_000005_0
2015-10-17 17:06:31,781 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0019_01_000008 to attempt_1445062781478_0019_m_000006_0
2015-10-17 17:06:31,781 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0019_01_000009 to attempt_1445062781478_0019_m_000007_0
2015-10-17 17:06:31,782 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0019_01_000010 to attempt_1445062781478_0019_m_000008_0
2015-10-17 17:06:31,782 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0019_01_000011 to attempt_1445062781478_0019_m_000009_0
2015-10-17 17:06:31,782 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:13312, vCores:-14>
2015-10-17 17:06:31,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 17:06:31,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 17:06:31,875 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:31,905 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0019/job.jar
2015-10-17 17:06:31,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0019/job.xml
2015-10-17 17:06:31,912 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 17:06:31,912 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 17:06:31,912 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 17:06:32,010 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:06:32,016 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:32,017 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:06:32,018 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:32,019 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:06:32,019 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:32,021 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:06:32,021 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:32,023 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:06:32,023 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:32,024 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:06:32,025 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:32,026 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:06:32,027 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:32,028 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:06:32,029 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:32,030 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:06:32,030 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:06:32,032 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:06:32,035 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0019_01_000002 taskAttempt attempt_1445062781478_0019_m_000000_0
2015-10-17 17:06:32,035 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0019_01_000003 taskAttempt attempt_1445062781478_0019_m_000001_0
2015-10-17 17:06:32,035 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0019_01_000004 taskAttempt attempt_1445062781478_0019_m_000002_0
2015-10-17 17:06:32,036 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0019_01_000005 taskAttempt attempt_1445062781478_0019_m_000003_0
2015-10-17 17:06:32,036 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0019_01_000006 taskAttempt attempt_1445062781478_0019_m_000004_0
2015-10-17 17:06:32,036 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0019_01_000007 taskAttempt attempt_1445062781478_0019_m_000005_0
2015-10-17 17:06:32,037 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0019_01_000008 taskAttempt attempt_1445062781478_0019_m_000006_0
2015-10-17 17:06:32,037 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0019_01_000009 taskAttempt attempt_1445062781478_0019_m_000007_0
2015-10-17 17:06:32,037 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0019_01_000010 taskAttempt attempt_1445062781478_0019_m_000008_0
2015-10-17 17:06:32,038 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0019_01_000011 taskAttempt attempt_1445062781478_0019_m_000009_0
2015-10-17 17:06:32,040 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0019_m_000005_0
2015-10-17 17:06:32,040 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0019_m_000000_0
2015-10-17 17:06:32,041 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0019_m_000008_0
2015-10-17 17:06:32,041 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0019_m_000006_0
2015-10-17 17:06:32,041 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0019_m_000002_0
2015-10-17 17:06:32,041 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0019_m_000004_0
2015-10-17 17:06:32,040 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0019_m_000001_0
2015-10-17 17:06:32,040 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0019_m_000009_0
2015-10-17 17:06:32,040 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0019_m_000003_0
2015-10-17 17:06:32,040 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0019_m_000007_0
2015-10-17 17:06:32,042 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:06:32,085 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:06:32,087 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 17:06:32,089 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:06:32,090 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 17:06:32,092 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:06:32,094 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 17:06:32,096 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:06:32,098 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:06:32,099 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 17:06:32,196 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0019_m_000001_0 : 13562
2015-10-17 17:06:32,196 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0019_m_000002_0 : 13562
2015-10-17 17:06:32,196 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0019_m_000003_0 : 13562
2015-10-17 17:06:32,196 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0019_m_000006_0 : 13562
2015-10-17 17:06:32,196 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0019_m_000009_0 : 13562
2015-10-17 17:06:32,196 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0019_m_000004_0 : 13562
2015-10-17 17:06:32,196 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0019_m_000000_0 : 13562
2015-10-17 17:06:32,196 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0019_m_000008_0 : 13562
2015-10-17 17:06:32,196 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0019_m_000007_0 : 13562
2015-10-17 17:06:32,196 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0019_m_000005_0 : 13562
2015-10-17 17:06:32,199 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0019_m_000000_0] using containerId: [container_1445062781478_0019_01_000002 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 17:06:32,205 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:06:32,206 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0019_m_000001_0] using containerId: [container_1445062781478_0019_01_000003 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 17:06:32,206 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:06:32,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0019_m_000009_0] using containerId: [container_1445062781478_0019_01_000011 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 17:06:32,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:06:32,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0019_m_000004_0] using containerId: [container_1445062781478_0019_01_000006 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 17:06:32,208 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:06:32,208 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0019_m_000008_0] using containerId: [container_1445062781478_0019_01_000010 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 17:06:32,209 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:06:32,209 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0019_m_000006_0] using containerId: [container_1445062781478_0019_01_000008 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 17:06:32,210 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:06:32,210 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0019_m_000007_0] using containerId: [container_1445062781478_0019_01_000009 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 17:06:32,211 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:06:32,211 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0019_m_000003_0] using containerId: [container_1445062781478_0019_01_000005 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 17:06:32,212 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:06:32,212 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0019_m_000002_0] using containerId: [container_1445062781478_0019_01_000004 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 17:06:32,213 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:06:32,213 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0019_m_000005_0] using containerId: [container_1445062781478_0019_01_000007 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 17:06:32,214 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:06:32,214 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0019_m_000000
2015-10-17 17:06:32,214 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:06:32,215 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0019_m_000001
2015-10-17 17:06:32,215 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:06:32,215 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0019_m_000009
2015-10-17 17:06:32,216 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:06:32,216 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0019_m_000004
2015-10-17 17:06:32,216 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:06:32,217 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0019_m_000008
2015-10-17 17:06:32,217 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:06:32,217 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0019_m_000006
2015-10-17 17:06:32,217 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:06:32,218 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0019_m_000007
2015-10-17 17:06:32,218 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:06:32,218 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0019_m_000003
2015-10-17 17:06:32,219 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:06:32,219 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0019_m_000002
2015-10-17 17:06:32,219 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:06:32,220 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0019_m_000005
2015-10-17 17:06:32,220 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:06:32,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0019: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:13312, vCores:-14> knownNMs=4
2015-10-17 17:06:34,818 INFO [Socket Reader #1 for port 19667] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0019 (auth:SIMPLE)
2015-10-17 17:06:34,820 INFO [Socket Reader #1 for port 19667] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0019 (auth:SIMPLE)
2015-10-17 17:06:34,825 INFO [Socket Reader #1 for port 19667] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0019 (auth:SIMPLE)
2015-10-17 17:06:34,828 INFO [Socket Reader #1 for port 19667] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0019 (auth:SIMPLE)
2015-10-17 17:06:34,836 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0019_m_000005 asked for a task
2015-10-17 17:06:34,836 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0019_m_000005 given task: attempt_1445062781478_0019_m_000003_0
2015-10-17 17:06:34,837 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0019_m_000004 asked for a task
2015-10-17 17:06:34,837 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0019_m_000004 given task: attempt_1445062781478_0019_m_000002_0
2015-10-17 17:06:34,837 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0019_m_000002 asked for a task
2015-10-17 17:06:34,837 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0019_m_000002 given task: attempt_1445062781478_0019_m_000000_0
2015-10-17 17:06:34,840 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0019_m_000003 asked for a task
2015-10-17 17:06:34,840 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0019_m_000003 given task: attempt_1445062781478_0019_m_000001_0
2015-10-17 17:06:36,453 INFO [Socket Reader #1 for port 19667] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0019 (auth:SIMPLE)
2015-10-17 17:06:36,457 INFO [Socket Reader #1 for port 19667] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0019 (auth:SIMPLE)
2015-10-17 17:06:36,466 INFO [Socket Reader #1 for port 19667] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0019 (auth:SIMPLE)
2015-10-17 17:06:36,471 INFO [Socket Reader #1 for port 19667] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0019 (auth:SIMPLE)
2015-10-17 17:06:36,478 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0019_m_000010 asked for a task
2015-10-17 17:06:36,478 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0019_m_000010 given task: attempt_1445062781478_0019_m_000008_0
2015-10-17 17:06:36,480 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0019_m_000008 asked for a task
2015-10-17 17:06:36,481 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0019_m_000008 given task: attempt_1445062781478_0019_m_000006_0
2015-10-17 17:06:36,486 INFO [Socket Reader #1 for port 19667] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0019 (auth:SIMPLE)
2015-10-17 17:06:36,490 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0019_m_000006 asked for a task
2015-10-17 17:06:36,491 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0019_m_000006 given task: attempt_1445062781478_0019_m_000004_0
2015-10-17 17:06:36,494 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0019_m_000009 asked for a task
2015-10-17 17:06:36,494 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0019_m_000009 given task: attempt_1445062781478_0019_m_000007_0
2015-10-17 17:06:36,510 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0019_m_000011 asked for a task
2015-10-17 17:06:36,510 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0019_m_000011 given task: attempt_1445062781478_0019_m_000009_0
2015-10-17 17:06:36,524 INFO [Socket Reader #1 for port 19667] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0019 (auth:SIMPLE)
2015-10-17 17:06:36,546 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0019_m_000007 asked for a task
2015-10-17 17:06:36,546 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0019_m_000007 given task: attempt_1445062781478_0019_m_000005_0
2015-10-17 17:06:41,871 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.106493875
2015-10-17 17:06:41,872 INFO [IPC Server handler 14 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.10660437
2015-10-17 17:06:41,875 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.1066108
2015-10-17 17:06:41,877 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.10635664
2015-10-17 17:06:44,044 INFO [IPC Server handler 15 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.10530092
2015-10-17 17:06:44,063 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.1051141
2015-10-17 17:06:44,076 INFO [IPC Server handler 28 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.10681946
2015-10-17 17:06:44,086 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000009_0 is : 0.2869474
2015-10-17 17:06:44,092 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.10680563
2015-10-17 17:06:44,106 INFO [IPC Server handler 23 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.10685723
2015-10-17 17:06:44,903 INFO [IPC Server handler 15 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.10660437
2015-10-17 17:06:44,903 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.10635664
2015-10-17 17:06:44,904 INFO [IPC Server handler 28 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.1066108
2015-10-17 17:06:44,904 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.106493875
2015-10-17 17:06:47,045 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.106881365
2015-10-17 17:06:47,081 INFO [IPC Server handler 23 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.106964506
2015-10-17 17:06:47,092 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000009_0 is : 0.295472
2015-10-17 17:06:47,092 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.10681946
2015-10-17 17:06:47,105 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.10680563
2015-10-17 17:06:47,106 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.10685723
2015-10-17 17:06:47,927 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.11248493
2015-10-17 17:06:47,936 INFO [IPC Server handler 23 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.10635664
2015-10-17 17:06:47,939 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.1066108
2015-10-17 17:06:47,946 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.10660437
2015-10-17 17:06:50,060 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.106881365
2015-10-17 17:06:50,092 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.106964506
2015-10-17 17:06:50,108 INFO [IPC Server handler 24 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000009_0 is : 0.295472
2015-10-17 17:06:50,109 INFO [IPC Server handler 9 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.10681946
2015-10-17 17:06:50,110 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.10685723
2015-10-17 17:06:50,111 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.10680563
2015-10-17 17:06:50,953 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.18438563
2015-10-17 17:06:50,958 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.15274122
2015-10-17 17:06:50,967 INFO [IPC Server handler 24 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.16094975
2015-10-17 17:06:50,969 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.18070504
2015-10-17 17:06:53,078 INFO [IPC Server handler 9 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.106881365
2015-10-17 17:06:53,112 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.106964506
2015-10-17 17:06:53,140 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.113882355
2015-10-17 17:06:53,140 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.1101773
2015-10-17 17:06:53,143 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.11319657
2015-10-17 17:06:53,145 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000009_0 is : 0.295472
2015-10-17 17:06:53,973 INFO [IPC Server handler 9 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.19209063
2015-10-17 17:06:53,974 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.19211523
2015-10-17 17:06:53,985 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.19158794
2015-10-17 17:06:53,986 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.19212553
2015-10-17 17:06:56,092 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.15665239
2015-10-17 17:06:56,128 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.1475532
2015-10-17 17:06:56,159 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.18574089
2015-10-17 17:06:56,160 INFO [IPC Server handler 19 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.1832373
2015-10-17 17:06:56,163 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.18962245
2015-10-17 17:06:56,164 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000009_0 is : 0.40627488
2015-10-17 17:06:56,996 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.19211523
2015-10-17 17:06:57,011 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.19209063
2015-10-17 17:06:57,014 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.19212553
2015-10-17 17:06:57,016 INFO [IPC Server handler 19 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.19158794
2015-10-17 17:06:59,107 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.19258286
2015-10-17 17:06:59,139 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.19266446
2015-10-17 17:06:59,172 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.19255035
2015-10-17 17:06:59,173 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.19242907
2015-10-17 17:06:59,173 INFO [IPC Server handler 1 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.19247705
2015-10-17 17:06:59,174 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000009_0 is : 0.5323719
2015-10-17 17:07:00,015 INFO [IPC Server handler 19 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.19211523
2015-10-17 17:07:00,029 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.24016695
2015-10-17 17:07:00,030 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.21195112
2015-10-17 17:07:00,032 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.19158794
2015-10-17 17:07:02,124 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.19258286
2015-10-17 17:07:02,155 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.19266446
2015-10-17 17:07:02,191 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.19255035
2015-10-17 17:07:02,192 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.19247705
2015-10-17 17:07:02,195 INFO [IPC Server handler 14 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.19242907
2015-10-17 17:07:02,196 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000009_0 is : 0.5323719
2015-10-17 17:07:03,033 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.23701967
2015-10-17 17:07:03,047 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.27765483
2015-10-17 17:07:03,047 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.27772525
2015-10-17 17:07:03,049 INFO [IPC Server handler 1 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.23384856
2015-10-17 17:07:05,139 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.19258286
2015-10-17 17:07:05,174 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.19266446
2015-10-17 17:07:05,202 INFO [IPC Server handler 28 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000009_0 is : 0.5323719
2015-10-17 17:07:05,203 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.19242907
2015-10-17 17:07:05,211 INFO [IPC Server handler 15 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.19255035
2015-10-17 17:07:05,215 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.19247705
2015-10-17 17:07:06,052 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.27776006
2015-10-17 17:07:06,065 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.27765483
2015-10-17 17:07:06,066 INFO [IPC Server handler 14 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.27772525
2015-10-17 17:07:06,066 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.27696857
2015-10-17 17:07:08,163 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.20594423
2015-10-17 17:07:08,191 INFO [IPC Server handler 28 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.20259006
2015-10-17 17:07:08,228 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000009_0 is : 0.53471047
2015-10-17 17:07:08,229 INFO [IPC Server handler 23 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.23693843
2015-10-17 17:07:08,229 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.2598128
2015-10-17 17:07:08,234 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.26957324
2015-10-17 17:07:09,085 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.27776006
2015-10-17 17:07:09,086 INFO [IPC Server handler 28 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.27772525
2015-10-17 17:07:09,097 INFO [IPC Server handler 15 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.27696857
2015-10-17 17:07:09,102 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.27765483
2015-10-17 17:07:10,022 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000009_0 is : 0.53471047
2015-10-17 17:07:11,168 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.27811313
2015-10-17 17:07:11,199 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.2783809
2015-10-17 17:07:11,234 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.27825075
2015-10-17 17:07:11,235 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000009_0 is : 0.667
2015-10-17 17:07:11,235 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.2781602
2015-10-17 17:07:11,245 INFO [IPC Server handler 24 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.27813601
2015-10-17 17:07:12,105 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.27776006
2015-10-17 17:07:12,124 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.27696857
2015-10-17 17:07:12,128 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.27772525
2015-10-17 17:07:12,129 INFO [IPC Server handler 23 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.3337486
2015-10-17 17:07:14,170 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.27811313
2015-10-17 17:07:14,200 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.2783809
2015-10-17 17:07:14,250 INFO [IPC Server handler 24 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000009_0 is : 0.667
2015-10-17 17:07:14,252 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.27813601
2015-10-17 17:07:14,253 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.27825075
2015-10-17 17:07:14,253 INFO [IPC Server handler 9 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.2781602
2015-10-17 17:07:15,136 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.31844702
2015-10-17 17:07:15,153 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.35140306
2015-10-17 17:07:15,154 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.2952839
2015-10-17 17:07:15,155 INFO [IPC Server handler 24 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.36323506
2015-10-17 17:07:17,174 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.27811313
2015-10-17 17:07:17,203 INFO [IPC Server handler 9 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.2783809
2015-10-17 17:07:17,269 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.27813601
2015-10-17 17:07:17,271 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.2781602
2015-10-17 17:07:17,272 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.27825075
2015-10-17 17:07:17,272 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000009_0 is : 0.667
2015-10-17 17:07:18,155 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.36319977
2015-10-17 17:07:18,171 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.36317363
2015-10-17 17:07:18,171 INFO [IPC Server handler 9 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.3624012
2015-10-17 17:07:18,172 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.36323506
2015-10-17 17:07:20,188 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.27811313
2015-10-17 17:07:20,215 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.2783809
2015-10-17 17:07:20,284 INFO [IPC Server handler 19 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.32920817
2015-10-17 17:07:20,284 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.29674622
2015-10-17 17:07:20,286 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.33944678
2015-10-17 17:07:20,286 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000009_0 is : 0.7302472
2015-10-17 17:07:21,174 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.36319977
2015-10-17 17:07:21,190 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.3624012
2015-10-17 17:07:21,190 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.36323506
2015-10-17 17:07:21,191 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.36317363
2015-10-17 17:07:23,207 INFO [IPC Server handler 19 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.35081092
2015-10-17 17:07:23,217 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.3511876
2015-10-17 17:07:23,298 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.3638923
2015-10-17 17:07:23,298 INFO [IPC Server handler 1 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000009_0 is : 0.79934967
2015-10-17 17:07:23,303 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.36388028
2015-10-17 17:07:23,303 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.36390656
2015-10-17 17:07:24,194 INFO [IPC Server handler 19 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.36319977
2015-10-17 17:07:24,209 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.4486067
2015-10-17 17:07:24,210 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.40093255
2015-10-17 17:07:24,210 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.3624012
2015-10-17 17:07:26,216 INFO [IPC Server handler 1 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.3637686
2015-10-17 17:07:26,216 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.36404583
2015-10-17 17:07:26,315 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.36390656
2015-10-17 17:07:26,315 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.3638923
2015-10-17 17:07:26,321 INFO [IPC Server handler 14 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.36388028
2015-10-17 17:07:26,322 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000009_0 is : 0.90066063
2015-10-17 17:07:27,222 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.44650277
2015-10-17 17:07:27,230 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.44859612
2015-10-17 17:07:27,237 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.4486067
2015-10-17 17:07:27,246 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.43356818
2015-10-17 17:07:29,218 INFO [IPC Server handler 1 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.36404583
2015-10-17 17:07:29,221 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.3637686
2015-10-17 17:07:29,330 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.36390656
2015-10-17 17:07:29,330 INFO [IPC Server handler 28 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000009_0 is : 0.9874797
2015-10-17 17:07:29,333 INFO [IPC Server handler 15 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.36388028
2015-10-17 17:07:29,333 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.3638923
2015-10-17 17:07:29,914 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000009_0 is : 1.0
2015-10-17 17:07:29,916 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0019_m_000009_0
2015-10-17 17:07:29,917 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:07:29,917 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0019_01_000011 taskAttempt attempt_1445062781478_0019_m_000009_0
2015-10-17 17:07:29,917 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0019_m_000009_0
2015-10-17 17:07:29,918 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:07:29,936 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:07:29,941 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0019_m_000009_0
2015-10-17 17:07:29,942 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:07:29,944 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-17 17:07:30,243 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.448704
2015-10-17 17:07:30,251 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.44859612
2015-10-17 17:07:30,254 INFO [IPC Server handler 14 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.4486067
2015-10-17 17:07:30,264 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.44789755
2015-10-17 17:07:30,576 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0019_m_000004
2015-10-17 17:07:30,576 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 17:07:30,576 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0019_m_000004
2015-10-17 17:07:30,576 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:07:30,577 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:07:30,577 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000004_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:07:30,863 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 17:07:30,866 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0019: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:13312, vCores:-14> knownNMs=4
2015-10-17 17:07:30,866 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:13312, vCores:-14>
2015-10-17 17:07:30,866 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-17 17:07:30,866 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:23552, vCores:-4> finalMapResourceLimit:<memory:11264, vCores:11> finalReduceResourceLimit:<memory:12288, vCores:-15> netScheduledMapResource:<memory:11264, vCores:11> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-17 17:07:30,866 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping up 1
2015-10-17 17:07:30,867 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 17:07:31,873 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0019: ask=1 release= 0 newContainers=1 finishedContainers=1 resourcelimit=<memory:13312, vCores:-14> knownNMs=4
2015-10-17 17:07:31,873 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0019_01_000011
2015-10-17 17:07:31,874 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 17:07:31,874 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0019_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:07:31,874 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0019_01_000012 to attempt_1445062781478_0019_m_000004_1
2015-10-17 17:07:31,874 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:11 RackLocal:0
2015-10-17 17:07:31,874 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:07:31,875 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000004_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:07:31,875 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0019_01_000012 taskAttempt attempt_1445062781478_0019_m_000004_1
2015-10-17 17:07:31,875 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0019_m_000004_1
2015-10-17 17:07:31,875 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:07:31,887 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0019_m_000004_1 : 13562
2015-10-17 17:07:31,888 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0019_m_000004_1] using containerId: [container_1445062781478_0019_01_000012 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 17:07:31,888 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000004_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:07:31,888 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0019_m_000004
2015-10-17 17:07:32,228 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.36404583
2015-10-17 17:07:32,238 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.3637686
2015-10-17 17:07:32,352 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.42821532
2015-10-17 17:07:32,352 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.402416
2015-10-17 17:07:32,353 INFO [IPC Server handler 23 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.4401269
2015-10-17 17:07:32,881 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0019: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:12288, vCores:-15> knownNMs=4
2015-10-17 17:07:32,881 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 17:07:32,881 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 17:07:32,881 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0019_01_000013 to attempt_1445062781478_0019_r_000000_0
2015-10-17 17:07:32,881 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:11 RackLocal:0
2015-10-17 17:07:32,890 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:07:32,891 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:07:32,891 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0019_01_000013 taskAttempt attempt_1445062781478_0019_r_000000_0
2015-10-17 17:07:32,891 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0019_r_000000_0
2015-10-17 17:07:32,891 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 17:07:33,276 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.448704
2015-10-17 17:07:33,283 INFO [IPC Server handler 28 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.4972733
2015-10-17 17:07:33,287 INFO [IPC Server handler 15 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.44859612
2015-10-17 17:07:33,296 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.44789755
2015-10-17 17:07:33,316 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0019_r_000000_0 : 13562
2015-10-17 17:07:33,316 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0019_r_000000_0] using containerId: [container_1445062781478_0019_01_000013 on NM: [04DN8IQ.fareast.corp.microsoft.com:52150]
2015-10-17 17:07:33,317 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:07:33,317 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0019_r_000000
2015-10-17 17:07:33,317 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:07:33,883 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0019: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:12288, vCores:-15> knownNMs=4
2015-10-17 17:07:33,919 INFO [Socket Reader #1 for port 19667] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0019 (auth:SIMPLE)
2015-10-17 17:07:33,933 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0019_m_000012 asked for a task
2015-10-17 17:07:33,934 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0019_m_000012 given task: attempt_1445062781478_0019_m_000004_1
2015-10-17 17:07:35,237 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.41623443
2015-10-17 17:07:35,240 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.4172788
2015-10-17 17:07:35,369 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.44968578
2015-10-17 17:07:35,369 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.44950968
2015-10-17 17:07:35,370 INFO [IPC Server handler 24 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.44964966
2015-10-17 17:07:36,296 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.45736918
2015-10-17 17:07:36,300 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.5343203
2015-10-17 17:07:36,304 INFO [IPC Server handler 23 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.5342037
2015-10-17 17:07:36,313 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.44839332
2015-10-17 17:07:36,805 INFO [Socket Reader #1 for port 19667] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0019 (auth:SIMPLE)
2015-10-17 17:07:36,824 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0019_r_000013 asked for a task
2015-10-17 17:07:36,824 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0019_r_000013 given task: attempt_1445062781478_0019_r_000000_0
2015-10-17 17:07:38,238 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.44980705
2015-10-17 17:07:38,252 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.44950172
2015-10-17 17:07:38,381 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.44968578
2015-10-17 17:07:38,383 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.44950968
2015-10-17 17:07:38,383 INFO [IPC Server handler 9 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.44964966
2015-10-17 17:07:38,619 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 0 maxEvents 10000
2015-10-17 17:07:39,323 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.53425497
2015-10-17 17:07:39,325 INFO [IPC Server handler 24 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.5343203
2015-10-17 17:07:39,329 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.5342037
2015-10-17 17:07:39,338 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.53341997
2015-10-17 17:07:39,628 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:07:40,629 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:07:41,109 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_1 is : 0.10680563
2015-10-17 17:07:41,258 INFO [IPC Server handler 14 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.44950172
2015-10-17 17:07:41,258 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.44980705
2015-10-17 17:07:41,397 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.44964966
2015-10-17 17:07:41,404 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.44950968
2015-10-17 17:07:41,405 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.44968578
2015-10-17 17:07:41,644 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:07:42,342 INFO [IPC Server handler 9 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.53425497
2015-10-17 17:07:42,343 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.5343203
2015-10-17 17:07:42,347 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.5342037
2015-10-17 17:07:42,355 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.53341997
2015-10-17 17:07:42,658 INFO [IPC Server handler 19 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:07:43,664 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:07:44,114 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_1 is : 0.10680563
2015-10-17 17:07:44,269 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.44980705
2015-10-17 17:07:44,269 INFO [IPC Server handler 28 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.44950172
2015-10-17 17:07:44,414 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.47156626
2015-10-17 17:07:44,416 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.4979334
2015-10-17 17:07:44,416 INFO [IPC Server handler 19 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.52242225
2015-10-17 17:07:44,577 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.0
2015-10-17 17:07:44,692 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:07:45,362 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.53425497
2015-10-17 17:07:45,363 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.6199081
2015-10-17 17:07:45,364 INFO [IPC Server handler 19 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.5541856
2015-10-17 17:07:45,373 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.53341997
2015-10-17 17:07:45,578 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0019_m_000008
2015-10-17 17:07:45,578 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 17:07:45,578 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0019_m_000008
2015-10-17 17:07:45,579 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:07:45,579 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:07:45,579 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000008_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:07:45,727 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:07:45,897 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:11 RackLocal:0
2015-10-17 17:07:45,898 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0019: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:12288, vCores:-15> knownNMs=4
2015-10-17 17:07:46,724 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:07:46,901 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 17:07:46,902 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:07:46,902 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0019_01_000014 to attempt_1445062781478_0019_m_000008_1
2015-10-17 17:07:46,902 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-17 17:07:46,902 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:07:46,903 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000008_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:07:46,903 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0019_01_000014 taskAttempt attempt_1445062781478_0019_m_000008_1
2015-10-17 17:07:46,903 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0019_m_000008_1
2015-10-17 17:07:46,903 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 17:07:47,077 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0019_m_000008_1 : 13562
2015-10-17 17:07:47,077 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0019_m_000008_1] using containerId: [container_1445062781478_0019_01_000014 on NM: [04DN8IQ.fareast.corp.microsoft.com:52150]
2015-10-17 17:07:47,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000008_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:07:47,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0019_m_000008
2015-10-17 17:07:47,115 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_1 is : 0.10680563
2015-10-17 17:07:47,271 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.49342176
2015-10-17 17:07:47,271 INFO [IPC Server handler 15 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.49302736
2015-10-17 17:07:47,430 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.5352028
2015-10-17 17:07:47,430 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.5352825
2015-10-17 17:07:47,431 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.5352021
2015-10-17 17:07:47,632 INFO [IPC Server handler 1 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.0
2015-10-17 17:07:47,771 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:07:47,905 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0019: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:11264, vCores:-16> knownNMs=4
2015-10-17 17:07:48,382 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.6196791
2015-10-17 17:07:48,383 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.6197233
2015-10-17 17:07:48,383 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.6199081
2015-10-17 17:07:48,391 INFO [IPC Server handler 1 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.61898744
2015-10-17 17:07:48,785 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:07:49,741 INFO [Socket Reader #1 for port 19667] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0019 (auth:SIMPLE)
2015-10-17 17:07:49,757 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0019_m_000014 asked for a task
2015-10-17 17:07:49,757 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0019_m_000014 given task: attempt_1445062781478_0019_m_000008_1
2015-10-17 17:07:49,815 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:07:50,116 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_1 is : 0.12387828
2015-10-17 17:07:50,270 INFO [IPC Server handler 28 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.53521925
2015-10-17 17:07:50,271 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.53543663
2015-10-17 17:07:50,444 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.5352028
2015-10-17 17:07:50,449 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.5352825
2015-10-17 17:07:50,450 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.5352021
2015-10-17 17:07:50,717 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.033333335
2015-10-17 17:07:50,817 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:07:51,404 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.6197233
2015-10-17 17:07:51,409 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.6196791
2015-10-17 17:07:51,414 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.6199081
2015-10-17 17:07:51,415 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.61898744
2015-10-17 17:07:51,815 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:07:52,836 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:07:53,118 INFO [IPC Server handler 14 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_1 is : 0.19242907
2015-10-17 17:07:53,276 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.53521925
2015-10-17 17:07:53,279 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.53543663
2015-10-17 17:07:53,448 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.5352028
2015-10-17 17:07:53,462 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.5352021
2015-10-17 17:07:53,465 INFO [IPC Server handler 14 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.5352825
2015-10-17 17:07:53,647 INFO [IPC Server handler 28 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.6199081
2015-10-17 17:07:53,743 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.033333335
2015-10-17 17:07:53,850 INFO [IPC Server handler 15 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:07:54,425 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.6197233
2015-10-17 17:07:54,428 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.6196791
2015-10-17 17:07:54,430 INFO [IPC Server handler 14 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.667
2015-10-17 17:07:54,432 INFO [IPC Server handler 28 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.61898744
2015-10-17 17:07:54,886 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:07:55,725 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.6196791
2015-10-17 17:07:55,928 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:07:56,116 INFO [IPC Server handler 23 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_1 is : 0.19242907
2015-10-17 17:07:56,288 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.53521925
2015-10-17 17:07:56,289 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.53543663
2015-10-17 17:07:56,461 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.56296563
2015-10-17 17:07:56,476 INFO [IPC Server handler 15 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.5859567
2015-10-17 17:07:56,477 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.61120826
2015-10-17 17:07:56,812 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.033333335
2015-10-17 17:07:56,956 INFO [IPC Server handler 23 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:07:57,181 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.6197233
2015-10-17 17:07:57,327 INFO [IPC Server handler 24 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_1 is : 0.106881365
2015-10-17 17:07:57,444 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.667
2015-10-17 17:07:57,446 INFO [IPC Server handler 15 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.667
2015-10-17 17:07:57,448 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.667
2015-10-17 17:07:57,450 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.66246057
2015-10-17 17:07:57,543 INFO [IPC Server handler 23 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.66246057
2015-10-17 17:07:57,957 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:07:58,973 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:07:59,116 INFO [IPC Server handler 24 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_1 is : 0.19242907
2015-10-17 17:07:59,290 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.5833458
2015-10-17 17:07:59,294 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.5867992
2015-10-17 17:07:59,476 INFO [IPC Server handler 23 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.6208445
2015-10-17 17:07:59,483 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.620844
2015-10-17 17:07:59,497 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.6209487
2015-10-17 17:07:59,846 INFO [IPC Server handler 24 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.033333335
2015-10-17 17:08:00,270 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:00,346 INFO [IPC Server handler 9 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_1 is : 0.106881365
2015-10-17 17:08:00,462 INFO [IPC Server handler 23 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.667
2015-10-17 17:08:00,463 INFO [IPC Server handler 23 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.667
2015-10-17 17:08:00,464 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.66727746
2015-10-17 17:08:00,466 INFO [IPC Server handler 24 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.667
2015-10-17 17:08:00,582 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0019_m_000006
2015-10-17 17:08:00,582 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 17:08:00,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0019_m_000006
2015-10-17 17:08:00,583 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:08:00,583 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:08:00,583 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000006_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:08:00,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-17 17:08:00,921 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0019: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:11264, vCores:-16> knownNMs=4
2015-10-17 17:08:01,285 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:01,927 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 17:08:01,927 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:08:01,927 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0019_01_000015 to attempt_1445062781478_0019_m_000006_1
2015-10-17 17:08:01,927 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 17:08:01,928 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:08:01,928 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000006_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:08:01,929 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0019_01_000015 taskAttempt attempt_1445062781478_0019_m_000006_1
2015-10-17 17:08:01,929 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0019_m_000006_1
2015-10-17 17:08:01,929 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 17:08:01,958 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0019_m_000006_1 : 13562
2015-10-17 17:08:01,958 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0019_m_000006_1] using containerId: [container_1445062781478_0019_01_000015 on NM: [04DN8IQ.fareast.corp.microsoft.com:52150]
2015-10-17 17:08:01,958 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000006_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:08:01,958 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0019_m_000006
2015-10-17 17:08:02,121 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_1 is : 0.2319244
2015-10-17 17:08:02,286 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:02,296 INFO [IPC Server handler 9 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.6210422
2015-10-17 17:08:02,305 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.6207798
2015-10-17 17:08:02,485 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.6208445
2015-10-17 17:08:02,494 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.620844
2015-10-17 17:08:02,514 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.6209487
2015-10-17 17:08:02,883 INFO [IPC Server handler 9 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.033333335
2015-10-17 17:08:02,931 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0019: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:10240, vCores:-17> knownNMs=4
2015-10-17 17:08:03,286 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:03,385 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_1 is : 0.106881365
2015-10-17 17:08:03,483 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.667
2015-10-17 17:08:03,488 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.67286587
2015-10-17 17:08:03,490 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.667
2015-10-17 17:08:03,493 INFO [IPC Server handler 9 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.70597947
2015-10-17 17:08:04,289 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:04,892 INFO [Socket Reader #1 for port 19667] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0019 (auth:SIMPLE)
2015-10-17 17:08:04,920 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0019_m_000015 asked for a task
2015-10-17 17:08:04,920 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0019_m_000015 given task: attempt_1445062781478_0019_m_000006_1
2015-10-17 17:08:05,134 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_1 is : 0.2781602
2015-10-17 17:08:05,306 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.6207798
2015-10-17 17:08:05,306 INFO [IPC Server handler 19 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:05,307 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.6210422
2015-10-17 17:08:05,509 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.6208445
2015-10-17 17:08:05,510 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.620844
2015-10-17 17:08:05,522 INFO [IPC Server handler 19 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.6209487
2015-10-17 17:08:06,045 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.033333335
2015-10-17 17:08:06,318 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:06,416 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_1 is : 0.106881365
2015-10-17 17:08:06,511 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.6742629
2015-10-17 17:08:06,512 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.70000845
2015-10-17 17:08:06,521 INFO [IPC Server handler 19 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.73320067
2015-10-17 17:08:06,528 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.6670329
2015-10-17 17:08:06,825 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.6209487
2015-10-17 17:08:07,320 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:07,527 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.620844
2015-10-17 17:08:08,133 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_1 is : 0.2781602
2015-10-17 17:08:08,142 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.6208445
2015-10-17 17:08:08,305 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.6207798
2015-10-17 17:08:08,320 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.6210422
2015-10-17 17:08:08,340 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:08,509 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.667
2015-10-17 17:08:08,526 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.667
2015-10-17 17:08:08,527 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.667
2015-10-17 17:08:09,115 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.033333335
2015-10-17 17:08:09,418 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:09,452 INFO [IPC Server handler 15 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_1 is : 0.114636905
2015-10-17 17:08:09,545 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.7269136
2015-10-17 17:08:09,547 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.7088764
2015-10-17 17:08:09,553 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.7601498
2015-10-17 17:08:09,562 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.6995093
2015-10-17 17:08:10,493 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:10,838 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.6210422
2015-10-17 17:08:11,111 INFO [IPC Server handler 1 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.6207798
2015-10-17 17:08:11,137 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_1 is : 0.2781602
2015-10-17 17:08:11,315 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.667
2015-10-17 17:08:11,325 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.667
2015-10-17 17:08:11,515 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.667
2015-10-17 17:08:11,529 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.667
2015-10-17 17:08:11,542 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:11,545 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.667
2015-10-17 17:08:12,165 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.033333335
2015-10-17 17:08:12,558 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_1 is : 0.14101796
2015-10-17 17:08:12,579 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.7457092
2015-10-17 17:08:12,580 INFO [IPC Server handler 1 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.7291317
2015-10-17 17:08:12,582 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.77912855
2015-10-17 17:08:12,587 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.7195493
2015-10-17 17:08:12,617 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:13,033 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_1 is : 0.060574774
2015-10-17 17:08:13,804 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:14,151 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_1 is : 0.31883466
2015-10-17 17:08:14,328 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.667
2015-10-17 17:08:14,337 INFO [IPC Server handler 28 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.667
2015-10-17 17:08:14,531 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.667
2015-10-17 17:08:14,543 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.667
2015-10-17 17:08:14,561 INFO [IPC Server handler 1 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.6699794
2015-10-17 17:08:14,873 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:15,285 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.033333335
2015-10-17 17:08:15,607 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.752935
2015-10-17 17:08:15,609 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.8060484
2015-10-17 17:08:15,609 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.7713582
2015-10-17 17:08:15,615 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.7437315
2015-10-17 17:08:15,845 INFO [IPC Server handler 28 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_1 is : 0.18451601
2015-10-17 17:08:15,919 INFO [IPC Server handler 14 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:16,197 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_1 is : 0.096087016
2015-10-17 17:08:16,961 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:17,157 INFO [IPC Server handler 15 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_1 is : 0.36388028
2015-10-17 17:08:17,345 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.667
2015-10-17 17:08:17,347 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.667
2015-10-17 17:08:17,550 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.6787196
2015-10-17 17:08:17,566 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.6952039
2015-10-17 17:08:17,578 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.7014389
2015-10-17 17:08:17,960 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:18,367 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.033333335
2015-10-17 17:08:18,629 INFO [IPC Server handler 28 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.837782
2015-10-17 17:08:18,639 INFO [IPC Server handler 14 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.80305237
2015-10-17 17:08:18,645 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.76288503
2015-10-17 17:08:18,647 INFO [IPC Server handler 15 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.7730783
2015-10-17 17:08:18,915 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_1 is : 0.19258286
2015-10-17 17:08:18,961 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:19,273 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_1 is : 0.106964506
2015-10-17 17:08:19,961 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:20,174 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_1 is : 0.36388028
2015-10-17 17:08:20,360 INFO [IPC Server handler 23 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.6802609
2015-10-17 17:08:20,363 INFO [IPC Server handler 24 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.682133
2015-10-17 17:08:20,559 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.7141932
2015-10-17 17:08:20,581 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.7308819
2015-10-17 17:08:20,591 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.73915946
2015-10-17 17:08:20,962 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:21,403 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.033333335
2015-10-17 17:08:21,668 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.86411196
2015-10-17 17:08:21,674 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.8293807
2015-10-17 17:08:21,681 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.7802866
2015-10-17 17:08:21,687 INFO [IPC Server handler 23 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.79125553
2015-10-17 17:08:21,993 INFO [IPC Server handler 24 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:22,056 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_1 is : 0.19258286
2015-10-17 17:08:22,319 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_1 is : 0.106964506
2015-10-17 17:08:22,992 INFO [IPC Server handler 24 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:23,189 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_1 is : 0.36388028
2015-10-17 17:08:23,375 INFO [IPC Server handler 9 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.7101118
2015-10-17 17:08:23,380 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.70751405
2015-10-17 17:08:23,565 INFO [IPC Server handler 1 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.76483583
2015-10-17 17:08:23,593 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.7805753
2015-10-17 17:08:23,612 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.7935066
2015-10-17 17:08:23,992 INFO [IPC Server handler 24 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:24,432 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.033333335
2015-10-17 17:08:24,701 INFO [IPC Server handler 24 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.89103615
2015-10-17 17:08:24,704 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.857357
2015-10-17 17:08:24,709 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.8116558
2015-10-17 17:08:24,718 INFO [IPC Server handler 9 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.8231511
2015-10-17 17:08:24,993 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:25,092 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_1 is : 0.19258286
2015-10-17 17:08:25,355 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_1 is : 0.106964506
2015-10-17 17:08:25,993 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:26,204 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_1 is : 0.39021546
2015-10-17 17:08:26,391 INFO [IPC Server handler 19 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.7372228
2015-10-17 17:08:26,396 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.7404082
2015-10-17 17:08:26,576 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.8195918
2015-10-17 17:08:26,612 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.83591753
2015-10-17 17:08:26,621 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.84787863
2015-10-17 17:08:26,993 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:27,465 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.033333335
2015-10-17 17:08:27,730 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.91126615
2015-10-17 17:08:27,742 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.84277683
2015-10-17 17:08:27,743 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.87929344
2015-10-17 17:08:27,749 INFO [IPC Server handler 19 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.8577491
2015-10-17 17:08:27,993 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:28,131 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_1 is : 0.19258286
2015-10-17 17:08:28,385 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_1 is : 0.106964506
2015-10-17 17:08:28,997 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:29,218 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_1 is : 0.44968578
2015-10-17 17:08:29,405 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.76602715
2015-10-17 17:08:29,411 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.7693466
2015-10-17 17:08:29,581 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.86074364
2015-10-17 17:08:29,623 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.8918322
2015-10-17 17:08:29,629 INFO [IPC Server handler 28 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.8782908
2015-10-17 17:08:30,011 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:30,503 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.033333335
2015-10-17 17:08:30,758 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.93700624
2015-10-17 17:08:30,764 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.8655953
2015-10-17 17:08:30,782 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.90515786
2015-10-17 17:08:30,783 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.87974143
2015-10-17 17:08:31,058 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:31,169 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_1 is : 0.23286176
2015-10-17 17:08:31,419 INFO [IPC Server handler 1 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_1 is : 0.13959679
2015-10-17 17:08:32,109 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:32,239 INFO [IPC Server handler 1 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_1 is : 0.44968578
2015-10-17 17:08:32,428 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.7961599
2015-10-17 17:08:32,429 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.79942
2015-10-17 17:08:32,597 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.91311663
2015-10-17 17:08:32,634 INFO [IPC Server handler 14 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.9444813
2015-10-17 17:08:32,642 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.9274534
2015-10-17 17:08:33,170 INFO [IPC Server handler 1 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:33,668 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.033333335
2015-10-17 17:08:33,795 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.9663826
2015-10-17 17:08:33,799 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.89255834
2015-10-17 17:08:33,814 INFO [IPC Server handler 1 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.90678215
2015-10-17 17:08:33,820 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.9336004
2015-10-17 17:08:34,287 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:34,448 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_1 is : 0.2744871
2015-10-17 17:08:34,619 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_1 is : 0.18679045
2015-10-17 17:08:35,254 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_1 is : 0.44968578
2015-10-17 17:08:35,358 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:35,444 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.82784736
2015-10-17 17:08:35,445 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.8251581
2015-10-17 17:08:35,616 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.9568511
2015-10-17 17:08:35,643 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 0.9904789
2015-10-17 17:08:35,662 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 0.97115016
2015-10-17 17:08:36,345 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000005_0 is : 1.0
2015-10-17 17:08:36,348 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0019_m_000005_0
2015-10-17 17:08:36,349 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:08:36,349 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0019_01_000007 taskAttempt attempt_1445062781478_0019_m_000005_0
2015-10-17 17:08:36,350 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0019_m_000005_0
2015-10-17 17:08:36,351 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:08:36,355 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:36,370 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:08:36,371 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0019_m_000005_0
2015-10-17 17:08:36,371 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:08:36,372 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-17 17:08:36,763 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.033333335
2015-10-17 17:08:36,834 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 0.9890094
2015-10-17 17:08:36,840 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.91635436
2015-10-17 17:08:36,853 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.93078864
2015-10-17 17:08:36,856 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.95618784
2015-10-17 17:08:36,994 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 17:08:37,354 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:08:37,551 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_1 is : 0.27811313
2015-10-17 17:08:37,725 INFO [IPC Server handler 9 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_1 is : 0.19266446
2015-10-17 17:08:37,868 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000007_0 is : 1.0
2015-10-17 17:08:37,871 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0019_m_000007_0
2015-10-17 17:08:37,871 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:08:37,872 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0019_01_000009 taskAttempt attempt_1445062781478_0019_m_000007_0
2015-10-17 17:08:37,872 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0019_m_000007_0
2015-10-17 17:08:37,872 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:08:37,892 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:08:37,892 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0019_m_000007_0
2015-10-17 17:08:37,893 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:08:37,893 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-17 17:08:37,996 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 17:08:37,998 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0019_01_000007
2015-10-17 17:08:37,998 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 17:08:37,999 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0019_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:08:38,270 INFO [IPC Server handler 28 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_1 is : 0.4719788
2015-10-17 17:08:38,358 INFO [IPC Server handler 14 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 17:08:38,429 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000003_0 is : 1.0
2015-10-17 17:08:38,430 INFO [IPC Server handler 15 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0019_m_000003_0
2015-10-17 17:08:38,430 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:08:38,430 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0019_01_000005 taskAttempt attempt_1445062781478_0019_m_000003_0
2015-10-17 17:08:38,430 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0019_m_000003_0
2015-10-17 17:08:38,431 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 17:08:38,441 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:08:38,441 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0019_m_000003_0
2015-10-17 17:08:38,441 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:08:38,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-17 17:08:38,454 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.85463935
2015-10-17 17:08:38,459 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.8574068
2015-10-17 17:08:38,624 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 0.9983716
2015-10-17 17:08:38,833 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000004_0 is : 1.0
2015-10-17 17:08:38,834 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0019_m_000004_0
2015-10-17 17:08:38,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:08:38,835 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0019_01_000006 taskAttempt attempt_1445062781478_0019_m_000004_0
2015-10-17 17:08:38,835 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0019_m_000004_0
2015-10-17 17:08:38,835 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:08:38,847 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:08:38,847 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0019_m_000004_0
2015-10-17 17:08:38,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0019_m_000004_1
2015-10-17 17:08:38,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:08:38,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-17 17:08:38,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000004_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 17:08:38,848 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0019_01_000012 taskAttempt attempt_1445062781478_0019_m_000004_1
2015-10-17 17:08:38,848 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0019_m_000004_1
2015-10-17 17:08:38,849 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:08:38,861 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000004_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 17:08:38,861 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 17:08:38,868 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out4/_temporary/1/_temporary/attempt_1445062781478_0019_m_000004_1
2015-10-17 17:08:38,870 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000004_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 17:08:38,945 INFO [Socket Reader #1 for port 19667] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 19667: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 17:08:38,998 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 17:08:39,000 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0019_01_000009
2015-10-17 17:08:39,000 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 17:08:39,000 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0019_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:08:39,404 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 17:08:39,797 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.033333335
2015-10-17 17:08:39,873 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.94103456
2015-10-17 17:08:39,881 INFO [IPC Server handler 28 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.9556286
2015-10-17 17:08:39,886 INFO [IPC Server handler 14 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 0.9822171
2015-10-17 17:08:40,003 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0019_01_000005
2015-10-17 17:08:40,003 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0019_01_000006
2015-10-17 17:08:40,003 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0019_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:08:40,003 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0019_01_000012
2015-10-17 17:08:40,003 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0019_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:08:40,003 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 17:08:40,004 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0019_m_000004_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:08:40,454 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 17:08:40,806 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_1 is : 0.27811313
2015-10-17 17:08:40,832 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_1 is : 0.19266446
2015-10-17 17:08:41,470 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.88731915
2015-10-17 17:08:41,476 INFO [IPC Server handler 23 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.8855006
2015-10-17 17:08:41,482 INFO [IPC Server handler 24 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 17:08:42,125 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000002_0 is : 1.0
2015-10-17 17:08:42,127 INFO [IPC Server handler 15 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0019_m_000002_0
2015-10-17 17:08:42,128 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:08:42,128 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0019_01_000004 taskAttempt attempt_1445062781478_0019_m_000002_0
2015-10-17 17:08:42,129 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0019_m_000002_0
2015-10-17 17:08:42,129 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 17:08:42,146 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:08:42,146 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0019_m_000002_0
2015-10-17 17:08:42,146 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:08:42,147 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-17 17:08:42,514 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 17:08:42,878 INFO [IPC Server handler 28 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.033333335
2015-10-17 17:08:42,911 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.96947074
2015-10-17 17:08:42,918 INFO [IPC Server handler 15 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 0.9874741
2015-10-17 17:08:43,007 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 17:08:43,559 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 17:08:43,898 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_1 is : 0.27811313
2015-10-17 17:08:43,909 INFO [IPC Server handler 15 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_1 is : 0.19266446
2015-10-17 17:08:44,004 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000001_0 is : 1.0
2015-10-17 17:08:44,006 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0019_m_000001_0
2015-10-17 17:08:44,007 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:08:44,007 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0019_01_000003 taskAttempt attempt_1445062781478_0019_m_000001_0
2015-10-17 17:08:44,008 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0019_m_000001_0
2015-10-17 17:08:44,008 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 17:08:44,010 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0019_01_000004
2015-10-17 17:08:44,011 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 17:08:44,011 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0019_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:08:44,024 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:08:44,024 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0019_m_000001_0
2015-10-17 17:08:44,024 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:08:44,025 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-17 17:08:44,486 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.91967285
2015-10-17 17:08:44,487 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.91861856
2015-10-17 17:08:44,606 INFO [IPC Server handler 9 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 17:08:45,012 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 17:08:45,654 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 17:08:45,940 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 0.9992176
2015-10-17 17:08:45,967 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.033333335
2015-10-17 17:08:46,015 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0019_01_000003
2015-10-17 17:08:46,016 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 17:08:46,016 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0019_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:08:46,231 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000000_0 is : 1.0
2015-10-17 17:08:46,233 INFO [IPC Server handler 23 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0019_m_000000_0
2015-10-17 17:08:46,233 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:08:46,233 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0019_01_000002 taskAttempt attempt_1445062781478_0019_m_000000_0
2015-10-17 17:08:46,233 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0019_m_000000_0
2015-10-17 17:08:46,233 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 17:08:46,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:08:46,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0019_m_000000_0
2015-10-17 17:08:46,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:08:46,245 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-17 17:08:46,717 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 17:08:46,975 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_1 is : 0.27811313
2015-10-17 17:08:47,009 INFO [IPC Server handler 23 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_1 is : 0.19266446
2015-10-17 17:08:47,016 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 17:08:47,488 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.95044196
2015-10-17 17:08:47,500 INFO [IPC Server handler 9 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.9500738
2015-10-17 17:08:48,019 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0019_01_000002
2015-10-17 17:08:48,020 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 17:08:48,020 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0019_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:08:48,059 INFO [IPC Server handler 24 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 17:08:49,065 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.033333335
2015-10-17 17:08:49,106 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 17:08:50,074 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_1 is : 0.29246226
2015-10-17 17:08:50,109 INFO [IPC Server handler 9 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_1 is : 0.21708328
2015-10-17 17:08:50,153 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 17:08:50,502 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 0.98093855
2015-10-17 17:08:50,508 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 0.9808887
2015-10-17 17:08:51,217 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 17:08:52,158 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.033333335
2015-10-17 17:08:52,278 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 17:08:52,515 INFO [IPC Server handler 19 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000008_0 is : 1.0
2015-10-17 17:08:52,517 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0019_m_000008_0
2015-10-17 17:08:52,518 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:08:52,518 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0019_01_000010 taskAttempt attempt_1445062781478_0019_m_000008_0
2015-10-17 17:08:52,519 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0019_m_000008_0
2015-10-17 17:08:52,519 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:08:52,536 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:08:52,536 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0019_m_000008_0
2015-10-17 17:08:52,536 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0019_m_000008_1
2015-10-17 17:08:52,537 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:08:52,537 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-17 17:08:52,538 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000008_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 17:08:52,538 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0019_01_000014 taskAttempt attempt_1445062781478_0019_m_000008_1
2015-10-17 17:08:52,538 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0019_m_000008_1
2015-10-17 17:08:52,540 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 17:08:52,565 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_m_000006_0 is : 1.0
2015-10-17 17:08:52,569 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0019_m_000006_0
2015-10-17 17:08:52,569 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:08:52,569 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0019_01_000008 taskAttempt attempt_1445062781478_0019_m_000006_0
2015-10-17 17:08:52,570 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0019_m_000006_0
2015-10-17 17:08:52,570 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:08:52,586 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:08:52,587 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0019_m_000006_0
2015-10-17 17:08:52,587 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0019_m_000006_1
2015-10-17 17:08:52,587 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:08:52,588 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-17 17:08:52,588 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000006_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 17:08:52,589 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0019_01_000015 taskAttempt attempt_1445062781478_0019_m_000006_1
2015-10-17 17:08:52,589 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0019_m_000006_1
2015-10-17 17:08:52,589 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 17:08:52,652 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0019_m_000006
2015-10-17 17:08:52,652 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 17:08:52,832 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000006_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 17:08:52,833 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 17:08:52,840 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out4/_temporary/1/_temporary/attempt_1445062781478_0019_m_000006_1
2015-10-17 17:08:52,841 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000006_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 17:08:52,874 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000008_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 17:08:52,875 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 17:08:52,877 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out4/_temporary/1/_temporary/attempt_1445062781478_0019_m_000008_1
2015-10-17 17:08:52,878 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_m_000008_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 17:08:52,961 INFO [Socket Reader #1 for port 19667] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 19667: readAndProcess from client *********** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 17:08:53,027 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 17:08:53,111 INFO [Socket Reader #1 for port 19667] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 19667: readAndProcess from client *********** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 17:08:53,326 INFO [IPC Server handler 19 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 17:08:54,031 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0019_01_000014
2015-10-17 17:08:54,031 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0019_01_000015
2015-10-17 17:08:54,031 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0019_m_000008_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:08:54,031 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0019_01_000008
2015-10-17 17:08:54,031 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0019_m_000006_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:08:54,032 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0019_01_000010
2015-10-17 17:08:54,032 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0019_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:08:54,032 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 17:08:54,032 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0019_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:08:54,358 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:08:55,234 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.06666667
2015-10-17 17:08:55,406 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:08:56,470 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:08:57,548 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:08:58,312 INFO [IPC Server handler 19 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.06666667
2015-10-17 17:08:58,612 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:08:59,672 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:00,738 INFO [IPC Server handler 1 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:01,397 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.06666667
2015-10-17 17:09:01,797 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:02,859 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:03,923 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:04,483 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.06666667
2015-10-17 17:09:04,991 INFO [IPC Server handler 23 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:06,048 INFO [IPC Server handler 24 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:07,094 INFO [IPC Server handler 9 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:07,578 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.06666667
2015-10-17 17:09:08,158 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:09,203 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:10,273 INFO [IPC Server handler 19 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:10,692 INFO [IPC Server handler 1 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.10000001
2015-10-17 17:09:11,331 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:12,375 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:13,407 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:13,771 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.13333334
2015-10-17 17:09:14,483 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:15,582 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:16,659 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:16,865 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.13333334
2015-10-17 17:09:17,721 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:18,802 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:19,862 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:20,263 INFO [IPC Server handler 19 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.13333334
2015-10-17 17:09:20,929 INFO [IPC Server handler 15 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:21,971 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:23,018 INFO [IPC Server handler 24 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:23,381 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.13333334
2015-10-17 17:09:24,098 INFO [IPC Server handler 9 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:25,160 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:26,272 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:26,486 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.13333334
2015-10-17 17:09:27,284 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:28,317 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:29,387 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:29,563 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.13333334
2015-10-17 17:09:30,430 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:31,520 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:32,608 INFO [IPC Server handler 1 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:32,694 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.13333334
2015-10-17 17:09:33,661 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:34,710 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:35,787 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:35,809 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.20000002
2015-10-17 17:09:36,849 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:37,914 INFO [IPC Server handler 17 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:38,894 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.20000002
2015-10-17 17:09:38,992 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:40,038 INFO [IPC Server handler 12 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:41,085 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:41,962 INFO [IPC Server handler 20 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.20000002
2015-10-17 17:09:42,146 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:43,178 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:44,225 INFO [IPC Server handler 19 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:45,067 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.20000002
2015-10-17 17:09:45,271 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:46,324 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:47,381 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:48,165 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.20000002
2015-10-17 17:09:48,463 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:49,495 INFO [IPC Server handler 1 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:50,555 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:51,246 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.23333333
2015-10-17 17:09:51,586 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:52,653 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:53,722 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:54,363 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.23333333
2015-10-17 17:09:54,800 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:55,872 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:56,936 INFO [IPC Server handler 15 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:57,461 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.23333333
2015-10-17 17:09:57,996 INFO [IPC Server handler 11 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:09:59,058 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:10:00,122 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:10:00,541 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.26666668
2015-10-17 17:10:01,187 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:10:02,243 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:10:03,312 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:10:03,668 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.26666668
2015-10-17 17:10:04,370 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:10:05,431 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:10:06,478 INFO [IPC Server handler 1 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:10:06,779 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.26666668
2015-10-17 17:10:07,511 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:10:08,541 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:10:09,559 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:10:09,881 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.3
2015-10-17 17:10:10,593 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:10:11,619 INFO [IPC Server handler 7 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:10:12,655 INFO [IPC Server handler 4 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:10:12,949 INFO [IPC Server handler 16 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.3
2015-10-17 17:10:13,684 INFO [IPC Server handler 26 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0019_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:10:14,561 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.3
2015-10-17 17:10:14,589 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.3
2015-10-17 17:10:16,035 INFO [IPC Server handler 24 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.66896725
2015-10-17 17:10:19,058 INFO [IPC Server handler 13 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.6882406
2015-10-17 17:10:22,089 INFO [IPC Server handler 9 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.7079548
2015-10-17 17:10:25,119 INFO [IPC Server handler 29 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.7279293
2015-10-17 17:10:28,160 INFO [IPC Server handler 27 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.7477663
2015-10-17 17:10:31,190 INFO [IPC Server handler 18 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.76722485
2015-10-17 17:10:34,223 INFO [IPC Server handler 19 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.7869841
2015-10-17 17:10:37,246 INFO [IPC Server handler 3 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.8068755
2015-10-17 17:10:40,282 INFO [IPC Server handler 22 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.8268375
2015-10-17 17:10:43,325 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.8468555
2015-10-17 17:10:46,359 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.8668474
2015-10-17 17:10:49,389 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.8864418
2015-10-17 17:10:52,422 INFO [IPC Server handler 1 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.90629363
2015-10-17 17:10:55,462 INFO [IPC Server handler 2 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.92551786
2015-10-17 17:10:58,493 INFO [IPC Server handler 8 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.9445019
2015-10-17 17:11:01,517 INFO [IPC Server handler 10 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.96395195
2015-10-17 17:11:04,550 INFO [IPC Server handler 25 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 0.9835174
2015-10-17 17:11:07,308 INFO [IPC Server handler 5 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445062781478_0019_r_000000_0
2015-10-17 17:11:07,308 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_r_000000_0 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-17 17:11:07,308 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445062781478_0019_r_000000_0 given a go for committing the task output.
2015-10-17 17:11:07,311 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445062781478_0019_r_000000_0
2015-10-17 17:11:07,311 INFO [IPC Server handler 0 on 19667] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445062781478_0019_r_000000_0:true
2015-10-17 17:11:07,359 INFO [IPC Server handler 21 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0019_r_000000_0 is : 1.0
2015-10-17 17:11:07,366 INFO [IPC Server handler 6 on 19667] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0019_r_000000_0
2015-10-17 17:11:07,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_r_000000_0 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:11:07,367 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0019_01_000013 taskAttempt attempt_1445062781478_0019_r_000000_0
2015-10-17 17:11:07,367 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0019_r_000000_0
2015-10-17 17:11:07,369 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 17:11:07,410 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0019_r_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:11:07,410 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0019_r_000000_0
2015-10-17 17:11:07,411 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0019_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:11:07,411 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-17 17:11:07,412 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0019Job Transitioned from RUNNING to COMMITTING
2015-10-17 17:11:07,413 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-17 17:11:07,480 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-17 17:11:07,482 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0019Job Transitioned from COMMITTING to SUCCEEDED
2015-10-17 17:11:07,483 INFO [Thread-100] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-17 17:11:07,484 INFO [Thread-100] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-17 17:11:07,484 INFO [Thread-100] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-17 17:11:07,484 INFO [Thread-100] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-17 17:11:07,484 INFO [Thread-100] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-17 17:11:07,484 INFO [Thread-100] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-17 17:11:07,485 INFO [Thread-100] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-17 17:11:07,626 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0019/job_1445062781478_0019_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0019-1445071656627-msrabi-pagerank-1445073067476-10-1-SUCCEEDED-default-1445072789649.jhist_tmp
2015-10-17 17:11:07,761 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0019-1445071656627-msrabi-pagerank-1445073067476-10-1-SUCCEEDED-default-1445072789649.jhist_tmp
2015-10-17 17:11:07,766 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0019/job_1445062781478_0019_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0019_conf.xml_tmp
2015-10-17 17:11:07,890 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0019_conf.xml_tmp
2015-10-17 17:11:07,896 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0019.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0019.summary
2015-10-17 17:11:07,899 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0019_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0019_conf.xml
2015-10-17 17:11:07,902 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0019-1445071656627-msrabi-pagerank-1445073067476-10-1-SUCCEEDED-default-1445072789649.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0019-1445071656627-msrabi-pagerank-1445073067476-10-1-SUCCEEDED-default-1445072789649.jhist
2015-10-17 17:11:07,903 INFO [Thread-100] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-17 17:11:07,908 INFO [Thread-100] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-17 17:11:07,911 INFO [Thread-100] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MSRA-SA-41.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445062781478_0019
2015-10-17 17:11:07,921 INFO [Thread-100] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-17 17:11:08,924 INFO [Thread-100] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 17:11:08,926 INFO [Thread-100] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0019
2015-10-17 17:11:08,935 INFO [Thread-100] org.apache.hadoop.ipc.Server: Stopping server on 19667
2015-10-17 17:11:08,938 INFO [IPC Server listener on 19667] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 19667
2015-10-17 17:11:08,940 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-17 17:11:08,940 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
