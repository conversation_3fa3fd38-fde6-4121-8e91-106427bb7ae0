2015-10-17 22:31:23,374 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 22:31:23,589 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 22:31:23,589 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-17 22:31:23,634 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 22:31:23,634 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445087491445_0009, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@78093f60)
2015-10-17 22:31:23,957 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 22:31:24,541 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445087491445_0009
2015-10-17 22:31:25,631 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 22:31:26,577 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 22:31:26,602 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@5538f906
2015-10-17 22:31:26,905 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/wordcount2.txt:536870912+134217728
2015-10-17 22:31:26,993 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-17 22:31:26,993 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-17 22:31:26,993 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-17 22:31:26,993 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-17 22:31:26,994 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-17 22:31:27,005 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-17 22:31:30,400 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:31:30,400 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 34177712; bufvoid = 104857600
2015-10-17 22:31:30,400 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 13787308(55149232); length = 12427089/6553600
2015-10-17 22:31:30,400 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 44663464 kvi 11165860(44663440)
2015-10-17 22:31:41,122 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-17 22:31:41,125 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 44663464 kv 11165860(44663440) kvi 8544432(34177728)
2015-10-17 22:31:41,982 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:31:41,982 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 44663464; bufend = 78840030; bufvoid = 104857600
2015-10-17 22:31:41,982 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 11165860(44663440); kvend = 24952888(99811552); length = 12427373/6553600
2015-10-17 22:31:41,982 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 89325783 kvi 22331440(89325760)
2015-10-17 22:31:49,667 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-17 22:31:49,670 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 89325783 kv 22331440(89325760) kvi 19710012(78840048)
2015-10-17 22:31:50,468 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:31:50,469 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 89325783; bufend = 18642951; bufvoid = 104857595
2015-10-17 22:31:50,469 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 22331440(89325760); kvend = 9903620(39614480); length = 12427821/6553600
2015-10-17 22:31:50,469 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29128707 kvi 7282172(29128688)
2015-10-17 22:31:58,304 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-17 22:31:58,307 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29128707 kv 7282172(29128688) kvi 4660744(18642976)
2015-10-17 22:31:59,113 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:31:59,113 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29128707; bufend = 63305552; bufvoid = 104857600
2015-10-17 22:31:59,113 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7282172(29128688); kvend = 21069272(84277088); length = 12427301/6553600
2015-10-17 22:31:59,113 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 73791312 kvi 18447824(73791296)
