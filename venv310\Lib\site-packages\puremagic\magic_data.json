{"extension_only": [["", 0, ".txt", "text/plain", "Text File"], ["", 0, ".log", "text/plain", "Logger File"], ["", 0, ".yaml", "application/x-yaml", "YAML File"], ["", 0, ".yml", "application/x-yaml", "YAML File"], ["", 0, ".toml", "application/toml", "TOML File"], ["", 0, ".py", "text/x-python", "Python File"], ["", 0, ".pyc", "application/x-python", "Python Complied File"], ["", 0, ".pyd", "application/x-python", "Python Complied File"], ["", 0, ".python_history", "text/plain", "Python History File"], ["", 0, ".bat", "application/x-script", "Windows BAT file"], ["", 0, ".gitconfig", "text/plain", "Git Ignore File"], ["", 0, ".rdp", "", "Windows Remote Desktop File"], ["", 0, ".ini", "text/plain", "INI Config file"], ["", 0, ".key", "", "Encryption Key"], ["", 0, ".pem", "application/x-pem-file", "X.509 Certificate"], ["", 0, ".ps1", "text/plain", "<PERSON><PERSON> Script"], ["", 0, ".ipynb", "", "Jupyter Notebook File"], ["", 0, ".crt", "text/plain", "X.509 Certificate"], ["", 0, ".reg", "", "Windows Registry File"], ["", 0, ".md", "text/plain", "Markdown File"], ["", 0, ".json", "application/json", "JSON File"], ["", 0, ".rst", "text/plain", "Restructured Text File"], ["", 0, ".cfg", "text/plain", "Configuration File"], ["", 0, ".flake8", "text/plain", "Flake 8 Configuration File"], ["", 0, ".coveragerc", "text/plain", "Coverage File"], ["", 0, ".c", "text/x-csrc", "C Code File"], ["", 0, ".cc", "text/x-csrc", "C Code File"], ["", 0, ".h", "text/x-csrc", "C Header File"], ["", 0, ".pdf", "application/pdf", "Adobe Portable Document Format file"], ["", 0, ".stl", "model/stl", "stereolithography CAD software"], ["", 0, ".srt", "application/x-subrip", "SubRip subtitles"], ["", 0, ".obj", "", "Relocatable object code"], ["", 0, ".asx", "video/x-ms-asf", "Advanced Stream Redirector"], ["", 0, ".com", "application/octet-stream", "Windows executable file"], ["", 0, ".sys", "application/octet-stream", "Windows executable file"], ["", 0, ".db4", "", "dBASE IV file"], ["", 0, ".sys", "", "Windows executable"], ["", 0, ".db3", "", "dBASE III file"], ["", 0, ".dat", "", "MapInfo Native Data Format"], ["", 0, ".drw", "application/drafting", "Generic drawing programs"], ["", 0, ".gpg", "", "GPG public keyring"], ["", 0, ".cat", "application/vnd.ms-pki.seccat", "MS security catalog file"], ["", 0, ".xdr", "video/x-amt-demorun", "BizTalk XML-Data Reduced Schema"], ["", 0, ".db", "", "Database, dBASE IV, or dBFast configuration file"], ["", 0, ".bsb", "", "MapInfo Sea Chart"], ["", 0, ".js", "application/javascript", "JavaScript File"], ["", 0, ".jsx", "application/javascript", "JavaScript File"], ["", 0, ".ts", "application/x-typescript", "Transport Stream File"], ["", 0, ".tsx", "application/x-typescript", "Typescript File"], ["", 0, ".conf", "text/plain", "Configuration File"], ["", 0, ".okta", "", "Oktalyzer tracker module (Memory dump format)"], ["", 0, ".jxsi", "image/jxsi", "JPEG XS image"], ["", 0, ".jxss", "image/jxss", "JPEG XS image"], ["", 0, ".jxsv", "video/jxsv", "JPEG XS video"], ["", 0, ".hc", "", "VeraCrypt File Container"], ["", 0, ".cdi", "", "DiscJuggler image"], ["", 0, ".bws", "", "BlindRead Sub Channel Data"], ["", 0, ".bwt", "", "BlindRead Control File"], ["", 0, ".bwi", "", "BlindRead Image File"], ["", 0, ".b5i", "", "BlindWrite 5 Image File"], ["", 0, ".b6i", "", "BlindWrite 6 Image File"], ["", 0, ".cl2", "", "Adaptec Easy CD/DVD Creator image file"], ["", 0, ".cl3", "", "Adaptec Easy CD/DVD Creator image file"], ["", 0, ".cl4", "", "Adaptec Easy CD/DVD Creator image file"], ["", 0, ".vba", "", "Visual Basic Script"], ["", 0, "README", "text/plain", "README File"], ["", 0, ".azw1", "", "Amazon Kindle eBook with Topaz DRM"], ["", 0, ".tpz", "", "Amazon Kindle eBook with Topaz DRM"], ["", 0, ".mbp", "", "Mobipocket/Kindle eBook metadata file"], ["", 0, ".kcr", "", "Kindle Cloud Reader or Kindle for Mac file"], ["", 0, ".azk", "", "Kindle Previewer or Kindle for iOS file"], ["", 0, ".azw9.res", "", "Amazon Kindle for MAC Resource Container file"], ["", 0, ".azw9.ms", "", "Amazon Kindle for MAC Metadata Container file"], ["", 0, ".prc", "", "Palm OS Resource File"], ["", 0, ".lrx", "", "Sony Broad Band (BBeB) DRM encrypted eBook file"], ["", 0, ".lrf", "", "Sony Librie Reader Source eBook file"], ["", 0, ".lmp", "", "Quake image file"], ["", 0, ".rc", "", "Quake resource file"], ["", 0, ".hdp", "", "Microsoft HD Photo image"]], "multi-part": {"7b22": [["227d", -2, ".json", "application/json", "JSON File"]], "7b": [["22", -1, ".json", "application/json", "JSON File"]], "464f524d": [["494c424d", 8, ".iff", "image/x-ilbm", "IFF Interleaved Bitmap Image"], ["38535658", 8, ".iff", "audio/x-8svx", "IFF 8-Bit Sampled Voice"], ["4143424d", 8, ".iff", "application/x-iff", "Amiga Contiguous Bitmap"], ["414e424d", 8, ".iff", "application/x-iff", "IFF Animated Bitmap"], ["414e494d", 8, ".iff", "application/x-iff", " IFF CEL Animation"], ["46415858", 8, ".iff", "application/x-iff", "IFF Facsimile Image"], ["46545854", 8, ".iff", "application/x-iff", "IFF Formatted Text"], ["534d5553", 8, ".iff", "application/x-iff", "IFF Facsimile Image"], ["434d5553", 8, ".iff", "application/x-iff", "IFF Formatted Text"], ["5955564e", 8, ".iff", "application/x-iff", "IFF YUV Image"], ["46414e54", 8, ".iff", "application/x-iff", "Amiga Fantavision Movie"], ["41494646", 8, ".aiff", "audio/x-aiff", "Audio Interchange File Format"], ["41494643", 8, ".aifc", "audio/x-aiff", "Audio Interchange File Format (Compressed)"], ["53434448", 8, ".sc2", "", "SimCity 2000 Map File"]], "52494646": [["57415645", 8, ".wav", "audio/wave", "Waveform Audio File Format"], ["41564920", 8, ".avi", "video/avi", "Audio Video Interleave"], ["********", 8, ".webp", "image/webp", "WebP graphics file format"], ["41434f4e", 8, ".ani", "", "Animated cursor"], ["43444441", 8, ".cda", "", "CD-DA stub file"], ["514c434d", 8, ".qcp", "audio/qcelp", "Qualcomm PureVoice"], ["5644524d", 8, ".vdr", "", "VirtualDub"], ["********", 8, ".trd", "", "TrID"], ["********", 8, ".shw", "", "Corel SHOW! 4.0"], ["********", 8, ".shw", "", "Corel SHOW! 5.0"], ["********", 8, ".shr", "", "Corel SHOW! 5.0 player"], ["********", 8, ".shb", "", " Corel SHOW! 5.0 background"], ["524d4d50", 8, ".mmm", "", "MacroMind Multimedia Movie or Microsoft Multimedia Movie"], ["7366626b4c495354", 8, ".sbk", "", "Creative Labs AWE Soundbank"], ["********56503820", 8, ".webp", "image/webp", "RIFF WebP File (VP8 Lossy Compression)"], ["********5650384c", 8, ".webp", "image/webp", "RIFF WebP File (VP8 Lossless)"], ["********56503858", 8, ".webp", "image/webp", "RIFF WebP File (VP8 Extended File Format)"], ["********", 8, ".webp", "image/webp", "RIFF WebP File"], ["696d6167", 8, ".cif", "application/x-cif", "Adaptec Easy CD/DVD Creator image file"]], "41542654464f524d": [["444a5655", 12, ".djvu", "image/vnd.djvu", "DjVu single page document or image"], ["444a564d", 12, ".djvu", "image/vnd.djvu+multipage", "DjVu document multi-page document"]], "********": [["4647444d", 8, ".dcr", "", "Adobe Shockwave"], ["4d563933", 8, ".dir", "", "Macromedia Director file format"]], "4352454d": [["444f4e4500000000", -8, ".ctm", "", "CreamTracker module"]], "3c747261636b206e616d653d22": [["3c2f747261636b3e0a", -9, ".pt2", "", "PicaTune 2 module"]], "3c6d6c74": [["3c2f6d6c743e0a", -7, ".mlt", "", "Shotcut project"]], "4efa": [["4efa", 12, ".fred", "", "FRED Editor module"]], "5354312e": [["4d6f64756c65494e464f", 6, ".nt", "", "StarTrekker FM Synth Pattern file"]], "494d504d": [["0300", 41, ".mptm", "", "OpenMPT ********** to OpenMPT ********** module"], ["0888", 41, ".mptm", "", "OpenMPT ********** to OpenMPT 1.18 module"], ["14021402", 40, ".it", "audio/x-it", "UNMO3 Impulse Tracker module"], ["04020002", 40, ".it", "audio/x-it", "Unknown XM to IT converter module"], ["14020002", 40, ".it", "audio/x-it", "OpenSPC Impulse Tracker module"], ["02020002", 40, ".mptm", "", "ModPlug Tracker 1.0 pre-alpha module"], ["14020002", 40, ".mptm", "", "ModPlug Tracker 1.0 alpha module"], ["14020202", 40, ".mptm", "", "ModPlug Tracker 1.0 beta module"], ["17020002", 40, ".mptm", "", "ModPlug Tracker / OpenMPT 1.17 module"], ["14021402", 40, ".ct", "", "CheeseTracker module"], ["43484249", 60, ".ct", "", "ChibiTracker module"], ["2551", 40, ".mptm", "", "OpenMPT 1.22 module"], ["2551", 40, ".mptm", "", "OpenMPT 1.25 module"], ["2651", 40, ".mptm", "", "OpenMPT 1.26 module"], ["2851", 40, ".mptm", "", "OpenMPT 1.28+ module"], ["1402", 40, ".it", "audio/x-it", "Impulse Tracker 2.14 module"], ["1702", 40, ".it", "audio/x-it", "Impulse Tracker 2.14a3 module"], ["6000", 40, ".it", "", "BeRo Tracker module"], ["17020002", 40, ".it", "", "BeRo Tracker (old) module"], ["17021402", 40, ".it", "", "BeRo Tracker (old) module"]], "30313233343536373839": [["30313233343536373839", -62, ".puremagic_multi_footer", "text/ascii", "TESTFILE"]], "494433": [["544147", -128, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) audio file"]], "4944330200": [["544147", -128, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["425546", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["434E54", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["434F4D", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["435241", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["43524D", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["455443", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["455155", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["47454F", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["49504C", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["4C4E4B", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["4D4349", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["4D4C4C", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["504943", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["504F50", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["524556", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["525641", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["534C54", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["535443", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["54414C", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["544250", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["54434D", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["54434F", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["544352", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["544441", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["544459", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["54454E", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["544654", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["54494D", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["544B45", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["544C41", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["544C45", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["544D54", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["544F41", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["544F46", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["544F4C", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["544F52", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["544F54", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["545031", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["545032", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["545033", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["545034", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["545041", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["545042", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["545243", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["545244", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["54524B", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["545349", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["545353", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["545431", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["545432", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["545433", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["545854", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["545858", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["545945", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["554649", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["554C54", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["574146", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["574152", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["574153", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["57434D", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["574350", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["575042", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"], ["575858", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.2.0 audio file"]], "4944330300": [["41454E43", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["41504943", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["41535049", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["434F4D4D", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["434F4D52", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["454E4352", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["45515532", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["4554434F", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["47454F42", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["47524944", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["4C494E4B", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["4D434449", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["4D4C4C54", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["4F574E45", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["50524956", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["50434E54", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["504F504D", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["504F5353", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["52425546", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["52564132", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["52565242", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["5345454B", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["5349474E", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["53594C54", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["53595443", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54414C42", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["5442504D", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54434F4D", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54434F4E", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54434F50", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["5444454E", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54444C59", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54444F52", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54445243", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["5444524C", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54445447", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54454E43", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54455854", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54464C54", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["5449504C", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54495431", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54495432", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54495433", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["544B4559", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["544C414E", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["544C454E", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["544D434C", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["544D4544", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["544D4F4F", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["544F414C", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["544F464E", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["544F4C59", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["544F5045", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["544F574E", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54504531", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54504532", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54504533", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54504534", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54504F53", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["5450524F", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54505542", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["5452434B", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["5452534E", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["5452534F", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54534F41", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54534F50", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54534F54", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54535243", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54535345", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54535354", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["54585858", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["55464944", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["55534552", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["55534C54", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["57434F4D", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["57434F50", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["574F4146", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["574F4152", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["574F4153", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["574F5253", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["57504159", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["57505542", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["57585858", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"], ["544147", -128, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.3.0 audio file"]], "4944330400": [["41454E43", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["41504943", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["41535049", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["434F4D4D", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["434F4D52", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["454E4352", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["45515532", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["4554434F", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["47454F42", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["47524944", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["4C494E4B", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["4D434449", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["4D4C4C54", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["4F574E45", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["50524956", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["50434E54", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["504F504D", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["504F5353", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["52425546", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["52564132", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["52565242", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["5345454B", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["5349474E", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["53594C54", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["53595443", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54414C42", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["5442504D", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54434F4D", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54434F4E", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54434F50", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["5444454E", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54444C59", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54444F52", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54445243", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["5444524C", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54445447", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54454E43", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54455854", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54464C54", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["5449504C", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54495431", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54495432", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54495433", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["544B4559", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["544C414E", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["544C454E", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["544D434C", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["544D4544", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["544D4F4F", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["544F414C", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["544F464E", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["544F4C59", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["544F5045", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["544F574E", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54504531", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54504532", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54504533", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54504534", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54504F53", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["5450524F", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54505542", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["5452434B", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["5452534E", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["5452534F", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54534F41", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54534F50", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54534F54", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54535243", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54535345", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54535354", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["54585858", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["55464944", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["55534552", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["55534C54", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["57434F4D", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["57434F50", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["574F4146", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["574F4152", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["574F4153", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["574F5253", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["57504159", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["57505542", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["57585858", 10, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"], ["544147", -128, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) ID3v2.4.0 audio file"]], "01da": [["00010001", 2, ".rgb", "image/x-rgb", "Silicon Graphics RGB Bitmap (Uncompressed, 1bpc, single row)"], ["01010001", 2, ".rgb", "image/x-rgb", "Silicon Graphics RGB Bitmap (RLE compressed, 1bpc, single row)"], ["00020001", 2, ".rgb", "image/x-rgb", "Silicon Graphics RGB Bitmap (Uncompressed, 2bpc, single row)"], ["01020001", 2, ".rgb", "image/x-rgb", "Silicon Graphics RGB Bitmap (RLE compressed, 2bpc, single row)"], ["00010002", 2, ".rgb", "image/x-rgb", "Silicon Graphics RGB Bitmap (Uncompressed, 1bpc, 2D Image)"], ["01010002", 2, ".rgb", "image/x-rgb", "Silicon Graphics RGB Bitmap (RLE compressed, 1bpc, 2D Image)"], ["00020002", 2, ".rgb", "image/x-rgb", "Silicon Graphics RGB Bitmap (Uncompressed, 2bpc, 2D Image)"], ["01020002", 2, ".rgb", "image/x-rgb", "Silicon Graphics RGB Bitmap (RLE compressed, 2bpc, Multiple 2D Images)"], ["00010003", 2, ".rgb", "image/x-rgb", "Silicon Graphics RGB Bitmap (Uncompressed, 1bpc, Multiple 2D Images)"], ["01010003", 2, ".rgb", "image/x-rgb", "Silicon Graphics RGB Bitmap (RLE compressed, 1bpc, Multiple 2D Images)"], ["00020003", 2, ".rgb", "image/x-rgb", "Silicon Graphics RGB Bitmap (Uncompressed, 2bpc, Multiple 2D Images)"], ["01020003", 2, ".rgb", "image/x-rgb", "Silicon Graphics RGB Bitmap (RLE compressed, 2bpc, Multiple 2D Images)"]], "59a66a95": [["00000000", 22, ".sun", "image/x-sun-raster", "Sun raster image (Old, No color map)"], ["00010000", 22, ".sun", "image/x-sun-raster", "Sun raster image (Standard, No color map)"], ["00020000", 22, ".sun", "image/x-sun-raster", "Sun raster image (Byte-Encoded, No color map)"], ["00030000", 22, ".sun", "image/x-sun-raster", "Sun raster image (RGB format, No color map)"], ["00040000", 22, ".sun", "image/x-sun-raster", "Sun raster image (TIFF format, No color map)"], ["00050000", 22, ".sun", "image/x-sun-raster", "Sun raster image (IFF format, No color map)"], ["FFFF0000", 22, ".sun", "image/x-sun-raster", "Sun raster image (Experimental, No color map)"], ["00000001", 22, ".sun", "image/x-sun-raster", "Sun raster image (Old, RGB color map)"], ["00010001", 22, ".sun", "image/x-sun-raster", "Sun raster image (Standard, RGB color map)"], ["00020001", 22, ".sun", "image/x-sun-raster", "Sun raster image (Byte-Encoded, RGB color map)"], ["00030001", 22, ".sun", "image/x-sun-raster", "Sun raster image (RGB format, RGB color map)"], ["00040001", 22, ".sun", "image/x-sun-raster", "Sun raster image (TIFF format, RGB color map)"], ["00050001", 22, ".sun", "image/x-sun-raster", "Sun raster image (IFF format, RGB color map)"], ["FFFF0001", 22, ".sun", "image/x-sun-raster", "Sun raster image (Experimental, RGB color map)"], ["00000002", 22, ".sun", "image/x-sun-raster", "Sun raster image (Old, RAW color map)"], ["00010002", 22, ".sun", "image/x-sun-raster", "Sun raster image (Standard, RAW color map)"], ["00020002", 22, ".sun", "image/x-sun-raster", "Sun raster image (Byte-Encoded, RAW color map)"], ["00030002", 22, ".sun", "image/x-sun-raster", "Sun raster image (RGB format, RAW color map)"], ["00040002", 22, ".sun", "image/x-sun-raster", "Sun raster image (TIFF format, RAW color map)"], ["00050002", 22, ".sun", "image/x-sun-raster", "Sun raster image (IFF format, RAW color map)"], ["FFFF0002", 22, ".sun", "image/x-sun-raster", "Sun raster image (Experimental, RAW color map)"]], "716f6966": [["0300", 12, ".qoi", "", "Quite OK image (RGB, sRGB with linear alpha)"], ["0301", 12, ".qoi", "", "Quite OK image (RGB, All channels alpha)"], ["0400", 12, ".qoi", "", "Quite OK image (RGBA, sRGB with linear alpha)"], ["0401", 12, ".qoi", "", "Quite OK image (RGBA, All channels alpha)"]], "5031": [["20", 2, ".pbm", "image/x-portable-bitmap", "PBM image (Ascii)"], ["0a", 2, ".pbm", "image/x-portable-bitmap", "PBM image (Ascii)"], ["0d", 2, ".pbm", "image/x-portable-bitmap", "PBM image (Ascii)"], ["09", 2, ".pbm", "image/x-portable-bitmap", "PBM image (Ascii)"], ["2023", 2, ".pbm", "image/x-portable-bitmap", "PBM image (Ascii)"], ["0a23", 2, ".pbm", "image/x-portable-bitmap", "PBM image (Ascii)"], ["0d23", 2, ".pbm", "image/x-portable-bitmap", "PBM image (Ascii)"], ["0923", 2, ".pbm", "image/x-portable-bitmap", "PBM image (Ascii)"]], "5034": [["20", 2, ".pbm", "image/x-portable-bitmap", "PBM image (Binary)"], ["0a", 2, ".pbm", "image/x-portable-bitmap", "PBM image (Binary)"], ["0d", 2, ".pbm", "image/x-portable-bitmap", "PBM image (Binary)"], ["09", 2, ".pbm", "image/x-portable-bitmap", "PBM image (Binary)"], ["2023", 2, ".pbm", "image/x-portable-bitmap", "PBM image (Binary)"], ["0a23", 2, ".pbm", "image/x-portable-bitmap", "PBM image (Binary)"], ["0d23", 2, ".pbm", "image/x-portable-bitmap", "PBM image (Binary)"], ["0923", 2, ".pbm", "image/x-portable-bitmap", "PBM image (Binary)"]], "5032": [["20", 2, ".pgm", "image/x-portable-graymap", "PGM image (Ascii)"], ["0a", 2, ".pgm", "image/x-portable-graymap", "PGM image (Ascii)"], ["0d", 2, ".pgm", "image/x-portable-graymap", "PGM image (Ascii)"], ["09", 2, ".pgm", "image/x-portable-graymap", "PGM image (Ascii)"], ["2023", 2, ".pgm", "image/x-portable-graymap", "PGM image (Ascii)"], ["0a23", 2, ".pgm", "image/x-portable-graymap", "PGM image (Ascii)"], ["0d23", 2, ".pgm", "image/x-portable-graymap", "PGM image (Ascii)"], ["0923", 2, ".pgm", "image/x-portable-graymap", "PGM image (Ascii)"]], "5035": [["20", 2, ".pgm", "image/x-portable-graymap", "PGM image (Binary)"], ["0a", 2, ".pgm", "image/x-portable-graymap", "PGM image (Binary)"], ["0d", 2, ".pgm", "image/x-portable-graymap", "PGM image (Binary)"], ["09", 2, ".pgm", "image/x-portable-graymap", "PGM image (Binary)"], ["2023", 2, ".pgm", "image/x-portable-graymap", "PGM image (Binary)"], ["0a23", 2, ".pgm", "image/x-portable-graymap", "PGM image (Binary)"], ["0d23", 2, ".pgm", "image/x-portable-graymap", "PGM image (Binary)"], ["0923", 2, ".pgm", "image/x-portable-graymap", "PGM image (Binary)"]], "5033": [["20", 2, ".ppm", "image/x-portable-pixmap", "PPM image (Ascii)"], ["0a", 2, ".ppm", "image/x-portable-pixmap", "PPM image (Ascii)"], ["0d", 2, ".ppm", "image/x-portable-pixmap", "PPM image (Ascii)"], ["09", 2, ".ppm", "image/x-portable-pixmap", "PPM image (Ascii)"], ["2023", 2, ".ppm", "image/x-portable-pixmap", "PPM image (Ascii)"], ["0a23", 2, ".ppm", "image/x-portable-pixmap", "PPM image (Ascii)"], ["0d23", 2, ".ppm", "image/x-portable-pixmap", "PPM image (Ascii)"], ["0923", 2, ".ppm", "image/x-portable-pixmap", "PPM image (Ascii)"]], "5036": [["20", 2, ".ppm", "image/x-portable-pixmap", "PPM image (Binary)"], ["0a", 2, ".ppm", "image/x-portable-pixmap", "PPM image (Binary)"], ["0d", 2, ".ppm", "image/x-portable-pixmap", "PPM image (Binary)"], ["09", 2, ".ppm", "image/x-portable-pixmap", "PPM image (Binary)"], ["2023", 2, ".ppm", "image/x-portable-pixmap", "PPM image (Binary)"], ["0a23", 2, ".ppm", "image/x-portable-pixmap", "PPM image (Binary)"], ["0d23", 2, ".ppm", "image/x-portable-pixmap", "PPM image (Binary)"], ["0923", 2, ".ppm", "image/x-portable-pixmap", "PPM image (Binary)"]], "5046": [["0a", 2, ".pfm", "", "Portable Float Map (Colour)"], ["0d", 2, ".pbm", "", "Portable Float Map (Colour)"]], "5066": [["0a", 2, ".pfm", "", "Portable Float Map (Greyscale)"], ["0d", 2, ".pfm", "", "Portable Float Map (Greyscale)"]], "504634": [["0a", 3, ".pfm", "", "Augmented Portable Float Map"], ["0d", 3, ".pfm", "", "Augmented Portable Float Map"]], "5037": [["0a", 2, ".pam", "image/x-portable-arbitrarymap", "Portable Arbitrary Map"], ["0d", 2, ".pam", "image/x-portable-arbitrarymap", "Portable Arbitrary Map"], ["0a5749445448", 2, ".pam", "image/x-portable-arbitrarymap", "Portable Arbitrary Map"], ["0d5749445448", 2, ".pam", "image/x-portable-arbitrarymap", "Portable Arbitrary Map"], ["0a484549474854", 2, ".pam", "image/x-portable-arbitrarymap", "Portable Arbitrary Map"], ["0d484549474854", 2, ".pam", "image/x-portable-arbitrarymap", "Portable Arbitrary Map"]], "73696262": [["72686c62", 8, ".uif", "", "MagicISO Disk Image (Encrypted)"]], "49492a00": [["435202", 8, ".cr2", "", "Canon Camera RAW 2 image"]], "424f4f4b4d4f4249": [["e98e0d0a", -4, ".mobi", "application/x-mobipocket-ebook", "Mobipocket eBook file"], ["e98e0d0a", -4, ".azw", "application/vnd.amazon.mobi8-ebook", "Amazon Kindle eBook file"], ["434f4e54424f554e44415259e98e0d0a", -16, ".azw3", "application/vnd.amazon.mobi8-ebook", "Amazon Kindle Format 8 eBook file (KF8 Dual MOBI/EPUB Format)"]], "3f5f0300": [["0000ffffffff", 6, ".hlp", "application/winhlp", "Windows Help file"], ["0000ffffffff", 6, ".gid", "", "Windows Help Global Index file"]], "5041434b": [["4944504f", 12, ".pak", "", "Quake archive file"], ["52494646", 12, ".pak", "", "Quake archive file"], ["49425350", 12, ".pak", "", "Quake archive file"]], "28": [["2e", -1, ".pickle", "", "Python Pickle file (Protocol 0)"]], "7d71": [["2e", -1, ".pickle", "", "Python Pickle file (Protocol 1)"]], "8002": [["2e", -1, ".pickle", "", "Python Pickle file (Protocol 2)"]], "8003": [["2e", -1, ".pickle", "", "Python Pickle file (Protocol 3)"]], "8004": [["2e", -1, ".pickle", "", "Python Pickle file (Protocol 4)"]], "8005": [["2e", -1, ".pickle", "", "Python Pickle file (Protocol 5)"]], "464c5601": [["04", 4, ".flv", "video/x-flv", "Adobe Flash video file (Audio only)"], ["01", 4, ".flv", "video/x-flv", "Adobe Flash video file (Video only)"], ["05", 4, ".flv", "video/x-flv", "Adobe Flash video file (Audio and Video)"]], "4949bc01": [["574d50484f544f00", 90, ".jxr", "image/jxr", "JPEG XR image"]], "444f53": [["00", 3, ".adf", "application/x-amiga-disk-format", "Amiga disk image (OFS)"], ["01", 3, ".adf", "application/x-amiga-disk-format", "Amiga disk image (FFS)"], ["02", 3, ".adf", "application/x-amiga-disk-format", "Amiga disk image (OFS International)"], ["03", 3, ".adf", "application/x-amiga-disk-format", "Amiga disk image (FFS International)"], ["04", 3, ".adf", "application/x-amiga-disk-format", "Amiga disk image (OFS International and Directory Cache)"], ["05", 3, ".adf", "application/x-amiga-disk-format", "Amiga disk image (FFS International and Directory Cache)"], ["06", 3, ".adf", "application/x-amiga-disk-format", "Amiga disk image (OFS Long Filename)"], ["07", 3, ".adf", "application/x-amiga-disk-format", "Amiga disk image (FFS Long Filename)"]]}, "footers": [["54525545564953494f4e2d5846494c452e00", -18, ".tga", "image/tga", "Truevision Targa Graphic file"], ["000001b7", -4, ".mpeg", "video/mpeg", "MPEG video file"], ["3c2f7376673e", -8, ".svg", "image/svg+xml", "Scalable Vector Graphics Image"], ["3c2f7376673e", -7, ".svg", "image/svg+xml", "Scalable Vector Graphics Image"], ["3c2f7376673e", -6, ".svg", "image/svg+xml", "Scalable Vector Graphics Image"], ["6b6f6c79", -512, ".dmg", "application/x-apple-diskimage", "MacOS X image file"], ["4e45524f", -8, ".nrg", "", "Nero Disk Image (Version 1)"], ["4e455235", -12, ".nrg", "", "Nero Disk Image (Version 2)"]], "headers": [["595556344d504547", 0, ".y4m", "video/x-yuv4mpeg", "YUV4MPEG2 video file"], ["3c68746d6c", 0, ".html", "text/html", "HTML File"], ["424c5545", 0, ".bvr", "", "Blue Iris Video File"], ["2d2d2d2d2d424547494e20504b4353372d2d2d2d2d", 0, ".p7b", "", "PKCS 7 Certificate File"], ["7b22", 0, ".json", "application/json", "JSON File"], ["7b", 0, ".json", "application/json", "JSON File"], ["50755454592d557365722d4b65792d46696c65", 0, ".ppk", "", "PuTTY User Key File"], ["2d2d2d2d20424547494e2053534832205055424c4943204b4559202d2d2d2d", 0, "", "", "SSH Public Key"], ["2d2d2d2d424547494e", 0, "", "", "Key or Cert File"], ["2d2d2d2d20424547494e", 0, "", "", "Key or Cert File"], ["30313233343536373839", 0, ".puremagic_multi_footer", "text/ascii", "TESTFILE"], ["ff0a", 0, ".jxl", "image/jxl", "JPEG XL image (Raw stream)"], ["0000000c4a584c200d0a870a", 0, ".jxl", "image/jxl", "JPEG XL image (ISOBMFF container)"], ["3c3f786d6c", 0, ".xml", "application/xml", "XML Document"], ["454c46", 1, ".AppImage", "application/x-iso9660-appimage", "AppImage application bundle"], ["4341434845204d414e4946455354", 0, ".manifest", "text/cache-manifest", "Web application cache manifest"], ["425a68", 0, ".tar.bz2", "application/x-bzip2", "bzip2 compressed archive"], ["64383a616e6e6f756e6365", 0, ".torrent", "application/x-bittorrent", "BitTorrent seed file"], ["504b5c3030335c303034", 0, ".fb2.zip", "application/x-zip-compressed-fb2", "Compressed FictionBook document"], ["3c3f786d6c", 0, ".docbook", "application/x-docbook+xml", "DocBook document"], ["302048454144", 0, ".gedcom", "application/x-gedcom", "GEDCOM family history"], ["5b4465736b746f7020416374696f6e", 0, ".kdelnk", "application/x-desktop", "desktop configuration file"], ["5b4b4445204465736b746f7020456e7472795d", 0, ".kdelnk", "application/x-desktop", "desktop configuration file"], ["2320436f6e6669672046696c65", 0, ".kdelnk", "application/x-desktop", "desktop configuration file"], ["23204b444520436f6e6669672046696c65", 0, ".kdelnk", "application/x-desktop", "desktop configuration file"], ["5c303031666370", 0, ".pcf.gz", "application/x-font-pcf", "PCF font"], ["5c3033375c323133", 0, ".karbon", "application/x-karbon", "Karbon14 drawing"], ["504b5c3030335c303034", 0, ".karbon", "application/x-karbon", "Karbon14 drawing"], ["517469506c6f74", 0, ".qti.gz", "application/x-qtiplot", "QtiPlot document"], ["4253444946463430", 0, ".bsdiff", "application/x-bsdiff", "binary differences between files"], ["42534449464e3430", 0, ".bsdiff", "application/x-bsdiff", "binary differences between files"], ["7864672d6170705c7830305c7830315c7830305c7838395c786535", 0, ".xdgapp", "application/vnd.flatpak", "Flatpak application bundle"], ["666c617470616b5c7830305c7830315c7830305c7838395c786535", 0, ".xdgapp", "application/vnd.flatpak", "Flatpak application bundle"], ["000100005374616e6461726420414345204442", 0, ".accdb", "application/msaccess", "Microsoft Access 2007 file"], ["62706c697374", 0, ".plist", "application/x-plist", "Binary Property list"], ["cafebabe", 0, ".class", "application/java", "Java bytecode"], ["4d52564e", 0, ".nvram", "", "VMware BIOS state file"], ["1f9d90", 0, ".tar.z", "", "Compressed tape archive"], ["1fa0", 0, ".tar.z", "", "Compressed tape archive"], ["424c454e444552", 0, ".blend", "application/x-blender", "Blender scene"], ["66747970336732", 4, ".3gpp2", "video/3gpp2", "3GPP2 multimedia file"], ["646966665c74", 0, ".patch", "text/x-patch", "differences between files"], ["6469666620", 0, ".patch", "text/x-patch", "differences between files"], ["2a2a2a5c74", 0, ".patch", "text/x-patch", "differences between files"], ["2a2a2a20", 0, ".patch", "text/x-patch", "differences between files"], ["3d3d3d20", 0, ".patch", "text/x-patch", "differences between files"], ["2d2d2d20", 0, ".patch", "text/x-patch", "differences between files"], ["4f6e6c7920696e5c74", 0, ".patch", "text/x-patch", "differences between files"], ["4f6e6c7920696e20", 0, ".patch", "text/x-patch", "differences between files"], ["436f6d6d6f6e207375626469726563746f726965733a20", 0, ".patch", "text/x-patch", "differences between files"], ["496e6465783a", 0, ".patch", "text/x-patch", "differences between files"], ["646f63756d656e74636c617373", 1, ".latex", "text/x-tex", "TeX document"], ["4d4f5649", 0, ".movie", "video/x-sgi-movie", "SGI video"], ["425a68", 0, ".bzip2", "application/x-bzip2", "BZIP2 Compressed Archive file"], ["664c614300000022", 0, ".flac", "audio/flac", "Free Lossless Audio Codec file"], ["434f5744", 0, ".vmdk", "application/octet-stream", "VMware Sparse Extent Image file"], ["23204469736b2044657363726970746f7246696c65", 0, ".vmdk", "application/octet-stream", "VMware Image Descriptor File"], ["4b444d56", 0, ".vmdk", "application/octet-stream", "VMware Virtual Single Disk file"], ["e310000100000000", 0, ".info", "", "Amiga icon"], ["5468697320697320", 0, ".info", "", "GNU Info Reader file"], ["456c6646696c6500", 0, ".evtx", "", "Windows Vista event log"], ["4d444d5093a7", 0, ".hdmp", "", "Windows dump file"], ["464f524d", 0, ".aiff", "audio/aiff", "Audio Interchange File"], ["2e524d46", 0, ".rmvb", "", "RealMedia streaming media"], ["504b0304", 0, ".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "MS Office Open XML Format Document"], ["504b0304", 0, ".pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation", "MS Office Open XML Format Document"], ["504b0304", 0, ".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "MS Office Open XML Format Document"], ["504b0304", 0, ".xlsb", "application/vnd.ms-excel.sheet.binary.macroenabled.12", "Microsoft Excel - Binary Workbook"], ["504b0304", 0, ".xltm", "application/vnd.ms-excel.template.macroenabled.12", "Microsoft Excel - Macro-Enabled Template File"], ["504b0304", 0, ".xltx", "application/vnd.openxmlformats-officedocument.spreadsheetml.template", "Microsoft Office - OOXML - Spreadsheet Template"], ["504b0304", 0, ".xlam", "application/vnd.ms-excel.addin.macroenabled.12", "Microsoft Excel - Add-In File"], ["504b0304", 0, ".docm", "application/vnd.ms-word.document.macroEnabled.12", "Microsoft Word - Macro-Enabled Document"], ["504b0304", 0, ".dotx", "application/vnd.openxmlformats-officedocument.wordprocessingml.template", "Microsoft Office - OOXML - Word Document Template"], ["504b0304", 0, ".dotm", "application/vnd.ms-word.template.macroenabled.12", "Microsoft Word - Macro-Enabled Template"], ["504b0304", 0, ".pptm", "application/vnd.ms-powerpoint.presentation.macroEnabled.12", "Microsoft PowerPoint - Macro-Enabled Presentation File"], ["504b0304", 0, ".potx", "application/vnd.openxmlformats-officedocument.presentationml.template", "Microsoft Office - OOXML - Presentation Template"], ["504b0304", 0, ".potm", "application/vnd.ms-powerpoint.template.macroenabled.12", "Microsoft PowerPoint - Macro-Enabled Template File"], ["504b0304", 0, ".xlsm", "application/vnd.ms-excel.sheet.macroEnabled.12", "Microsoft Excel - Macro-Enabled Workbook"], ["7a626578", 0, ".info", "", "ZoomBrowser Image Index"], ["425a68", 0, ".tbz2", "application/x-bzip2", "bzip2 compressed archive"], ["ffd8ff", 0, ".jfif", "image/jpeg", "JPEG|EXIF|SPIFF images"], ["514649", 0, ".qemu", "", "Qcow Disk Image"], ["504b5c3030335c303034", 0, ".epub", "application/epub+zip", "electronic book document"], ["6d696d65747970656170706c69636174696f6e2f657075622b7a6970", 30, ".epub", "application/epub+zip", "electronic book document"], ["46726f6d20", 0, ".mbox", "application/mbox", "mailbox file"], ["232552414d4c20", 0, ".raml", "application/raml+yaml", "RAML document"], ["7a1a2010", 0, ".sisx", "x-epoc/x-sisx-app", "SISX package"], ["24464c32", 0, ".zsav", "application/x-spss-sav", "SPSS Data File"], ["24464c33", 0, ".zsav", "application/x-spss-sav", "SPSS Data File"], ["303730373031", 0, ".cpio", "application/x-cpio", "CPIO archive"], ["303730373032", 0, ".cpio", "application/x-cpio", "CPIO archive"], ["213c617263683e", 0, ".udeb", "application/vnd.debian.binary-package", "Debian package"], ["774f4646", 0, ".woff", "application/font-woff", "WOFF font"], ["cafed00d", 0, ".pack", "application/x-java-pack200", "Pack200 Java archive"], ["5c3033375c323133", 0, ".chrt", "application/x-kchart", "KChart chart"], ["504b5c3030335c303034", 0, ".chrt", "application/x-kchart", "KChart chart"], ["1a45dfa3", 0, ".webm", "video/webm", "WebM video"], ["4f676753", 0, ".opus", "audio/ogg", "Ogg Audio"], ["4f676753", 0, ".opus", "audio/x-opus+ogg", "Opus audio"], ["4f6c656f", 31, ".oleo", "application/x-oleo", "GNU Oleo spreadsheet"], ["50415232", 0, ".par2", "application/x-par2", "Parchive archive"], ["4d4d4d44", 0, ".smaf", "application/x-smaf", "SMAF audio"], ["50534944", 0, ".psid", "audio/prs.sid", "Commodore 64 audio"], ["664c6143", 0, ".flac", "audio/flac", "FLAC audio"], ["fffb", 0, ".mpga", "audio/mpeg", "MP3 audio"], ["234558544d3355", 0, ".m3u8", "application/vnd.apple.mpegurl", "HTTP Live Streaming playlist"], ["2521", 0, ".epsf", "image/x-eps", "EPS image"], ["5c3030342521", 0, ".epsf", "image/x-eps", "EPS image"], ["53494d504c4520203d", 0, ".fits", "image/fits", "FITS document"], ["69636e73", 0, ".icns", "image/x-icns", "MacOS X icon"], ["53202020202020315c783061", 72, ".iges", "model/iges", "IGES document"], ["53303030303030315c783061", 72, ".iges", "model/iges", "IGES document"], ["424547494e3a5643415244", 0, ".gcrd", "text/vcard", "electronic business card"], ["626567696e3a7663617264", 0, ".gcrd", "text/vcard", "electronic business card"], ["646e3a20636e3d", 0, ".ldif", "text/x-ldif", "LDIF address book"], ["646e3a206d61696c3d", 0, ".ldif", "text/x-ldif", "LDIF address book"], ["23212f62696e2f707974686f6e", 0, ".wsgi", "text/x-python", "Python script"], ["2321202f62696e2f707974686f6e", 0, ".wsgi", "text/x-python", "Python script"], ["6576616c205c2265786563202f62696e2f707974686f6e", 0, ".wsgi", "text/x-python", "Python script"], ["23212f7573722f62696e2f707974686f6e", 0, ".wsgi", "text/x-python", "Python script"], ["2321202f7573722f62696e2f707974686f6e", 0, ".wsgi", "text/x-python", "Python script"], ["6576616c205c2265786563202f7573722f62696e2f707974686f6e", 0, ".wsgi", "text/x-python", "Python script"], ["23212f7573722f6c6f63616c2f62696e2f707974686f6e", 0, ".wsgi", "text/x-python", "Python script"], ["2321202f7573722f6c6f63616c2f62696e2f707974686f6e", 0, ".wsgi", "text/x-python", "Python script"], ["6576616c205c2265786563202f7573722f6c6f63616c2f62696e2f707974686f6e", 0, ".wsgi", "text/x-python", "Python script"], ["53756d6d6172793a20", 0, ".spec", "text/x-rpm-spec", "RPM spec file"], ["25646566696e6520", 0, ".spec", "text/x-rpm-spec", "RPM spec file"], ["667479707174", 4, ".qtvr", "video/quicktime", "QuickTime video"], ["41564630", 0, ".divx", "video/x-msvideo", "AVI video"], ["4954382e37", 0, ".it87", "application/x-it87", "IT 8.7 color calibration file"], ["43434d58", 0, ".ccmx", "application/x-ccmx", "CCMX color correction file"], ["73717368", 0, ".sqsh", "application/vnd.squashfs", "Squashfs filesystem"], ["68737173", 0, ".sqsh", "application/vnd.squashfs", "Squashfs filesystem"], ["0000002066747970336770", 0, ".3g2", "video/3gpp2", "3GPP2 3rd Generation Partnership Project video file"], ["0000002066747970336770", 0, ".3gpp", "video/3gpp2", "3GPP2 3rd Generation Partnership Project video file"], ["492049", 0, ".tiff", "image/tiff", "Tagged Image File Format file"], ["49492a00", 0, ".tiff", "image/tiff", "Tagged Image File Format file (Intel)"], ["4d4d002a", 0, ".tiff", "image/tiff", "Tagged Image File Format file (Motorola)"], ["4d4d002b", 0, ".tiff", "image/tiff", "BigTIFF files, Tagged Image File Format file larger than 4 GB"], ["ffd8ff", 0, ".jpeg", "image/jpeg", "JPEG/JFIF graphics file"], ["000000146674797069736f6d", 0, ".mp4", "video/mp4", "MPEG-4 video file"], ["0000001c667479704d534e56012900464d534e566d703432", 0, ".mp4", "video/mp4", "MPEG-4 video file"], ["000000146674797071742020", 0, ".mov", "video/quicktime", "QuickTime movie file"], ["000000186674797033677035", 0, ".mp4", "video/mp4", "MPEG-4 video files"], ["00000018667479706d703432", 0, ".mp4", "video/mp4", "MPEG-4 video file"], ["3026b2758e66cf11a6d900aa0062ce6c", 0, ".wmv", "video/x-ms-wmv", "Microsoft Windows Media Video File"], ["464c5601", 0, ".flv", "video/x-flv", "Adobe flash video file"], ["415649204c495354", 8, ".avi", "video/x-msvideo", "Windows Audio Video Interleave file"], ["2e524543", 0, ".ivr", "i-world/i-vrml", "RealPlayer video file"], ["6d6f6f76", 4, ".mov", "video/quicktime", "QuickTime movie file"], ["3026b2758e66cf11a6d900aa0062ce6c", 0, ".wma", "audio/x-ms-wma", "Microsoft Windows Media Audio file"], ["494433", 0, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 (MP3) audio file"], ["4944330200", 0, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 ID3v2.2.0 (MP3) audio file"], ["4944330300", 0, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 ID3v2.3.0 (MP3) audio file"], ["4944330400", 0, ".mp3", "audio/mpeg", "MPEG-1 Audio Layer 3 ID3v2.4.0 (MP3) audio file"], ["4f67675300020000000000000000", 0, ".ogg", "application/ogg", "Ogg Vorbis audio file"], ["57415645666d7420", 8, ".wav", "audio/x-wav", "Windows audio file "], ["464f524d", 0, ".aif", "audio/x-aiff", "Audio Interchange File"], ["fff94c80", 0, ".aac", "audio/aac", "AAC audio file"], ["474946383761", 0, ".gif", "image/gif", "Graphics interchange format file (GIF87a)"], ["474946383961", 0, ".gif", "image/gif", "Graphics interchange format file (GIF89a)"], ["00000100", 0, ".ico", "image/x-icon", "Icon Image file"], ["0100000001", 0, ".pic", "image/x-pict", "PICT Image file"], ["424d", 0, ".bmp", "image/x-ms-bmp", "Microsoft Windows Bitmap image"], ["4241", 0, ".bga", "", "OS/2 struct bitmap array"], ["4349", 0, ".ico", "", "OS/2 struct color icon"], ["4350", 0, ".cp", "", "OS/2 const color pointer"], ["4943", 0, ".ico", "", "OS/2 struct icon"], ["5054", 0, ".pt", "", "OS/2 pointer"], ["38425053", 0, ".psd", "image/vnd.adobe.photoshop", "Photoshop Image file"], ["89504e470d0a1a0a", 0, ".png", "image/png", "Portable Network Graphics file"], ["67696d702078636620", 0, ".xcf", "image/x-xcf", "XCF Gimp Image file"], ["eca5c100", 512, ".doc", "application/msword", "Microsoft Office Word Document file"], ["006e1ef0", 512, ".ppt", "application/vnd.ms-powerpoint", "Microsoft Office PowerPoint Presentation file"], ["0f00e803", 512, ".ppt", "application/vnd.ms-powerpoint", "Microsoft Office PowerPoint Presentation file"], ["000100004d534953414d204461746162617365", 0, ".mny", "application/x-msmoney", "Microsoft Money file"], ["000100005374616e64617264204a6574204442", 0, ".mdb", "application/x-msaccess", "Microsoft Access file"], ["25504446", 0, ".pdf", "application/pdf", "Adobe Portable Document Format file"], ["0d0a25504446", 0, ".pdf", "application/pdf", "Adobe Portable Document Format file"], ["a0461df0", 512, ".ppt", "application/vnd.ms-powerpoint", "Microsoft Office PowerPoint Presentation file"], ["cf11e0a1b11ae100", 0, ".doc", "application/msword", "Perfect Office Document file"], ["d0cf11e0a1b11ae1", 0, ".doc", "application/msword", "Microsoft Office Document file"], ["fdffffff", 512, ".ppt", "application/vnd.ms-powerpoint", "Microsoft Office PowerPoint presentation subheader"], ["1a0000040000", 0, ".nsf", "application/vnd.lotus-notes", "Lotus Notes database"], ["0000020006040600080000000000", 0, ".wk1", "application/vnd.lotus-1-2-3", "Lotus 1-2-3 spreadsheet (v1) file"], ["00001a000010040000000000", 0, ".wk3", "application/vnd.lotus-1-2-3", "Lotus 1-2-3 spreadsheet (v3) file"], ["00001a000210040000000000", 0, ".wk5", "application/vnd.lotus-1-2-3", "Lotus 1-2-3 spreadsheet (v4 or v5) file"], ["00001a00051004", 0, ".123", "application/vnd.lotus-1-2-3", "Lotus 1-2-3 spreadsheet (v9) file"], ["00ffffffffffffffffffff0000020001", 0, ".mdf", "application/octet-stream", "Alcohol 120% Virtual CD image"], ["435753", 0, ".swf", "application/x-shockwave-flash", "Adobe Shockwave Flash file"], ["465753", 0, ".swf", "application/x-shockwave-flash", "Macromedia Shockwave Flash file"], ["1a0b", 0, ".pak", "application/pak", "Compressed archive file (often associated with Quake Engine games)"], ["7573746172", 257, ".tar", "application/x-tar", "Tape Archive file"], ["2d6c68", 2, ".lzh", "application/octet-stream", "Compressed archive file"], ["504b0304", 0, ".zip", "application/zip", "PKZIP Archive file"], ["504b030414000100630000000000", 0, ".zip", "application/zip", "ZLock Pro Encrypted ZIP file"], ["504b4c495445", 30, ".zip", "application/zip", "PKLITE Compressed ZIP Archive file"], ["504b537058", 526, ".zip", "application/zip", "Self-Extracting Executable PKSFX Compressed file"], ["526172211a0700", 0, ".rar", "application/x-rar-compressed", "WinRAR Compressed Archive file"], ["4d534346", 0, ".cab", "application/vnd.ms-cab-compressed", "Microsoft cabinet file"], ["49536328", 0, ".cab", " application/vnd.ms-cab-compressed", "Install Shield v5.x or 6.x compressed file"], ["4d5a", 0, ".exe", "application/octet-stream", "Windows Executable file"], ["504b0304140008000800", 0, ".jar", "application/java-archive", "Java Archive file"], ["5f27a889", 0, ".jar", "application/java-archive", "Jar Archive file"], ["ed<PERSON><PERSON><PERSON>", 0, ".rpm", "application/x-rpm", "RedHat Package Manager file"], ["fffe", 0, ".ini", "text/plain", "Windows INI file"], ["fffe23006c0069006e00650020003100", 0, ".mof", "text/plain", "Windows MSinfo file"], ["ffffffff", 0, ".sys", "text/plain", "DOS system driver"], ["3c3f786d6c2076657273696f6e3d22312e30223f3e0d0a3c4d4d435f436f6e736f6c6546696c6520436f6e736f6c6556657273696f6e3d22", 0, ".msc", "", "MMC Snap-in Control file"], ["4d6963726f736f66742057696e646f7773204d6564696120506c61796572202d2d20", 84, ".wpl", "", "Windows Media Player playlist"], ["4d6963726f736f66742056697375616c", 0, ".sln", "", "Visual Studio .NET file"], ["4d6963726f736f667420432f432b2b20", 0, ".pdb", "", "MS C++ debugging symbols file"], ["4d5a90000300000004000000ffff", 0, ".zap", "", "ZoneAlam data file"], ["00000020667479704d3441", 0, ".m4a", "", "Apple audio and video files"], ["40404020000040404040", 32, ".enl", "", "EndNote Library File"], ["3e000300feff090006", 24, ".wb3", "", "Quatro Pro for Windows 7.0"], ["2a2a2a2020496e73", 0, ".log", "text/plain", "Symantec Wise Installer log"], ["0e4e65726f49534f", 0, ".nri", "", "Nero CD compilation"], ["504750644d41494e", 0, ".pgd", "", "PGP disk image"], ["0300000041505052", 0, ".adx", "", "Approach index file"], ["414f4c564d313030", 0, ".org", "", "AOL personal file cabinet"], ["414f4c564d313030", 0, ".pfc", "", "AOL personal file cabinet"], ["300000004c664c65", 0, ".evt", "", "Windows Event Viewer file"], ["d0cf11e0a1b11ae1", 0, ".spo", "", "SPSS output file"], ["001e849000000000", 0, ".snm", "", "Netscape Communicator (v4) mail folder"], ["ff00020004040554", 0, ".wks", "application/vnd.ms-works", "Microsoft Works spreadsheet (Version 1-5)"], ["0000020004040554", 0, ".wks", "application/vnd.ms-works", "Microsoft Works spreadsheet (Version 1-5)"], ["d0cf11e0a1b11ae1", 0, ".opt", "", "Developer Studio File Options file"], ["3026b2758e66cf11", 0, ".wmv", "video/x-ms-wmv", "Windows Media Audio|Video File"], ["3026b2758e66cf11", 0, ".asf", "video/x-ms-asf", "Windows Media Audio|Video File"], ["3026b2758e66cf11", 0, ".wma", "", "Windows Media Audio|Video File"], ["4c00000001140200", 0, ".lnk", "", "Windows shortcut file"], ["fdffffff43000000", 512, ".ppt", "application/mspowerpoint", "PowerPoint presentation subheader"], ["5b47656e6572616c", 0, ".ecf", "", "MS Exchange configuration file"], ["4d2d5720506f636b", 0, ".pdb", "", "Merriam-Webster Pocket Dictionary"], ["4d535f564f494345", 0, ".cdr", "", "Sony Compressed Voice File"], ["4d535f564f494345", 0, ".dvf", "", "Sony Compressed Voice File"], ["4d535f564f494345", 0, ".msv", "", "Sony Compressed Voice File"], ["414f4c2046656564", 0, ".bag", "", "AOL and AIM buddy list"], ["53494554524f4e49", 0, ".cpi", "", "Sietronics CPI XRD document"], ["5157205665722e20", 0, ".abd", "", "Quicken data file"], ["5157205665722e20", 0, ".qsd", "", "Quicken data file"], ["0000000c6a502020", 0, ".jp2", "image/jpeg", "JPEG2000 image files"], ["496e6e6f20536574", 0, ".dat", "", "Inno Setup Uninstall Log"], ["49544f4c49544c53", 0, ".lit", "application/x-ms-reader", "MS Reader eBook"], ["424547494e3a5643", 0, ".vcf", "", "vCard"], ["0908100000060500", 512, ".xls", "application/excel", "Excel spreadsheet subheader"], ["464158434f564552", 0, ".cpe", "", "MS Fax Cover Sheet"], ["4d53465402000100", 0, ".tlb", "", "OLE|SPSS|Visual C++ library file"], ["454e545259564344", 0, ".vcd", "application/x-cdlink", "VideoVCD|VCDImager file"], ["fdffffff1c000000", 512, ".ppt", "application/mspowerpoint", "PowerPoint presentation subheader"], ["d0cf11e0a1b11ae1", 0, ".rvt", "", "Revit Project file"], ["74424d504b6e5772", 60, ".prc", "", "PathWay Map file"], ["56455253494f4e20", 0, ".ctl", "", "Visual Basic User-defined Control file"], ["****************", 0, ".sit", "application/x-sit", "StuffIt compressed archive"], ["fdffffff0e000000", 512, ".ppt", "application/mspowerpoint", "PowerPoint presentation subheader"], ["d0cf11e0a1b11ae1", 0, ".vsd", "application/x-visio", "Visio file"], ["d0cf11e0a1b11ae1", 0, ".msi", "", "Microsoft Installer package"], ["233f52414449414e", 0, ".hdr", "", "Radiance High Dynamic Range image file"], ["636f6e6563746978", 0, ".vhd", "", "Virtual PC HD image"], ["455646090d0aff00", 0, ".e01", "", "Expert Witness Compression Format"], ["49491a0000004845", 0, ".crw", "", "Canon RAW file"], ["6d6174726f736b61", 24, ".mkv", "video/x-matroska", "Matroska stream file"], ["aced000573720012", 0, ".pdb", "", "BGBlitz position database file"], ["4c5646090d0aff00", 0, ".e01", "", "Logical File Evidence Format"], ["6375736800000002", 0, ".csh", "application/x-csh", "Photoshop Custom Shape"], ["d0cf11e0a1b11ae1", 0, ".pub", "", "MS Publisher file"], ["504b030414000800", 0, ".jar", "application/java-archive", "Java archive"], ["ff4b455942202020", 0, ".sys", "", "Keyboard driver file"], ["504b030414000100", 0, ".zip", "application/zip", "ZLock Pro encrypted ZIP"], ["4350543746494c45", 0, ".cpt", "application/x-cpt", "Corel Photopaint file"], ["0000020006040600", 0, ".wk1", "application/x-123", "Lotus 1-2-3 (v1)"], ["52657475726e2d50", 0, ".eml", "", "Generic e-mail"], ["fffe23006c006900", 0, ".mof", "", "MSinfo file"], ["3c21646f63747970", 0, ".dci", "", "AOL HTML mail"], ["d0cf11e0a1b11ae1", 0, ".mtw", "", "Minitab data file"], ["737263646f636964", 0, ".cal", "", "CALS raster bitmap"], ["2854686973206669", 0, ".hqx", "application/binhex", "BinHex 4 Compressed Archive"], ["436c69656e742055", 0, ".dat", "", "IE History file"], ["43232b44a4434da5", 0, ".rtd", "", "RagTime document"], ["4d5a900003000000", 0, ".flt", "", "Audition graphic filter"], ["53514c4f434f4e56", 0, ".cnv", "", "DB2 conversion file"], ["4b47425f61726368", 0, ".kgb", "", "KGB archive"], ["3a56455253494f4e", 0, ".sle", "", "Surfplan kite project file"], ["00001a0000100400", 0, ".wk3", "", "Lotus 1-2-3 (v3)"], ["55464f4f72626974", 0, ".dat", "", "UFO Capture map file"], ["9ccbcb8d1375d211", 0, ".wab", "", "Outlook address file"], ["24464c3240282329", 0, ".sav", "", "SPSS Data file"], ["6465780a30303900", 0, ".dex", "", "Dalvik (Android) executable file"], ["4d5a900003000000", 0, ".api", "", "Acrobat plug-in"], ["d0cf11e0a1b11ae1", 0, ".ac_", "", "CaseWare Working Papers"], ["436174616c6f6720", 0, ".ctf", "", "WhereIsIt Catalog"], ["415647365f496e74", 0, ".dat", "", "AVG6 Integrity database"], ["30314f52444e414e", 0, ".ntf", "", "National Transfer Format Map"], ["e4525c7b8cd8a74d", 0, ".one", "", "MS OneNote note"], ["4f67675300020000", 0, ".oga", "", "Ogg Vorbis Codec compressed file"], ["4f67675300020000", 0, ".ogg", "", "Ogg Vorbis Codec compressed file"], ["4f67675300020000", 0, ".ogv", "", "Ogg Vorbis Codec compressed file"], ["4f67675300020000", 0, ".ogx", "", "Ogg Vorbis Codec compressed file"], ["1a52545320434f4d", 0, ".dat", "", "Runtime Software disk image"], ["d0cf11e0a1b11ae1", 0, ".dot", "application/msword", "Microsoft Office document"], ["d0cf11e0a1b11ae1", 0, ".pps", "application/mspowerpoint", "Microsoft Office document"], ["d0cf11e0a1b11ae1", 0, ".ppt", "application/mspowerpoint", "Microsoft Office document"], ["d0cf11e0a1b11ae1", 0, ".xla", "application/excel", "Microsoft Office document"], ["d0cf11e0a1b11ae1", 0, ".xls", "application/excel", "Microsoft Office document"], ["d0cf11e0a1b11ae1", 0, ".wiz", "application/msword", "Microsoft Office document"], ["0764743264647464", 0, ".dtd", "", "DesignTools 2D Design file"], ["5850434f4d0a5479", 0, ".xpt", "", "XPCOM libraries"], ["4e41565452414646", 0, ".dat", "", "TomTom traffic data"], ["d0cf11e0a1b11ae1", 0, ".sou", "", "Visual Studio Solution User Options file"], ["56657273696f6e20", 0, ".mif", "application/x-mif", "MapInfo Interchange Format file"], ["4552465353415645", 0, ".dat", "", "EasyRecovery Saved State file"], ["5b666c7473696d2e", 0, ".cfg", "", "Flight Simulator Aircraft Configuration"], ["213c617263683e0a", 0, ".lib", "", "Unix archiver (ar)|MS Program Library Common Object File Format (COFF)"], ["252150532d41646f", 0, ".eps", "application/postscript", "Encapsulated PostScript file"], ["813284c18505d011", 0, ".wab", "", "Outlook Express address book (Win95)"], ["d0cf11e0a1b11ae1", 0, ".wps", "application/vnd.ms-works", "Microsoft Works Word document (Version 3+)"], ["b5a2b0b3b3b0a5b5", 0, ".cal", "", "Windows calendar"], ["537570657243616c", 0, ".cal", "", "SuperCalc worksheet"], ["504e4349554e444f", 0, ".dat", "", "Norton Disk Doctor undo file"], ["0000001466747970", 0, ".3gp", "", "3GPP multimedia files"], ["5b57696e646f7773", 0, ".cpx", "", "Microsoft Code Page Translation file"], ["a90d000000000000", 0, ".dat", "", "Access Data FTK evidence"], ["424f4f4b4d4f4249", 0, ".prc", "", "Palmpilot resource file"], ["4f504c4461746162", 0, ".dbf", "", "Psion Series 3 Database"], ["52415a4154444231", 0, ".dat", "", "S<PERSON><PERSON><PERSON> (P2P) thumbnail"], ["00001a0002100400", 0, ".wk4", "", "Lotus 1-2-3 (v4|v5)"], ["00001a0002100400", 0, ".wk5", "", "Lotus 1-2-3 (v4|v5)"], ["d0cf11e0a1b11ae1", 0, ".apr", "", "Lotus|IBM Approach 97 file"], ["454c49544520436f", 0, ".cdr", "", "Elite Plus Commander game file"], ["5000000020000000", 0, ".idx", "", "Quicken QuickFinder Information File"], ["d0cf11e0a1b11ae1", 0, ".msc", "", "Microsoft Common Console Document"], ["414f4c494e444558", 0, ".abi", "", "AOL address book index"], ["23204d6963726f73", 0, ".dsp", "", "MS Developer Studio project file"], ["76323030332e3130", 0, ".flt", "", "Qimage filter"], ["3c4d616b65724669", 0, ".mif", "application/x-mif", "Adobe FrameMaker"], ["534d415254445257", 0, ".sdr", "application/sounder", "SmartDraw Drawing file"], ["d0cf11e0a1b11ae1", 0, ".adp", "", "Access project file"], ["80000020031204", 0, ".adx", "", "Dreamcast audio"], ["43525553482076", 0, ".cru", "", "Crush compressed archive"], ["00004949585052", 0, ".qxd", "", "Quark Express (Intel)"], ["52454745444954", 0, ".reg", "", "WinNT Registry|Registry Undo files"], ["52454745444954", 0, ".sud", "", "WinNT Registry|Registry Undo files"], ["576f726450726f", 0, ".lwp", "", "Lotus WordPro file"], ["727473703a2f2f", 0, ".ram", "", "RealMedia metafile"], ["64737766696c65", 0, ".dsw", "", "MS Visual Studio workspace file"], ["5b50686f6e655d", 0, ".dun", "", "Dial-up networking file"], ["00004d4d585052", 0, ".qxd", "", "Quark Express (Motorola)"], ["424c4932323351", 0, ".bin", "application/octet-stream", "Speedtouch router firmware"], ["43505446494c45", 0, ".cpt", "application/x-cpt", "Corel Photopaint file"], ["575332303030", 0, ".ws2", "", "WordStar for Windows file"], ["414f4c494458", 0, ".ind", "", "AOL client preferences|settings file"], ["564350434830", 0, ".pch", "", "Visual C PreCompiled header"], ["554641c6d2c1", 0, ".ufa", "", "UFA compressed archive"], ["ac9ebd8f0000", 0, ".qdf", "", "Quicken data"], ["504147454455", 0, ".dmp", "", "Windows memory dump"], ["4d444d5093a7", 0, ".dmp", "", "Windows dump file"], ["458600000600", 0, ".qbb", "", "QuickBooks backup"], ["7b5c72746631", 0, ".rtf", "application/rtf", "RTF file"], ["5f434153455f", 0, ".cas", "", "EnCase case file"], ["5f434153455f", 0, ".cbk", "", "EnCase case file"], ["4e45534d1a01", 0, ".nsf", "", "NES Sound file"], ["01da", 0, ".rgb", "image/x-rgb", "Silicon Graphics RGB Bitmap"], ["0000ffffffff", 7, ".hlp", "application/x-helpfile", "Windows Help file"], ["4d4d4d440000", 0, ".mmf", "", "Yamaha Synthetic music Mobile Application Format"], ["504943540008", 0, ".img", "", "ChromaGraph Graphics Card Bitmap"], ["4e616d653a20", 0, ".cod", "", "Agent newsreader character map"], ["01ff02040302", 0, ".drw", "application/drafting", "Micrografx vector graphic file"], ["4a4152435300", 0, ".jar", "", "JARCS compressed archive"], ["434246494c45", 0, ".cbd", "", "WordPerfect dictionary"], ["2321414d52", 0, ".amr", "", "Adaptive Multi-Rate ACELP Codec (GSM)"], ["414f4c4442", 0, ".aby", "", "AOL address book"], ["5349542100", 0, ".sit", "application/x-sit", "StuffIt archive"], ["7b0d0a6f20", 0, ".lgc", "", "Windows application log"], ["7b0d0a6f20", 0, ".lgd", "", "Windows application log"], ["5b7665725d", 0, ".sam", "", "Lotus AMI Pro document"], ["7b5c707769", 0, ".pwi", "", "MS WinMobile personal note"], ["4d56323134", 0, ".mls", "", "Milestones project management file"], ["4848474231", 0, ".sh3", "", "Harvard Graphics presentation file"], ["fdffffff04", 512, ".suo", "", "Visual Studio Solution subheader"], ["fdffffff1f", 512, ".xls", "application/excel", "Excel spreadsheet subheader"], ["4d41723000", 0, ".mar", "text/plain", "MAr compressed archive"], ["4344303031", 0, ".iso", "", "ISO-9660 CD Disc Image"], ["fdffffff23", 512, ".xls", "application/excel", "Excel spreadsheet subheader"], ["4d494c4553", 0, ".mls", "", "Milestones project management file"], ["be000000ab", 0, ".wri", "application/mswrite", "MS Write file"], ["5b5645525d", 0, ".sam", "", "Lotus AMI Pro document"], ["4d41523100", 0, ".mar", "text/plain", "Mozilla archive"], ["fdffffff29", 512, ".xls", "application/excel", "Excel spreadsheet subheader"], ["fdffffff22", 512, ".xls", "application/excel", "Excel spreadsheet subheader"], ["ff464f4e54", 0, ".cpi", "", "Windows international code page"], ["5b4d535643", 0, ".vcw", "", "Visual C++ Workbench Info File"], ["4e49544630", 0, ".ntf", "", "National Imagery Transmission Format file"], ["fdffffff10", 512, ".xls", "application/excel", "Excel spreadsheet subheader"], ["414f4c4442", 0, ".idx", "", "AOL user configuration"], ["fdffffff20", 512, ".opt", "", "Developer Studio subheader"], ["fdffffff28", 512, ".xls", "application/excel", "Excel spreadsheet subheader"], ["504b0304", 0, ".xpi", "", "Mozilla Browser Archive"], ["504b0304", 0, ".wmz", "", "Windows Media compressed skin file"], ["0a000101", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (2.5, fixed EGA palette, 1bpp)"], ["0a020101", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (2.5, modified EGA palette, 1bpp)"], ["0a030101", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (2.8, 1bpp)"], ["0a040101", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (Paintbrush for Windows, 1bpp)"], ["0a050101", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (3.0, 1bpp)"], ["0a000001", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (2.5, fixed EGA palette, no encoding, 1bpp)"], ["0a020001", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (2.5, modified EGA palette, no encoding, 1bpp)"], ["0a030001", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (2.8, no encoding, 1bpp)"], ["0a040001", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (Paintbrush for Windows, no encoding, 1bpp)"], ["0a050001", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (3.0, no encoding, 1bpp)"], ["0a000102", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (2.5, fixed EGA palette, 2bpp)"], ["0a020102", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (2.5, modified EGA palette, 2bpp)"], ["0a030102", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (2.8, 2bpp)"], ["0a040102", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (Paintbrush for Windows, 2bpp)"], ["0a050102", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (3.0, 2bpp)"], ["0a000002", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (2.5, fixed EGA palette, no encoding, 2bpp)"], ["0a020002", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (2.5, modified EGA palette, no encoding, 2bpp)"], ["0a030002", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (2.8, no encoding, 2bpp)"], ["0a040002", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (Paintbrush for Windows, no encoding, 2bpp)"], ["0a050002", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (3.0, no encoding, 2bpp)"], ["0a030104", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (2.8, 4bpp)"], ["0a040104", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (Paintbrush for Windows, 4bpp)"], ["0a050104", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (3.0, 4bpp)"], ["0a000004", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (2.5, fixed EGA palette, no encoding, 4bpp)"], ["0a020004", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (2.5, modified EGA palette, no encoding, 4bpp)"], ["0a030004", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (2.8, no encoding, 4bpp)"], ["0a040004", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (Paintbrush for Windows, no encoding, 4bpp)"], ["0a050004", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (3.0, no encoding, 4bpp)"], ["0a030108", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (2.8, 8bpp)"], ["0a040108", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (Paintbrush for Windows, 8bpp)"], ["0a050108", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (3.0, 8bpp)"], ["0a000008", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (2.5, fixed EGA palette, no encoding, 8bpp)"], ["0a020008", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (2.5, modified EGA palette, no encoding, 8bpp)"], ["0a030008", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (2.8, no encoding, 8bpp)"], ["0a040008", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (Paintbrush for Windows, no encoding, 8bpp)"], ["0a050008", 0, ".pcx", "image/x-pcx", "ZSOFT Paintbrush file (3.0, no encoding, 8bpp)"], ["eb3c902a", 0, ".img", "", "GEM Raster file"], ["72656766", 0, ".dat", "", "WinNT registry file"], ["4d4c5357", 0, ".mls", "", "Skype localization data file"], ["504d4343", 0, ".grp", "", "Windows Program Manager group file"], ["47504154", 0, ".pat", "", "GIMP pattern file"], ["706e6f74", 4, ".mov", "video/quicktime", "QuickTime movie"], ["5041434b", 0, ".pak", "", "Quake archive file"], ["4d534346", 0, ".ppz", "application/mspowerpoint", "Powerpoint Packaged Presentation"], ["504b0304", 0, ".xpt", "", "eXact Packager Models"], ["4d415243", 0, ".mar", "text/plain", "Microsoft|MSN MARC archive"], ["0d444f43", 0, ".doc", "application/msword", "DeskMate Document"], ["504b0506", 0, ".zip", "application/zip", "PKZIP archive"], ["52494646", 0, ".4xm", "", "4X Movie video"], ["010f0000", 0, ".mdf", "", "SQL Data Base"], ["58435000", 0, ".cap", "", "Packet sniffer files"], ["02647373", 0, ".dss", "", "Digital Speech Standard file"], ["b168de3a", 0, ".dcx", "image/x-dcx", "PCX bitmap"], ["52494646", 0, ".cdr", "", "CorelDraw document"], ["736b6970", 4, ".mov", "video/quicktime", "QuickTime movie"], ["07534b46", 0, ".skf", "", "SkinCrafter skin"], ["49545346", 0, ".chi", "", "MS Compiled HTML Help File"], ["49545346", 0, ".chm", "", "MS Compiled HTML Help File"], ["43524547", 0, ".dat", "", "Win9x registry hive"], ["91334846", 0, ".hap", "", "Hamarsoft compressed archive"], ["52494646", 0, ".avi", "video/avi", "Resource Interchange File Format"], ["52494646", 0, ".cda", "", "Resource Interchange File Format"], ["52494646", 0, ".qcp", "audio/vnd.qcelp", "Resource Interchange File Format"], ["52494646", 0, ".rmi", "audio/mid", "Resource Interchange File Format"], ["52494646", 0, ".wav", "audio/wav", "Resource Interchange File Format"], ["504b0304", 0, ".kwd", "", "KWord document"], ["434d5831", 0, ".clb", "", "Corel Binary metafile"], ["25504446", 0, ".fdf", "application/vnd.fdf", "PDF file"], ["64000000", 0, ".p10", "application/pkcs10", "Intel PROset|Wireless Profile"], ["c3abcdab", 0, ".acs", "", "MS Agent Character file"], ["5343486c", 0, ".ast", "", "Underground Audio"], ["49536328", 0, ".hdr", "", "Install Shield compressed file"], ["41433130", 0, ".dwg", "image/x-dwg", "Generic AutoCAD drawing"], ["4e422a00", 0, ".jnt", "", "MS Windows journal"], ["4e422a00", 0, ".jtp", "", "MS Windows journal"], ["52494646", 0, ".ds4", "", "Micrografx Designer graphic"], ["52494646", 0, ".ani", "application/x-navi-animation", "Windows animated cursor"], ["53484f57", 0, ".shw", "", "Harvard Graphics presentation"], ["dba52d00", 0, ".doc", "application/msword", "Word 2.0 file"], ["50455354", 0, ".dat", "", "PestPatrol data|scan strings"], ["736c682e", 0, ".dat", "", "Allegro Generic <PERSON> (uncompressed)"], ["574d4d50", 0, ".dat", "", "Walkman MP3 file"], ["000001b3", 0, ".mpg", "video/mpeg", "MPEG video file"], ["00000200", 0, ".wb2", "", "QuattroPro spreadsheet"], ["49492a00", 0, ".tif", "image/tiff", "TIFF file"], ["6c33336c", 0, ".dbb", "", "Skype user data file"], ["52494646", 0, ".dat", "video/mpeg", "Video CD MPEG movie"], ["0e574b53", 0, ".wks", "", "DeskMate Worksheet"], ["6d646174", 4, ".mov", "video/quicktime", "QuickTime movie"], ["3f5f0300", 0, ".gid", "", "Windows Help Global Index file"], ["3f5f0300", 0, ".hlp", "application/x-helpfile", "Windows Help file"], ["68490000", 0, ".shd", "", "Win Server 2003 printer spool file"], ["00000100", 0, ".spl", "application/futuresplash", "Windows icon|printer spool file"], ["53434d49", 0, ".img", "", "Img Software Bitmap"], ["51454c20", 92, ".qel", "", "Quicken data"], ["737a657a", 0, ".pdb", "", "PowerBASIC Debugger Symbols"], ["00000200", 0, ".cur", "", "Windows cursor"], ["77696465", 4, ".mov", "video/quicktime", "QuickTime movie"], ["4b490000", 0, ".shd", "", "Win9x printer spool file"], ["ff575043", 0, ".wpd", "application/wordperfect", "WordPerfect text and graphics"], ["ff575043", 0, ".wpg", "application/wordperfect", "WordPerfect text and graphics"], ["ff575043", 0, ".wpp", "application/wordperfect", "WordPerfect text and graphics"], ["ff575043", 0, ".wp5", "application/wordperfect", "WordPerfect text and graphics"], ["ff575043", 0, ".wp6", "application/wordperfect", "WordPerfect text and graphics"], ["1a350100", 0, ".eth", "", "WinPharoah capture file"], ["66490000", 0, ".shd", "", "WinNT printer spool file"], ["444d5321", 0, ".dms", "", "Amiga DiskMasher compressed archive"], ["d7cdc69a", 0, ".wmf", "windows/metafile", "Windows graphics metafile"], ["52494646", 0, ".cmx", "", "Corel Presentation Exchange metadata"], ["4d563243", 0, ".mls", "", "Milestones project management file"], ["b46e6844", 0, ".tib", "", "Acronis <PERSON> Image"], ["4d534346", 0, ".snp", "", "MS Access Snapshot Viewer file"], ["434f4d2b", 0, ".clb", "", "COM+ Catalog"], ["03000000", 0, ".qph", "", "Quicken price history"], ["504b0304", 0, ".xps", "", "XML paper specification file"], ["504b0304", 0, ".jar", "application/java-archive", "Java archive"], ["67490000", 0, ".shd", "", "Win2000|XP printer spool file"], ["7e424b00", 0, ".psp", "", "Corel Paint Shop Pro image"], ["414d594f", 0, ".syw", "", "Harvard Graphics symbol graphic"], ["d20a0000", 0, ".ftr", "", "WinPharoah filter file"], ["504b0304", 0, ".odt", "application/vnd.oasis.opendocument.text", "OpenDocument template"], ["504b0304", 0, ".odp", "application/vnd.oasis.opendocument.presentation", "OpenDocument template"], ["504b0304", 0, ".ott", "application/vnd.oasis.opendocument.text-template", "OpenDocument template"], ["c5d0d3c6", 0, ".eps", "application/postscript", "Adobe encapsulated PostScript"], ["c8007900", 0, ".lbk", "", "Je<PERSON>sen FliteLog file"], ["4c4e0200", 0, ".gid", "", "Windows help file"], ["4c4e0200", 0, ".hlp", "application/x-helpfile", "Windows help file"], ["55434558", 0, ".uce", "", "Unicode extensions"], ["41724301", 0, ".arc", "application/octet-stream", "FreeArc compressed file"], ["b04d4643", 0, ".pwl", "", "Win95 password file"], ["5a4f4f20", 0, ".zoo", "application/octet-stream", "ZOO compressed archive"], ["52545353", 0, ".cap", "", "WinNT Netmon capture file"], ["46726f6d", 0, ".eml", "", "Generic e-mail"], ["66726565", 4, ".mov", "video/quicktime", "QuickTime movie"], ["e3828596", 0, ".pwl", "", "Win98 password file"], ["736c6821", 0, ".dat", "", "Allegro Generic Pack<PERSON>le (compressed)"], ["00014244", 0, ".dba", "", "Palm DateBook Archive"], ["cfad12fe", 0, ".dbx", "", "Outlook Express e-mail folder"], ["00014241", 0, ".aba", "", "Palm Address Book Archive"], ["504b0708", 0, ".zip", "application/zip", "PKZIP archive"], ["504b0304", 0, ".sxd", "", "OpenOffice documents"], ["504b0304", 0, ".sxi", "", "OpenOffice documents"], ["504b0304", 0, ".sxw", "", "OpenOffice documents"], ["81cdab", 0, ".wpf", "", "WordPerfect text"], ["504158", 0, ".pax", "", "PAX password protected bitmap"], ["445644", 0, ".ifo", "", "DVD info file"], ["492049", 0, ".tif", "image/tiff", "TIFF file"], ["414376", 0, ".sle", "", "Steganos virtual secure drive"], ["414f4c", 0, ".abi", "", "AOL config files"], ["414f4c", 0, ".aby", "", "AOL config files"], ["414f4c", 0, ".bag", "", "AOL config files"], ["414f4c", 0, ".idx", "", "AOL config files"], ["414f4c", 0, ".ind", "", "AOL config files"], ["414f4c", 0, ".pfc", "", "AOL config files"], ["425a68", 0, ".bz2", "application/x-bzip2", "bzip2 compressed archive"], ["425a68", 0, ".tb2", "application/x-bzip2", "bzip2 compressed archive"], ["ffd8ff", 0, ".jpe", "image/jpeg", "JPEG|EXIF|SPIFF images"], ["ffd8ff", 0, ".jpg", "image/jpeg", "JPEG|EXIF|SPIFF images"], ["2d6c68", 2, ".lha", "application/x-lha", "Compressed archive"], ["445644", 0, ".dvr", "", "DVR-Studio stream file"], ["1a0000", 0, ".ntf", "", "Lotus Notes database template"], ["736d5f", 0, ".pdb", "", "PalmOS SuperMemo"], ["475832", 0, ".gx2", "", "Show Partner graphics file"], ["0110", 0, ".tr1", "", "Novell LANalyzer capture file"], ["4c01", 0, ".obj", "", "MS COFF relocatable object code"], ["4d5a", 0, ".ocx", "", "ActiveX|OLE Custom Control"], ["1a04", 0, ".arc", "application/octet-stream", "LH archive (old vers.|type 3)"], ["4d56", 0, ".dsn", "", "CD Stomper Pro label file"], ["1a09", 0, ".arc", "application/octet-stream", "LH archive (old vers.|type 5)"], ["582d", 0, ".eml", "", "Exchange e-mail"], ["1a02", 0, ".arc", "application/octet-stream", "LH archive (old vers.|type 1)"], ["60ea", 0, ".arj", "application/arj", "Compressed archive file"], ["4d5a", 0, ".scr", "", "Screen saver"], ["4d5a", 0, ".acm", "", "MS audio compression manager driver"], ["1a08", 0, ".arc", "application/octet-stream", "LH archive (old vers.|type 4)"], ["4d5a", 0, ".olb", "", "OLE object library"], ["9901", 0, ".pkr", "", "PGP public keyring"], ["4550", 0, ".mdi", "", "MS Document Imaging file"], ["9500", 0, ".skr", "", "PGP secret keyring"], ["4d5a", 0, ".fon", "", "Font file"], ["2112", 0, ".ain", "", "AIN Compressed Archive"], ["4d5a", 0, ".vxd", "", "Windows virtual device drivers"], ["4d5a", 0, ".386", "", "Windows virtual device drivers"], ["5854", 0, ".bdr", "", "MS Publisher"], ["4d5a", 0, ".cpl", "", "Control panel application"], ["32be", 0, ".wri", "application/mswrite", "MS Write file"], ["0011", 0, ".fli", "video/fli", "FLIC animation"], ["d42a", 0, ".arl", "", "AOL history|typed URL files"], ["d42a", 0, ".aut", "", "AOL history|typed URL files"], ["9501", 0, ".skr", "", "PGP secret keyring"], ["424d", 0, ".dib", "", "Bitmap image"], ["1a03", 0, ".arc", "application/octet-stream", "LH archive (old vers.|type 2)"], ["dcfe", 0, ".efx", "", "eFax file"], ["dcdc", 0, ".cpl", "", "Corel color palette"], ["31be", 0, ".wri", "application/mswrite", "MS Write file"], ["4d5a", 0, ".com", "application/octet-stream", "Windows|DOS executable file"], ["4d5a", 0, ".dll", "application/octet-stream", "Windows|DOS executable file"], ["4d5a", 0, ".drv", "application/octet-stream", "Windows|DOS executable file"], ["4d5a", 0, ".pif", "application/octet-stream", "Windows|DOS executable file"], ["4d5a", 0, ".qts", "application/octet-stream", "Windows|DOS executable file"], ["4d5a", 0, ".qtx", "application/octet-stream", "Windows|DOS executable file"], ["4d5a", 0, ".sys", "application/octet-stream", "Windows|DOS executable file"], ["4d5a", 0, ".vbx", "application/octet-stream", "VisualBASIC application"], ["feef", 0, ".gho", "application/octet-stream", "Symantex Ghost image file"], ["feef", 0, ".ghs", "application/octet-stream", "Symantex Ghost image file"], ["4f7b", 0, ".dw4", "", "Visio|DisplayWrite 4 text file"], ["415441524937383030", 1, ".a78", "application/x-atari-7800-rom", "Atari 7800"], ["69726976657220554d5320504c41", 4, ".pla", "audio/x-iriver-pla", "iRiver Playlist"], ["2d2d2d2d2d424547494e20504750204d4553534147452d2d2d2d2d", 0, ".asc", "application/pgp-encrypted", "PGP/MIME-encrypted message header"], ["2d2d2d2d2d424547494e20504750205055424c4943204b455920424c4f434b2d2d2d2d2d", 0, ".gpg", "application/pgp-keys", "PGP keys"], ["2d2d2d2d2d424547494e205047502050524956415445204b455920424c4f434b2d2d2d2d2d", 0, ".gpg", "application/pgp-keys", "PGP keys"], ["9900", 0, ".gpg", "application/pgp-keys", "PGP keys"], ["2d2d2d2d2d424547494e20504750205349474e41545552452d2d2d2d2d", 0, ".gpg", "application/pgp-signature", "detached OpenPGP signature"], ["7b5c5c727466", 0, ".rtf", "application/rtf", "RTF document"], ["283b46465b335d", 0, ".sgf", "application/x-go-sgf", "SGF record"], ["283b46465b345d", 0, ".sgf", "application/x-go-sgf", "SGF record"], ["2559414d4c", 0, ".yml", "application/x-yaml", "YAML document"], ["434452587672736e", 8, ".cdr", "application/vnd.corel-draw", "Corel Draw drawing"], ["5c7830305c7830305c7830325c7830305c7830365c7830345c7830365c7830305c7830385c7830305c7830305c7830305c7830305c783030", 0, ".wks", "application/vnd.lotus-1-2-3", "Lotus 1-2-3 spreadsheet"], ["5c7830305c7830315c7830305c7830305374616e64617264204a6574204442", 0, ".mdb", "application/vnd.ms-access", "JET database"], ["4d5343465c305c305c305c30", 0, ".cab", "application/vnd.ms-cab-compressed", "Microsoft Cabinet archive"], ["4d6963726f736f667420457863656c20352e3020576f726b7368656574", 2080, ".xld", "application/vnd.ms-excel", "Excel spreadsheet"], ["5c7833315c7862655c7830305c783030", 0, ".doc", "application/msword", "Word document"], ["504f5e5160", 0, ".doc", "application/msword", "Word document"], ["5c3337365c3036375c305c303433", 0, ".doc", "application/msword", "Word document"], ["5c3333335c3234352d5c305c305c30", 0, ".doc", "application/msword", "Word document"], ["4d6963726f736f667420576f726420646f63756d656e742064617461", 2112, ".doc", "application/msword", "Word document"], ["626a626a", 546, ".doc", "application/msword", "Word document"], ["6a626a62", 546, ".doc", "application/msword", "Word document"], ["5c323131474e445c725c6e5c3033325c6e", 0, ".gnd", "application/gnunet-directory", "GNUnet search file"], ["53746172577269746572", 2089, ".sgl", "application/vnd.stardivision.writer", "StarWriter document"], ["504b5c3030335c303034", 0, ".sxc", "application/vnd.sun.xml.calc", "OpenOffice Calc spreadsheet"], ["504b5c3030335c303034", 0, ".stc", "application/vnd.sun.xml.calc.template", "OpenOffice Calc template"], ["504b5c3030335c303034", 0, ".sxd", "application/vnd.sun.xml.draw", "OpenOffice Draw drawing"], ["504b5c3030335c303034", 0, ".std", "application/vnd.sun.xml.draw.template", "OpenOffice Draw template"], ["504b5c3030335c303034", 0, ".sxi", "application/vnd.sun.xml.impress", "OpenOffice Impress presentation"], ["504b5c3030335c303034", 0, ".sti", "application/vnd.sun.xml.impress.template", "OpenOffice Impress template"], ["504b5c3030335c303034", 0, ".sxm", "application/vnd.sun.xml.math", "OpenOffice Math formula"], ["504b5c3030335c303034", 0, ".sxw", "application/vnd.sun.xml.writer", "OpenOffice Writer document"], ["504b5c3030335c303034", 0, ".sxg", "application/vnd.sun.xml.writer.global", "OpenOffice Writer global document"], ["504b5c3030335c303034", 0, ".stw", "application/vnd.sun.xml.writer.template", "OpenOffice Writer template"], ["504b5c3030335c303034", 0, ".odt", "application/vnd.oasis.opendocument.text", "ODT document"], ["504b5c3030335c303034", 0, ".ott", "application/vnd.oasis.opendocument.text-template", "ODT template"], ["504b5c3030335c303034", 0, ".oth", "application/vnd.oasis.opendocument.text-web", "OTH template"], ["504b5c3030335c303034", 0, ".odm", "application/vnd.oasis.opendocument.text-master", "ODM document"], ["504b5c3030335c303034", 0, ".odg", "application/vnd.oasis.opendocument.graphics", "ODG drawing"], ["504b5c3030335c303034", 0, ".otg", "application/vnd.oasis.opendocument.graphics-template", "ODG template"], ["504b5c3030335c303034", 0, ".odp", "application/vnd.oasis.opendocument.presentation", "ODP presentation"], ["504b5c3030335c303034", 0, ".otp", "application/vnd.oasis.opendocument.presentation-template", "ODP template"], ["504b5c3030335c303034", 0, ".ods", "application/vnd.oasis.opendocument.spreadsheet", "ODS spreadsheet"], ["504b5c3030335c303034", 0, ".ots", "application/vnd.oasis.opendocument.spreadsheet-template", "ODS template"], ["504b5c3030335c303034", 0, ".odc", "application/vnd.oasis.opendocument.chart", "ODC chart"], ["504b5c3030335c303034", 0, ".otc", "application/vnd.oasis.opendocument.chart-template", "ODC template"], ["504b5c3030335c303034", 0, ".odf", "application/vnd.oasis.opendocument.formula", "ODF formula"], ["504b5c3030335c303034", 0, ".otf", "application/vnd.oasis.opendocument.formula-template", "ODF template"], ["504b5c3030335c303034", 0, ".odb", "application/vnd.oasis.opendocument.database", "ODB database"], ["504b5c3030335c303034", 0, ".odi", "application/vnd.oasis.opendocument.image", "ODI image"], ["19040010", 8, ".sis", "application/vnd.symbian.install", "SIS package"], ["575043", 1, ".wpp", "application/vnd.wordperfect", "WordPerfect document"], ["4153434949205350535320504f52542046494c45", 40, ".por", "application/x-spss-por", "SPSS Portable Data File"], ["5445587452454164", 60, ".pdc", "application/x-aportisdoc", "AportisDoc document"], ["54455874546c4463", 60, ".pdc", "application/x-aportisdoc", "AportisDoc document"], ["23212f62696e2f6761776b", 0, ".awk", "application/x-awk", "AWK script"], ["2321202f62696e2f6761776b", 0, ".awk", "application/x-awk", "AWK script"], ["23212f7573722f62696e2f6761776b", 0, ".awk", "application/x-awk", "AWK script"], ["2321202f7573722f62696e2f6761776b", 0, ".awk", "application/x-awk", "AWK script"], ["23212f7573722f6c6f63616c2f62696e2f6761776b", 0, ".awk", "application/x-awk", "AWK script"], ["2321202f7573722f6c6f63616c2f62696e2f6761776b", 0, ".awk", "application/x-awk", "AWK script"], ["23212f62696e2f61776b", 0, ".awk", "application/x-awk", "AWK script"], ["2321202f62696e2f61776b", 0, ".awk", "application/x-awk", "AWK script"], ["23212f7573722f62696e2f61776b", 0, ".awk", "application/x-awk", "AWK script"], ["2321202f7573722f62696e2f61776b", 0, ".awk", "application/x-awk", "AWK script"], ["4c525a49", 0, ".lrz", "application/x-lrzip", "Lrzip archive"], ["43445f524f4d5c6e", 0, ".toc", "application/x-cdrdao-toc", "CD Table Of Contents"], ["43445f44415c6e", 0, ".toc", "application/x-cdrdao-toc", "CD Table Of Contents"], ["43445f524f4d5f58415c6e", 0, ".toc", "application/x-cdrdao-toc", "CD Table Of Contents"], ["43445f5445585420", 0, ".toc", "application/x-cdrdao-toc", "CD Table Of Contents"], ["434154414c4f472022", 0, ".toc", "application/x-cdrdao-toc", "CD Table Of Contents"], ["5b4576656e7420", 0, ".pgn", "application/vnd.chess-pgn", "PGN chess game notation"], ["5d1c9ea3", 24, ".iso", "application/x-wii-rom", "Wii disc image"], ["57424653", 0, ".iso", "application/x-wii-rom", "Wii disc image"], ["5749495c30303144495343", 0, ".iso", "application/x-wii-rom", "Wii disc image"], ["49735c305c30", 4, ".wad", "application/x-wii-wad", "WiiWare bundle"], ["69625c305c30", 4, ".wad", "application/x-wii-wad", "WiiWare bundle"], ["426b5c305c30", 4, ".wad", "application/x-wii-wad", "WiiWare bundle"], ["c2339f3d", 28, ".iso", "application/x-gamecube-rom", "GameCube disc image"], ["4858435049434645", 0, ".hfe", "application/x-hfe-floppy-image", "HFE floppy disk image"], ["53595354454d452044274152434849564147452050554b414c4c20532e412e502e2028632920416c6578616e6472652050554b414c4c20417672696c2031393938", 1, ".sap", "application/x-thomson-sap-image", "SAP Thomson floppy disk image"], ["f702", 0, ".dvi", "application/x-dvi", "TeX DVI document"], ["5354415254464f4e545c303430", 0, ".bdf", "application/x-font-bdf", "BDF font"], ["5c7833365c783034", 0, ".psf", "application/x-font-linux-psf", "Linux PSF console font"], ["4f54544f", 0, ".otf", "application/x-font-otf", "OpenType font"], ["44312e305c303135", 0, ".spd", "application/x-font-speedo", "Speedo font"], ["5c3030305c3030315c3030305c3030305c303030", 0, ".ttc", "application/x-font-ttf", "TrueType font"], ["5c7863655c7865645c7836365c7836365c7863635c7830645c7830305c7830625c7830335c7837335c7830305c7838335c7830305c7830635c7830305c7830645c7830305c7830385c7831315c7831665c7838385c7838395c7830305c783065", 260, ".sgb", "application/x-gameboy-rom", "Game Boy ROM"], ["5c7863655c7865645c7836365c7836365c7863635c7830645c7830305c7830625c7830335c7837335c7830305c7838335c7830305c7830635c7830305c7830645c7830305c783038", 260, ".cgb", "application/x-gameboy-color-rom", "Game Boy Color ROM"], ["534547412047454e45534953", 256, ".smd", "application/x-genesis-rom", "Genesis ROM"], ["53454741204d454741204452495645", 256, ".smd", "application/x-genesis-rom", "Genesis ROM"], ["4541474e", 640, ".smd", "application/x-genesis-rom", "Genesis ROM"], ["45414d47", 640, ".smd", "application/x-genesis-rom", "Genesis ROM"], ["5345474120333258", 256, ".mdx", "application/x-genesis-32x-rom", "Genesis 32X ROM"], ["5c3033375c323133", 0, ".kfo", "application/x-kformula", "KFormula formula"], ["504b5c3030335c303034", 0, ".kfo", "application/x-kformula", "KFormula formula"], ["5c3033375c323133", 0, ".kil", "application/x-killustrator", "KIllustrator drawing"], ["5c3033375c323133", 0, ".flw", "application/x-kivio", "Kivio flowchart"], ["504b5c3030335c303034", 0, ".flw", "application/x-kivio", "Kivio flowchart"], ["5c3033375c323133", 0, ".kon", "application/x-kontour", "<PERSON><PERSON><PERSON> drawing"], ["504b5c3030335c303034", 0, ".kon", "application/x-kontour", "<PERSON><PERSON><PERSON> drawing"], ["5c3033375c323133", 0, ".kpt", "application/x-kpresenter", "KPresenter presentation"], ["504b5c3030335c303034", 0, ".kpt", "application/x-kpresenter", "KPresenter presentation"], ["5c3033375c323133", 0, ".kra", "application/x-krita", "Krita document"], ["504b5c3030335c303034", 0, ".kra", "application/x-krita", "Krita document"], ["5c3033375c323133", 0, ".ksp", "application/x-kspread", "KSpread spreadsheet"], ["504b5c3030335c303034", 0, ".ksp", "application/x-kspread", "KSpread spreadsheet"], ["5c3033375c323133", 0, ".kwt", "application/x-kword", "KWord document"], ["504b5c3030335c303034", 0, ".kwt", "application/x-kword", "KWord document"], ["2d6c68202d", 2, ".lzh", "application/x-lha", "LHA archive"], ["2d6c68302d", 2, ".lzh", "application/x-lha", "LHA archive"], ["2d6c68312d", 2, ".lzh", "application/x-lha", "LHA archive"], ["2d6c68322d", 2, ".lzh", "application/x-lha", "LHA archive"], ["2d6c68332d", 2, ".lzh", "application/x-lha", "LHA archive"], ["2d6c68342d", 2, ".lzh", "application/x-lha", "LHA archive"], ["2d6c68352d", 2, ".lzh", "application/x-lha", "LHA archive"], ["2d6c6834302d", 2, ".lzh", "application/x-lha", "LHA archive"], ["2d6c68642d", 2, ".lzh", "application/x-lha", "LHA archive"], ["2d6c7a342d", 2, ".lzh", "application/x-lha", "LHA archive"], ["2d6c7a352d", 2, ".lzh", "application/x-lha", "LHA archive"], ["2d6c7a732d", 2, ".lzh", "application/x-lha", "LHA archive"], ["234c7958", 0, ".lyx", "application/x-lyx", "LyX document"], ["04224d18", 0, ".lz4", "application/x-lz4", "LZ4 archive"], ["02214c18", 0, ".lz4", "application/x-lz4", "LZ4 archive"], ["5c7838395c7834635c7835615c7834665c7830305c7830645c7830615c7831615c783061", 0, ".lzo", "application/x-lzop", "LZO archive"], ["78617221", 0, ".pkg", "application/x-xar", "XAR archive"], ["496e7465726e657453686f7274637574", 1, ".url", "application/x-mswinurl", "Internet shortcut"], ["44454641554c54", 1, ".url", "application/x-mswinurl", "Internet shortcut"], ["80371240", 0, ".v64", "application/x-n64-rom", "Nintendo64 ROM"], ["37804012", 0, ".v64", "application/x-n64-rom", "Nintendo64 ROM"], ["40123780", 0, ".v64", "application/x-n64-rom", "Nintendo64 ROM"], ["434f5059524947485420425920534e4b20434f52504f524154494f4e", 0, ".ngp", "application/x-neo-geo-pocket-rom", "Neo-Geo Pocket ROM"], ["4f676753", 0, ".anx", "application/annodex", "Annodex exchange format"], ["4f676753", 0, ".axv", "video/annodex", "Annodex Video"], ["4f676753", 0, ".axa", "audio/annodex", "Annodex Audio"], ["4f676753", 0, ".ogx", "application/ogg", "Ogg multimedia file"], ["4f676753", 0, ".ogg", "video/ogg", "Ogg Video"], ["4f676753", 0, ".ogg", "audio/x-vorbis+ogg", "Ogg Vorbis audio"], ["4f676753", 0, ".ogg", "audio/x-flac+ogg", "Ogg FLAC audio"], ["4f676753", 0, ".ogg", "audio/x-speex+ogg", "Ogg Speex audio"], ["5370656578", 0, ".spx", "audio/x-speex", "Speex audio"], ["4f676753", 0, ".ogg", "video/x-theora+ogg", "Ogg Theora video"], ["4f676753", 0, ".ogm", "video/x-ogm+ogg", "OGM video"], ["7b5c5c707769", 0, ".psw", "application/x-pocket-word", "Pocket Word document"], ["994e0d0a", 0, ".pyo", "application/x-python-bytecode", "Python bytecode"], ["3c3f786d6c", 0, ".qtl", "application/x-quicktime-media-link", "QuickTime metalink playlist"], ["5254535074657874", 0, ".qtl", "application/x-quicktime-media-link", "QuickTime metalink playlist"], ["7274737074657874", 0, ".qtl", "application/x-quicktime-media-link", "QuickTime metalink playlist"], ["534d494c74657874", 0, ".qtl", "application/x-quicktime-media-link", "QuickTime metalink playlist"], ["52617221", 0, ".rar", "application/vnd.rar", "RAR archive"], ["414c5a", 0, ".alz", "application/x-alz", "Alzip archive"], ["5c7865645c7861625c7865655c786462", 0, ".rpm", "application/x-rpm", "RPM package"], ["616a6b67", 0, ".shn", "application/x-shorten", "Shorten audio"], ["2323536b65746368", 0, ".sk1", "image/x-skencil", "Skencil document"], ["53495421", 0, ".sit", "application/x-stuffit", "StuffIt archive"], ["574542565454", 0, ".vtt", "text/vtt", "WebVTT subtitles"], ["7b317d", 0, ".sub", "text/x-microdvd", "MicroDVD subtitles"], ["7b307d", 0, ".sub", "text/x-microdvd", "MicroDVD subtitles"], ["5b494e464f524d4154494f4e5d", 0, ".sub", "text/x-subviewer", "SubViewer subtitles"], ["424547494e3a494d454c4f4459", 0, ".ime", "text/x-iMelody", "iMelody ringtone"], ["3c6d726d6c20", 0, ".mrl", "text/x-mrml", "MRML playlist"], ["584d465f", 0, ".xmf", "audio/x-xmf", "XMF audio"], ["5c3133305c3131355c3130365c3133375c3036325c3035365c3036305c3036305c3030305c3030305c3030305c303032", 0, ".xmf", "audio/x-xmf", "XMF audio"], ["75737461725c30", 257, ".gem", "application/x-tar", "Tar archive"], ["75737461725c3034305c3034305c30", 257, ".gem", "application/x-tar", "Tar archive"], ["2554474946", 0, ".obj", "application/x-tgif", "TGIF document"], ["dca7c4fd", 20, ".zoo", "application/x-zoo", "Zoo archive"], ["504b5c3030335c303034", 0, ".zip", "application/zip", "Zip archive"], ["4d5357494d5c3030305c3030305c303030", 0, ".swm", "application/x-ms-wim", "WIM disk Image"], ["0b77", 0, ".ac3", "audio/ac3", "Dolby Digital audio"], ["7ffe8001", 0, ".dts", "audio/vnd.dts", "DTS audio"], ["80017ffe", 0, ".dts", "audio/vnd.dts", "DTS audio"], ["1fffe800", 0, ".dts", "audio/vnd.dts", "DTS audio"], ["e8001fff", 0, ".dts", "audio/vnd.dts", "DTS audio"], ["2321414d525c6e", 0, ".amr", "audio/AMR", "AMR audio"], ["2321414d525f4d43312e305c6e", 0, ".amr", "audio/AMR", "AMR audio"], ["2321414d522d57425c6e", 0, ".awb", "audio/AMR-WB", "AMR-WB audio"], ["2321414d522d57425f4d43312e305c6e", 0, ".awb", "audio/AMR-WB", "AMR-WB audio"], ["4d414320", 0, ".ape", "audio/x-ape", "Monkey's audio"], ["7776706b", 0, ".wvp", "audio/x-wavpack", "WavPack audio"], ["7776706b", 0, ".wvc", "audio/x-wavpack-correction", "WavPack audio correction file"], ["4d4f33", 0, ".mo3", "audio/x-mo3", "MO3 compressed Tracker audio"], ["41444946", 0, ".aac", "audio/aac", "AAC audio"], ["fff0", 0, ".aac", "audio/aac", "AAC audio"], ["667479704d3441", 4, ".f4a", "audio/mp4", "MPEG-4 audio"], ["6674797069736f6d", 4, ".mp4", "video/mp4", "MPEG-4 video"], ["667479706d703432", 4, ".mp4", "video/mp4", "MPEG-4 video"], ["667479704d534e56", 4, ".mp4", "video/mp4", "MPEG-4 video"], ["667479704d345620", 4, ".mp4", "video/mp4", "MPEG-4 video"], ["6674797066347620", 4, ".mp4", "video/mp4", "MPEG-4 video"], ["667479704d3442", 4, ".f4b", "audio/x-m4b", "MPEG-4 audio book"], ["66747970336765", 4, ".3ga", "video/3gpp", "3GPP multimedia file"], ["66747970336767", 4, ".3ga", "video/3gpp", "3GPP multimedia file"], ["66747970336770", 4, ".3ga", "video/3gpp", "3GPP multimedia file"], ["66747970336773", 4, ".3ga", "video/3gpp", "3GPP multimedia file"], ["234558544d3355", 0, ".vlc", "audio/x-mpegurl", "MP3 audio (streamed)"], ["41534620", 0, ".wmx", "audio/x-ms-asx", "Microsoft ASX playlist"], ["505346", 0, ".psf", "audio/x-psf", "PSF audio"], ["4d502b", 0, ".mp+", "audio/x-musepack", "Musepack audio"], ["5343524d", 44, ".s3m", "audio/x-s3m", "Scream Tracker 3 audio"], ["5b706c61796c6973745d", 0, ".pls", "audio/x-scpls", "MP3 ShoutCast playlist"], ["5b506c61796c6973745d", 0, ".pls", "audio/x-scpls", "MP3 ShoutCast playlist"], ["5b504c41594c4953545d", 0, ".pls", "audio/x-scpls", "MP3 ShoutCast playlist"], ["2153637265616d215c783141", 20, ".stm", "audio/x-stm", "Scream Tracker audio"], ["2153435245414d215c783141", 20, ".stm", "audio/x-stm", "Scream Tracker audio"], ["424d4f443253544d5c783141", 20, ".stm", "audio/x-stm", "Scream Tracker audio"], ["57415645", 8, ".wav", "audio/x-wav", "WAV audio"], ["57415620", 8, ".wav", "audio/x-wav", "WAV audio"], ["54544131", 0, ".tta", "audio/x-tta", "TrueAudio audio"], ["424d787878785c3030305c303030", 0, ".dib", "image/bmp", "Windows BMP image"], ["5c3337375c3333305c333737", 0, ".jpe", "image/jpeg", "JPEG image"], ["ffd8", 0, ".jpe", "image/jpeg", "JPEG image"], ["5c7846465c7834465c7846465c7835315c783030", 0, ".jpf", "image/jp2", "JPEG-2000 image"], ["0c6a5020", 3, ".jpf", "image/jp2", "JPEG-2000 image"], ["6a7032", 20, ".jpf", "image/jp2", "JPEG-2000 image"], ["504b5c3030335c303034", 0, ".ora", "image/openraster", "OpenRaster archiving image"], ["444453", 0, ".dds", "image/x-dds", "DirectDraw surface"], ["300600", 0, ".exr", "image/x-exr", "EXR image"], ["49495c7831615c7830305c7830305c7830304845415043434452", 0, ".crw", "image/x-canon-crw", "Canon CRW raw image"], ["46554a4946494c4d4343442d52415720", 0, ".raf", "image/x-fuji-raf", "Fuji RAF raw image"], ["454153544d414e204b4f44414b20434f4d50414e59", 242, ".kdc", "image/x-kodak-kdc", "Kodak KDC raw image"], ["5c7830304d524d", 0, ".mrw", "image/x-minolta-mrw", "Minolta MRW raw image"], ["4949524f5c7830385c7830305c7830305c783030", 0, ".orf", "image/x-olympus-orf", "Olympus ORF raw image"], ["4949555c7830305c7830385c7830305c7830305c783030", 0, ".raw", "image/x-panasonic-raw", "Panasonic raw image"], ["4949555c7830305c7831385c7830305c7830305c783030", 0, ".rw2", "image/x-panasonic-raw2", "Panasonic raw2 image"], ["49495500", 0, ".rw2", "image/x-panasonic-raw2", "Panasonic raw2 image"], ["49495500", 0, ".rwl", "image/x-panasonic-raw2", "Leica raw image"], ["464f5662", 0, ".x3f", "image/x-sigma-x3f", "Sigma X3F raw image"], ["5c783839504e47", 0, ".png", "image/png", "PNG image"], ["5c7834355c7835305c7832415c783030", 0, ".mdi", "image/vnd.ms-modi", "Microsoft Document Imaging format"], ["4449434d", 128, ".dcm", "application/dicom", "DICOM image"], ["41542654464f524d", 0, ".djv", "image/vnd.djvu", "DjVu image"], ["464f524d", 0, ".djv", "image/vnd.djvu", "DjVu image"], ["41542654464f524d", 0, ".djv", "image/vnd.djvu+multipage", "DjVu document"], ["464f524d", 0, ".djv", "image/vnd.djvu+multipage", "DjVu document"], ["5c305c305c315c30", 0, ".ico", "image/vnd.microsoft.icon", "Windows icon"], ["494c424d", 8, ".lbm", "image/x-ilbm", "ILBM image"], ["50424d20", 8, ".lbm", "image/x-ilbm", "ILBM image"], ["5031", 0, ".pbm", "image/x-portable-bitmap", "PBM image (Ascii)"], ["5034", 0, ".pbm", "image/x-portable-bitmap", "PBM image (Binary)"], ["5032", 0, ".pgm", "image/x-portable-graymap", "PGM image (Ascii)"], ["5035", 0, ".pgm", "image/x-portable-graymap", "PGM image (Binary)"], ["5033", 0, ".ppm", "image/x-portable-pixmap", "PPM image (Ascii)"], ["5036", 0, ".ppm", "image/x-portable-pixmap", "PPM image (Binary)"], ["3842505320205c3030305c3030305c3030305c303030", 0, ".psd", "image/vnd.adobe.photoshop", "Photoshop image"], ["59a66a95", 0, ".sun", "image/x-sun-raster", "Sun raster image"], ["5c305c32", 1, ".vst", "image/x-tga", "TGA image"], ["5c305c305c325c30", 0, ".cur", "image/x-win-bitmap", "Windows cursor"], ["01000000", 0, ".emf", "image/emf", "EMF image"], ["0100", 0, ".wmf", "image/wmf", "WMF image"], ["67696d70207863662066696c65", 0, ".xcf", "image/x-xcf", "GIMP image"], ["67696d70207863662076", 0, ".xcf", "image/x-xcf", "GIMP image"], ["23464947", 0, ".fig", "image/x-xfig", "XFig image"], ["2f2a2058504d", 0, ".xpm", "image/x-xpixmap", "XPM image"], ["232120726e657773", 0, ".eml", "message/rfc822", "email message"], ["466f727761726420746f", 0, ".eml", "message/rfc822", "email message"], ["46726f6d3a", 0, ".eml", "message/rfc822", "email message"], ["4e232120726e657773", 0, ".eml", "message/rfc822", "email message"], ["5069706520746f", 0, ".eml", "message/rfc822", "email message"], ["52656365697665643a", 0, ".eml", "message/rfc822", "email message"], ["52656c61792d56657273696f6e3a", 0, ".eml", "message/rfc822", "email message"], ["52657475726e2d506174683a", 0, ".eml", "message/rfc822", "email message"], ["52657475726e2d706174683a", 0, ".eml", "message/rfc822", "email message"], ["5375626a6563743a20", 0, ".eml", "message/rfc822", "email message"], ["2356524d4c20", 0, ".wrl", "model/vrml", "VRML document"], ["424547494e3a5643414c454e444152", 0, ".ics", "text/calendar", "VCS/ICS calendar"], ["626567696e3a7663616c656e646172", 0, ".ics", "text/calendar", "VCS/ICS calendar"], ["2521706f737470726f63", 0, ".t2t", "text/x-txt2tags", "txt2tags document"], ["2521656e636f64696e67", 0, ".t2t", "text/x-txt2tags", "txt2tags document"], ["49443b", 0, ".slk", "text/spreadsheet", "spreadsheet interchange document"], ["6469677261706820", 0, ".dot", "text/vnd.graphviz", "Graphviz DOT graph"], ["737472696374206469677261706820", 0, ".dot", "text/vnd.graphviz", "Graphviz DOT graph"], ["677261706820", 0, ".dot", "text/vnd.graphviz", "Graphviz DOT graph"], ["73747269637420677261706820", 0, ".dot", "text/vnd.graphviz", "Graphviz DOT graph"], ["4d49446c65742d", 0, ".jad", "text/vnd.sun.j2me.app-descriptor", "JAD document"], ["2a2a4143452a2a", 7, ".ace", "application/x-ace", "ACE archive"], ["2520546869732066696c652077617320637265617465642077697468204a6162526566", 0, ".bib", "text/x-bibtex", "BibTeX document"], ["3c424f4459", 0, ".html", "text/html", "HTML document"], ["3c21444f4354595045", 0, ".html", "text/html", "HTML document"], ["3c21646f6374797065", 0, ".html", "text/html", "HTML document"], ["3c21446f6354797065", 0, ".html", "text/html", "HTML document"], ["3c21446f6374797065", 0, ".html", "text/html", "HTML document"], ["3c626f6479", 0, ".html", "text/html", "HTML document"], ["3c212d2d", 0, ".html", "text/html", "HTML document"], ["3c6831", 0, ".html", "text/html", "HTML document"], ["3c4831", 0, ".html", "text/html", "HTML document"], ["3c21646f63747970652048544d4c", 0, ".html", "text/html", "HTML document"], ["3c21444f43545950452068746d6c", 0, ".html", "text/html", "HTML document"], ["232e646f776e6c6f61642e7468652e667265652e476f6f676c652e566964656f2e506c61796572", 0, ".gvp", "text/x-google-video-pointer", "Google Video Pointer"], ["2320646f776e6c6f616420746865206672656520476f6f676c6520566964656f20506c61796572", 0, ".gvp", "text/x-google-video-pointer", "Google Video Pointer"], ["57696e646f777320526567697374727920456469746f722056657273696f6e20352e3030", 0, ".reg", "text/x-ms-regedit", "Windows Registry extract"], ["5c7866665c786665575c783030695c7830306e5c783030645c7830306f5c783030775c783030735c783030205c783030525c783030655c783030675c783030695c783030735c783030745c783030725c783030795c783030205c783030455c783030645c783030695c783030745c7830306f5c783030725c783030", 0, ".reg", "text/x-ms-regedit", "Windows Registry extract"], ["2f2f214d7570", 0, ".not", "text/x-mup", "Mup publication"], ["626567696e20", 0, ".uue", "text/x-uuencode", "uuencoded file"], ["3c3f786d6c", 0, ".rng", "application/xml", "XML document"], ["3c212d2d", 0, ".rng", "application/xml", "XML document"], ["234558544d3455", 0, ".mxu", "video/vnd.mpegurl", "MPEG video (streamed)"], ["69646174", 4, ".qif", "image/x-quicktime", "QuickTime image"], ["11af", 0, ".flc", "video/x-flic", "FLIC animation"], ["12af", 0, ".flc", "video/x-flic", "FLIC animation"], ["48575020446f63756d656e742046696c65", 0, ".hwp", "application/x-hwp", "Haansoft Hangul document"], ["5c7838414d4e475c7830445c7830415c7831415c783041", 0, ".mng", "video/x-mng", "MNG animation"], ["3026b275", 0, ".asf", "application/vnd.ms-asf", "ASF video"], ["5b5265666572656e63655d", 0, ".asf", "application/vnd.ms-asf", "ASF video"], ["5b416464726573735d", 0, ".nsc", "application/x-netshow-channel", "Windows Media Station file"], ["4e535666", 0, ".nsv", "video/x-nsv", "NullSoft video"], ["763d", 0, ".sdp", "application/sdp", "SDP multicast stream file"], ["6e4637594c616f", 0, ".emp", "application/vnd.emusic-emusic_package", "eMusic download package"], ["4043542030", 0, ".602", "application/x-t602", "T602 document"], ["4043542031", 0, ".602", "application/x-t602", "T602 document"], ["4043542032", 0, ".602", "application/x-t602", "T602 document"], ["5b6d61696e5d", 0, ".pcf", "application/x-cisco-vpn-settings", "Cisco VPN Settings"], ["61637370", 36, ".icm", "application/vnd.iccprofile", "ICC profile"], ["504b5c3030335c303034", 0, ".key", "application/x-iwork-keynote-sffkey", "Apple Keynote 5 presentation"], ["49574144", 0, ".wad", "application/x-doom-wad", "Doom WAD"], ["50574144", 0, ".wad", "application/x-doom-wad", "Doom WAD"], ["444f53", 0, ".adf", "application/x-amiga-disk-format", "Amiga disk image"], ["0000001466747970336770", 0, ".3g2", "video/3gpp", "3GPP 3rd Generation Partnership Project video file"], ["000001ba", 0, ".vob", "video/dvd", "DVD video file"], ["000001ba", 0, ".mpg", "video/mpeg", "DVD video file"], ["504b030414000600", 0, "xlsx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "Microsoft Office 2007+ Open XML Format Document file"], ["4d546864", 0, ".mid", "audio/x-midi", "Musical Instrument Digital Interface (MIDI) sound file"], ["1f9d", 0, ".tgz", "application/x-tgz", "Compressed tape archive file using standard compression"], ["1fa0", 0, ".tgz", "application/x-tgz", "Compressed tape archive file using LZH compression"], ["2e524d46", 0, ".rm", "application/vnd.rn-realmedia", "RealMedia media file"], ["2e524d460000001200", 0, ".ra", "audio/x-pn-realaudio", "RealAudio file"], ["2e7261fd00", 0, ".ra", "audio/x-pn-realaudio", "RealAudio media file"], ["1f8b08", 0, ".gz", "application/x-gzip", "GZIP Archive file"], ["fd377a585a00", 0, ".xz", "application/x-xz", "LMZA XZ Archive file"], ["377abcaf271c", 0, ".7z", "application/x-7z-compressed", "7-Zip Compressed file"], ["04000000", 524, ".db", "application/octet-stream", "Windows Thumbs.db file"], ["23212f7573722f62696e2f656e7620707974686f6e", 0, ".py", "text/x-python", "Python file"], ["23202d2a2d20636f64696e67", 0, ".py", "text/x-python", "Python file"], ["23202d2a2d20636f64696e67", 0, ".py", "text/x-python", "Python file"], ["696d706f727420", 0, ".py", "text/x-python", "Python file"], ["0006156100000002000004d200001000", 0, ".db", "", "Netscape Navigator (v4) database"], ["53514c69746520666f726d6174203300", 0, ".db", "", "SQLite database file"], ["d0cf11e0a1b11ae1", 0, ".db", "", "MSWorks database file"], ["5245564e554d3a2c", 0, ".ad", "", "Antenna data file"], ["2e524d4600000012", 0, ".ra", "audio/x-pn-realaudio", "RealAudio file"], ["8a0109000000e108", 0, ".aw", "", "MS Answer Wizard"], ["4d5a900003000000", 0, ".ax", "", "DirectShow filter"], ["1100000053434341", 0, ".pf", "", "Windows prefetch file"], ["3c4d616b65724669", 0, ".fm", "", "Adobe FrameMaker"], ["72696666", 0, ".ac", "", "Sonic Foundry Acid Music File"], ["fdffffff", 512, ".db", "", "Thumbs.db subheader"], ["4a47030e", 0, ".jg", "", "AOL ART file"], ["4a47040e", 0, ".jg", "", "AOL ART file"], ["44424648", 0, ".db", "", "Palm Zire photo database"], ["ff575043", 0, ".wp", "application/wordperfect", "WordPerfect text and graphics"], ["646e732e", 0, ".au", "audio/x-au", "Audacity audio file"], ["2e736e64", 0, ".au", "audio/x-au", "NeXT|Sun Microsystems audio file"], ["1d7d", 0, ".ws", "", "WordStar Version 5.0|6.0 document"], ["4d5a", 0, ".ax", "", "Library cache file"], ["0ced", 0, ".mp", "", "Monochrome Picture TIFF bitmap"], ["282a2a2a2a2a2a2a2a2a2a2a2a2a2a20436f6e74656e742d747970653a206170706c69636174696f6e2f6d617468656d6174696361", 0, ".nb", "application/mathematica", "Mathematica Notebook"], ["5c3030342521", 0, ".ps", "application/postscript", "PS document"], ["2521", 0, ".ps", "application/postscript", "PS document"], ["377a5c3237345c3235375c3034375c303334", 0, ".7z", "application/x-7z-compressed", "7-zip archive"], ["2a424547494e20535052454144534845455453", 0, ".as", "application/x-applix-spreadsheet", "Applix Spreadsheets spreadsheet"], ["2a424547494e", 0, ".as", "application/x-applix-spreadsheet", "Applix Spreadsheets spreadsheet"], ["2a424547494e", 0, ".aw", "application/x-applix-word", "Applix Words document"], ["3c61723e", 0, ".ar", "application/x-archive", "AR archive"], ["213c617263683e", 0, ".ar", "application/x-archive", "AR archive"], ["2320646174612066696c6520666f722074686520466c746b", 0, ".fl", "application/x-fluid", "FLTK Fluid file"], ["3c4d616b657246696c65", 0, ".fm", "application/vnd.framemaker", "Adobe FrameMaker document"], ["3c4d494646696c65", 0, ".fm", "application/vnd.framemaker", "Adobe FrameMaker document"], ["3c4d616b657244696374696f6e617279", 0, ".fm", "application/vnd.framemaker", "Adobe FrameMaker document"], ["3c4d616b657253637265656e466f6e", 0, ".fm", "application/vnd.framemaker", "Adobe FrameMaker document"], ["3c4d4d4c", 0, ".fm", "application/vnd.framemaker", "Adobe FrameMaker document"], ["3c426f6f6b", 0, ".fm", "application/vnd.framemaker", "Adobe FrameMaker document"], ["3c4d616b6572", 0, ".fm", "application/vnd.framemaker", "Adobe FrameMaker document"], ["5c3333365c32325c345c323235", 0, ".mo", "application/x-gettext-translation", "translated messages (machine-readable)"], ["5c3232355c345c32325c333336", 0, ".mo", "application/x-gettext-translation", "translated messages (machine-readable)"], ["5c3033375c323133", 0, ".gz", "application/gzip", "Gzip archive"], ["5c3231314844465c725c6e5c3033325c6e", 0, ".h5", "application/x-hdf", "HDF document"], ["5c3031365c3030335c3032335c303031", 0, ".h5", "application/x-hdf", "HDF document"], ["4c5a4950", 0, ".lz", "application/x-lzip", "Lzip archive"], ["7170726573733130", 0, ".qp", "application/x-qpress", "Qpress archive"], ["5c313737454c46", 0, ".so", "application/x-sharedlib", "shared library"], ["5c313737454c462020202020202020202020205c303033", 0, ".so", "application/x-sharedlib", "shared library"], ["2320546869732069732061207368656c6c2061726368697665", 10, ".sh", "application/x-shellscript", "shell script"], ["5c7866645c7833375c7837615c7835385c7835615c783030", 0, ".xz", "application/x-xz", "XZ archive"], ["494d504d", 0, ".it", "audio/x-it", "Impulse Tracker (and clones) audio"], ["457874656e64656420496e737472756d656e743a", 0, ".xi", "audio/x-xi", "Scream Tracker instrument"], ["457874656e646564204d6f64756c653a", 0, ".xm", "audio/x-xm", "FastTracker II audio"], ["2a424547494e", 0, ".ag", "image/x-applix-graphics", "Applix Graphics image"], ["5c30313228", 0, ".el", "text/x-emacs-lisp", "Emacs Lisp source code"], ["3b454c435c3032335c3030305c3030305c303030", 0, ".el", "text/x-emacs-lisp", "Emacs Lisp source code"], ["7265636f7264", 0, ".mo", "text/x-modelica", "Modelica model"], ["1f070000", 0, ".dv", "video/dv", "DV video"], ["5c3033375c323335", 0, ".Z", "application/x-compress", "UNIX-compressed file"], ["5c313737454c46", 0, ".o", "application/x-object", "object code"], ["2e5c5c5c22", 0, ".t", "text/troff", "Troff document"], ["275c5c5c22", 0, ".t", "text/troff", "Troff document"], ["272e5c5c5c22", 0, ".t", "text/troff", "Troff document"], ["5c5c5c22", 0, ".t", "text/troff", "Troff document"], ["23696d706f7274", 0, ".m", "text/x-objcsrc", "Objective-C source code"], ["66756e6374696f6e", 0, ".m", "text/x-matlab", "MATLAB script/function"], ["cd20aaaa02000000", 0, "", "", "NAV quarantined virus file"], ["535a2088f02733d1", 0, "", "", "QBASIC SZDD file"], ["4b57414a88f027d1", 0, "", "", "KWAJ (compressed) file"], ["37e45396c9dbd607", 0, "", "", "zisofs compressed file"], ["535a444488f02733", 0, "", "", "SZDD file format"], ["001400000102", 0, "", "", "BIOS details in RAM"], ["626567696e", 0, "", "", "UUencoded file"], ["3037303730", 0, "", "", "cpio archive"], ["04000000", 0, "", "", "INFO2 Windows recycle bin"], ["41435344", 0, "", "", "AOL parameter|info files"], ["a1b2c3d4", 0, "", "", "tcpdump (libpcap) capture file"], ["7f454c46", 0, "", "", "ELF executable"], ["d4c3b2a1", 0, "", "", "WinDump (winpcap) capture file"], ["a1b2cd34", 0, "", "", "Extended tcpdump (libpcap) capture file"], ["0a0d0d0a", 0, ".pcapng", "application/octet-stream", "pcapng capture file"], ["05000000", 0, "", "", "INFO2 Windows recycle bin"], ["34cdb2a1", 0, "", "", "Tcpdump capture file"], ["fffe0000", 0, "", "", "UTF-32|UCS-4 file"], ["efbbbf", 0, "", "", "UTF8 file"], ["feff", 0, "", "", "UTF-16|UCS-2 file"], ["6f3c", 0, "", "", "SMS text (SIM)"], ["aced", 0, "", "", "Java serialization data"], ["6d75737420626520636f6e76657274656420776974682042696e486578", 11, "", "application/mac-binhex40", "Macintosh BinHex-encoded file"], ["44617461506c6b72", 60, "", "application/prs.plucker", "Plucker document"], ["2a2a20546869732066696c6520636f6e7461696e7320616e2053514c697465", 0, "", "application/x-sqlite2", "SQLite2 database"], ["53514c69746520666f726d61742033", 0, "", "application/x-sqlite3", "SQLite3 database"], ["1a080000", 0, "", "application/x-arc", "ARC archive"], ["1a090000", 0, "", "application/x-arc", "ARC archive"], ["1a020000", 0, "", "application/x-arc", "ARC archive"], ["1a030000", 0, "", "application/x-arc", "ARC archive"], ["1a040000", 0, "", "application/x-arc", "ARC archive"], ["1a060000", 0, "", "application/x-arc", "ARC archive"], ["53454741205049434f", 256, "", "application/x-sega-pico-rom", "Sega Pico ROM"], ["5c313737454c46", 0, "", "application/x-executable", "executable"], ["1c52", 0, "", "application/x-executable", "executable"], ["5c7866665c7834365c7834665c783465", 0, "", "application/x-font-dos", "DOS font"], ["5c7830305c7834355c7834375c783431", 7, "", "application/x-font-dos", "DOS font"], ["5c7830305c7835365c7834395c783434", 7, "", "application/x-font-dos", "DOS font"], ["3c4d616b657253637265656e466f6e74", 0, "", "application/x-font-framemaker", "Adobe FrameMaker font"], ["5c7831345c7830325c7835395c783139", 0, "", "application/x-font-libgrx", "LIBGRX font"], ["5374617274466f6e74", 0, "", "application/x-font-sunos-news", "SunOS News font"], ["5c7831335c7837415c783239", 0, "", "application/x-font-sunos-news", "SunOS News font"], ["5c7831335c7837415c783242", 8, "", "application/x-font-sunos-news", "SunOS News font"], ["5c3336375c323033", 0, "", "application/x-font-tex", "TeX font"], ["5c3336375c313331", 0, "", "application/x-font-tex", "TeX font"], ["5c3336375c333132", 0, "", "application/x-font-tex", "TeX font"], ["5c3030305c303231", 2, "", "application/x-font-tex-tfm", "TeX font metrics"], ["5c3030305c303232", 2, "", "application/x-font-tex-tfm", "TeX font metrics"], ["464f4e54", 0, "", "application/x-font-vfont", "V font"], ["13579ace", 0, "", "application/x-gdbm", "GDBM database"], ["ce9a5713", 0, "", "application/x-gdbm", "GDBM database"], ["4744424d", 0, "", "application/x-gdbm", "GDBM database"], ["67746b74616c6f6720", 4, "", "application/x-gtktalog", "GTKtalog catalog"], ["464f524d", 0, "", "application/x-iff", "IFF file"], ["532054204f2050", 0, "", "application/x-ipod-firmware", "iPod firmware"], ["0d1a2702", 0, "", "application/x-kspread-crypt", "KSpread spreadsheet (encrypted)"], ["4b53797356", 4, "", "application/x-ksysv-package", "KSysV init package"], ["0d1a2701", 0, "", "application/x-kword-crypt", "KWord document (encrypted)"], ["6d42494e", 102, "", "application/x-macbinary", "Macintosh MacBinary file"], ["1a45dfa3", 0, "", "application/x-matroska", "Matroska stream"], ["5c3332305c3331375c3032315c3334305c3234315c3236315c3033325c333431", 0, "", "application/x-ole-storage", "OLE2 compound document storage"], ["d0cf11e0", 0, "", "application/x-ole-storage", "OLE2 compound document storage"], ["4a6f7921", 0, "", "application/x-pef-executable", "PEF executable"], ["5370726561647368656574", 38, "", "application/x-sc", "SC/Xspread spreadsheet"], ["2e736400", 0, "", "audio/x-adpcm", "PCM audio"], ["58637572", 0, "", "image/x-xcursor", "X11 cursor"], ["5c7832385c30305c30305c3030", 0, "", "image/x-dib", "DIB image"], ["53445058", 0, "", "image/dpx", "DPX image"], ["46506978", 0, "", "image/x-fpx", "FPX image"], ["49494e31", 0, "", "image/x-niff", "NIFF image"], ["41727469636c65", 0, "", "message/news", "Usenet news message"], ["506174683a", 0, "", "message/news", "Usenet news message"], ["587265663a", 0, "", "message/news", "Usenet news message"], ["2320786d6364", 0, "", "text/xmcd", "XMCD CD database"], ["4344303031", 32769, ".iso", "application/octet-stream", "ISO-9660 CD Disc Image file"], ["4344303031", 34817, ".iso", "application/octet-stream", "ISO-9660 CD Disc Image file"], ["4344303031", 36865, ".iso", "application/octet-stream", "ISO-9660 CD Disc Image file"], ["28b52ffd", 0, ".zst", "application/zstd", "ZStandard Archive"], ["28b52ffd", 0, ".tzst", "application/x-zstd-compressed-tar", "ZStandard Tar Archive"], ["28b52ffd", 0, ".tar.zst", "application/x-zstd-compressed-tar", "ZStandard Tar Archive"], ["04224d18", 0, ".lz4", "application/x-lz4", "LZ4 Archive"], ["04224d18", 0, ".tlz4", "application/x-lz4-compressed-tar", "LZ4 Tar Archive"], ["04224d18", 0, ".tar.lz4", "application/x-lz4-compressed-tar", "LZ4 Tar Archive"], ["4f50434c444154", 0, ".attachment", "", "1Password 4 Cloud Keychain"], ["63616666", 0, ".caf", "audio/x-caf", "Apple Core Audio File"], ["667479704d344120", 4, ".m4a", "", "Apple Lossless Audio Codec file"], ["3c7e363c5c255f30675371683b", 0, ".b85", "", "BASE85 file"], ["425047fb", 0, ".bpg", "", "Better Portable Graphics"], ["000000006231050009000000002000000009000000000000", 8, ".dat", "", "Bitcoin Core wallet.dat file"], ["f9beb4d9", 0, ".dat", "", "Bitcoin-Qt blockchain block file"], ["426c696e6b", 0, ".bli", "", "Blink compressed archive"], ["2350454330303031", 0, ".pec", "", "Brother-<PERSON><PERSON>-<PERSON><PERSON> Home Embroidery"], ["2350455330", 0, ".pes", "", "Brother-<PERSON><PERSON>-<PERSON><PERSON> Home Embroidery"], ["43616c63756c757820496e646f6f7220", 0, ".cin", "", "Calculux Indoor lighting project file"], ["4349534f", 0, ".cso", "", "Compressed ISO CD image"], ["00000000000000000000000000000000", 0, ".xxx", "", "Compucon-Singer embroidery design file"], ["437265617469766520566f6963652046", 0, ".voc", "audio/voc", "Creative Voice"], ["********************************", 0, ".csd", "", "Csound music"], ["6465780a", 0, ".dex", "", "Dalvik (Android) executable file"], ["44415800", 0, ".dax", "", "DAX Compressed CD image"], ["7e742c015070024d52", 0, ".img", "", "Digital Watchdog DW-TP-500G audio"], ["44535462", 0, ".dst", "", "DST Compression"], ["10000000", 0, ".cl5", "", "Easy CD Creator 5 Layout file"], ["455646320d0a81", 0, ".ex01", "", "EnCase Evidence File Format V2"], ["4644424800", 0, ".fdb", "", "Fiasco database definition file"], ["01003930", 0, ".fdb", "", "Firebird and Interbase database files"], ["01003930", 0, ".gdb", "", "Firebird and Interbase database files"], ["53494d504c4520203d202020202020202020202020202020202020202054", 0, ".fits", "image/fits", "Flexible Image Transport System (FITS) file"], ["256269746d6170", 0, ".fbm", "", "Fuzzy bitmap (FBM) file"], ["47656e65746563204f6d6e6963617374", 0, ".g64", "", "Genetec video archive"], ["67696d7020786366", 0, ".xcf", "image/x-xcf", "GIMP file"], ["3c6770782076657273696f6e3d22312e", 0, ".gpx", "", "GPS Exchange (v1.1)"], ["5dfcc800", 0, ".hus", "", "<PERSON><PERSON><PERSON><PERSON><PERSON> Designer"], ["496e7465724063746976652050616765", 0, ".ipd", "", "Inter@ctive Pager Backup (BlackBerry file"], ["cecec<PERSON>e", 0, ".jceks", "", "Java Cryptography Extension keystore"], ["974a42320d0a1a0a", 0, ".jb2", "", "JBIG2 image file"], ["802a5fd7", 0, ".cin", "", "Kodak Cineon image"], ["5a5753", 0, ".swf", "application/x-shockwave-flash", "Macromedia Shockwave Flash"], ["2142444e", 0, ".ost", "", "Microsoft Outlook Exchange Offline Storage Folder"], ["4d5357494d", 0, ".wim", "", "Microsoft Windows Imaging Format"], ["504d4f43434d4f43", 0, ".pmoccmoc", "", "Microsoft Windows User State Migration Tool"], ["fff9", 0, ".aac", "audio/aac", "MPEG-2 AAC audio"], ["fff1", 0, ".aac", "audio/aac", "MPEG-4 AAC audio"], ["6674797033677035", 4, ".mp4", "video/mp4", "MPEG-4 video file"], ["0000001c66747970", 0, ".mp4", "video/mp4", "MPEG-4 video"], ["fdffffff02", 512, ".pub", "", "MS Publisher file subheader"], ["53505642", 0, ".spvb", "", "MultiBit Bitcoin blockchain file"], ["0a166f72672e626974636f696e2e7072", 0, ".wallet", "", "MultiBit Bitcoin wallet file"], ["6d756c74694269742e696e666f", 0, ".info", "", "MultiBit Bitcoin wallet information"], ["504b03040a000200", 0, ".epub", "", "Open Publication Structure eBook"], ["762f3101", 0, ".exr", "image/x-exr", "OpenEXR bitmap image"], ["bebafeca0f50616c6d53472044617461", 0, ".dat", "", "Palm Desktop DateBook"], ["3203100000000000000080000000ff00", 0, ".pcs", "", "Pfaff Home Embroidery"], ["426567696e20507566666572", 0, ".apuf", "", "Puffer ASCII encrypted archive"], ["50554658", 0, ".puf", "", "<PERSON><PERSON><PERSON> encrypted archive"], ["ff0a00", 0, ".qrp", "", "QuickReport Report"], ["6674797071742020", 4, ".mov", "video/quicktime", "QuickTime movie"], ["43444441666d7420", 8, ".cda", "", "RIFF CD audio"], ["514c434d666d7420", 8, ".qcp", "audio/vnd.qcelp", "RIFF Qualcomm PureVoice"], ["52494646", 0, ".webp", "image/webp", "RIFF WebP"], ["524d494464617461", 8, ".rmi", "", "RIFF Windows MIDI"], ["484541444552205245434f52442a2a2a", 0, ".xpt", "", "SAS Transport dataset"], ["232153494c4b0a", 0, ".sil", "", "Skype audio compression"], ["58504453", 0, ".dpx", "", "SMPTE DPX file (little endian)"], ["424c49323233", 0, ".bin", "application/x-binary", "Speedtouch router firmware"], ["424c49323233", 0, ".bli", "application/octet-stream", "Speedtouch router firmware"], ["424c49323233", 0, ".rbi", "application/octet-stream", "Speedtouch router firmware"], ["49443303000000", 0, ".koz", "", "Sprint Music Store audio"], ["5350464900", 0, ".spf", "", "StorageCraft ShadownProtect backup file"], ["4c413a", 0, ".dst", "", "Tajima emboridery"], ["4d435720546563686e6f676f6c696573", 0, ".mte", "", "TargetExpress target file"], ["01014719a400000000000000", 0, ".tbi", "", "The Bat! Message Base Index"], ["2f2f203c212d2d203c6d64623a6d6f726b3a7a", 0, ".msf", "", "Thunderbird|Mozilla Mail Summary File"], ["0001000000", 0, ".ttf", "", "TrueType font file"], ["626567696e2d626173653634", 0, ".b64", "", "UUencoded BASE64 file"], ["4d73526366", 0, ".gdb", "", "VMapSource GPS Waypoint Database"], ["5b564d445d", 0, ".vmd", "application/vocaltec-media-desc", "VocalTec VoIP media file"], ["54485000", 0, ".thp", "", "Wii-GameCube"], ["69736f32617663316d7034", 20, ".mp4", "video/mp4", "MP4 Video"], ["434d4d4d15000000", 0, ".db", "", "Windows 7 thumbnail"], ["494d4d4d15000000", 0, ".db", "", "Windows 7 thumbnail"], ["0000000014000000", 0, ".tbi", "", "Windows Disk Image"], ["53434341", 4, ".pf", "", "Windows prefetch"], ["6f70646174613031", 0, "", "", "1Password 4 Cloud Keychain encrypted data"], ["eb52902d4656452d", 0, "", "", "BitLocker boot sector (Vista)"], ["eb58902d4656452d", 0, "", "", "BitLocker boot sector (Win7)"], ["f0ffff", 0, "", "", "FAT12 File Allocation Table"], ["f8ffffff", 0, "", "", "FAT16 File Allocation Table"], ["f8ffff0fffffff0f", 0, "", "", "FAT32 File Allocation Table"], ["f8ffff0fffffffff", 0, "", "", "FAT32 File Allocation Table"], ["feedfeed", 0, "", "", "JavaKeyStore"], ["000dbba0", 0, "", "", "Mbox table of contents file"], ["42414144", 0, "", "", "NTFS MFT (BAAD)"], ["46494c45", 0, "", "", "NTFS MFT (FILE)"], ["cefaedfe", 0, "", "", "OS X ABI Mach-O binary (32-bit reverse)"], ["feedface", 0, "", "", "OS X ABI Mach-O binary (32-bit)"], ["cffaedfe", 0, "", "", "OS X ABI Mach-O binary (64-bit reverse)"], ["feedfacf", 0, "", "", "OS X ABI Mach-O binary (64-bit)"], ["7c4bc374e1c853a479b9011dfc4fdd13", 0, ".csd", "", "Huskygram Poem or Singer embroidery"], ["49495500", 0, ".raw", "application/octet-stream", "Raw Image File"], ["49495500", 0, ".rw2", "application/octet-stream", "Raw Image File"], ["49495253", 0, ".orf", "application/octet-stream", "Raw Image File"], ["4d6178324f626a", 2, ".obj", "text/plain", "Wavefront geometry definition file format"], ["426c656e646572", 2, ".obj", "text/plain", "Wavefront geometry definition file format"], ["4d6178324d746c", 2, ".mtl", "text/plain", "Material Definitions for OBJ Files"], ["426c656e646572204d544c2046696c65", 2, ".mtl", "text/plain", "Material Definitions for OBJ Files"], ["787369", 0, ".xsi", "text/plain", "Softimage XSI 3D Image"], ["706c79", 50, ".ply", "text/plain", "Finale Playback File"], ["4d617961", 2, ".ma", "text/plain", "Maya Project File"], ["56524d4c", 1, ".wrl", "text/plain", "VRML World"], ["583344", 50, ".x3d", "application/xml", "Xara3D Project"], ["464258", 2, ".fbx", "application/octet-stream", "Autodesk FBX Interchange File"], ["4d533344", 0, ".ms3d", "application/octet-stream", "MilkShape 3D Model"], ["5843344443344436", 0, ".c4d", "application/octet-stream", "Cinema 4D Model File"], ["2321414d", 0, ".amr", "application/octet-stream", "Adaptive Multi-Rate Codec File"], ["4372656174697665", 0, ".voc", "application/octet-stream", "Creative Labs Audio File"], ["6d6174726f736b61", 31, ".mka", "audio/x-matroska", "Matroska Audio File"], ["6d6174726f736b61", 31, ".mkv", "video/x-matroska", "Matroska Video File"], ["74657874", 73, ".odt", "application/vnd.oasis.opendocument.text", "OpenDocument Text File"], ["70726573656e746174696f6e", 73, ".odp", "application/vnd.oasis.opendocument.presentation", "OpenDocument Presentation"], ["7370726561647368656574", 73, ".ods", "application/vnd.oasis.opendocument.spreadsheet", "OpenDocument Spreadsheet"], ["526172211a070100", 0, ".rar", "application/vnd.rar", "RAR Archive"], ["4d5a9000", 0, ".exe", "application/vnd.microsoft.portable-executable", "Windows Executable"], ["00010000", 0, ".ttf", "font/ttf", "TTF Font"], ["774f4632", 0, ".woff2", "font/woff2", "WOFF2 Font"], ["3082", 0, ".cat", "application/vnd.ms-pki.seccat", "Windows System File"], ["4d5a9000", 0, ".dll", "application/vnd.microsoft.portable-executable", "Windows DLL File"], ["4d5a9000", 0, ".drv", "application/vnd.microsoft.portable-executable", "Windows Driver File"], ["73646266", 8, ".sdb", "application/vnd.microsoft.portable-executable", "Windows System File"], ["4d5a8000", 0, ".sys", "application/vnd.microsoft.portable-executable", "Windows System File"], ["4d5a9000", 0, ".sys", "application/vnd.microsoft.portable-executable", "Windows System File"], ["57696e646f7773205265676973747279", 0, ".reg", "application/vnd.microsoft.portable-executable", "Windows Registry File"], ["3c73766720", 0, ".svg", "image/svg+xml", "Scalable Vector Graphics Image"], ["3c3f786d6c2076657273696f6e3d22312e3022207374616e64616c6f6e653d22796573223f3e0a3c73766720", 0, ".svg", "image/svg+xml", "Scalable Vector Graphics Image"], ["3c3f786d6c2076657273696f6e3d22312e3022207374616e64616c6f6e653d22796573223f3e3c73766720", 0, ".svg", "image/svg+xml", "Scalable Vector Graphics Image"], ["6674797061766966", 4, ".avif", "image/avif", "AV1 Image format"], ["6674797061766973", 4, ".avif", "image/avif", "AV1 Image format sequence (AVIS)"], ["6674797068656963", 4, ".heif", "image/heif", "HEIF Image format"], ["6674797068656978", 4, ".heic", "image/heic", "HEIC Image format (HEIX)"], ["6674797068657663", 4, ".heic", "image/heic", "HEIC Animated Image format (HEVC)"], ["6674797068657678", 4, ".heic", "image/heic", "HEIC Animated Image format (HEVX)"], ["6674797068656973", 4, ".heic", "image/heic", "HEIC Image format (HEIS scalable)"], ["667479706865696d", 4, ".heic", "image/heic", "HEIC Image format (HEIM multiview)"], ["667479706865766d", 4, ".heic", "image/heic", "HEIC Animated Image format (HEIM multiview)"], ["6674797068657673", 4, ".heic", "image/heic", "HEIC Animated Image format (HEIS scalable)"], ["4352454D", 44, ".ctm", "", "CreamTracker module"], ["3c747261636b206e616d653d22", 0, ".pt2", "", "PicaTune 2 module"], ["3c6d6c74", 38, ".mlt", "", "Shotcut project"], ["50503230", 0, ".pp", "", "PowerPacker compressed file (PP20)"], ["50503131", 0, ".pp", "", "PowerPacker compressed file (PP11)"], ["50583230", 0, ".pp", "", "PowerPacker encrypted compressed file (PX20)"], ["4efa", 0, ".fred", "", "FRED Editor tracker module"], ["4672656420456469746f7220", 0, ".fred", "", "FRED Editor song"], ["5a584159454d554c", 0, ".ay", "", "ZXAYEMUL AY audio"], ["70ff4e7544454c495249554d", 36, ".cust", "", "Custom Amiga Module audio"], ["70ff4e7544454c495249554d", 32, ".cust", "", "Custom Amiga Module audio"], ["4d454402", 0, ".med", "audio/x-mod", "MED module (MED2)"], ["4d454403", 0, ".med", "audio/x-mod", "MED module (MED3)"], ["4d454404", 0, ".med", "audio/x-mod", "MED module (MED4)"], ["4d4d4430", 0, ".med", "audio/x-mod", "Amiga MED tracker module (MMD0)"], ["4d4d4431", 0, ".med", "audio/x-mod", "Amiga OctaMED Professional tracker module (MMD1)"], ["4d4d4432", 0, ".med", "audio/x-mod", "Amiga OctaMED Professional V5 tracker module (MMD2)"], ["4d4d4433", 0, ".med", "audio/x-mod", "OctaMED Soundstudio tracker module (MMD3)"], ["4d544d", 0, ".mtm", "audio/x-mod", "Multi Track Module tracker audio"], ["6966", 0, ".669", "audio/x-mod", "669 Tracker Module audio"], ["4a4e", 0, ".669", "audio/x-mod", "Extended 669 Tracker Module audio"], ["4d41535f55547261636b5f5630", 0, ".ult", "audio/x-mod", "UltraTracker audio"], ["54485800", 0, ".ahx", "", "AHX-Module (formerly THX Music Tracker) audio (AHX0)"], ["54485801", 0, ".ahx", "", "AHX-Module (formerly THX Music Tracker) audio (AHX1)"], ["594D5354", 0, ".myst", "", "MYST YM audio"], ["48e7f1fe6100", 0, ".dw", "", "<PERSON> audio"], ["562E32", 26, ".bp", "", "<PERSON> audio (V.2)"], ["562E33", 26, ".bp", "", "<PERSON> audio (V.3)"], ["534F4E47", 60, ".sfx", "", "SoundFX tracker audio"], ["534f3331", 124, ".sfx2", "", "SoundFX 2.0 tracker audio"], ["ffffe002e102", 0, ".pn", "", "PokeyNoise Chiptune audio"], ["585041434b", 0, ".xpack", "", "XPACK compressed file"], ["4d2e4b2e", 1080, ".mod", "", "Soundtracker (and clones) module"], ["464c5434", 1080, ".mod", "", "StarTrekker 4 Channel module"], ["464c5438", 1080, ".mod", "", "StarTrekker 8 Channel module"], ["5354312e", 0, ".nt", "", "StarTrekker FM Synth Pattern file"], ["4f435441", 1080, ".mod", "", "OctaComposer module"], ["3243484e", 1080, ".mod", "", "Fast Tracker 2 Channel module"], ["3443484e", 1080, ".mod", "", "Fast Tracker 4 Channel module"], ["3643484e", 1080, ".mod", "", "Fast Tracker 6 Channel module"], ["3843484e", 1080, ".mod", "", "Fast Tracker 8 Channel module"], ["31304348", 1080, ".mod", "", "Fast Tracker 10 Channel module"], ["31324348", 1080, ".mod", "", "Fast Tracker 12 Channel module"], ["31344348", 1080, ".mod", "", "Fast Tracker 14 Channel module"], ["31364348", 1080, ".mod", "", "Fast Tracker 16 Channel module"], ["31384348", 1080, ".mod", "", "Fast Tracker 18 Channel module"], ["32304348", 1080, ".mod", "", "Fast Tracker 20 Channel module"], ["32324348", 1080, ".mod", "", "Fast Tracker 22 Channel module"], ["32344348", 1080, ".mod", "", "Fast Tracker 24 Channel module"], ["32364348", 1080, ".mod", "", "Fast Tracker 26 Channel module"], ["32384348", 1080, ".mod", "", "Fast Tracker 28 Channel module"], ["33304348", 1080, ".mod", "", "Fast Tracker 30 Channel module"], ["33324348", 1080, ".mod", "", "Fast Tracker 32 Channel module"], ["43443831", 1080, ".mod", "", "Falcon 8 channel module"], ["54445a31", 1080, ".mod", "", "TakeTracker 1 channel module"], ["54445a32", 1080, ".mod", "", "TakeTracker 2 channel module"], ["54445A33", 1080, ".mod", "", "TakeTracker 3 channel module"], ["3543484E", 1080, ".mod", "", "TakeTracker 5 channel module"], ["3743484E", 1080, ".mod", "", "TakeTracker 7 channel module"], ["3943484E", 1080, ".mod", "", "TakeTracker 9 channel module"], ["31314348", 1080, ".mod", "", "TakeTracker 11 channel module"], ["31334348", 1080, ".mod", "", "TakeTracker 13 channel module"], ["31354348", 1080, ".mod", "", "TakeTracker 15 channel module"], ["4D264B21", 1080, ".mod", "", "fleg's module train-er module"], ["4d214b21", 1080, ".mod", "", "ProTracker 2.3 >64 Patterns module"], ["4e2e542e", 1080, ".mod", "", "NoiseTracker module module"], ["5041434b", 0, ".mod", "", "SoundTracker compressed song data"], ["534d4f44", 0, ".fc", "", "Future Composer module (1.0-1.3)"], ["46433134", 0, ".fc", "", "Future Composer module (1.4)"], ["414E20434F4F4C2E", 0, ".mod", "", "TCB Tracker module"], ["414E20434F4F4C21", 0, ".mod", "", "TCB Tracker module"], ["53464844", 0, ".pmc", "", "PowerplayerMusic Cruncher file (1.0)"], ["53464344", 0, ".pmc", "", "PowerplayerMusic Cruncher file (2.0)"], ["4d544e", 1464, ".mod", "", "SoundTracker 2.6 module"], ["4f4b5441534f4e47", 0, ".okt", "", "Oktalyzer tracker module (IFF Style)"], ["504b0304", 0, ".apk", "", "Android Application Package"], ["0000000c4a5853200d0a870a", 0, ".jxs", "image/jxs", "JPEG XS image"], ["ff10ff50", 0, ".jxsc", "image/jxsc", "JPEG XS codestream"], ["ff10ff50", 0, ".jxsv", "video/jxsv", "JPEG XS video"], ["23646566696e6520", 0, ".xbm", "image/x-xbitmap", "X BitMap image"], ["716f6966", 0, ".qoi", "", "Quite OK image"], ["716f6166", 0, ".qoa", "", "Quite OK audio"], ["464f524d", 0, ".sc2", "", "SimCity 2000 Map File"], ["5a585461706521", 0, ".tzx", "", "TZX Cassette Tape File"], ["5046", 0, ".pfm", "", "Portable Float Map (Colour)"], ["5066", 0, ".pfm", "", "Portable Float Map (Greyscale)"], ["504634", 0, ".pfm", "", "Augmented Portable Float Map"], ["5037", 0, ".pam", "image/x-portable-arbitrarymap", "Portable Arbitrary Map"], ["46535344", 65, ".hcom", "", "HCOM Audio File (FSSD Header)"], ["48434f4d", 128, ".hcom", "", "HCOM Audio File"], ["534f554e44", 0, ".sndt", "", "SoundTool/SNDTOOL Audio File"], ["3030", 0, ".sndr", "", "Sounder Audio File"], ["7668647866696c65", 0, ".vhdx", "", "Virtual PC HD image (VHDX)"], ["514649fb00000001", 0, ".qcow", "", "QEMU HD Image"], ["514649fb00000002", 0, ".qcow2", "", "QEMU HD Image (QCOW2)"], ["514649fb00000003", 0, ".qcow2", "", "QEMU HD Image (QCOW3)"], ["514544", 0, ".qed", "", "QEMU Enhanced Disk Image"], ["4c554b53babe0001", 0, ".luks", "", "Linux Unified Key Setup Image (LUKS1)"], ["4c554b53babe0002", 0, ".luks", "", "Linux Unified Key Setup Image (LUKS2)"], ["3c3c3c2053756e2078564d205669727475616c426f78204469736b20496d616765203e3e3e", 0, ".vdi", "", "VirtualBox Disk Image (SUN)"], ["3c3c3c204f7261636c6520564d205669727475616c426f78204469736b20496d616765203e3e3e", 0, ".vdi", "", "VirtualBox Disk Image (ORACLE)"], ["73696262", 0, ".uif", "", "MagicISO Disk Image"], ["444141", 0, ".daa", "", "PowerISO Direct Access Archive"], ["474249", 0, ".gbi", "", "gBurner Disk Image"], ["5354414b", 4, ".hc", "", "Apple HyperCard Stack"], ["49735a21", 0, ".isz", "", "Compressed ISO image"], ["5b436c6f6e6543445d", 0, ".ccd", "", "CloneCD Control File"], ["ffffffffffffffffffffffff", 0, ".sub", "", "CloneCD Sub Channel File"], ["00ffffffffffffffffffff", 0, ".img", "", "CloneCD Image File"], ["f7fff9fffdfffbfff6fff7fff7fff5fff8fff7fff5fff0fffcfffafffafff7fff8fff6fff7fff7fff2fff2fff8", 0, ".img", "", "CloneCD Image File"], ["ffffffffffffffffffffffff", 0, ".b5i", "", "BlindWrite 5 Image File"], ["425754352053545245414d205349474e", 0, ".b5t", "", "BlindWrite 5 Stream File"], ["425754352053545245414d205349474e", 0, ".b6t", "", "BlindWrite 6 Stream File"], ["4164617074656320436551756164726174205669727475616c43442046696c65", 0, ".c2d", "", "WinOnCD Image file (Adaptec version)"], ["526f78696f20496d6167652046696c6520466f726d617420332e30", 0, ".c2d", "", "WinOnCD Image file (Roxio version)"], ["4d454449412044455343524950544f5201", 0, ".mds", "", "Alcohol 120% Image Data File"], ["4d454449412044455343524950544f5201", 0, ".xmd", "", "GameJack Image Data File"], ["4d454449412044455343524950544f5202", 0, ".mdx", "", "Daemon Tools image file"], ["45520200", 0, ".toast", "", "Apple CD Image File"], ["504b0304", 0, ".cbz", "application/vnd.comicbook+zip", "Comic Book Archive (ZIP compression)"], ["377abcaf271c", 0, ".cb7", "", "Comic Book Archive (7-Zip compression)"], ["7573746172", 257, ".cbt", "", "Comic Book Archive (TAR compression)"], ["2a2a4143452a2a", 7, ".cba", "", "Comic Book Archive (ACE compression)"], ["526172211a0700", 0, ".cbr", "application/vnd.comicbook-rar", "Comic Book Archive (RAR compression)"], ["424f4f4b4d4f4249", 60, ".mobi", "application/x-mobipocket-ebook", "Mobipocket eBook file"], ["5445587452454164", 60, ".prc", "application/vnd.palm", "PalmDOC eBook file"], ["424f4f4b4d4f4249", 60, ".azw", "application/vnd.amazon.mobi8-ebook", "Amazon Kindle eBook file"], ["424f4f4b4d4f4249", 60, ".azw3", "application/vnd.amazon.mobi8-ebook", "Amazon Kindle Format 8 eBook file (KF8 MOBI Format)"], ["ea44524d494f4eeee00100eaee9e8183de9a86be97de95848d50726f74656374656444617461", 0, ".azw4", "application/vnd.amazon.mobi8-ebook", "Amazon Print Replica ebook file (KFX/KF10)"], ["ea44524d494f4eeee00100eaee9e8183de9a86be97de95848d50726f74656374656444617461", 0, ".kfx", "application/vnd.amazon.mobi8-ebook", "Amazon Print Replica ebook file (KFX/KF10)"], ["e00100eaee9e8183de9a86be97de95848d50726f74656374656444617461", 0, ".voucher", "", "Amazon Print Replica ebook DRM file"], ["7b226d6435223a22", 0, ".voucher", "", "Amazon Print Replica ebook metadata file"], ["3c3f786d6c2076657273696f6e3d22312e302220656e636f64696e673d225554462d3822207374616e64616c6f6e653d22796573223f3e", 0, ".phl", "", "Amazon Kindle Popular Highlights file"], ["434f4e540200", 0, ".azw.res", "", "Amazon Kindle Resource Container file"], ["434f4e540200", 0, ".azw.md", "", "Amazon Kindle Metadata Container file"], ["2e70646641444245", 60, ".pdb", "application/vnd.palm", "PalmDB Adobe Reader file"], ["42566f6b42444943", 60, ".pdb", "application/vnd.palm", "PalmDB BDicty Dictionary Reader file"], ["4442393944424f53", 60, ".pdb", "application/vnd.palm", "PalmDB DB file"], ["504e526450507273", 60, ".pdb", "application/vnd.palm", "PalmDB eReader / Palm Reader file"], ["4461746150507273", 60, ".pdb", "application/vnd.palm", "PalmDB eReader file"], ["76494d4756696577", 60, ".pdb", "application/vnd.palm", "PalmDB FireViewer/ImageViewer file"], ["506d4442506d4442", 60, ".pdb", "application/vnd.palm", "PalmDB HanDBase file"], ["496e666f494e4442", 60, ".pdb", "application/vnd.palm", "PalmDB InfoView file"], ["546f476f546f476f", 60, ".pdb", "application/vnd.palm", "PalmDB iSilo file"], ["53446f6353696c58", 60, ".pdb", "application/vnd.palm", "PalmDB iSilo 3 file"], ["4a6244624a426173", 60, ".pdb", "application/vnd.palm", "PalmDB JFile file"], ["4a6644624a46696c", 60, ".pdb", "application/vnd.palm", "PalmDB JFile Pro file"], ["444154414c536462", 60, ".pdb", "application/vnd.palm", "PalmDB LIST file"], ["4d6f62696c654442", 60, ".pdb", "application/vnd.palm", "PalmDB MobileDB file"], ["44617461506c6b72", 60, ".pdb", "application/vnd.palm", "PalmDB Plucker file"], ["4461746153707264", 60, ".pdb", "application/vnd.palm", "PalmDB QuickSheet file"], ["534d3031534d656d", 60, ".pdb", "application/vnd.palm", "PalmDB SuperMemo file"], ["54455874546c4463", 60, ".pdb", "application/vnd.palm", "PalmDB TealDoc file"], ["496e666f546c4966", 60, ".pdb", "application/vnd.palm", "PalmDB TealInfo file"], ["44617461546c4d6c", 60, ".pdb", "application/vnd.palm", "PalmDB TealMeal file"], ["44617461546c5074", 60, ".pdb", "application/vnd.palm", "PalmDB TealPaint file"], ["6461746154444250", 60, ".pdb", "application/vnd.palm", "PalmDB ThinkDB file"], ["5464617454696465", 60, ".pdb", "application/vnd.palm", "PalmDB Tides file"], ["546f526154525057", 60, ".pdb", "application/vnd.palm", "PalmDB TomeRaider file"], ["7a54585447506c6d", 60, ".pdb", "application/vnd.palm", "PalmDB TealInfo file"], ["42444f4357726453", 60, ".pdb", "application/vnd.palm", "PalmDB WordSmith file"], ["70716120636c7072", 60, ".pdb", "application/vnd.palm", "PalmDB Palm Query Application file"], ["444154414772503f", 60, ".pdb", "application/vnd.palm", "PalmDB GrayPaint file"], ["6170706c", 60, ".pdb", "application/vnd.palm", "PalmDB Palm Application file"], ["7a545854", 60, ".pdb", "application/vnd.palm", "PalmDB zTXT compressed file"], ["370000106d000010d2160010dcf4ddfcd1", 0, ".tr", "", "TomeRaider2 eBook file"], ["370000106d000010d2160010dcf4ddfcd1", 0, ".tr2", "", "TomeRaider2 eBook file"], ["5452334454523343", 60, ".tr3", "", "TomeRaider3 eBook file"], ["3c3f786d6c2076657273696f6e3d22312e302220656e636f64696e673d225554462d38223f3e0a3c46696374696f6e426f6f6b", 0, ".fb2", "application/fictionbook2+zip", "FictionBook 2.0 eBook file"], ["504b0304", 0, ".fb2.zip", "application/fictionbook2+zip", "FictionBook 2 eBook file (Zip compressed)"], ["504b0304", 0, ".fbz", "application/fictionbook2+zip", "FictionBook 2 eBook file (Zip compressed)"], ["504b0304", 0, ".fb3", "application/fictionbook3+zip", "FictionBook 3 eBook file"], ["3a42617365", 0, ".cnt", "", "Windows Help CoNTent file "], ["4c00520046000000", 0, ".lrf", "application/x-ext-lrf", "Sony Broad Band (BBeB) eBook file"], ["2121382d4269742121", 0, ".tcr", "", "Sony Broad Band (BBeB) eBook file"], ["b00cb00c", 0, ".rb", "application/x-rocketbook", "Rocket eBook file"], ["b00cc0de", 0, ".rb", "", "Rocket eBook system file"], ["b00cf00d", 0, ".rb", "", "Rocket eBook system file"], ["534e425030303042", 0, ".snb", "application/x-snb-ebook", "Shanda Bambook eBook file"], ["3c3f786d6c2076657273696f6e3d22312e302220656e636f64696e673d227574662d38223f3e0d0a3c43686561745461626c65", 0, ".CETRAINER", "", "Cheat Engine Trainer file"], ["1d000000", 0, ".bsp", "", "Quake level file"], ["1c000000", 0, ".bsp", "", "Quake model file"], ["4944504f", 0, ".mdl", "", "Quake model file"], ["7b0a22", 0, ".map", "", "Quake map file"], ["49445350", 0, ".spr", "", "Quake sprite file"], ["28", 0, ".pickle", "", "Python Pickle file (Protocol 0)"], ["7d71", 0, ".pickle", "", "Python Pickle file (Protocol 1)"], ["8002", 0, ".pickle", "", "Python Pickle file (Protocol 2)"], ["8003", 0, ".pickle", "", "Python Pickle file (Protocol 3)"], ["8004", 0, ".pickle", "", "Python Pickle file (Protocol 4)"], ["8005", 0, ".pickle", "", "Python Pickle file (Protocol 5)"], ["534d4b32", 0, ".smk", "video/vnd.radgamettools.smacker", "Smacker video file (Early format)"], ["534d4b34", 0, ".smk", "video/vnd.radgamettools.smacker", "Smacker video file (Later format)"], ["42494b", 0, ".bik", "video/vnd.radgametools.bink", "Bink video file"], ["40646174616261736520616d69676167756964652e6775696465", 0, ".guide", "", "AmigaGuide document file"], ["43524944", 0, ".usm", "", "CRI Movie 2 file"], ["20540200000005540200", 0, ".wdb", "application/vnd.ms-works", "Microsoft Works Database file (Early version)"], ["d0cf11e0a1b11ae1", 0, ".wdb", "application/vnd.ms-works", "Microsoft Works Database file (Later version)"], ["d0cf11e0a1b11ae1", 0, ".xlr", "application/vnd.ms-works", "Microsoft Works spreadsheet (Version 6+)"], ["01fe", 0, ".wps", "application/vnd.ms-works", "Microsoft Works Word document (Version 1-2)"], ["4949bc01", 0, ".jxr", "image/jxr", "JPEG XR image"], ["4949bc01", 0, ".wdp", "image/vnd.ms-photo", "JPEG XR image"], ["ffd8fff7", 0, ".jls", "image/jls", "JPEG-LS image"], ["5244534b", 0, ".hdf", "", "Amiga Harddisk image"], ["504653", 0, ".hdf", "", "Amiga Harddisk image (Professional Filesystem 3)"], ["504453", 0, ".hdf", "", "Amiga Harddisk image (Professional Filesystem 3)"], ["534653", 0, ".hdf", "", "Amiga Harddisk image (Smart File System)"]]}