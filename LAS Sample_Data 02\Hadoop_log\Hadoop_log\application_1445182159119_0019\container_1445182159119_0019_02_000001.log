2015-10-19 17:53:03,191 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445182159119_0019_000002
2015-10-19 17:53:03,551 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-19 17:53:03,551 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 19 cluster_timestamp: 1445182159119 } attemptId: 2 } keyId: 1694045684)
2015-10-19 17:53:03,816 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-19 17:53:05,269 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-19 17:53:05,379 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-19 17:53:05,426 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-19 17:53:05,441 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-19 17:53:05,441 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-19 17:53:05,441 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-19 17:53:05,441 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-19 17:53:05,457 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-19 17:53:05,457 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-19 17:53:05,457 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-19 17:53:05,551 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 17:53:05,597 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 17:53:05,644 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 17:53:05,660 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-19 17:53:05,660 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Recovery is enabled. Will try to recover from previous life on best effort basis.
2015-10-19 17:53:05,707 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 17:53:05,707 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Previous history file is at hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0019/job_1445182159119_0019_1.jhist
2015-10-19 17:53:05,738 WARN [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Unable to parse prior job history, aborting recovery
java.io.IOException: Incompatible event log version: null
	at org.apache.hadoop.mapreduce.jobhistory.EventReader.<init>(EventReader.java:71)
	at org.apache.hadoop.mapreduce.jobhistory.JobHistoryParser.parse(JobHistoryParser.java:139)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.parsePreviousJobHistory(MRAppMaster.java:1183)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.processRecovery(MRAppMaster.java:1152)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.serviceStart(MRAppMaster.java:1016)
	at org.apache.hadoop.service.AbstractService.start(AbstractService.java:193)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster$4.run(MRAppMaster.java:1500)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:422)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.initAndStartAppMaster(MRAppMaster.java:1496)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.main(MRAppMaster.java:1429)
2015-10-19 17:53:05,801 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 17:53:05,801 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Previous history file is at hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0019/job_1445182159119_0019_1.jhist
2015-10-19 17:53:05,801 WARN [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Could not parse the old history file. Will not have old AMinfos 
java.io.IOException: Incompatible event log version: null
	at org.apache.hadoop.mapreduce.jobhistory.EventReader.<init>(EventReader.java:71)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.readJustAMInfos(MRAppMaster.java:1229)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.processRecovery(MRAppMaster.java:1156)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.serviceStart(MRAppMaster.java:1016)
	at org.apache.hadoop.service.AbstractService.start(AbstractService.java:193)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster$4.run(MRAppMaster.java:1500)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:422)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.initAndStartAppMaster(MRAppMaster.java:1496)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.main(MRAppMaster.java:1429)
2015-10-19 17:53:05,879 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-19 17:53:06,316 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 17:53:06,441 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 17:53:06,441 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-19 17:53:06,457 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445182159119_0019 to jobTokenSecretManager
2015-10-19 17:53:06,879 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445182159119_0019 because: not enabled; too many maps; too much input;
2015-10-19 17:53:06,910 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445182159119_0019 = 1256521728. Number of splits = 10
2015-10-19 17:53:06,910 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445182159119_0019 = 1
2015-10-19 17:53:06,910 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0019Job Transitioned from NEW to INITED
2015-10-19 17:53:06,926 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445182159119_0019.
2015-10-19 17:53:06,988 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 17:53:07,004 INFO [Socket Reader #1 for port 56172] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 56172
2015-10-19 17:53:07,051 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-19 17:53:07,051 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 17:53:07,051 INFO [IPC Server listener on 56172] org.apache.hadoop.ipc.Server: IPC Server listener on 56172: starting
2015-10-19 17:53:07,051 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MSRA-SA-39.fareast.corp.microsoft.com/**************:56172
2015-10-19 17:53:07,176 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-19 17:53:07,191 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-19 17:53:07,207 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-19 17:53:07,223 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-19 17:53:07,223 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-19 17:53:07,223 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-19 17:53:07,223 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-19 17:53:07,238 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 56179
2015-10-19 17:53:07,238 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-19 17:53:07,316 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\2\Jetty_0_0_0_0_56179_mapreduce____.s1e00p\webapp
2015-10-19 17:53:07,629 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:56179
2015-10-19 17:53:07,629 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 56179
2015-10-19 17:53:08,191 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-19 17:53:08,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445182159119_0019
2015-10-19 17:53:08,207 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 17:53:08,207 INFO [Socket Reader #1 for port 56183] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 56183
2015-10-19 17:53:08,223 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 17:53:08,223 INFO [IPC Server listener on 56183] org.apache.hadoop.ipc.Server: IPC Server listener on 56183: starting
2015-10-19 17:53:08,254 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-19 17:53:08,254 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-19 17:53:08,254 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-19 17:53:08,316 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-19 17:53:08,426 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-19 17:53:08,426 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-19 17:53:08,426 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-19 17:53:08,426 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-19 17:53:08,441 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0019Job Transitioned from INITED to SETUP
2015-10-19 17:53:08,441 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-19 17:53:08,457 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0019Job Transitioned from SETUP to RUNNING
2015-10-19 17:53:08,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:08,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:08,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000000_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:08,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000001_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:08,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000002_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:08,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000003_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:08,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000004_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:08,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000005_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:08,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000006_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:08,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000007_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:08,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000008_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:08,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000009_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:08,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_r_000000_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:08,519 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-19 17:53:08,535 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-19 17:53:08,566 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445182159119_0019, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0019/job_1445182159119_0019_2.jhist
2015-10-19 17:53:09,426 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-19 17:53:09,488 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0019: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:14336, vCores:-13> knownNMs=4
2015-10-19 17:53:09,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:14336, vCores:-13>
2015-10-19 17:53:09,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:10,520 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 10
2015-10-19 17:53:10,520 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0019_02_000002 to attempt_1445182159119_0019_m_000000_1000
2015-10-19 17:53:10,535 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0019_02_000003 to attempt_1445182159119_0019_m_000001_1000
2015-10-19 17:53:10,535 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0019_02_000004 to attempt_1445182159119_0019_m_000002_1000
2015-10-19 17:53:10,535 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0019_02_000005 to attempt_1445182159119_0019_m_000003_1000
2015-10-19 17:53:10,535 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0019_02_000006 to attempt_1445182159119_0019_m_000004_1000
2015-10-19 17:53:10,535 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0019_02_000007 to attempt_1445182159119_0019_m_000005_1000
2015-10-19 17:53:10,535 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0019_02_000008 to attempt_1445182159119_0019_m_000006_1000
2015-10-19 17:53:10,535 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0019_02_000009 to attempt_1445182159119_0019_m_000007_1000
2015-10-19 17:53:10,535 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0019_02_000010 to attempt_1445182159119_0019_m_000008_1000
2015-10-19 17:53:10,535 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0019_02_000011 to attempt_1445182159119_0019_m_000009_1000
2015-10-19 17:53:10,535 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-24>
2015-10-19 17:53:10,535 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:10,535 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-19 17:53:10,613 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:10,645 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0019/job.jar
2015-10-19 17:53:10,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0019/job.xml
2015-10-19 17:53:10,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-19 17:53:10,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-19 17:53:10,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-19 17:53:10,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000000_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:53:10,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:10,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000001_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:53:10,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:10,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000002_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:53:10,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:10,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000003_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:53:10,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:10,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000004_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:53:10,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:10,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000005_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:53:10,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:10,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000006_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:53:10,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:10,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000007_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:53:10,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:10,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000008_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:53:10,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:10,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000009_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:53:10,723 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0019_02_000002 taskAttempt attempt_1445182159119_0019_m_000000_1000
2015-10-19 17:53:10,723 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0019_02_000003 taskAttempt attempt_1445182159119_0019_m_000001_1000
2015-10-19 17:53:10,723 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0019_02_000004 taskAttempt attempt_1445182159119_0019_m_000002_1000
2015-10-19 17:53:10,723 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0019_02_000005 taskAttempt attempt_1445182159119_0019_m_000003_1000
2015-10-19 17:53:10,723 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0019_02_000006 taskAttempt attempt_1445182159119_0019_m_000004_1000
2015-10-19 17:53:10,723 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0019_02_000007 taskAttempt attempt_1445182159119_0019_m_000005_1000
2015-10-19 17:53:10,738 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0019_02_000008 taskAttempt attempt_1445182159119_0019_m_000006_1000
2015-10-19 17:53:10,738 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0019_02_000009 taskAttempt attempt_1445182159119_0019_m_000007_1000
2015-10-19 17:53:10,738 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0019_02_000010 taskAttempt attempt_1445182159119_0019_m_000008_1000
2015-10-19 17:53:10,738 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0019_02_000011 taskAttempt attempt_1445182159119_0019_m_000009_1000
2015-10-19 17:53:10,738 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0019_m_000008_1000
2015-10-19 17:53:10,738 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0019_m_000001_1000
2015-10-19 17:53:10,738 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0019_m_000002_1000
2015-10-19 17:53:10,738 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0019_m_000003_1000
2015-10-19 17:53:10,738 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0019_m_000000_1000
2015-10-19 17:53:10,738 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0019_m_000006_1000
2015-10-19 17:53:10,738 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0019_m_000005_1000
2015-10-19 17:53:10,738 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0019_m_000009_1000
2015-10-19 17:53:10,738 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0019_m_000007_1000
2015-10-19 17:53:10,738 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0019_m_000004_1000
2015-10-19 17:53:10,738 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:53:10,785 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:53:10,785 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:53:10,785 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:53:10,785 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:53:10,785 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:53:10,785 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 17:53:10,785 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:53:10,785 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 17:53:10,785 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 17:53:10,895 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0019_m_000001_1000 : 13562
2015-10-19 17:53:10,895 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0019_m_000002_1000 : 13562
2015-10-19 17:53:10,895 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0019_m_000000_1000 : 13562
2015-10-19 17:53:10,895 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0019_m_000003_1000 : 13562
2015-10-19 17:53:10,895 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0019_m_000005_1000 : 13562
2015-10-19 17:53:10,895 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0019_m_000003_1000] using containerId: [container_1445182159119_0019_02_000005 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:53:10,895 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0019_m_000008_1000 : 13562
2015-10-19 17:53:10,895 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0019_m_000007_1000 : 13562
2015-10-19 17:53:10,895 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000003_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:53:10,895 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0019_m_000001_1000] using containerId: [container_1445182159119_0019_02_000003 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 17:53:10,895 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0019_m_000006_1000 : 13562
2015-10-19 17:53:10,895 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000001_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0019_m_000000_1000] using containerId: [container_1445182159119_0019_02_000002 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000000_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:53:10,910 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0019_m_000009_1000 : 13562
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0019_m_000002_1000] using containerId: [container_1445182159119_0019_02_000004 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000002_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0019_m_000005_1000] using containerId: [container_1445182159119_0019_02_000007 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000005_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0019_m_000008_1000] using containerId: [container_1445182159119_0019_02_000010 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000008_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0019_m_000007_1000] using containerId: [container_1445182159119_0019_02_000009 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000007_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0019_m_000003
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0019_m_000006_1000] using containerId: [container_1445182159119_0019_02_000008 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000006_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0019_m_000001
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0019_m_000000
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0019_m_000009_1000] using containerId: [container_1445182159119_0019_02_000011 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:53:10,910 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0019_m_000004_1000 : 13562
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000009_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0019_m_000002
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0019_m_000005
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0019_m_000008
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0019_m_000007
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0019_m_000006
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0019_m_000009
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0019_m_000004_1000] using containerId: [container_1445182159119_0019_02_000006 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000004_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0019_m_000004
2015-10-19 17:53:10,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:53:11,542 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0019: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:3072, vCores:-24> knownNMs=4
2015-10-19 17:53:14,308 INFO [Socket Reader #1 for port 56183] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0019 (auth:SIMPLE)
2015-10-19 17:53:14,355 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0019_m_000004 asked for a task
2015-10-19 17:53:14,355 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0019_m_000004 given task: attempt_1445182159119_0019_m_000002_1000
2015-10-19 17:53:14,355 INFO [Socket Reader #1 for port 56183] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0019 (auth:SIMPLE)
2015-10-19 17:53:14,355 INFO [Socket Reader #1 for port 56183] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0019 (auth:SIMPLE)
2015-10-19 17:53:14,386 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0019_m_000002 asked for a task
2015-10-19 17:53:14,386 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0019_m_000002 given task: attempt_1445182159119_0019_m_000000_1000
2015-10-19 17:53:14,386 INFO [IPC Server handler 26 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0019_m_000003 asked for a task
2015-10-19 17:53:14,386 INFO [IPC Server handler 26 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0019_m_000003 given task: attempt_1445182159119_0019_m_000001_1000
2015-10-19 17:53:15,230 INFO [Socket Reader #1 for port 56183] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0019 (auth:SIMPLE)
2015-10-19 17:53:15,261 INFO [Socket Reader #1 for port 56183] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0019 (auth:SIMPLE)
2015-10-19 17:53:15,261 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0019_m_000005 asked for a task
2015-10-19 17:53:15,261 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0019_m_000005 given task: attempt_1445182159119_0019_m_000003_1000
2015-10-19 17:53:15,277 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0019_m_000010 asked for a task
2015-10-19 17:53:15,277 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0019_m_000010 given task: attempt_1445182159119_0019_m_000008_1000
2015-10-19 17:53:15,292 INFO [Socket Reader #1 for port 56183] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0019 (auth:SIMPLE)
2015-10-19 17:53:15,324 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0019_m_000009 asked for a task
2015-10-19 17:53:15,324 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0019_m_000009 given task: attempt_1445182159119_0019_m_000007_1000
2015-10-19 17:53:15,558 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-23>
2015-10-19 17:53:15,558 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:15,808 INFO [Socket Reader #1 for port 56183] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0019 (auth:SIMPLE)
2015-10-19 17:53:15,824 INFO [Socket Reader #1 for port 56183] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0019 (auth:SIMPLE)
2015-10-19 17:53:15,839 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0019_m_000006 asked for a task
2015-10-19 17:53:15,839 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0019_m_000006 given task: attempt_1445182159119_0019_m_000004_1000
2015-10-19 17:53:15,839 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0019_m_000011 asked for a task
2015-10-19 17:53:15,839 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0019_m_000011 given task: attempt_1445182159119_0019_m_000009_1000
2015-10-19 17:53:15,855 INFO [Socket Reader #1 for port 56183] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0019 (auth:SIMPLE)
2015-10-19 17:53:15,871 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0019_m_000007 asked for a task
2015-10-19 17:53:15,871 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0019_m_000007 given task: attempt_1445182159119_0019_m_000005_1000
2015-10-19 17:53:15,886 INFO [Socket Reader #1 for port 56183] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0019 (auth:SIMPLE)
2015-10-19 17:53:15,902 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0019_m_000008 asked for a task
2015-10-19 17:53:15,902 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0019_m_000008 given task: attempt_1445182159119_0019_m_000006_1000
2015-10-19 17:53:20,558 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-24>
2015-10-19 17:53:20,558 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:21,902 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.10394478
2015-10-19 17:53:21,949 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.10635664
2015-10-19 17:53:21,949 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.1066108
2015-10-19 17:53:22,558 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 17:53:22,558 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:23,215 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000009_1000 is : 0.295472
2015-10-19 17:53:23,230 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.10680563
2015-10-19 17:53:23,293 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.10685723
2015-10-19 17:53:23,418 INFO [IPC Server handler 20 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.106964506
2015-10-19 17:53:23,621 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.106881365
2015-10-19 17:53:23,621 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.106493875
2015-10-19 17:53:23,637 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.10681946
2015-10-19 17:53:24,918 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.10660437
2015-10-19 17:53:24,949 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.10635664
2015-10-19 17:53:24,965 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.1066108
2015-10-19 17:53:26,230 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000009_1000 is : 0.295472
2015-10-19 17:53:26,262 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.10680563
2015-10-19 17:53:26,309 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.10685723
2015-10-19 17:53:26,449 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.106964506
2015-10-19 17:53:26,637 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.106881365
2015-10-19 17:53:26,637 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.106493875
2015-10-19 17:53:26,652 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.10681946
2015-10-19 17:53:27,559 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-19 17:53:27,559 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:27,918 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.10660437
2015-10-19 17:53:27,965 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.10635664
2015-10-19 17:53:27,965 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.1066108
2015-10-19 17:53:29,262 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000009_1000 is : 0.295472
2015-10-19 17:53:29,277 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.10680563
2015-10-19 17:53:29,340 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.10685723
2015-10-19 17:53:29,465 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.106964506
2015-10-19 17:53:29,668 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.106881365
2015-10-19 17:53:29,668 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.106493875
2015-10-19 17:53:29,684 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.10681946
2015-10-19 17:53:30,949 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.10660437
2015-10-19 17:53:30,981 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.108781725
2015-10-19 17:53:30,981 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.10635664
2015-10-19 17:53:31,559 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:31,559 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:32,309 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000009_1000 is : 0.29965714
2015-10-19 17:53:32,324 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.13710664
2015-10-19 17:53:32,387 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.11593631
2015-10-19 17:53:32,512 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.14160493
2015-10-19 17:53:32,699 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.13726272
2015-10-19 17:53:32,699 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.14980216
2015-10-19 17:53:32,715 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.1417813
2015-10-19 17:53:33,559 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-19 17:53:33,559 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:33,965 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.124968685
2015-10-19 17:53:33,996 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.18389814
2015-10-19 17:53:33,996 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.17777202
2015-10-19 17:53:35,340 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000009_1000 is : 0.524377
2015-10-19 17:53:35,340 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.19242907
2015-10-19 17:53:35,403 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.1902514
2015-10-19 17:53:35,528 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.19266446
2015-10-19 17:53:35,715 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.19209063
2015-10-19 17:53:35,731 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.19258286
2015-10-19 17:53:35,746 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.19255035
2015-10-19 17:53:36,965 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.19212553
2015-10-19 17:53:36,996 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.19211523
2015-10-19 17:53:37,012 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.19158794
2015-10-19 17:53:37,559 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:37,559 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:38,356 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000009_1000 is : 0.5323719
2015-10-19 17:53:38,372 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.19242907
2015-10-19 17:53:38,434 INFO [IPC Server handler 25 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.19247705
2015-10-19 17:53:38,559 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.19266446
2015-10-19 17:53:38,747 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.19209063
2015-10-19 17:53:38,778 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.19258286
2015-10-19 17:53:38,778 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.19255035
2015-10-19 17:53:39,982 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.19212553
2015-10-19 17:53:39,998 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.19211523
2015-10-19 17:53:40,029 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.19158794
2015-10-19 17:53:41,388 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000009_1000 is : 0.5323719
2015-10-19 17:53:41,388 INFO [IPC Server handler 25 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.19242907
2015-10-19 17:53:41,451 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.19247705
2015-10-19 17:53:41,576 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.19266446
2015-10-19 17:53:41,763 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.19209063
2015-10-19 17:53:41,795 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.19258286
2015-10-19 17:53:41,795 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.19255035
2015-10-19 17:53:42,998 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.19212553
2015-10-19 17:53:43,013 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.21517316
2015-10-19 17:53:43,045 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.1948611
2015-10-19 17:53:44,404 INFO [IPC Server handler 25 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000009_1000 is : 0.5323719
2015-10-19 17:53:44,404 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.19242907
2015-10-19 17:53:44,467 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.20372403
2015-10-19 17:53:44,592 INFO [IPC Server handler 10 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.22723113
2015-10-19 17:53:44,779 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.23740155
2015-10-19 17:53:44,810 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.24723715
2015-10-19 17:53:44,810 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.24502383
2015-10-19 17:53:45,998 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.19212553
2015-10-19 17:53:46,029 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.27776006
2015-10-19 17:53:46,029 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000009_1000 is : 0.5323719
2015-10-19 17:53:46,060 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.26858845
2015-10-19 17:53:47,420 INFO [IPC Server handler 25 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000009_1000 is : 0.667
2015-10-19 17:53:47,420 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.2781602
2015-10-19 17:53:47,498 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.27813601
2015-10-19 17:53:47,623 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.2783809
2015-10-19 17:53:47,810 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.27765483
2015-10-19 17:53:47,826 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.27825075
2015-10-19 17:53:47,842 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.27811313
2015-10-19 17:53:49,014 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.24445581
2015-10-19 17:53:49,029 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.27776006
2015-10-19 17:53:49,060 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.27696857
2015-10-19 17:53:50,436 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000009_1000 is : 0.667
2015-10-19 17:53:50,436 INFO [IPC Server handler 25 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.2781602
2015-10-19 17:53:50,514 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.27813601
2015-10-19 17:53:50,639 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.2783809
2015-10-19 17:53:50,826 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.27765483
2015-10-19 17:53:50,857 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.27825075
2015-10-19 17:53:50,857 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.27811313
2015-10-19 17:53:52,014 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.27772525
2015-10-19 17:53:52,029 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.27776006
2015-10-19 17:53:52,061 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.27696857
2015-10-19 17:53:53,467 INFO [IPC Server handler 25 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000009_1000 is : 0.67487156
2015-10-19 17:53:53,467 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.2781602
2015-10-19 17:53:53,529 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.27813601
2015-10-19 17:53:53,654 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.2783809
2015-10-19 17:53:53,858 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.27765483
2015-10-19 17:53:53,889 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.27825075
2015-10-19 17:53:53,889 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.27811313
2015-10-19 17:53:55,029 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.27772525
2015-10-19 17:53:55,045 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.3243128
2015-10-19 17:53:55,061 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.30291036
2015-10-19 17:53:56,483 INFO [IPC Server handler 25 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.29593822
2015-10-19 17:53:56,483 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000009_1000 is : 0.76102096
2015-10-19 17:53:56,561 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.35281968
2015-10-19 17:53:56,686 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.36206445
2015-10-19 17:53:56,873 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.3554983
2015-10-19 17:53:56,905 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.360263
2015-10-19 17:53:56,920 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.3585813
2015-10-19 17:53:58,030 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.27772525
2015-10-19 17:53:58,061 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.36319977
2015-10-19 17:53:58,061 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.3624012
2015-10-19 17:53:59,514 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000009_1000 is : 0.84687364
2015-10-19 17:53:59,514 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.36388028
2015-10-19 17:53:59,577 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.36390656
2015-10-19 17:53:59,702 INFO [IPC Server handler 16 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.36404583
2015-10-19 17:53:59,889 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.36323506
2015-10-19 17:53:59,920 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.3638923
2015-10-19 17:53:59,936 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.3637686
2015-10-19 17:54:01,045 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.27772525
2015-10-19 17:54:01,077 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.36319977
2015-10-19 17:54:01,077 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.3624012
2015-10-19 17:54:02,530 INFO [IPC Server handler 25 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000009_1000 is : 0.9459533
2015-10-19 17:54:02,530 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.36388028
2015-10-19 17:54:02,592 INFO [IPC Server handler 10 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.36390656
2015-10-19 17:54:02,717 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.36404583
2015-10-19 17:54:02,920 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.36323506
2015-10-19 17:54:02,952 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.3638923
2015-10-19 17:54:02,952 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.3637686
2015-10-19 17:54:03,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 17:54:03,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:04,077 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.33056664
2015-10-19 17:54:04,077 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.3624012
2015-10-19 17:54:04,092 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.36319977
2015-10-19 17:54:04,280 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000009_1000 is : 1.0
2015-10-19 17:54:04,295 INFO [IPC Server handler 25 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0019_m_000009_1000
2015-10-19 17:54:04,295 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000009_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:54:04,295 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0019_02_000011 taskAttempt attempt_1445182159119_0019_m_000009_1000
2015-10-19 17:54:04,295 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0019_m_000009_1000
2015-10-19 17:54:04,295 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:54:04,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000009_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:54:04,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0019_m_000009_1000
2015-10-19 17:54:04,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:54:04,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-19 17:54:04,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-19 17:54:04,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 17:54:04,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-19 17:54:04,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-19 17:54:04,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-19 17:54:04,920 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0019_m_000002
2015-10-19 17:54:04,920 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 17:54:04,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0019_m_000002
2015-10-19 17:54:04,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:54:04,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:54:04,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000002_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:54:05,546 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.36388028
2015-10-19 17:54:05,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-19 17:54:05,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0019: ask=5 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:2048, vCores:-25> knownNMs=4
2015-10-19 17:54:05,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0019_02_000011
2015-10-19 17:54:05,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-19 17:54:05,561 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0019_m_000009_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:54:05,608 INFO [IPC Server handler 16 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.36390656
2015-10-19 17:54:05,749 INFO [IPC Server handler 18 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.39102918
2015-10-19 17:54:05,936 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.41400805
2015-10-19 17:54:05,967 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.421291
2015-10-19 17:54:05,967 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.42233998
2015-10-19 17:54:06,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:54:06,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-19 17:54:06,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0019_02_000012 to attempt_1445182159119_0019_r_000000_1000
2015-10-19 17:54:06,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:10 RackLocal:0
2015-10-19 17:54:06,577 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:54:06,577 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_r_000000_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:54:06,577 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0019_02_000012 taskAttempt attempt_1445182159119_0019_r_000000_1000
2015-10-19 17:54:06,577 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0019_r_000000_1000
2015-10-19 17:54:06,577 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:54:06,983 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0019_r_000000_1000 : 13562
2015-10-19 17:54:06,983 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0019_r_000000_1000] using containerId: [container_1445182159119_0019_02_000012 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 17:54:06,983 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_r_000000_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:54:06,983 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0019_r_000000
2015-10-19 17:54:06,983 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:54:07,077 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.38659573
2015-10-19 17:54:07,077 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.36317363
2015-10-19 17:54:07,092 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.41347364
2015-10-19 17:54:07,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0019: ask=1 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 17:54:07,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:54:07,561 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:54:07,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0019_02_000013 to attempt_1445182159119_0019_m_000002_1001
2015-10-19 17:54:07,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-19 17:54:07,561 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:54:07,561 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000002_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:54:07,561 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0019_02_000013 taskAttempt attempt_1445182159119_0019_m_000002_1001
2015-10-19 17:54:07,561 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0019_m_000002_1001
2015-10-19 17:54:07,561 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:54:08,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0019: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 17:54:08,561 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.42679533
2015-10-19 17:54:08,639 INFO [IPC Server handler 18 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.44950968
2015-10-19 17:54:08,764 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.44980705
2015-10-19 17:54:08,874 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0019_m_000002_1001 : 13562
2015-10-19 17:54:08,874 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0019_m_000002_1001] using containerId: [container_1445182159119_0019_02_000013 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 17:54:08,874 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000002_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:54:08,874 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0019_m_000002
2015-10-19 17:54:08,952 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.4486067
2015-10-19 17:54:08,983 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.44964966
2015-10-19 17:54:08,983 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.44950172
2015-10-19 17:54:10,077 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.44789755
2015-10-19 17:54:10,093 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.448704
2015-10-19 17:54:10,093 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.36317363
2015-10-19 17:54:11,593 INFO [IPC Server handler 16 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.44968578
2015-10-19 17:54:11,655 INFO [IPC Server handler 18 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.44950968
2015-10-19 17:54:11,780 INFO [IPC Server handler 26 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.44980705
2015-10-19 17:54:11,968 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.4486067
2015-10-19 17:54:11,999 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.44964966
2015-10-19 17:54:11,999 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.44950172
2015-10-19 17:54:13,077 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.44789755
2015-10-19 17:54:13,093 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.36317363
2015-10-19 17:54:13,108 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.448704
2015-10-19 17:54:14,608 INFO [IPC Server handler 18 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.44968578
2015-10-19 17:54:14,671 INFO [IPC Server handler 10 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.44950968
2015-10-19 17:54:14,796 INFO [IPC Server handler 26 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.44980705
2015-10-19 17:54:14,999 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.4486067
2015-10-19 17:54:15,015 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.44950172
2015-10-19 17:54:15,030 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.44964966
2015-10-19 17:54:16,077 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.44789755
2015-10-19 17:54:16,108 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.40339205
2015-10-19 17:54:16,108 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.448704
2015-10-19 17:54:17,624 INFO [IPC Server handler 18 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.44968578
2015-10-19 17:54:17,702 INFO [IPC Server handler 26 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.4800264
2015-10-19 17:54:17,827 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.5043758
2015-10-19 17:54:18,015 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.5343203
2015-10-19 17:54:18,046 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.53521925
2015-10-19 17:54:18,046 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.5352825
2015-10-19 17:54:19,109 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.46541902
2015-10-19 17:54:19,109 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.50285363
2015-10-19 17:54:19,124 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.44859612
2015-10-19 17:54:19,937 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0019_m_000004
2015-10-19 17:54:19,937 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 17:54:19,937 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0019_m_000004
2015-10-19 17:54:19,937 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:54:19,937 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:54:19,937 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000004_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:54:20,562 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-19 17:54:20,562 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0019: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 17:54:20,655 INFO [IPC Server handler 10 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.52721065
2015-10-19 17:54:20,718 INFO [IPC Server handler 26 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.5352021
2015-10-19 17:54:20,843 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.53543663
2015-10-19 17:54:21,031 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.5343203
2015-10-19 17:54:21,062 INFO [IPC Server handler 20 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.5352825
2015-10-19 17:54:21,062 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.53521925
2015-10-19 17:54:22,109 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.53341997
2015-10-19 17:54:22,124 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.53425497
2015-10-19 17:54:22,124 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.44859612
2015-10-19 17:54:22,890 INFO [Thread-54] org.apache.hadoop.hdfs.DFSClient: Exception in createBlockOutputStream
java.io.IOException: Bad connect ack with firstBadLink as ************:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.createBlockOutputStream(DFSOutputStream.java:1461)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.nextBlockOutputStream(DFSOutputStream.java:1362)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.run(DFSOutputStream.java:589)
2015-10-19 17:54:22,890 INFO [Thread-54] org.apache.hadoop.hdfs.DFSClient: Abandoning BP-1347369012-**************-1444972147527:blk_1073744061_3286
2015-10-19 17:54:22,890 INFO [Thread-54] org.apache.hadoop.hdfs.DFSClient: Excluding datanode ************:50010
2015-10-19 17:54:23,671 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.5352028
2015-10-19 17:54:23,734 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.5352021
2015-10-19 17:54:23,859 INFO [IPC Server handler 28 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.53543663
2015-10-19 17:54:24,046 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.5343203
2015-10-19 17:54:24,078 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.5352825
2015-10-19 17:54:24,093 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.53521925
2015-10-19 17:54:25,109 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.53341997
2015-10-19 17:54:25,124 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.44859612
2015-10-19 17:54:25,140 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.53425497
2015-10-19 17:54:26,704 INFO [IPC Server handler 18 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.5352028
2015-10-19 17:54:26,766 INFO [IPC Server handler 28 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.5352021
2015-10-19 17:54:26,891 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.53543663
2015-10-19 17:54:27,079 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.61072564
2015-10-19 17:54:27,110 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.5965759
2015-10-19 17:54:27,110 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.6126403
2015-10-19 17:54:27,579 INFO [Socket Reader #1 for port 56183] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0019 (auth:SIMPLE)
2015-10-19 17:54:27,782 INFO [Socket Reader #1 for port 56183] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0019 (auth:SIMPLE)
2015-10-19 17:54:27,797 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0019_r_000012 asked for a task
2015-10-19 17:54:27,813 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0019_r_000012 given task: attempt_1445182159119_0019_r_000000_1000
2015-10-19 17:54:28,047 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0019_m_000013 asked for a task
2015-10-19 17:54:28,047 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0019_m_000013 given task: attempt_1445182159119_0019_m_000002_1001
2015-10-19 17:54:28,110 INFO [IPC Server handler 1 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.53341997
2015-10-19 17:54:28,126 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.44859612
2015-10-19 17:54:28,141 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.53425497
2015-10-19 17:54:29,719 INFO [IPC Server handler 18 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.5628982
2015-10-19 17:54:29,782 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.6209487
2015-10-19 17:54:29,907 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.6210422
2015-10-19 17:54:30,094 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.6199081
2015-10-19 17:54:30,126 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.620844
2015-10-19 17:54:30,126 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.6207798
2015-10-19 17:54:31,126 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.5522524
2015-10-19 17:54:31,157 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.5095292
2015-10-19 17:54:31,157 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.6017114
2015-10-19 17:54:31,563 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:54:31,563 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:54:31,563 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0019_02_000014 to attempt_1445182159119_0019_m_000004_1001
2015-10-19 17:54:31,563 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-19 17:54:31,563 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:54:31,563 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000004_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:54:31,563 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0019_02_000014 taskAttempt attempt_1445182159119_0019_m_000004_1001
2015-10-19 17:54:31,563 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0019_m_000004_1001
2015-10-19 17:54:31,563 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:54:32,454 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0019_m_000004_1001 : 13562
2015-10-19 17:54:32,454 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0019_m_000004_1001] using containerId: [container_1445182159119_0019_02_000014 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 17:54:32,454 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000004_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:54:32,454 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0019_m_000004
2015-10-19 17:54:32,563 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0019: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 17:54:32,751 INFO [IPC Server handler 26 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.6208445
2015-10-19 17:54:32,813 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.6209487
2015-10-19 17:54:32,938 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.6210422
2015-10-19 17:54:33,126 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.6199081
2015-10-19 17:54:33,157 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.620844
2015-10-19 17:54:33,173 INFO [IPC Server handler 1 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.6207798
2015-10-19 17:54:34,126 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.61898744
2015-10-19 17:54:34,157 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.6197233
2015-10-19 17:54:34,157 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.5342037
2015-10-19 17:54:34,204 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 0 maxEvents 10000
2015-10-19 17:54:35,235 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:35,767 INFO [IPC Server handler 16 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.6208445
2015-10-19 17:54:35,845 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.6209487
2015-10-19 17:54:35,970 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.6210422
2015-10-19 17:54:36,079 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.6199081
2015-10-19 17:54:36,157 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.667
2015-10-19 17:54:36,173 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.6452596
2015-10-19 17:54:36,204 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.66537446
2015-10-19 17:54:36,235 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.66537446
2015-10-19 17:54:36,329 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:36,892 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.6452596
2015-10-19 17:54:37,126 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.61898744
2015-10-19 17:54:37,157 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.5342037
2015-10-19 17:54:37,173 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.6197233
2015-10-19 17:54:37,407 INFO [IPC Server handler 10 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:38,485 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:38,798 INFO [IPC Server handler 26 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.6208445
2015-10-19 17:54:38,860 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.64745617
2015-10-19 17:54:38,985 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.66521084
2015-10-19 17:54:39,017 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.66521084
2015-10-19 17:54:39,173 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.667
2015-10-19 17:54:39,189 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.667
2015-10-19 17:54:39,220 INFO [IPC Server handler 22 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.667
2015-10-19 17:54:39,251 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.64745617
2015-10-19 17:54:39,548 INFO [IPC Server handler 16 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:40,017 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.0
2015-10-19 17:54:40,126 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.61898744
2015-10-19 17:54:40,173 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.5342037
2015-10-19 17:54:40,189 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.6446815
2015-10-19 17:54:40,610 INFO [IPC Server handler 26 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:40,970 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.6446815
2015-10-19 17:54:41,048 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0019_m_000000
2015-10-19 17:54:41,048 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 17:54:41,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0019_m_000000
2015-10-19 17:54:41,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:54:41,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:54:41,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000000_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:54:41,173 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.6208445
2015-10-19 17:54:41,267 INFO [Socket Reader #1 for port 56183] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0019 (auth:SIMPLE)
2015-10-19 17:54:41,485 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0019_m_000014 asked for a task
2015-10-19 17:54:41,485 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0019_m_000014 given task: attempt_1445182159119_0019_m_000004_1001
2015-10-19 17:54:41,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-19 17:54:41,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0019: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 17:54:41,845 INFO [IPC Server handler 28 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.667
2015-10-19 17:54:41,939 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.667
2015-10-19 17:54:42,064 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.667
2015-10-19 17:54:42,189 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.667
2015-10-19 17:54:42,204 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.667
2015-10-19 17:54:42,220 INFO [IPC Server handler 22 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:42,236 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.667
2015-10-19 17:54:43,126 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.667
2015-10-19 17:54:43,126 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.667
2015-10-19 17:54:43,189 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.5342037
2015-10-19 17:54:43,189 INFO [IPC Server handler 22 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.667
2015-10-19 17:54:43,329 INFO [IPC Server handler 25 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:44,079 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1001 is : 0.016380234
2015-10-19 17:54:44,392 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:44,751 INFO [IPC Server handler 18 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.0
2015-10-19 17:54:44,892 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.667
2015-10-19 17:54:44,986 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.667
2015-10-19 17:54:45,111 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.667
2015-10-19 17:54:45,220 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.69132215
2015-10-19 17:54:45,251 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.6779769
2015-10-19 17:54:45,283 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.6923886
2015-10-19 17:54:45,470 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:46,126 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.667
2015-10-19 17:54:46,204 INFO [IPC Server handler 22 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.59646577
2015-10-19 17:54:46,204 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.667
2015-10-19 17:54:46,548 INFO [IPC Server handler 16 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:47,329 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1001 is : 0.043639295
2015-10-19 17:54:47,626 INFO [IPC Server handler 26 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:47,908 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.667
2015-10-19 17:54:47,923 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.0
2015-10-19 17:54:48,001 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.6675142
2015-10-19 17:54:48,126 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.67558074
2015-10-19 17:54:48,251 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.73348606
2015-10-19 17:54:48,267 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.7183496
2015-10-19 17:54:48,298 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.7354205
2015-10-19 17:54:48,705 INFO [IPC Server handler 18 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:49,142 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.667
2015-10-19 17:54:49,220 INFO [IPC Server handler 22 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.6681547
2015-10-19 17:54:49,220 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.6196791
2015-10-19 17:54:49,923 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:50,673 INFO [IPC Server handler 18 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1001 is : 0.059922766
2015-10-19 17:54:50,939 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.6797831
2015-10-19 17:54:51,017 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:51,033 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.7001761
2015-10-19 17:54:51,158 INFO [IPC Server handler 22 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.7115852
2015-10-19 17:54:51,252 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.0
2015-10-19 17:54:51,283 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.757781
2015-10-19 17:54:51,298 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.74151844
2015-10-19 17:54:51,345 INFO [IPC Server handler 20 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.7599814
2015-10-19 17:54:52,095 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:52,158 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.66714954
2015-10-19 17:54:52,220 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.6196791
2015-10-19 17:54:52,236 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.6943572
2015-10-19 17:54:53,205 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:53,955 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.7217471
2015-10-19 17:54:54,049 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.7411928
2015-10-19 17:54:54,174 INFO [IPC Server handler 22 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.7552234
2015-10-19 17:54:54,283 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:54,299 INFO [IPC Server handler 20 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.7846617
2015-10-19 17:54:54,314 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.7674506
2015-10-19 17:54:54,361 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.7885486
2015-10-19 17:54:54,439 INFO [IPC Server handler 25 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1001 is : 0.072357535
2015-10-19 17:54:55,158 INFO [IPC Server handler 22 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.033333335
2015-10-19 17:54:55,174 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.6955358
2015-10-19 17:54:55,220 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.6196791
2015-10-19 17:54:55,252 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.7262846
2015-10-19 17:54:55,377 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:56,064 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1001 is : 0.043156985
2015-10-19 17:54:56,486 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:57,002 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.746995
2015-10-19 17:54:57,095 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.7660728
2015-10-19 17:54:57,221 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.778935
2015-10-19 17:54:57,330 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.81903166
2015-10-19 17:54:57,346 INFO [IPC Server handler 1 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.8004005
2015-10-19 17:54:57,392 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.8223591
2015-10-19 17:54:57,596 INFO [IPC Server handler 16 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:57,830 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1001 is : 0.10175717
2015-10-19 17:54:58,189 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.72579026
2015-10-19 17:54:58,236 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.6196791
2015-10-19 17:54:58,267 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.75944906
2015-10-19 17:54:58,471 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.033333335
2015-10-19 17:54:58,986 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:54:59,330 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1001 is : 0.06676305
2015-10-19 17:55:00,017 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.773568
2015-10-19 17:55:00,064 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:55:00,127 INFO [IPC Server handler 22 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.7936655
2015-10-19 17:55:00,236 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.8084495
2015-10-19 17:55:00,377 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.84707767
2015-10-19 17:55:00,377 INFO [IPC Server handler 25 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.8336158
2015-10-19 17:55:00,424 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.85712224
2015-10-19 17:55:01,158 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:55:01,189 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.7568521
2015-10-19 17:55:01,252 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.6465807
2015-10-19 17:55:01,283 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.7921466
2015-10-19 17:55:02,158 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.033333335
2015-10-19 17:55:02,221 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1001 is : 0.10660437
2015-10-19 17:55:02,221 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.6465807
2015-10-19 17:55:02,236 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:55:02,846 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1001 is : 0.075558126
2015-10-19 17:55:03,049 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.80830956
2015-10-19 17:55:03,158 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.8276353
2015-10-19 17:55:03,268 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.84453225
2015-10-19 17:55:03,299 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:55:03,408 INFO [IPC Server handler 1 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.8675425
2015-10-19 17:55:03,424 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.8529882
2015-10-19 17:55:03,471 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.8780673
2015-10-19 17:55:04,205 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.7878011
2015-10-19 17:55:04,252 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.667
2015-10-19 17:55:04,299 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.82477516
2015-10-19 17:55:04,377 INFO [IPC Server handler 1 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:55:05,518 INFO [IPC Server handler 16 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:55:05,861 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.033333335
2015-10-19 17:55:05,924 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1001 is : 0.10660437
2015-10-19 17:55:06,065 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.845921
2015-10-19 17:55:06,174 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.86344665
2015-10-19 17:55:06,283 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.88332087
2015-10-19 17:55:06,440 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.89398754
2015-10-19 17:55:06,440 INFO [IPC Server handler 25 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.87938565
2015-10-19 17:55:06,471 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1001 is : 0.0945463
2015-10-19 17:55:06,487 INFO [IPC Server handler 16 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.90642077
2015-10-19 17:55:06,627 INFO [IPC Server handler 10 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:55:07,205 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.81768197
2015-10-19 17:55:07,268 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.667
2015-10-19 17:55:07,299 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.8560278
2015-10-19 17:55:07,705 INFO [IPC Server handler 26 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:55:08,768 INFO [IPC Server handler 18 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:55:09,096 INFO [IPC Server handler 28 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.88768053
2015-10-19 17:55:09,205 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.9031517
2015-10-19 17:55:09,315 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.92447376
2015-10-19 17:55:09,362 INFO [IPC Server handler 1 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.033333335
2015-10-19 17:55:09,471 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.90766734
2015-10-19 17:55:09,471 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1001 is : 0.10660437
2015-10-19 17:55:09,471 INFO [IPC Server handler 16 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.92363554
2015-10-19 17:55:09,518 INFO [IPC Server handler 10 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.9357531
2015-10-19 17:55:09,830 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:55:09,877 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1001 is : 0.10680563
2015-10-19 17:55:10,221 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.8475377
2015-10-19 17:55:10,284 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.667
2015-10-19 17:55:10,330 INFO [IPC Server handler 1 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.8873018
2015-10-19 17:55:10,862 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:55:11,972 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:55:12,144 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.9238391
2015-10-19 17:55:12,222 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.9391674
2015-10-19 17:55:12,332 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.9595412
2015-10-19 17:55:12,488 INFO [IPC Server handler 10 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.9509443
2015-10-19 17:55:12,503 INFO [IPC Server handler 26 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.9337547
2015-10-19 17:55:12,550 INFO [IPC Server handler 18 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.96288574
2015-10-19 17:55:12,894 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.033333335
2015-10-19 17:55:13,035 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:55:13,128 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1001 is : 0.10660437
2015-10-19 17:55:13,238 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.880285
2015-10-19 17:55:13,300 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.678726
2015-10-19 17:55:13,347 INFO [IPC Server handler 1 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.92113507
2015-10-19 17:55:13,660 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1001 is : 0.10680563
2015-10-19 17:55:14,128 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:55:14,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:55:14,582 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:55:14,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0019_02_000015 to attempt_1445182159119_0019_m_000000_1001
2015-10-19 17:55:14,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:10 RackLocal:3
2015-10-19 17:55:14,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:55:14,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000000_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:55:14,582 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0019_02_000015 taskAttempt attempt_1445182159119_0019_m_000000_1001
2015-10-19 17:55:14,582 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0019_m_000000_1001
2015-10-19 17:55:14,582 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 17:55:15,191 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:55:15,207 INFO [IPC Server handler 22 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.9549086
2015-10-19 17:55:15,285 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.97119546
2015-10-19 17:55:15,379 INFO [IPC Server handler 20 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 0.99328744
2015-10-19 17:55:15,535 INFO [IPC Server handler 26 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.96900606
2015-10-19 17:55:15,550 INFO [IPC Server handler 18 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.9504901
2015-10-19 17:55:15,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0019: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 17:55:15,597 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 0.98149294
2015-10-19 17:55:15,629 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0019_m_000000_1001 : 13562
2015-10-19 17:55:15,629 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0019_m_000000_1001] using containerId: [container_1445182159119_0019_02_000015 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 17:55:15,629 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000000_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:55:15,629 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0019_m_000000
2015-10-19 17:55:16,238 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:55:16,254 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.91209686
2015-10-19 17:55:16,300 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.7076595
2015-10-19 17:55:16,347 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.033333335
2015-10-19 17:55:16,363 INFO [IPC Server handler 20 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.95401
2015-10-19 17:55:16,629 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1001 is : 0.10660437
2015-10-19 17:55:16,785 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000006_1000 is : 1.0
2015-10-19 17:55:16,785 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0019_m_000006_1000
2015-10-19 17:55:16,785 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000006_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:55:16,785 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0019_02_000008 taskAttempt attempt_1445182159119_0019_m_000006_1000
2015-10-19 17:55:16,785 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0019_m_000006_1000
2015-10-19 17:55:16,785 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:55:16,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000006_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:55:16,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0019_m_000006_1000
2015-10-19 17:55:16,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:55:16,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-19 17:55:17,222 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1001 is : 0.10680563
2015-10-19 17:55:17,300 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-19 17:55:17,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:10 RackLocal:3
2015-10-19 17:55:17,722 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000008_1000 is : 1.0
2015-10-19 17:55:17,738 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0019_m_000008_1000
2015-10-19 17:55:17,738 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000008_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:55:17,738 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0019_02_000010 taskAttempt attempt_1445182159119_0019_m_000008_1000
2015-10-19 17:55:17,738 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0019_m_000008_1000
2015-10-19 17:55:17,738 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:55:17,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000008_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:55:17,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0019_m_000008_1000
2015-10-19 17:55:17,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:55:17,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-19 17:55:18,254 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.97014797
2015-10-19 17:55:18,332 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 0.9870312
2015-10-19 17:55:18,347 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:55:18,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:10 RackLocal:3
2015-10-19 17:55:18,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0019_02_000008
2015-10-19 17:55:18,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:10 RackLocal:3
2015-10-19 17:55:18,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0019_m_000006_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:55:18,582 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 0.99410284
2015-10-19 17:55:18,597 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.97556126
2015-10-19 17:55:19,269 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.9446994
2015-10-19 17:55:19,301 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.74664754
2015-10-19 17:55:19,379 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 0.98745716
2015-10-19 17:55:19,426 INFO [IPC Server handler 10 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:55:19,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0019_02_000010
2015-10-19 17:55:19,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:10 RackLocal:3
2015-10-19 17:55:19,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0019_m_000008_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:55:19,691 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000003_1000 is : 1.0
2015-10-19 17:55:19,691 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0019_m_000003_1000
2015-10-19 17:55:19,691 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000003_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:55:19,691 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0019_02_000005 taskAttempt attempt_1445182159119_0019_m_000003_1000
2015-10-19 17:55:19,691 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0019_m_000003_1000
2015-10-19 17:55:19,691 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:55:19,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000003_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:55:19,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0019_m_000003_1000
2015-10-19 17:55:19,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:55:19,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-19 17:55:19,754 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.033333335
2015-10-19 17:55:20,097 INFO [IPC Server handler 28 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1001 is : 0.10660437
2015-10-19 17:55:20,254 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000005_1000 is : 1.0
2015-10-19 17:55:20,254 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0019_m_000005_1000
2015-10-19 17:55:20,254 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000005_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:55:20,254 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0019_02_000007 taskAttempt attempt_1445182159119_0019_m_000005_1000
2015-10-19 17:55:20,254 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0019_m_000005_1000
2015-10-19 17:55:20,254 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:55:20,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000005_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:55:20,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0019_m_000005_1000
2015-10-19 17:55:20,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:55:20,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-19 17:55:20,504 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:55:20,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:10 RackLocal:3
2015-10-19 17:55:20,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0019_02_000005
2015-10-19 17:55:20,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:10 RackLocal:3
2015-10-19 17:55:20,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0019_m_000003_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:55:20,597 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1001 is : 0.10680563
2015-10-19 17:55:20,738 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000001_1000 is : 1.0
2015-10-19 17:55:20,738 INFO [IPC Server handler 28 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0019_m_000001_1000
2015-10-19 17:55:20,738 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000001_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:55:20,738 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0019_02_000003 taskAttempt attempt_1445182159119_0019_m_000001_1000
2015-10-19 17:55:20,738 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0019_m_000001_1000
2015-10-19 17:55:20,738 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 17:55:20,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000001_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:55:20,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0019_m_000001_1000
2015-10-19 17:55:20,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:55:20,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-19 17:55:21,285 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 0.9997146
2015-10-19 17:55:21,410 INFO [IPC Server handler 10 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000004_1000 is : 1.0
2015-10-19 17:55:21,410 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0019_m_000004_1000
2015-10-19 17:55:21,410 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000004_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:55:21,410 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0019_02_000006 taskAttempt attempt_1445182159119_0019_m_000004_1000
2015-10-19 17:55:21,410 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0019_m_000004_1000
2015-10-19 17:55:21,410 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:55:21,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000004_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:55:21,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0019_m_000004_1000
2015-10-19 17:55:21,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0019_m_000004_1001
2015-10-19 17:55:21,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:55:21,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-19 17:55:21,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000004_1001 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 17:55:21,426 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0019_02_000014 taskAttempt attempt_1445182159119_0019_m_000004_1001
2015-10-19 17:55:21,426 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0019_m_000004_1001
2015-10-19 17:55:21,426 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:55:21,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:10 RackLocal:3
2015-10-19 17:55:21,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0019_02_000007
2015-10-19 17:55:21,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:10 RackLocal:3
2015-10-19 17:55:21,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0019_m_000005_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:55:21,629 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 0.9940365
2015-10-19 17:55:21,644 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:55:22,285 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 0.97553384
2015-10-19 17:55:22,316 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.7869564
2015-10-19 17:55:22,379 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0019_m_000004
2015-10-19 17:55:22,379 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 17:55:22,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0019_02_000003
2015-10-19 17:55:22,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0019_02_000006
2015-10-19 17:55:22,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0019_m_000001_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:55:22,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:10 RackLocal:3
2015-10-19 17:55:22,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0019_m_000004_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:55:22,598 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000007_1000 is : 1.0
2015-10-19 17:55:22,598 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0019_m_000007_1000
2015-10-19 17:55:22,598 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000007_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:55:22,598 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0019_02_000009 taskAttempt attempt_1445182159119_0019_m_000007_1000
2015-10-19 17:55:22,598 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0019_m_000007_1000
2015-10-19 17:55:22,598 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:55:22,613 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000007_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:55:22,613 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000004_1001 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 17:55:22,613 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0019_m_000007_1000
2015-10-19 17:55:22,613 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:55:22,613 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-19 17:55:22,613 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 17:55:22,629 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out4/_temporary/2/_temporary/attempt_1445182159119_0019_m_000004_1001
2015-10-19 17:55:22,629 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000004_1001 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 17:55:22,769 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-19 17:55:23,238 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.033333335
2015-10-19 17:55:23,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:10 RackLocal:3
2015-10-19 17:55:23,723 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1001 is : 0.10660437
2015-10-19 17:55:23,754 INFO [Socket Reader #1 for port 56183] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 56183: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 17:55:23,894 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 17:55:24,285 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000000_1000 is : 1.0
2015-10-19 17:55:24,285 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0019_m_000000_1000
2015-10-19 17:55:24,285 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000000_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:55:24,285 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0019_02_000002 taskAttempt attempt_1445182159119_0019_m_000000_1000
2015-10-19 17:55:24,285 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0019_m_000000_1000
2015-10-19 17:55:24,285 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 17:55:24,301 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000000_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:55:24,301 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0019_m_000000_1000
2015-10-19 17:55:24,301 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0019_m_000000_1001
2015-10-19 17:55:24,301 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:55:24,301 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-19 17:55:24,301 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000000_1001 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 17:55:24,301 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0019_02_000015 taskAttempt attempt_1445182159119_0019_m_000000_1001
2015-10-19 17:55:24,301 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0019_m_000000_1001
2015-10-19 17:55:24,301 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 17:55:24,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:10 RackLocal:3
2015-10-19 17:55:24,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0019_02_000009
2015-10-19 17:55:24,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:10 RackLocal:3
2015-10-19 17:55:24,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0019_m_000007_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:55:24,973 INFO [IPC Server handler 22 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 17:55:25,332 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.8194983
2015-10-19 17:55:25,379 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000000_1001 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 17:55:25,379 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 17:55:25,379 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out4/_temporary/2/_temporary/attempt_1445182159119_0019_m_000000_1001
2015-10-19 17:55:25,379 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000000_1001 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 17:55:25,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0019_02_000002
2015-10-19 17:55:25,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0019_02_000014
2015-10-19 17:55:25,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0019_m_000000_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:55:25,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:10 RackLocal:3
2015-10-19 17:55:25,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0019_m_000004_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:55:26,035 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 17:55:26,738 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.033333335
2015-10-19 17:55:27,113 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 17:55:27,270 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1001 is : 0.10660437
2015-10-19 17:55:28,207 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 17:55:28,348 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.87097865
2015-10-19 17:55:28,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0019_02_000015
2015-10-19 17:55:28,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:10 RackLocal:3
2015-10-19 17:55:28,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0019_m_000000_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:55:29,301 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 17:55:30,223 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.033333335
2015-10-19 17:55:30,348 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 17:55:30,676 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1001 is : 0.10660437
2015-10-19 17:55:31,364 INFO [IPC Server handler 20 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.9258646
2015-10-19 17:55:31,426 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 17:55:32,504 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 17:55:33,567 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 17:55:33,661 INFO [IPC Server handler 18 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.033333335
2015-10-19 17:55:33,973 INFO [IPC Server handler 22 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1001 is : 0.11431367
2015-10-19 17:55:34,379 INFO [IPC Server handler 20 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 0.97752357
2015-10-19 17:55:34,614 INFO [IPC Server handler 18 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 17:55:35,661 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 17:55:35,692 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_m_000002_1000 is : 1.0
2015-10-19 17:55:35,692 INFO [IPC Server handler 26 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0019_m_000002_1000
2015-10-19 17:55:35,692 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000002_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:55:35,692 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0019_02_000004 taskAttempt attempt_1445182159119_0019_m_000002_1000
2015-10-19 17:55:35,692 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0019_m_000002_1000
2015-10-19 17:55:35,692 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 17:55:35,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000002_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:55:35,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0019_m_000002_1000
2015-10-19 17:55:35,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0019_m_000002_1001
2015-10-19 17:55:35,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:55:35,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-19 17:55:35,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000002_1001 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 17:55:35,707 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0019_02_000013 taskAttempt attempt_1445182159119_0019_m_000002_1001
2015-10-19 17:55:35,707 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0019_m_000002_1001
2015-10-19 17:55:35,707 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:55:36,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000002_1001 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 17:55:36,207 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 17:55:36,223 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out4/_temporary/2/_temporary/attempt_1445182159119_0019_m_000002_1001
2015-10-19 17:55:36,223 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_m_000002_1001 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 17:55:36,583 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:10 RackLocal:3
2015-10-19 17:55:36,723 INFO [Socket Reader #1 for port 56183] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 56183: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 17:55:36,786 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 17:55:36,989 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.033333335
2015-10-19 17:55:37,583 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0019_02_000004
2015-10-19 17:55:37,583 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0019_02_000013
2015-10-19 17:55:37,583 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0019_m_000002_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:55:37,583 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:10 RackLocal:3
2015-10-19 17:55:37,583 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0019_m_000002_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:55:37,817 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:55:38,879 INFO [IPC Server handler 28 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:55:39,958 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:55:40,176 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.033333335
2015-10-19 17:55:41,036 INFO [IPC Server handler 22 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:55:42,098 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:55:43,145 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:55:43,395 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.033333335
2015-10-19 17:55:44,223 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:55:45,286 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:55:46,333 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:55:46,661 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.033333335
2015-10-19 17:55:47,395 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:55:48,458 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:55:49,567 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:55:50,130 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.033333335
2015-10-19 17:55:50,630 INFO [IPC Server handler 1 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:55:51,692 INFO [IPC Server handler 18 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:55:52,771 INFO [IPC Server handler 26 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:55:53,458 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.033333335
2015-10-19 17:55:53,849 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:55:54,911 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:55:55,974 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:55:56,599 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.10000001
2015-10-19 17:55:57,021 INFO [IPC Server handler 28 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:55:58,069 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:55:59,147 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:55:59,834 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.10000001
2015-10-19 17:56:00,194 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:01,256 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:02,303 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:02,975 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.10000001
2015-10-19 17:56:03,397 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:04,444 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:05,507 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:06,163 INFO [IPC Server handler 22 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.10000001
2015-10-19 17:56:06,538 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:07,569 INFO [IPC Server handler 20 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:08,647 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:09,272 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.10000001
2015-10-19 17:56:09,679 INFO [IPC Server handler 1 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:10,757 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:11,772 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:12,382 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.10000001
2015-10-19 17:56:12,819 INFO [IPC Server handler 10 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:13,882 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:14,913 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:15,804 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.10000001
2015-10-19 17:56:15,991 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:17,054 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:18,116 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:19,179 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:19,304 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.10000001
2015-10-19 17:56:20,242 INFO [IPC Server handler 22 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:21,304 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:22,351 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:22,429 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.13333334
2015-10-19 17:56:23,398 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:24,445 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:25,507 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:25,586 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.13333334
2015-10-19 17:56:26,554 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:27,601 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:28,648 INFO [IPC Server handler 20 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:28,742 INFO [IPC Server handler 1 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.16666667
2015-10-19 17:56:29,726 INFO [IPC Server handler 1 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:30,820 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:31,883 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:32,383 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.16666667
2015-10-19 17:56:32,945 INFO [IPC Server handler 10 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:34,008 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:35,055 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:35,617 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.16666667
2015-10-19 17:56:36,102 INFO [IPC Server handler 18 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:37,133 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:38,164 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:39,039 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.16666667
2015-10-19 17:56:39,274 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:40,336 INFO [IPC Server handler 28 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:41,399 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:42,305 INFO [IPC Server handler 16 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.16666667
2015-10-19 17:56:42,477 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:43,556 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:44,650 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:45,712 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:45,806 INFO [IPC Server handler 1 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.16666667
2015-10-19 17:56:46,837 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:47,900 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:48,978 INFO [IPC Server handler 10 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:49,322 INFO [IPC Server handler 16 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.16666667
2015-10-19 17:56:50,041 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:51,119 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:52,197 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:52,681 INFO [IPC Server handler 20 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.16666667
2015-10-19 17:56:53,275 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:54,322 INFO [IPC Server handler 16 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:55,431 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:55,978 INFO [IPC Server handler 10 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.16666667
2015-10-19 17:56:56,494 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:57,557 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:58,635 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:56:59,432 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.20000002
2015-10-19 17:56:59,682 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:00,729 INFO [IPC Server handler 20 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:01,791 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:02,869 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:02,994 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.23333333
2015-10-19 17:57:03,947 INFO [IPC Server handler 1 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:05,010 INFO [IPC Server handler 10 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:06,057 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:06,416 INFO [IPC Server handler 16 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.23333333
2015-10-19 17:57:07,104 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:08,166 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:09,245 INFO [IPC Server handler 26 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:09,870 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.23333333
2015-10-19 17:57:10,307 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:11,385 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:12,432 INFO [IPC Server handler 16 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:13,526 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:13,698 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.23333333
2015-10-19 17:57:14,660 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:15,738 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:16,817 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:17,192 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.26666668
2015-10-19 17:57:17,989 INFO [IPC Server handler 1 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:19,114 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:20,207 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:20,895 INFO [IPC Server handler 20 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.26666668
2015-10-19 17:57:21,286 INFO [IPC Server handler 26 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:22,348 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:23,442 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:24,348 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.26666668
2015-10-19 17:57:24,536 INFO [IPC Server handler 16 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:25,567 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:26,676 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:27,739 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:27,895 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.26666668
2015-10-19 17:57:28,792 INFO [IPC Server handler 28 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:29,835 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:30,897 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:31,491 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.26666668
2015-10-19 17:57:31,960 INFO [IPC Server handler 20 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:33,054 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:34,132 INFO [IPC Server handler 1 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:35,226 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:35,397 INFO [IPC Server handler 26 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.26666668
2015-10-19 17:57:36,304 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:37,366 INFO [IPC Server handler 26 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:38,444 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:38,929 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.26666668
2015-10-19 17:57:39,507 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:40,585 INFO [IPC Server handler 16 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:41,726 INFO [IPC Server handler 22 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:42,523 INFO [IPC Server handler 16 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.3
2015-10-19 17:57:42,835 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:43,929 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:44,992 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:46,054 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:46,179 INFO [IPC Server handler 1 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.3
2015-10-19 17:57:47,132 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:48,148 INFO [IPC Server handler 1 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:49,195 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:49,851 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.3
2015-10-19 17:57:50,289 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:51,351 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:52,414 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:53,398 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.3
2015-10-19 17:57:53,461 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:54,508 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:55,555 INFO [IPC Server handler 26 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0019_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 17:57:56,117 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.3
2015-10-19 17:57:56,226 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.3
2015-10-19 17:57:57,008 INFO [IPC Server handler 25 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.6666667
2015-10-19 17:58:01,320 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.6682114
2015-10-19 17:58:04,914 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.6732092
2015-10-19 17:58:08,618 INFO [IPC Server handler 27 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.6779541
2015-10-19 17:58:12,212 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.68303674
2015-10-19 17:58:15,884 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.6880477
2015-10-19 17:58:19,477 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.6930393
2015-10-19 17:58:23,103 INFO [IPC Server handler 22 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.69816583
2015-10-19 17:58:26,712 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.7030546
2015-10-19 17:58:30,072 INFO [IPC Server handler 22 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.7079118
2015-10-19 17:58:33,806 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.713512
2015-10-19 17:58:37,556 INFO [IPC Server handler 13 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.71924984
2015-10-19 17:58:41,135 INFO [IPC Server handler 22 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.72358006
2015-10-19 17:58:44,744 INFO [IPC Server handler 19 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.72893924
2015-10-19 17:58:48,275 INFO [IPC Server handler 18 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.7340402
2015-10-19 17:58:51,979 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.7397736
2015-10-19 17:58:55,557 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.7446486
2015-10-19 17:58:59,182 INFO [IPC Server handler 10 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.74911946
2015-10-19 17:59:02,682 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.75449336
2015-10-19 17:59:06,276 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.7598647
2015-10-19 17:59:09,792 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.764735
2015-10-19 17:59:13,495 INFO [IPC Server handler 18 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.77018344
2015-10-19 17:59:17,339 INFO [IPC Server handler 10 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.7754492
2015-10-19 17:59:21,292 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.7811562
2015-10-19 17:59:25,027 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.7861187
2015-10-19 17:59:28,746 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.7917964
2015-10-19 17:59:32,262 INFO [IPC Server handler 5 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.7973094
2015-10-19 17:59:35,824 INFO [IPC Server handler 7 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.80304694
2015-10-19 17:59:39,496 INFO [IPC Server handler 10 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.8089151
2015-10-19 17:59:43,043 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.8143567
2015-10-19 17:59:46,810 INFO [IPC Server handler 17 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.8194424
2015-10-19 17:59:50,763 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.82508785
2015-10-19 17:59:54,451 INFO [IPC Server handler 8 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.83074033
2015-10-19 17:59:58,029 INFO [IPC Server handler 0 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.83621037
2015-10-19 18:00:01,373 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.8418144
2015-10-19 18:00:04,827 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.8480226
2015-10-19 18:00:08,280 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.85317284
2015-10-19 18:00:11,702 INFO [IPC Server handler 11 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.8605091
2015-10-19 18:00:14,983 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.86837167
2015-10-19 18:00:18,249 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.87561053
2015-10-19 18:00:21,608 INFO [IPC Server handler 29 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.88292134
2015-10-19 18:00:24,874 INFO [IPC Server handler 10 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.88976276
2015-10-19 18:00:28,109 INFO [IPC Server handler 24 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.8967041
2015-10-19 18:00:31,437 INFO [IPC Server handler 25 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.90360665
2015-10-19 18:00:34,876 INFO [IPC Server handler 9 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.91110235
2015-10-19 18:00:38,110 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.91808313
2015-10-19 18:00:41,688 INFO [IPC Server handler 6 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.9251123
2015-10-19 18:00:44,985 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.9317502
2015-10-19 18:00:48,267 INFO [IPC Server handler 26 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.93958765
2015-10-19 18:00:51,470 INFO [IPC Server handler 16 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.9468132
2015-10-19 18:00:54,830 INFO [IPC Server handler 21 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.95411193
2015-10-19 18:00:58,127 INFO [IPC Server handler 1 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.9612774
2015-10-19 18:01:01,424 INFO [IPC Server handler 23 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.9681872
2015-10-19 18:01:04,767 INFO [IPC Server handler 25 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.9750513
2015-10-19 18:01:08,158 INFO [IPC Server handler 14 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.9822508
2015-10-19 18:01:11,393 INFO [IPC Server handler 15 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.98923755
2015-10-19 18:01:14,846 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 0.99615645
2015-10-19 18:01:17,846 INFO [IPC Server handler 12 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445182159119_0019_r_000000_1000
2015-10-19 18:01:17,846 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_r_000000_1000 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-19 18:01:17,846 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445182159119_0019_r_000000_1000 given a go for committing the task output.
2015-10-19 18:01:17,846 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445182159119_0019_r_000000_1000
2015-10-19 18:01:17,846 INFO [IPC Server handler 4 on 56183] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445182159119_0019_r_000000_1000:true
2015-10-19 18:01:18,143 INFO [IPC Server handler 3 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0019_r_000000_1000 is : 1.0
2015-10-19 18:01:18,143 INFO [IPC Server handler 2 on 56183] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0019_r_000000_1000
2015-10-19 18:01:18,143 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_r_000000_1000 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 18:01:18,143 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0019_02_000012 taskAttempt attempt_1445182159119_0019_r_000000_1000
2015-10-19 18:01:18,143 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0019_r_000000_1000
2015-10-19 18:01:18,143 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 18:01:18,284 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0019_r_000000_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 18:01:18,284 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0019_r_000000_1000
2015-10-19 18:01:18,284 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0019_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 18:01:18,284 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-19 18:01:18,284 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0019Job Transitioned from RUNNING to COMMITTING
2015-10-19 18:01:18,284 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-19 18:01:18,346 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-19 18:01:18,346 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0019Job Transitioned from COMMITTING to SUCCEEDED
2015-10-19 18:01:18,346 INFO [Thread-109] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-19 18:01:18,346 INFO [Thread-109] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-19 18:01:18,346 INFO [Thread-109] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-19 18:01:18,346 INFO [Thread-109] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-19 18:01:18,346 INFO [Thread-109] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-19 18:01:18,346 INFO [Thread-109] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-19 18:01:18,346 INFO [Thread-109] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-19 18:01:18,539 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0019/job_1445182159119_0019_2.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0019-1445247552638-msrabi-pagerank-1445248878331-10-1-SUCCEEDED-default-1445248388426.jhist_tmp
2015-10-19 18:01:18,617 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:14 ContRel:0 HostLocal:10 RackLocal:3
2015-10-19 18:01:18,882 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0019-1445247552638-msrabi-pagerank-1445248878331-10-1-SUCCEEDED-default-1445248388426.jhist_tmp
2015-10-19 18:01:18,882 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0019/job_1445182159119_0019_2_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0019_conf.xml_tmp
2015-10-19 18:01:19,164 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0019_conf.xml_tmp
2015-10-19 18:01:19,179 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0019.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0019.summary
2015-10-19 18:01:19,179 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0019_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0019_conf.xml
2015-10-19 18:01:19,179 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0019-1445247552638-msrabi-pagerank-1445248878331-10-1-SUCCEEDED-default-1445248388426.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0019-1445247552638-msrabi-pagerank-1445248878331-10-1-SUCCEEDED-default-1445248388426.jhist
2015-10-19 18:01:19,179 INFO [Thread-109] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-19 18:01:19,195 INFO [Thread-109] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-19 18:01:19,195 INFO [Thread-109] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MSRA-SA-39.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445182159119_0019
2015-10-19 18:01:19,195 INFO [Thread-109] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-19 18:01:20,195 INFO [Thread-109] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:14 ContRel:0 HostLocal:10 RackLocal:3
2015-10-19 18:01:20,195 INFO [Thread-109] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0019
2015-10-19 18:01:20,211 INFO [Thread-109] org.apache.hadoop.ipc.Server: Stopping server on 56183
2015-10-19 18:01:20,211 INFO [IPC Server listener on 56183] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 56183
2015-10-19 18:01:20,211 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-19 18:01:20,211 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
