{"cells": [{"cell_type": "code", "execution_count": 1, "id": "616e2194-1c3b-49eb-bfb1-c8324fa1b534", "metadata": {}, "outputs": [], "source": ["from pywaffle import Waffle\n", "import pandas as pd\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 41, "id": "30fd6f65-8717-400d-b65f-ce03e767c511", "metadata": {}, "outputs": [], "source": ["lith_dict = {'LITH': ['Shale', 'Sandstone', \n", "                      'Sandstone/Shale', 'Chalk', \n", "                      'Limestone', 'Marl', 'Tuff'],\n", "             'Well1': [61,15, 10, 5, \n", "                            5, 3, 1],\n", "             'Well2': [35 ,21, 16, 12, \n", "                            7, 5, 4]}\n", "\n", "lith_data_df = pd.DataFrame.from_dict(lith_dict)"]}, {"cell_type": "code", "execution_count": 8, "id": "5843b1d6-bd58-4df2-8085-5b30c95ba3db", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LITH</th>\n", "      <th>Well1</th>\n", "      <th>Well2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Shale</td>\n", "      <td>61</td>\n", "      <td>35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Sandstone</td>\n", "      <td>15</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Sandstone/Shale</td>\n", "      <td>10</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Chalk</td>\n", "      <td>5</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Limestone</td>\n", "      <td>5</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Marl</td>\n", "      <td>3</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Tuff</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              LITH  Well1  Well2\n", "0            Shale     61     35\n", "1        Sandstone     15     21\n", "2  Sandstone/Shale     10     16\n", "3            Chalk      5     12\n", "4        Limestone      5      7\n", "5             Marl      3      5\n", "6             Tuff      1      4"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["lith_data_df"]}, {"cell_type": "code", "execution_count": 10, "id": "89c72e27-3612-4d8a-a0de-b961f4cf6bef", "metadata": {}, "outputs": [], "source": ["colours = ['#8dd3c7', '#deb887', '#bebada', '#fb8072', \n", "           '#80b1d3', '#fdb462', '#b3de69']"]}, {"cell_type": "code", "execution_count": 66, "id": "dd73b47d-92c3-4799-8d74-ca05d2eabe36", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Waffle size 720x720 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plot_labels = [f'{i} ({str(j)} %)' for i,j in zip(lith_data_df.LITH, \n", "                                                    lith_data_df.Well1)]\n", "\n", "plt.figure(FigureClass=Waffle, figsize=(10,10), rows=5, columns = 20, \n", "                 values=list(lith_data_df['Well1']),\n", "                 colors=colours,\n", "                 labels=plot_labels, \n", "                icons='circle',\n", "                font_size='20',\n", "                legend={'loc':'lower center', 'bbox_to_anchor': (0.5, -0.8), \n", "                        'ncol':3, 'fontsize':12},\n", "                 starting_location='NW')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 44, "id": "5e1a5c86-7766-4a19-b9af-105341b6b632", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAsgAAAE6CAYAAAALGaT5AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/YYfK9AAAACXBIWXMAAAsTAAALEwEAmpwYAABA5ElEQVR4nO3deVyU9f7///csLMMwwybryK4sKqua69GjnrRTol+X0EyPv6zcl8pTkWmUZYcyM81M2z2dtPhgm1qaqWl6sjjKJiQKIqIsIiAMMqwzvz88w+dKB8Q+IVM+7rebt5tc2/s1M+/r4sl1va9rZCaTSQAAAAC4St7VBQAAAADWhIAMAAAASBCQAQAAAAkCMgAAACBBQAYAAAAklO3NfPXE0VIhhOctqkWq7LE+A72stZYurIdarL8Wi/VYUy1CCFGwJ6lLPqfAMQnUYt21tFnPgb2nu+S9GXFnT2rpYC3Nzy3tsj6jTFzzi3qsqRYhhHjuyxNd8jkljutj1bUYdz3YJbXI73n3ulp2Fszvkj4zNnCjzNL0G51B7orO3Va71lRLe9M7E7XcXJvW1GesqZb2pncmarm5Nukz1NIWa6qlrXatqZb2pncmarm5Nruqz1jEEAsAAABAgoAMAAAASBCQAQAAAAkCMgAAACBBQAYAAAAkCMgAAACABAEZAAAAkCAgAwAAABIEZAAAAECCgAwAAABIEJABAAAACQIyAAAAIEFABgAAACQIyAAAAIAEARkAAACQICADAAAAEgRkAAAAQIKADAAAAEgQkAEAAAAJAjIAAAAgQUAGAAAAJAjIAAAAgAQBGQAAAJAgIAMAAAASBGQAAABAgoAMAAAASBCQAQAAAAkCMgAAACBxo4Bcdkuq6Fi71lRLe9M7E7XcXJvW1GesqZb2pncmarm5Nukz1NIWa6qlrXatqZb2pncmarm5Nruqz1gkM5lMXV0DAAAAYDUYYgEAAABIEJABAAAACQIyAAAAIEFABgAAACSU7c189cTRUiGE5y2qRarssT4Dvay1li6sh1qsvxaL9VhTLUIIUbAnqUs+p8AxCdRi3bW0Wc+Bvae75L0ZcWdPaulgLc3PLe2yPqNMXPOLeqypli6sx2Itz315oktqSRzX57pajLse7JJa5Pe8e10tOwvmd0mfGRu4UWZp+o3OIHdF526rXWuqpb3pnYlabq5Na+oz1lRLe9M7E7XcXJv0GWppizXV0la71lRLe9M7E7XcXJtd1WcsYogFAAAAIEFABgAAACQIyAAAAIAEARkAAACQICADAAAAEgRkAAAAQIKADAAAAEgQkAEAAAAJAjIAAAAgQUAGAAAAJAjIAAAAgAQBGQAAAJAgIAMAAAASBGQAAABAgoAMAAAASBCQAQAAAAkCMgAAACBBQAYAAAAkCMgAAACABAEZAAAAkCAgAwAAABIEZAAAAECCgAwAAABIEJABAAAACQIyAAAAIEFABgAAACQIyAAAAIDEjQJy2S2pomPtWlMt7U3vTNRyc21aU5+xplram96ZqOXm2qTPUEtbrKmWttq1plram96ZqOXm2uyqPmORzGQydXUNAAAAgNVgiAUAAAAgQUAGAAAAJAjIAAAAgAQBGQAAAJBQtjfz1RNHS4UQnreoFqmyx/oM9LLWWrqwHmqx/los1mNNtQghRMGepC75nALHJFCLddfSZj0H9p7ukvdmxJ09qaWDtTQ/t7TL+owycc0v6rGmWrqwHou1PPfliS6pJXFcn+tqMe56sEtqkd/z7nW17CyY3yV9ZmzgRpml6Tc6g9wVnbutdq2plvamdyZqubk2ranPWFMt7U3vTNRyc23SZ6ilLdZUS1vtWlMt7U3vTNRyc212VZ+xiCEWAAAAgAQBGQAAAJAgIAMAAAASBGQAAABAgoAMAAAASBCQAQAAAAkCMgAAACBBQAYAAAAkCMgAAACABAEZAAAAkCAgAwAAABIEZAAAAECCgAwAAABIEJABAAAACQIyAAAAIEFABgAAACQIyAAAAIAEARkAAACQICADAAAAEgRkAAAAQIKADAAAAEgQkAEAAAAJAjIAAAAgQUAGAAAAJAjIAAAAgAQBGQAAAJAgIAMAAAASNwrIZbekio61a021tDe9M1HLzbVpTX3Gmmppb3pnopaba5M+Qy1tsaZa2mrXmmppb3pnopaba7Or+oxFMpPJ1NU1AAAAAFaDIRYAAACABAEZAAAAkCAgAwAAABIEZAAAAEBC2d7MV08cLRVCeN6iWqTKHusz0Mtaa+nCeqjF+muxWI811SKEEAV7krrkcwock0At1l1Lm/Uc2Hu6S96bEXf2pBbrrsViPc3PLe2yWpSJa657b7qoHou1PPfliS6pJXFcn+tqMe56sEtqkd/z7nW17CyY3yV9ZmzgRpml6Tc6g9wVnbutdq2plvamdyZqubk2ranPWFMt7U3vTNRyc23SZ6ilLdZUS1vtWlMt7U3vTNRyc212VZ+xiCEWAAAAgAQBGQAAAJAgIAMAAAASBGQAAABAgoAMAAAASBCQAQAAAAkCMgAAACBBQAYAAAAkCMgAAACABAEZAAAAkCAgAwAAABIEZAAAAECCgAwAAABIEJABAAAACQIyAAAAIEFABgAAACQIyAAAAIAEARkAAACQICADAAAAEgRkAAAAQIKADAAAAEgQkAEAAAAJAjIAAAAgQUAGAAAAJAjIAAAAgAQBGQAAAJAgIAMAAAASNwrIZbekio61a021tDe9M1HLzbVpTX3Gmmppb3pnopaba5M+Qy1tsaZa2mrXmmppb3pnopaba7Or+oxFMpPJ1NU1AAAAAFaDIRYAAACABAEZAAAAkCAgAwAAABIEZAAAAEBC2d7MV08cLRVCeN6iWqTKHusz0Mtaa+nCeqjF+muxWI811SKEEAV7krrkcwock0At1l2LtdVjsZYDe093SS0j7uxJLR2sp/m5pV1WizJxzXXvTRfVY7GW57480SW1JI7rY9W1GHc92CV9Rn7PuzKL02+wXld07rbataZa2pvemajl5tq0pj5jTbW0N70zUcvNtUmfoZa2WFMtbbVrTbW0N70zUcvNtdlVfcYihlgAAAAAEgRkAAAAQIKADAAAAEgQkAEAAAAJAjIAAAAgQUAGAAAAJAjIAAAAgAQBGQAAAJAgIAMAAAASBGQAAABAgoAMAAAASBCQAQAAAAkCMgAAACBBQAYAAAAkCMgAAACABAEZAAAAkCAgAwAAABIEZAAAAECCgAwAAABIEJABAAAACQIyAAAAIEFABgAAACQIyAAAAIAEARkAAACQICADAAAAEgRkAAAAQIKADAAAAEgQkAEAAAAJmclk6uoaAAAAAKvBGWQAAABAgoAMAAAASBCQAQAAAAkCMgAAACBBQAYAAAAkCMgAAACABAEZAAAAkCAgAwAAABIEZAAAAECCgAwAAABIEJABAAAACQIyAAAAIEFABgAAACQIyAAAAIAEARkAAACQICADAAAAEgRkAAAAQIKADAAAAEgQkAEAAAAJAjIAAAAgQUAGAAAAJAjIAAAAgAQBGQAAAJAgIAMAAAASBGQAAABAgoAMAAAASBCQAQAAAAkCMgAAACBBQAYAAAAkCMgAAACABAEZAAAAkCAgAwAAABIEZAAAAECCgAwAAABIEJABAAAACQIyAAAAIEFABgAAACQIyAAAAIAEARkAAACQICADAAAAEgRkAAAAQIKADAAAAEgQkAEAAAAJAjIAAAAgQUAGAAAAJAjIAAAAgAQBGQAAAJAgIAMAAAASBGQAAABAgoAMAAAASBCQAQAAAAkCMgAAACBBQAYAAAAkCMgAAACABAEZAAAAkCAgAwAAABIEZAAAAECCgAwAAABIEJABAAAACQIyAAAAIEFABgAAACQIyAAAAIAEARkAAACQICADAAAAEgRkAAAAQIKADAAAAEgQkAEAAAAJAjIAAAAgoezqAnDrHTt2zFapVL4thBgqhFB0dT0AAFixFiHE4ebm5of79u3b2NXF4NYgIN+G5HL5PK1WO8Tf3/+yXC43dXU9AABYK6PRKCssLBx6+fLleUKIdV1dD24NhljchhQKxQM+Pj5XCMcAALRPLpebfHx8ahUKxf/X1bXg1iEg34ZMJpOTra1tU1fXAQDA74GtrW2TyWRy6uo6cOsQkG9PMplM1tU1AADwu/Df35lkptsIHzYAAAAgQUDG78b69evd+vbtG/pr1t25c6fG09Mz8te2vX37du1f/vKX4F+7/m/l4Ycf7v7SSy+5d3UdsG6PPfaYz/jx4wN/zbrX7mcymazviRMn7Dq6fmxsbNiRI0dUv6bt34rBYJAFBgb2Li4u5kZ0K5abm2srk8n6NjW1PeJvwYIFupUrV3p0di2rVq3ymDdvnq6z28HvBwcPiI0//yeqvqW50/qCvULZPD+8X0ZHlt2zZ49jQkJC97y8PHu5XC6Cg4Pr165de2748OF1nVVfRyQmJurWrVt3zvzzgAEDQk6dOqVqamqS63S6hhUrVhRPnz79shBXw/i4ceNC7O3tjeblX3rppXOLFi2quHa7TU1NYsKECUEHDx7URkdHX/niiy/yXV1djUIIkZCQ4GVvb2969tlny8zLL1++vGzAgAHhS5YsuWRvb89NlrdY88srooShrvOOmyqHZuUTz3doX9m0aZPrhg0bPM+cOWOvVqtbwsPDDU8//XTJmDFjajutvhvYunWrk1qtbhkyZIhBCCFSU1PtH330Ud/s7GyHy5cvK00m0zHzsgaDQTZz5ky/w4cPa6urq5W+vr4NK1euPB8fH19jads//PCDasaMGUEVFRXKJUuWlJr3i4aGBln//v1DP/300/wePXo0CSGESqUy3X///ZcSExO93n777fO34rV3BuM3i6NE05XO62826mb56PU37G86nS7i4sWLNufOncv09vZuNk8PDw/vdfLkSdXJkyezQkNDf/PHnxUXFyv/53/+x+3MmTNZQlwN1GFhYREqlar12LpgwYLS1atXl1haf9asWb4pKSluQUFB9du3b88PDg5uEuLqvnP06FH1Bx98UGRe9tFHHy0PDAyMWL58eZlOp2u2tD3cXjiDDNGZ4fhmtl9ZWSmfPHlyj7lz516sqqpKv3DhQsby5cuLuzoIHjx40EGv1ytGjRp1xTxt/fr1RRcvXsyora1Ne/PNNwvnzJkTWFhYaGOe7+7u3lRXV5dm/mcpHAshxJYtW1xkMpmpsrIyXavVtrz22mvuQghx8uRJ26+//tr56aefLpMu7+/v3xQUFGTYtm2bcye9XLSnM8PxTWz/2Wef9Vy2bJnv448/XlJaWppx/vz5rLlz51789NNPnTu1vht466233KdNm9ba121tbU2TJk2qev311wuvXbapqUnWvXv3pgMHDuTW1NSkJSYmXnjggQeCc3NzbS1tOyEhQffiiy8WHT9+POe1117zPnfunFIIIVauXOk5bty4KnM4NnvggQcqU1JS3AwGw+/3hovODMc3uX2dTtf43nvvuZp//umnn1QGg+FXZ4j2zhqbvfnmm24jR46sdnR0/MXvgOrq6tZja1vh+MCBAw4ZGRkOZWVlGQMGDKh97rnnvIUQoqKiQrFu3TqvV1999YJ0eQcHB9OIESOqN2/e7PZrXxP+WAjIsBonTpywF0KIOXPmVCqVSuHo6GiaOHFizYABAwzS5WbPnt1dq9VG63S6iOTkZK15+rp169yCgoJ6q9XqmO7du0esXr26W1ttnT171mbMmDHBLi4uUTqdLuKFF15o8xLejh07nAYOHKiXThswYIDBxuZqHpbJZKbm5mbZmTNnbCxuoB0FBQV2w4YN09vY2Ijhw4frz5w5YyeEEPPnz/dbvXp1kbkNqT/96U/6Xbt2cTf1baqiokLx8ssv+7zyyivnZs6ceVmr1Rrt7OxM06ZNq968eXPr2dKmpibZhAkTAtRqdUyPHj16Hzp0yME8b9myZV6+vr591Gp1THBwcO9//vOfzh1pe8+ePY5eXl6RO3fu1Fw7r76+Xnb06FHt6NGjW/eVqKiohkcfffRSdHS04drltVqt8dVXXy0ODQ1tVCgU4r777qvW6XQNP/zwg8O1ywohRFFRkV1cXJw+MDCwyd/fvz4/P9/21KlTtl9++aXLihUrLl67fHBwcJNWq23Zv3+/uiOvDe2Lj4+v2LZtW2t4fOedd9ymTJlySbrMxx9/7BQeHt7L0dExxsvLK/Kxxx7zMc8zD6dYu3ZtN29v74hBgwbdcLjc3r17nYYNG6a/0XKW5OXl2Q0cOLBWpVKZ7rzzzprCwkJbIYR49NFHdUuWLCk1X6mTGj58uH7Pnj0cWyGEICDDivTp06deoVCIiRMnBiQnJ2vLy8uv+5a/jIwMdWhoaH1lZWX64sWLSxcuXBhgNF49znl6ejbv2LEjT6/Xp23evLngmWee8T18+PB1v2xbWlrEPffc0yMiIqKupKQkc+/evbmbNm3y3L59u/baZYUQIjs7WxUSElJ/7fQRI0b0sLOzix05cmT4HXfcoR82bFjrMJDKykqlm5tblE6ni3jwwQd9a2pqLO5rERERhu+++05rMBhkBw8e1PTu3dvwz3/+09nV1bV59OjRVyyt06tXr/qcnJwuHeOJrnPgwAF1Y2OjfMaMGVXtLbdv3z7nqVOnVlVXV6eNGTPm8qJFi/zM83r06NHw/fff59bU1KQlJCQUX3sFxJKUlBTtzJkzg7Zt25Y3duzY60LLiRMn7GQymcl8GftmFRUVKQsLC+2joqKu29eEECI0NNTw2WefafPz820uXLhgFx4e3jB//nzfpKSkIjs7O4tXmYKDg+vT0tIsBm7cnMGDB9fW1tYqjh8/bt/c3Cy++OIL1wcffLBSuoyjo6Nxy5YtBdXV1WlffPHF6S1btrh/+OGHztJlDh065Jibm5v93XffnbpRm7m5uarevXtf1x90Ol2kp6dn5OTJkwNKSkosngWPjo42HD161LG2tla2d+9ebVhYWP2hQ4cc8vLy7ObOnVtpaZ2IiIj6kydP0l8ghCAgw4q4uroa9+/ff1Imk4lFixYFeHt7R48cObJHUVFR6wHQx8encenSpZeUSqWYP39+RXl5uc358+eVQggxderU6t69ezfI5XJxzz331A4ZMqTmwIEDjte2c/DgQXVlZaXylVdeKbG3tzf16tWrccaMGeXbtm1zvXZZIYSorq5WaDSa6842HDhwIK+mpibtk08+OT1q1KgaheJqno+MjDQcPXo05+LFixn79u3LzcjIcJg7d66vpW3Hx8dX+/v7N0RFRYVrtdqWBx54oPLFF1/0Wb9+/flFixbp+vXrFzp9+nS/+vr61svEWq3WWFNTw/0Dt6ny8nKls7Nzs6WrC1J9+/atnTJlSrVSqRSzZs2qyM3Nbf3FP2vWrKqAgIAmhUIhHn744Sp/f/+G77//vs0zrR999JHL4sWL/Xfs2HFqxIgRFu8HqKysVKjV6uv2k45oaGiQTZkyJWjSpEkVMTExFgPyunXrzm/evNk9Li6ux6pVq4r27dvn6OjoaAwJCWkcNWpUcP/+/UPfe+89F+k6jo6OLZcvX77uD238OvHx8RXvvvuu2+eff64NDg42BAYG/mLc8dixY/V33HGHQaFQiAEDBhjGjx9f+d133/3iasOqVauKtVqt8dphE5bo9XqFVqtt7VNeXl7NBw8e/PnChQuZ//nPf3Jqa2vl9957r8WbUfv3718/bty4qr59+4afP3/eNjExsXTJkiV+GzZsKHrhhRc8+vXrFzpu3LjAS5cutfYPJyenltraWvoLhBAEZFiZ2NjY+u3bt58tKyvLTE1Nzb548aLN/PnzW8Olu7t769kpc2itqalRCCFEcnKyNioqKszJySlao9FEHzx40OnSpUvXBckzZ87YlpeX22o0mmjzv/Xr13uXl5dbDJ3Ozs4ter3e4r5iZ2dnio+Pr9m3b5/2o48+chJCCD8/v+a+ffvWKxQKERYW1vjSSy+d//rrr50trS+Xy8XGjRsvnDp1Kmfbtm2FiYmJ3rNmzSr//vvv1WlpaQ4//fRTblNTk2z9+vWtw0VqamrkWq2Wm0huU+7u7s2XL19W3mgMp3RfcXR0NDY0NMjM62zYsMEtLCysl7n/5+Xlqdrq/0II8dZbb3nGxcVV9e/f32J4FUIINze3litXrtz075SWlhYxadKkQBsbG+P7779/rq3lQkJCGg8ePJiXk5Pz87Rp0y6vXLlS9/rrrxctWbKk+7333lu1e/fuvGXLlvmWlZW1Bpza2lqFs7Nzy83WBMseeuihis8++8z1gw8+6Hb//fdfd1/F/v371QMGDAhxcXGJ0mg00R999JF7RUXFL/rVzVxh0Gq1LdKrb05OTsZhw4bV2djYCF9f3+a33nrr3JEjR7RVVVUW+11iYuLF3NzcnF27dp3ZsmWLy6BBg/RGo1Fs2bLF/ciRI6fCwsLqn3nmGS/z8tXV1QpHR0f6C4QQBGRYsZiYmPpp06Zdys3NveFwgv/eER/8yCOPlF28eDFDr9enDx8+vNpkuv4kRUBAQKNOp2vQ6/Xp5n9XrlxJO3jwYJ6lbffp08dw+vRp+/bab2lpkeXl5Vl8FJZMJhNGo/GGNwr99NNPqtTUVMelS5eWZ2ZmqqKjo+vkcrno16/flczMzNb3ICcnx75Xr17XjenE7WHEiBFXbGxsjP/6179cbrz09U6dOmX72GOP+a9bt+5cVVVVul6vT+/Ro4fB0r5i9q9//St/9+7dzs8//3ybY/V79+7dIIQQBQUFHR6LbzQaxZQpUwLKy8uVu3btym9rqMS1nnzySe+ZM2eW+/r6Np88eVI1ePDgK25ubi1eXl6NOTk5rfthfn6+fUxMTJc+AeePJCQkpLF79+6N3333ndOMGTMuXzt/5syZgXffffflCxcuZOr1+vT777+//Np+JZfLO3zTdVhYmCEnJ6fNY6/5C69udHwtKipSfvDBB+4vvfRSSVpamiosLKzOzs7ONGDAgCvZ2dmtx9asrCz7sLAw+guEEARkWJG0tDT7xMREz/z8fBshhMjLy7NJSUlxi42NtTgWV6q+vl7W2Ngo9/DwaLKxsTElJydrjxw5YnFM8Z///OcrarW65emnn/aqra2VNTc3i9TUVPuDBw9aHHs2bty46qNHj7ZeJkxLS7NPTk7W1tbWyhoaGmQbN250TU1NdRw1alStEELs2LFDc+rUKVuj0Sjy8vJsnnrqKd2dd955ub36jUajWLBggd+6devOKRQKERgY2HD06FHH+vp62aFDhzRBQUEN5mUPHz6sueuuu6pv9J7gj8nNza3liSeeKP773//u9+GHHzrr9Xp5Q0ODLDk5WTt37tzuN1pfr9fLZTKZ8PLyahLi6s2teXl57f4R6uvr2/Ttt9+e2rx5s2dbz+G2t7c3DRo0SL9nz57WfcVoNIq6ujpZQ0ODTAgh6urqZNKnSkyfPt3v9OnT9t98801eRy65CyHEsWPH7I8cOaJ54oknyv9bW8Pu3bu1/x3DbBccHNwoxNWgXl1drRw5cuQNjx/ouPfff//sV199lSsd+mB25coVhaura4uDg4PpwIEDDp9//rnFYWsdNXr06OpDhw619qf9+/erMzIy7FpaWkRpaali7ty5fnfccYfezc2t3bO+CxYs8H366aeLNRqNsUePHg2ZmZnq6upq+f79+zX+/v6tw0QOHTqkufPOOzm2QghBQIa4+pxia9i+s7NzS2pqqnrQoEHhKpUqZvDgweFhYWGGjRs3Ft1oXRcXF+MLL7xw7m9/+1uwk5NT9NatW91GjRpl8UCnVCrFV199lZeZmakKCAiIdHV1jX7ooYcCqqqqLI49Gzp0aJ2jo2Pr3fAmk0m88MILPp6entHdunWL2rhxo+e77757ZujQoXVCCHHs2DGHoUOHhqnV6pihQ4eGh4eHG95+++02Lx0LcfXLGcLCwgx/+tOf6oQQYubMmVVeXl5N7u7uUVVVVcpHHnmkXAghCgsLbfLy8lTmZy7jFlM5dO7Qlg5u/7nnnit74YUXil566SVvDw+PKJ1OF7lx40aPSZMmtXvjnhBC9O3bt3727Nllw4YNC3d3d4/KyspSxcTE3PDZyT179mz89ttvc//7iCyLT4iZPXt2+datW1ufdHD69GlbtVod269fv95CCKFWq2N79OjRR4irZ7K3bdvmfvLkSQdvb+8oBweHGAcHh5g333yz3VA1f/58v7Vr1xYplVev3CclJV3YvHmzR2RkZO8lS5aU+vn5NQshxHvvvec6efLkSyqV6vf7vHAbdef2t1+x/d69ezdIb0iWWrNmzbl//OMfPmq1OmblypU+Y8eOvWF/bM+cOXMq9u/f71RbWysTQojTp0/b3X333SGOjo4xERERvW1tbY3/8z//c6a9bXz55Zeampoaxd/+9rfLQggxYsSIulGjRlX7+vpGHj58WJOYmFgixNU/3vbv3+80Z84ci4/kxO1H1t5lNfwxZWRknI2Kirp04yVh9umnn2o3btzo/u233+Z3ZR0PP/xw9+Dg4IaEhITyrqwDaEtsbGzY66+/Xmj+spCuYDAYZL169ep1+PDhXL704fdt4cKFOg8Pj6Znnnnmukf5/ZZWrVrlUVRUZLtp06Y2v1gmIyOjW1RUVEBn1gHrQUC+DRGQAQC4OQTk2wtDLAAAAAAJAjIAAAAgQUAGAAAAJAjIAAAAgAQBGQAAAJAgIAMAAAASBGQAAABAgoCM25ZMJut74sQJu66uoz3FxcXKwMDA3uZvkuoqP/74oyomJiasK2vAje3evdsxICCgT1fXcSsdO3bMvk+fPuFG43XffPybMhgMssDAwN7FxcXKTm3oD6KoqEjZr1+/ULVaHfPwww93NxqNYvLkyQFarTY6IiIi3NI6t+p4x2eJjqBzQBTuXxdlbDJ0Wl+Q26ia/UcuyejIsnv27HFMSEjonpeXZy+Xy0VwcHD92rVrzw0fPtziV5t2tp07d2oefPDBwLKyssyuaD8xMdHrvvvuq3B0dDQJIcTs2bO779mzx/nSpUs2Hh4ejUuXLi1duHBh61ej3nffff4//PCD5ty5c3avvfba2cWLF7f5tambNm1yXbFiRXdbW1vTpk2bzsbFxemFECI7O9tu+vTpgampqSfNX+c7YMAAg1arbd66davTtGnTLH6F9+3g5a9/jjI0tXTavqKyUTQ/8dfwG+4rOp0u4o033jj7//7f/9NLp9911121Z8+ePdFZ9Vmyfv16ty1btnQ7duxY7q1s12zZsmW6xYsXl8rlV8/3ODg4xEjnNzQ0yKdPn35xy5Yt131l/Q8//KCaMWNGUEVFhXLJkiWlzz77bNl/15H1798/9NNPP83v0aNHkxBCqFQq0/33338pMTHR6+23327z29Z+S3sKH49qMl7ptP5mI1c3j/Ff3W5/k76f9fX1cltbW5NcLjcJIcSaNWsK582bV2lpvfXr17u7uro26/X6NLlcLnbv3u34/fffa8+fP5+p1Wot/jVz7fHOrKysTBEWFtYnKCiovq1+Zu2fJX5/OIMM0Znh+Ga2X1lZKZ88eXKPuXPnXqyqqkq/cOFCxvLly4vt7e1vy697NBgMspSUFLcHH3ywNeSq1Wrj559/nldTU5P27rvvnl22bJnv3r171eb5kZGRdevWrSvs1atXu39QNDU1ieeee073n//85+dXXnnl3GOPPeZnnrdgwQLfNWvWFJnDsdnUqVMr3377bfff8CX+7nRmOL4V2/+jKSwstDl69Khm+vTpl83T6urq0sz/SkpKMuzs7IxTp06tsrR+QkKC7sUXXyw6fvx4zmuvveZ97tw5pRBCrFy50nPcuHFV5kBl9sADD1SmpKS4GQyGW3JFpzPDcUe3L30/vb29Gz/++OPT5p/bCsdCCHHu3DnbsLAwg/kPlzNnzth27969oa1wbOl4Z7ZkyZLuwcHB9e3Vae2fJX5/CMiwGidOnLAXQog5c+ZUKpVK4ejoaJo4cWLNgAEDDEJcPbM5cODAEGdn52gXF5eocePGBV66dElhXl+n00U888wzniEhIb00Gk30PffcE1RXV9d68FuxYoWnu7t7pIeHR+Rrr73mJm37k08+cQoODu6tVqtjPDw8Ip955hnPmpoa+eTJk3uWl5fbODg4xDg4OMScPXvWxmAwyGbNmuXr4eER6eHhETlr1ixf80F2586dGk9Pz8jExERPV1fXKHd398h169a1tmUwGGSzZ8/u7u3tHeHm5hY1bdo0v7YuJ3733XdqjUbTEhwc3HpgX7t2bXFMTEy9QqEQI0eOvNKvX7/aw4cPO5rnP/XUU+Xjx4/X29nZtXu9ubS0VOnp6dnk7+/fNH78+Jrz58/bCiHE+++/7+Lt7d00cuTIK9euc9ddd+l/+OEHLb9QrJe5/5l/1ul0EStWrPAMCQnppVKpYuLj4/2LioqUw4YN66lWq2MGDx4cUl5e3roP7du3Tx0TExOm0WiiQ0NDe+3cuVNjnrd+/Xq37t27R6jV6hidThfx5ptvuh4/ftz+8ccf909PT3d0cHCI0Wg00UIIUVFRoZgwYUKAi4tLlI+PT8QTTzzh3dLS0rqdvn37hs6ePbu7VquN1ul0EcnJyVpzOxUVFYr4+Hh/8766ePFin+bmZouvd8eOHdrevXvXOTg4WPwj+p///KeLq6tr85gxY2otzS8qKrKLi4vTBwYGNvn7+9fn5+fbnjp1yvbLL790WbFixcVrlw8ODm7SarUt+/fvV1va3u3kscce8xk/fnyg+efc3FxbmUzWt6mpSUyaNClg+/btbm+++aaXg4NDzOrVq7s9+uijAeZ+8uijj/pcuz1LxzshhNi7d6/65MmTqhkzZlxqrx4+S/zWCMiwGn369KlXKBRi4sSJAcnJyVrpL24hhDCZTOLJJ58sLSkpycjOzs4uLi62feKJJ35xoP3ss89cv/nmm9N5eXlZP//8s2rDhg3dhBAiJSVFu3HjRq+vvvrqVF5e3ol9+/ZppestXLjQ/4033ii8cuVKWnZ2dvbo0aP1Wq3WmJKSctrd3b3JfMYkICCg6amnnvI+duyYOi0tLSc9PT0nLS1NnZCQ4G3eVkVFhU11dbWipKQkc8OGDYUJCQl+5teycOHC7nl5efbp6ek5eXl5WaWlpbZPPvnkdb8shBAiPT1dFRQU1OZZk9raWllWVpZDRESE4Wbfax8fn+bLly8r8/PzbT7//HNtcHBwfVVVlfyll17yXrt2rcVLjoGBgU1KpdKUmZlpf7Ptoet8+eWXLvv27TuVk5Nz4ttvv3UeM2ZMz3/84x/ny8vL041Go0hKSvIQQoiCggKbSZMm9UxISCi5fPlyelJS0vnp06cHFxcXK2tqauTLli3z27Vr16krV66k/fvf/z7Zv3//utjY2PrVq1cXRkdH19bV1aXp9fp0IYR46KGHfGtqahRnzpzJOnDgQG5ycrLb+vXru5lrysjIUIeGhtZXVlamL168uHThwoUB5jHEU6dODVAqlSI/P/9EWlpazoEDB5zWrl3bzdJry8rKUrV3ZvGjjz5yi4+PrzCfxbxWaGio4bPPPtPm5+fbXLhwwS48PLxh/vz5vklJSUV2dnYWQ3dwcHB9WlqaQwff/tvS9u3bz44fP75y3rx5pXV1dWmPP/74JWk/Wbt2bfG161g63jU3N4slS5b4vf766+dksvb/LuezxG+NgAyr4erqaty/f/9JmUwmFi1aFODt7R09cuTIHkVFRUohhOjTp0/DhAkTalQqlcnHx6d5yZIlZT/88INGuo158+aVBQQENHl6eraMHj26Oj09XSWEEJ988onrlClTLvXv379eq9UaV61a9YsDtFKpNGVlZdlXVlbK3d3dW4YOHdrmEIXt27e7Llu2rESn0zX7+Pg0L1++vDglJcVNuq3Vq1cX29nZmaZMmVKtUqmMmZmZ9kajUWzdurXb66+/XuTp6dni4uJiXLZsWcnnn3/uaqmdy5cvKxwdHds8E/y3v/3NPywszDBx4sSajr3D/0uhUIh169YVTpw4MXjt2rVeb7311tnHH3/cZ86cORdTU1MdBgwYEDJ06NCeqampvwjDDg4OLZWVlYq2tgvrM3fu3Iu+vr7NgYGBTf3796+NiYm5MmTIEIODg4MpLi7uckZGhoMQQrzzzjtuf/7zn6unTJlSrVAoxIQJE2r69OlzZfv27U5CCCGTyUxpaWmq2tpamb+/f1O/fv0sBtPm5maxc+dO15dffvmCi4uLMTQ0tHHBggWl27Zta91HfHx8GpcuXXpJqVSK+fPnV5SXl9ucP39eWVRUpDx48KDTW2+9dU6r1Rp1Ol3zwoULy1JSUizuI9XV1QqNRtNiad6pU6dsU1NTNbNnz27zzOO6devOb9682T0uLq7HqlWrivbt2+fo6OhoDAkJaRw1alRw//79Q9977z0X6TqOjo4tly9fZh/4jVk63q1atcojJibmyp/+9Kcb3oPCZ4nfGuPdYFViY2Prt2/fflYIIdLS0uxnzJgROH/+fN8dO3YUFBUVKefNm+f3008/OdbV1SmMRqPQarW/+OXo4+PTennOwcHBWFJSYiOEEKWlpTaxsbGtwwZ69uzZKF3v448/zl+5cqX3888/3z00NNTwj3/84/xf/vKX64YZCCFEeXm5bXBwcIP556CgoMaLFy/amH92cnJqtrFp/VGoVCqjXq+Xl5SUKOvr6+UDBw78xR3cLS0tFk+NuLi4tNTW1lr8I3bOnDndc3NzVd9//31uW2fHbmT8+PH68ePHnxTi6g0u6enp6k2bNp3v3r175KFDh04WFBTYPvTQQwEZGRknzevU1dUpXF1dLQYSWCdvb+/WfcLe3t7o6enZOl5BpVIZ6+rqFEIIUVhYaPv111+7aDQaJ/P85uZm2bBhw/Rardb4wQcfnFmzZo3nokWLAvr27Vu7du3a8zExMdeF5JKSEmVzc7NMuo8FBgY2lpWVte4U7u7urTVpNBqjEELU1NQoysvLFc3NzTJvb+8o83yTySTz8vL6xf5q5uzs3KLX6y0GnHfeecctNja2NiwszOK6QggREhLSePDgwTwhhNDr9fJ+/fqFffvtt6fmzJnjd++991bde++91b179+59zz331Hh6erYIIURtba3C2dmZfeA3du3x7uzZszZvvfWW5/Hjx3M6sj6fJX5rnEGG1YqJiamfNm3apdzcXJUQQixdulQnk8lMWVlZ2bW1tWmbN28uMJk6dv+ep6dnU1FRka3557y8PFvp/OHDh9ft27cvv7y8PGPs2LFV06dPDxbi6lmza7fl7u7emJ+f3/p4uIKCAlsPD4+ma5e7lpeXV7O9vb0xMzMzW6/Xp5v/1dXVpbXx+g0FBQXXDWd49NFHffbv3++0b9++U66urv/nZ1sZjUaxcOFCv/Xr158rKSlRGo1GERIS0jhs2LArp06dUpmXKygosGlqapJFRka2e7MMfp98fX0bJ0yYUCHtmwaDIe3FF18sFUKISZMm1fz73/8+XVJSktGzZ8/6hx56yF8IIa699O3t7d2sVCpNp0+fbt3Hzp49a+vp6XnDfSQoKKjJ1tbWVFlZ2VpDbW1tWl5eXral5SMjI+uk+6JUcnKy2/3339/uuFWpJ5980nvmzJnlvr6+zSdPnlQNHjz4ipubW4uXl1djTk5Oaxv5+fn2MTExXfJUHWuiVqtbDAZDa4Y4f/68TXvL38i1x7vvv/9efenSJZvevXv36datW9TTTz/tm5mZqe7WrVtUW2PSzfgs8VsgIMNqpKWl2ScmJnrm5+fbCCFEXl6eTUpKipv5zG9tba1CrVYb3dzcWgoKCmzWrl3r1dFtx8fHVyYnJ3c7duyYvV6vly9fvrx13G99fb3szTffdK2oqFDY2dmZtFqt0fwYIx8fn+bq6mplRUVF61mqCRMmVCYlJXkXFxcrS0pKlKtWrfKeNGlSm49TM1MoFGLq1KmXFixY4HvhwgWlEFdD5/bt27WWlh8+fPiVmpoaRUFBQesvnqeeesrr008/dd23b1+ul5fXdWc+6uvrZXV1dTKTySRramqS1dXVycw3R7Vl7dq13SIjI+sGDx5s8PLyaq6vr5cfO3bMfteuXZru3bu3nn3bs2ePZuDAgTUqleq2fKqItWlsbJTV1dW1/mtqumH+bNeDDz5Y8e233zpv375d29zcLOrq6mQ7d+7U5Ofn2xQVFSn/9a9/OdfU1MhVKpXJ0dHRaL5y4e3t3VRaWmpbX18vE0IIpVIp7r777qqEhARdVVWV/NSpU7ZvvPGG59SpU2+4j/j7+zcNGTKkevbs2b6VlZXylpYWkZ2dbbdr1y5HS8vHxcXV5OTkqKU34wpx9causrIym5kzZ1p8esW1jh07Zn/kyBHNE088US6EEL6+vg27d+/WFhUVKQsLC+2Cg4Mbhbi6v1ZXVyst3cR6u4mNjTWkpqY6nj592raiokLx4osvdvh4bMm1x7vJkydXnz59OvP48ePZx48fz37iiSeKw8PD644fP5597RN2pPgs8VshIEPIbVTt/zl+i7bv7Ozckpqaqh40aFC4SqWKGTx4cHhYWJhh48aNRUIIsXLlyuKsrCwHrVYb89e//rVnXFxch375CSFEfHx8zezZs8vGjBkTGhwc3GfEiBG/GLe7detWt8DAwAhHR8eYd9991/39998vEOLqWey4uLjK4ODgCI1GE3327FmbpKSkkqioqCtRUVG9IiMje0VERNQlJSWVdKSON95443xQUFDDgAEDwh0dHWNGjRoV8vPPP1u86c3e3t4UHx9f8c4777SO3UxKStKVlJTYhoWFRZifrJGQkND6i2nYsGEharU6Ni0tTf33v//dX61Wx3799dcaS9sX4url8E2bNnmuWbPmghBC2NjYiJdeeuncmDFjQpcsWeL/6quvnjMv+/HHH7vOnj27vCOv849KZaPo1H3lZrY/ZcqUnmq1Otb8b+nSpRZv9uyoHj16NCUnJ+clJSV5u7m5Ret0ushXXnnF02g0yoxGo2zdunWeOp0u0tnZOfrIkSOaTZs2FQohxNixY/U9e/Y0eHp6Rrm4uEQJIcQ777xzzsHBwRgUFBQxbNiwsEmTJlUuWbKkQ2dzk5OTzzY2NsrCw8P7ODs7R0+ePDn4woULFs9O+vr6Ng8aNKjmo48+cpZOf//997uNGTPmsouLS4eusMyfP99v7dq1rY82TEpKurB582aPyMjI3kuWLCn18/NrFkKI9957z3Xy5MmXbtUfiTZydaf2t//L9idMmFAzduzYqtjY2F4xMTHhd9999//p+ejXHu9UKpXJz8+v2fzPycmpxcbGxmT+LNpirZ8lfn9kHb1EjT+OjIyMs1FRUR2+9IiuU1xcrBwyZEhoVlZWzrUPz7+VfvzxR9WcOXP809PTT954aeDWOXbsmP3MmTMDMzMzf/614/E7wmAwyHr16tXr8OHDuTqdrlOD6+3qVh3vfu1nmZGR0S0qKiqgs+qCdSEg34YIyAAA3BwC8u2FIRYAAACABAEZAAAAkCAgAwAAABIEZAAAAECCgAwAAABIEJABAAAACQIyAAAAIEFAxm1LJpP1PXHihF1X1/Fr3XHHHaGvvvpqt1+z7qRJkwIWL178q795LS4uLvDDDz90/rXr/1YiIiLC//Of/1j8JkIAAH6ttr/QHLeN77/Lj2puMnZaX1DayJv/9OfgjI4su2fPHseEhITueXl59nK5XAQHB9evXbv23PDhw+s6q7727Ny5U/Pggw8GlpWVZd6qNp966ikvvV6v2LBhw4WEhASvDz/80L2qqkqp0Wha+vXrV7tr164zt6oWS3788UdVTk6Ow/33318ghBCFhYU2DzzwgP+JEyccysvLbU6ePJkVGhraKF3n888/1yxbtqx7QUGBvVarbVm1alXRQw89dN1Xhefl5dlMmjQp+OzZs/bx8fGX3n777fPmecOGDev5wgsvXBg2bFhrX1iyZEnp008/rduzZ09+Z75mAMDthTPIEJ0Zjm9m+5WVlfLJkyf3mDt37sWqqqr0CxcuZCxfvrzY3t7+tvq6x2+++cYpLi6u+vXXX3dLTk52++abb07V1dWlpaam5owYMaKmq+vbsGGD++TJkyvMX+srl8tNo0ePrt66davFkHrs2DH7WbNmBT377LMXampq0jIyMrIHDRpk8Q+eZ5991nvatGkVZ86cydq9e7fzoUOHHIQQ4u2333bx8/NrkIZjIYSYNm3a5aNHj2rOnTvHH/sAgN8MARlW48SJE/ZCCDFnzpxKpVIpHB0dTRMnTqwZMGCAQQghsrOz7QYOHBji7Owc7eLiEjVu3LjAS5cuKczr63S6iGeeecYzJCSkl0ajib7nnnuC6urqZOb5K1as8HR3d4/08PCIfO2119ykbX/yySdOwcHBvdVqdYyHh0fkM88841lTUyOfPHlyz/LychsHB4cYBweHmLNnz9oYDAbZrFmzfD08PCI9PDwiZ82a5WswGGRCXD3j7OnpGZmYmOjp6uoa5e7uHrlu3brWtgwGg2z27Nndvb29I9zc3KKmTZvmV1tb21pjeXm5oqCgwH7UqFG1qamp6j//+c81vXv3bhBCCD8/v+a///3vv/iK8MLCQtvY2NgwtVodM2TIkJ4lJSWtQfGvf/1rULdu3aI0Gk10v379QtsbirBt2zansLCwXhqNJjomJibsxx9/VLW17IEDB5xGjhxZa/7Z19e3OSEhoXz48OFXLC3/7LPPes+YMaM8Pj6+xsbGRnh5ebWYX9O1zp07Z3fXXXfVuLm5tURFRdWdOnXKrrKyUv7qq696v/baaxeuXd7BwcHUu3fvK1988YVTW/UCAHCzCMiwGn369KlXKBRi4sSJAcnJydry8nKFdL7JZBJPPvlkaUlJSUZ2dnZ2cXGx7RNPPPGLcbSfffaZ6zfffHM6Ly8v6+eff1Zt2LChmxBCpKSkaDdu3Oj11VdfncrLyzuxb98+rXS9hQsX+r/xxhuFV65cScvOzs4ePXq0XqvVGlNSUk67u7s31dXVpdXV1aUFBAQ0PfXUU97Hjh1Tp6Wl5aSnp+ekpaWpExISvM3bqqiosKmurlaUlJRkbtiwoTAhIcHP/FoWLlzYPS8vzz49PT0nLy8vq7S01PbJJ5/0kdSvHTJkSI1SqRQDBw6s3b59u9uKFSs8Dx065NDc3Hzde/bpp5+6fvDBBwVlZWXpTU1N8ueff97TPG/MmDHVp0+fzrp48WJGZGRk3fTp04Msve9HjhxRLViwIGDjxo2FVVVV6bNmzSqfOHFiD3Pol6qpqZFfuHDBNiIiov4GH2ertLQ0RyGECAkJ6eXu7h45fvz4wLKyMoWlZcPDww27du3SXrp0SZGVleUQHR1tWLp0qW7+/Pll3bp1a7G0TkhISH1GRkabgR4AgJtFQIbVcHV1Ne7fv/+kTCYTixYtCvD29o4eOXJkj6KiIqUQQvTp06dhwoQJNSqVyuTj49O8ZMmSsh9++EEj3ca8efPKAgICmjw9PVtGjx5dnZ6erhJCiE8++cR1ypQpl/r371+v1WqNq1atKpaup1QqTVlZWfaVlZVyd3f3lqFDh7Y55nn79u2uy5YtK9HpdM0+Pj7Ny5cvL05JSXGTbmv16tXFdnZ2pilTplSrVCpjZmamvdFoFFu3bu32+uuvF3l6era4uLgYly1bVvL555+7mtf96quvnO+6665qIYSYP39+ZVJS0rl9+/Y5jRkzJrRbt27RTz/9tJe0lvvuu68iMjKy4b9n2yuzsrIczPMeeeSRChcXF6NKpTK9/PLLxbm5uaqKiorrgunGjRvdZ8yYUT5y5MgrSqVSLFq0qMLGxsa0f/9+9bXLmtd3dna2GFYtKSsrs0lJSXFLSUnJz8vLO1FfXy9/+OGH/Swtu3LlypIjR45ohgwZEjpr1qyLDQ0NspycHFV8fPzluLi4wH79+oW++OKL7tJ1NBqNsbq6miEWAIDfDAEZViU2NrZ++/btZ8vKyjJTU1OzL168aDN//nxfIYQoKipSjh07NsjDwyPS0dExZs6cOYFVVVW/CEY+Pj5N5v87ODgYr1y5ohBCiNLSUhtfX9/WG8d69uz5i5vIPv744/zdu3c7BQQERPbv3z/022+/vS4cmpWXl9sGBwe3DhEICgpqvHjxoo35Zycnp2Ybm9YfhUqlMur1enlJSYmyvr5ePnDgwHCNRhOt0WiiJ0yY0NP8GlpaWsThw4e1EyZMaB1nPG/evMp///vfpy5fvpz+6quvFr788ss+27dvbz377eXl9YvXW1dXJxdCiObmZjF//nydr69vH0dHx5jAwMCI/74P1wXJ8+fP27711lue5po0Gk10WVmZzfnz522vXdbNza1FCCEuX75s8QywJfb29sapU6deioyMbHBycjIuX7685LvvvrM4JMLT07Nl165dZ3Jzc3OWL19+cfHixX4bNmw4l5iY6N27d2/D4cOHT73//vsex48fbx0uotfr5U5OTtefXgcA4FciIMNqxcTE1E+bNu1Sbm6uSgghli5dqpPJZKasrKzs2tratM2bNxeYTB27f8/T07OpqKioNfDl5eX9IvwNHz68bt++ffnl5eUZY8eOrZo+fXqwEELIZLLrGnB3d2/Mz89vfTxcQUGBrYeHR9O1y13Ly8ur2d7e3piZmZmt1+vTzf/q6urShBDi4MGDah8fnwYfH5/rwp6dnZ1p1qxZVaGhoYbMzMwbDifYvHmz6+7du5337t17qqamJq2goCBLiKvDVK6l0+maFi9eXCKtyWAwpM2ZM6fy2mW1Wq3R19e34WYejxcSEmKQyf53tIb0/+1Zs2aNe9++fa/079+/PicnR3XHHXfU2dvbm8LCwuqOHz/e+h6cOnXKPioqytDRegAAuBECMqxGWlqafWJiomd+fr6NEFcf+ZWSkuIWGxt7RQghamtrFWq12ujm5tZSUFBgs3btWq/2t/i/4uPjK5OTk7sdO3bMXq/Xy5cvX9467re+vl725ptvulZUVCjs7OxMWq3WKJfLTUII4ePj01xdXa2UDk2YMGFCZVJSkndxcbGypKREuWrVKu9JkyZV3KgGhUIhpk6demnBggW+Fy5cUAohREFBgY35jPCOHTucRo8eXW1efv369W4ff/yxU1VVlbylpUUkJydr8/LyVIMHD7Z4M5yUXq9X2Nramjw8PJpra2vljzzyiK6tZefOnVu+ZcsWj/3796uNRqOoqamRm9u1tPzIkSOr9+/f/4uhLXV1dTKDwSAX4ur7Kb05csaMGZe2bdvWLScnx1av18tffPFFr5EjR1Zfu12pCxcuKN955x331atXFwshhJ+fX8O+ffs01dXV8szMTHVISEiDud3s7Gx1XFxclz/dAwDwx0FAhlDayDv18nRHt+/s7NySmpqqHjRoULhKpYoZPHhweFhYmGHjxo1FQgixcuXK4qysLAetVhvz17/+tWdcXNx1z9FtS3x8fM3s2bPLxowZExocHNzn2selbd261S0wMDDC0dEx5t1333V///33C4S4ehY7Li6uMjg4OEKj0USfPXvWJikpqSQqKupKVFRUr8jIyF4RERF1SUlJJR2p44033jgfFBTUMGDAgHBHR8eYUaNGhfz888/2Qgixd+9ep3HjxrUGRycnp5aXX37Z29/fP9LJySlm+fLl3V9++eXCMWPG1LbdwlXz5s2r0Ol0Db6+vlFhYWG9Bw4c2GaoHjZsWN2GDRvOLl682M/JySk6ODi4z5YtW9zaWn7BggXlKSkpbkajsXWaWq2OdXJyihFCiOjo6D5qtTrWPO+RRx6piI+PrxgyZEi4n59fhK2trentt98+1179Cxcu7J6QkFDi5ORkFEKIZ599tuT777/X+Pr6Rt55552XzY97+/jjj50HDBigDwgIuOEZfAAAOkrW0UvU+OPIyMg4GxUVdenGS+JWKSoqUvbt27dXaWlppvn5wtYsLi4uMD4+vmrGjBmXu7KOyMjIsHffffds//79O/xUDQD4NTIyMrpFRUUFdHUduDW48xuwAlVVVYrnn3/+/O8hHAshxI4dOwq6ugYhhMjMzDzZ1TUAAP54CMiAFYiMjGyIjIy0+OUZAADg1vp9nK4CAAAAbhEC8u3JxNhzAAA65r+/M403Wg5/HATk25BMJqtubGy0ufGSAACgsbHRRiaTtft4SvyxEJBvQy0tLe8XFxerjUZjx76xAQCA25TRaJQVFxc7trS0fNDVteDW4Sa925DRaHyzpqYmNisra6gQosNfGQwAwG2oRQhx2Gg0vtnVheDW4TnIAAAAgARDLAAAAAAJAjIAAAAgQUAGAAAAJAjIAAAAgAQBGQAAAJD4/wGitFUIgAwNvQAAAABJRU5ErkJggg==\n", "text/plain": ["<Waffle size 720x720 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plot_labels = [f'{i} ({str(j)} %)' for i,j in zip(lith_data_df.LITH, \n", "                                                    lith_data_df.Well2)]\n", "\n", "fig = plt.figure(FigureClass=Waffle, figsize=(10,10), rows=5, columns = 20, \n", "                 values=list(lith_data_df['Well2']),\n", "                 colors=colours,\n", "                 labels=plot_labels, \n", "                legend={'loc':'lower center', 'bbox_to_anchor': (0.5, -0.8), \n", "                        'ncol':3, 'fontsize':12}, rounding_rule='ceil')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 97, "id": "c72d8ff1-91a9-47b3-b175-b1d5e9f92519", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAsgAAACRCAYAAADetU5gAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/YYfK9AAAACXBIWXMAAAsTAAALEwEAmpwYAAA00ElEQVR4nO3de1jT5f8/8NfebMA4y2mcFFAYY4BLLSpF/YBlWIT2IfKQIIplptU3NOngR1Oz8EAWllmmJZmmefh4DjMxUcvCjBhjAnJwDlBQYcAOsMPvj5y/OU76kRnq83FdXde29/2+n+/3entdL2/v3TfLYDAQAAAAAAD8jfmnLwAAAAAAoDdBgQwAAAAAYAIFMgAAAACACRTIAAAAAAAmUCADAAAAAJhAgQwAAAAAYILd1cEPxb/WEhHPQtkX08If8UJWz2VZOO++yarIybBYVuATb96prHZ592pW7o+lFs2Kfjz4ns8iItIummOx5569MPMfy1q0R2yxrIXx4f9Yln5/qkWfD+ap9dfz9lW8bLGsuMA1LEv0C3C7uhtBttQfvo76RtbtZ1kyD1l3V1ZHfSMLWV31fS8898iyTJ4lswB6JUyxAAAAAAAwgQIZAAAAAMAECmQAAAAAABMokAEAAAAATKBABgAAAAAwgQIZAAAAAMAECmQAAAAAABMokAEAAAAATKBABgAAAAAw0eVW0wAAAAB30unTpz3ZbPaXRBROGMgDy9ETkVir1U4fMmTIJfODKJABAACg12Cz2V96eXmFenh4XGUYxvBPXw/cm/R6Pauurk5YW1v7JRHFmx/H38wAAACgNwn38PBQoDgGS2IYxuDh4dFIf/9LRfvjd/h6AAAAALrCoDiGO+Hac9ZhLYwCGQAAAADABOYgAwAAQK+1pjhfpNZpe6xesbVia18OfbCgqzbp6eleO3bscGMYxsAwDK1Zs6YqJiamxdfXNyI/P7/Y29tbezNZ+/btc8zMzOTl5uaW3ez1VVVVcVJSUvxzc3PLcnNz7WbOnBlARGQwGOidd96pTk5ObiAi8vX1jbC3t9cxDENsNtsgFouLzftaunSp59dff+3h4+OjycnJOWdra2vIyclx2LZtW5/169fLiIiqq6vZ48ePD8zLyyu92Wu8H6BABgAAgF6rJ4vjm+nv8OHD9jk5OS6FhYUSLpdrqKmpYWs0GlZPXkNX3n//fV5qamo9EdGDDz6oLiwslHA4HKqqquIMGjRIOHHixAYOh0NERD///HNJV8X6tm3bXKVSadFbb73lvXPnTqcJEyY0LlmyxHvHjh3lxjY+Pj5aHo/XdujQIfvRo0e3WPwG7xKYYgEAAABwjVwu57i6umq5XK6BiMjb21sbEBDQZjy+fPlyT6FQGMrn84VnzpyxJSLKzc21e+CBBwShoaHCQYMGCQoKCmzM+1UoFExiYmJAREREaGhoqHDTpk0uHeXv37+/T0JCQiMRkaOjo95YDKtUKhaLdWt1usFgoNbWVpZSqWSsra0Nn332mevo0aMbeTyezrTduHHjGrKzs91uqfN7HApkAAAAgGvGjRunqK6utg4ICAifPHlyv/379zuYHnd3d9dKJJLiadOm1WVkZPCIiEQikfr333+XFhcXSxYuXCifN2+en3m/b7/9tnd0dLSisLCwOC8v7+z8+fP9FArFDXWYVCq1dnZ2vl6cExEdOXLEPigoKGzw4MFhq1atqjIWzEREo0aNCg4LCwtduXKle0f3MmPGjEuDBw8Olclk1qNGjWr+5ptv3NPT0+vM2w0bNqzlt99+c+ioj/sVCmQAAACAa5ydnfVisVjyySefVHl4eGinTJkyICsr6/ro6qRJk64SEUVGRiplMpkNEdGVK1esnnzyyQHBwcFh8+bN61tSUmJr3u/Ro0edVq1a5S0QCIRRUVEhGo2GVVZWZm3aRiaTcVxdXW+YMhETE9NSVlZWdPz48eIVK1Z4K5VKFhHR8ePHpRKJpPjQoUOl69at8zx48GC7AnfWrFlXiouLJbt3765YunQpb9asWZe2b9/uFBsb2z81NbWvTvf3QLKPj4/20qVL1ubn389QIAMAAACYYLPZFBcX17Rq1arqFStWnP/vf//bx3jM1tbWcK2NQavVsoiI0tPTfUeOHNlUWlpatHfv3rLW1tZ29ZXBYKDt27eXSaVSiVQqldTU1BQOHjxYbdrGzs5Or9FoOqzNBg8erLa3t9fl5+dziYgCAwPbiIh8fX21Tz31VMMvv/xi39n9VFZWck6fPm2flJTU8NFHH3nt27ev3MXFRbtnzx4nIiKlUsmysbHR3/o3de/qrkC+aMFs876RdftZlsxD1t2V1VHfyEJWV33fC889siyTZ8msXqegoMCmsLDw+hziM2fOcP38/Fq7OkehUFgZ23z++ecdTneIjo5WZGZm8vT6v+vQEydOcM3bREREaORy+fWRXKlUat3W9vf055KSEuvy8nLb4ODgVoVCwVy9epW5ls3k5uY6DRw4UNXZ9c2bN89nyZIl1UREarWaYbFYxDAMtbS0MEREYrHYls/nd3r+/YhlMGAtbgAAAOgdCgoKKkUiUb3x/Z1e5i0vL8/u1Vdf7adQKKysrKwMAQEBmo0bN1Z5e3trTZd5O3bsmN3cuXP7/vbbb2cPHz5sP3369EAul6t//PHHG3bs2OEml8sLTZd5a25uZr344ov98vPz7fV6Patv376ajpZ/e/TRR/nr1q2rCg8P13z66aeuq1at8maz2QaGYQxvvfVWTVJSUoNEIrF+5plngoiIdDodKyEh4fKyZctqO7qfEydOcD/++GPPbdu2VRERLV682DM7O9vD29u79dChQ2VcLtewYMECno2NjeGdd9651FPf892ioKDAXSQSBZh/jgIZAAAAeg3zAvl+k52d7ZKfn2+XlZVVfacyH3zwwZCDBw+WeXh46LpvfW/prEDGOsgAAAAAvURycnJDfX39HavPqqur2a+99trF+7E47gpGkAEAAKDXuN9HkOHO+p9GkD8U/1pLRDwLXdPFtPBHvJDVc1kWzrtvsipyMiyWFfjEm3cqq13evZqV+2OpRbOiHw++57OIiLSL5ljsuWcvzLwvshbtEVssa2F8+A1Z+v2pFn0+mKfWX8/bV/GyxbLiAtfcsR3qAG5Fd6tYWOoPX0d9I+v2syyZh6y7K6ujvpGFrK76vheee2RZJs+SWQC9EtZBBgAAAAAwgQIZAAAAAMAEVrEAAACAXqvqyMcifZuqx+oVhsPV+se81uk6yERE6enpXjt27HBjGMbAMAytWbOmKiYmpuV2syMjI0NWrlwpGzFihLKj41lZWW7x8fGKgICAttvN6kpVVRUnJSXFPzc3t2zXrl1O8+fP921ra2NxOBzDBx98cCE+Pr6JiOiVV17x/f77790UCoWVUqk801FfX3/9tct7773n6+zsrN2/f3+Zl5eXrqioyGbevHm++/fvLyciUqvVrKioKP4vv/xylsPhWPLWegxGkAEAAKDX6sni+Gb6O3z4sH1OTo5LYWGhpKSkRJKbm1vSv3//LnfS6ymbNm1yP3/+vMUryPfff5+XmppaT0Tk6enZtn///rKSkhLJ119/XTF9+vRAY7tx48Y1nDp1qrirvtasWcM7c+ZMcWpqat369evdiIjefPNNn4yMDLmxja2trWHkyJGKL7/80tVS99TTUCADAAAAXCOXyzmurq5aLpdrICLy9vbWGkd0586d6x0eHh4aHBwcNnHiRH/jttGRkZEhM2fO9I2IiAgNCAgI/+GHHxyIiJqbm1lxcXH9+/fvH/b4448PUKvVLCIirVZLCQkJAcHBwWF8Pl+4aNEiz6+++qqPWCy2S05O7i8QCITNzc2s3bt3O4aGhgr5fL4wMTExQKVSsYiIfH19I15//XUfoVAYyufzhWfOnLEl+nvb6cTExICIiIjQ0NBQ4aZNm1w6usf9+/f3SUhIaCQiGjZsmMp4f0OGDFFrNBrGmDNq1KgWf3//LkezGYYxqNVqllKpZDgcjuGHH35w8PT01EZERGhM2z377LMN3333HQpkAAAAgLvNuHHjFNXV1dYBAQHhkydP7rd//34H47E33njjklgsLi4tLS1SqVTMd99952w8ptVqWYWFhcXLli2TLV682IeIaOXKlZ5cLldfXl5e9N5771VLJBJ7IqJffvnFrqamhlNaWlpUUlIimTVr1uWpU6deDQ8PV2ZnZ5dLpVIJwzA0Y8aMwK1bt54rKSmRaLVaWrFihYcxz93dXSuRSIqnTZtWl5GRwSMievvtt72jo6MVhYWFxXl5eWfnz5/vp1Aobqj1pFKptbOz8/W/AJjauHFjn7CwMGVHxzqTnp5e+69//Yt/4MABl9TU1CtLlizxfv/999vtAvjQQw+p/vrrL/ub7fefhgIZAAAA4BpnZ2e9WCyWfPLJJ1UeHh7aKVOmDMjKynIjIjp48KDjwIEDBXw+X3jy5ElHsVjMNZ6XmJh4lYho6NChLRcuXLAmIjp+/LhDUlLSZSKihx9+WMXn85VERAKBQCOTyWymTJnSd/v27U59+vRpt4tdQUGBrZ+fn2bgwIEaIqKUlJTLx48fdzQenzRp0lUiosjISKVMJrMhIjp69KjTqlWrvAUCgTAqKipEo9GwysrKrE37lclkHFdXV615Xn5+vu2CBQt8161bV3Ur39czzzyjKCoqKj5y5EjZli1bXJ544olGsVhsGxsb23/ChAn+TU1NDBERm80mDodjuHr16l1Re+JHegAAAAAm2Gw2xcXFNcXFxTUNHDhQ9c0337hNnz79ypw5c/xPnTolCQoKaktLS/NRq9XXiz1bW1uD8VydTtflBigeHh46sVgs2bVrl9PatWs9tm7d6vr9999X3so1muQZtFoti4jIYDDQ9u3by0Qikaaz8+zs7PQajeaGIvXcuXOcZ599Nmj9+vUVYWFhnZ7blaamJmbTpk1uP//8c+moUaOCDhw4cG7jxo19vvjiC9c5c+bUExG1tbWx7Ozs7ootnO+KKh4AAADgTigoKLApLCy0Mb4/c+YM18/Pr1WpVDJERF5eXtrGxkZm7969fbrrKyoqqvnbb791JSL6/fffbUtKSuyIiGpqatg6nY5SUlIaPvjgA3lhYaEdEZGDg4OusbHRiohIJBKp5XK5tVgstiEiys7Odhs+fHhTV3nR0dGKzMxMnnFu9IkTJ7jmbSIiIjRyufz6qHJ9fb3Vk08+Gbxo0aILo0eP/p9X6nj33Xd5s2bNumRjY2NQq9UMi8UihmEMxu+ttrbWysXFRWtjY4MCGQAAAOB2MBxuu+kAluxPoVBYJScnBw4YMCCMz+cLpVIpd9myZdXu7u66559/vi40NDQsOjqaLxKJui0m586de6mlpcWqf//+Ye+8846vUChsISKqrKzkREVFhQgEAmFSUlL/xYsXXyAiSk5Orn/llVf8BQKBUK/X09q1aysTExMH8Pl8IcMwNHfu3Lqu8jIyMqq1Wi1LIBAIg4KCwubPn+9r3sbJyUnfr18/jbHwXr58uef58+dtPvjgAx+BQCAUCARCuVzOJiJ66aWX/Hg83kC1Ws3weLyBaWlpPh3lVlZWck6fPm2flJTUQET08ssvXxo0aFDo+vXrPVNTU68QER08eNDpsccea+zuO+stMMUCAAAAeq3u1izuacOHD1eeOXNG2tGxrKys6qysrHY/QPvtt9/OGl97e3tr5XJ5IRGRg4ODYd++feUd9SWRSNotn5aSktKQkpLSYHw/duzYprFjx0rM2xn7JyIaMWKE0pjv4OBg2Lx5c7dziGfOnHnpiy++cMvKyqpevnx5zfLly2s6ard27doLa9euvdBdfwEBAW1Hjx4tM76fNm3a1WnTpl01bbNlyxbXlStXdttXb4ECGQAAAOA+kpyc3FBfX3/HakC1Ws2Kj49vMP7g8G6AKRYAAAAA95m0tLT6O5Vla2trmD179uU7ldcTUCADAAAAAJhAgQwAAAAAYAIFMgAAAACACRTIAAAAAAAmsIoFAAAA9Fp5R8+JtG36HqtX2BxGO/xfA7pcOi49Pd1rx44dbgzDGBiGoTVr1lTFxMT8z5toGEVGRoasXLlSNmLECGVHx7Oystzi4+MVAQEBbbeb1ZGwsLDQ/Px86eeff+66Zs0aHhGRwWBgLVy4UD558uSG7q7P3NmzZ63j4uKCS0tLi272Gpqbm1nR0dH8X3755Sybzabhw4cH//nnn/YPPvhgc25u7vWl4vR6Pb322mu+e/fu7cMwjGHatGl18+fPv2Ta16FDh+xnz57tb21tbdiyZUt5RESEpr6+3mrs2LH9jx07VmplZUVEREOHDuXv3r37nIeHR7stvTuDAhkAAAB6rZ4sjm+mv8OHD9vn5OS4FBYWSrhcrqGmpoat0Wi63Dq6p2zatMn9gQceUFmiQJZKpdY8Hq+turqanZmZ6f3nn38Wu7m56RobG5mampo7Vg+uXr3aPT4+/iqb/Xfk3Llza1taWph169Z5mLVzu3DhAufcuXNiKysrMm5eYmrlypVeBw8eLC0rK7PJysryWLdu3YW3337b+6233qo1FsdERBMnTry8cuVKj2XLltXe7HViigUAAADANXK5nOPq6qrlcrkGor83/jAWrHPnzvUODw8PDQ4ODps4caK/cUvnyMjIkJkzZ/pGRESEBgQEhP/www8ORH+PlsbFxfXv379/2OOPPz5ArVaziIi0Wi0lJCQEBAcHh/H5fOGiRYs8v/rqqz5isdguOTm5v0AgEDY3N7N2797tGBoaKuTz+cLExMQAlUrFIiLy9fWNeP31132EQmEon88XnjlzxpaISKFQMImJiQERERGhoaGhwk2bNrkY72v37t3Oo0ePbqypqeHY29vrnZ2ddUREzs7OeoFA0Gpst2XLlj7m93H27FnrIUOGhAiFwlChUBj6448/2pt/b1qtlmbMmOEXHh4eyufzhStWrHDv6Pvdtm2b23PPPddgfD927NgmJycnvXm7L7/80nPJkiU1xkLX19e33Q6IHA7H0NLSwrS0tDAcDsdQVFRkI5fLrePi4m7YknvChAkNO3fudOvoejqDAhkAAADgmnHjximqq6utAwICwidPntxv//79DsZjb7zxxiWxWFxcWlpapFKpmO+++87ZeEyr1bIKCwuLly1bJlu8eLEPEdHKlSs9uVyuvry8vOi9996rlkgk9kREv/zyi11NTQ2ntLS0qKSkRDJr1qzLU6dOvRoeHq7Mzs4ul0qlEoZhaMaMGYFbt249V1JSItFqtbRixYrro6zu7u5aiURSPG3atLqMjAweEdHbb7/tHR0drSgsLCzOy8s7O3/+fD+FQsEQER06dMgpPj6+8ZFHHlG6u7u39e3bN+LZZ58N2Lx58/V76Ow+fHx8tHl5eSUSiaR469at5a+//no/8+/to48+cnd2dtaJxeLigoKC4o0bN3pIpVJr0zZqtZolk8lsQkJCWs3PNyeTyWy++eabPuHh4aEjRowILiwstDFv884779QkJSUFLlu2zGvOnDmX0tPTfTMyMuTm7Tw8PHStra2s2tpaK/NjnemuQL54sx39D8z7RtbtZ1kyD1l3V1ZHfSMLWV31fS8898iyTJ4ls3odZ2dnvVgslnzyySdVHh4e2ilTpgzIyspyIyI6ePCg48CBAwV8Pl948uRJR7FYzDWel5iYeJWIaOjQoS0XLlywJiI6fvy4Q1JS0mUioocffljF5/OVREQCgUAjk8lspkyZ0nf79u1Offr0aTc3tqCgwNbPz09j3H0uJSXl8vHjxx2NxydNmnSViCgyMlIpk8lsiIiOHj3qtGrVKm+BQCCMiooK0Wg0rLKyMmu1Ws2qra21FgqFrWw2m44dO1a6efPmc8HBweo333yzb1pamk9X99Ha2sqaNGlSwLWR7AHnzp2zNb/ew4cPO23bts1NIBAIBw0aFHr16lW2RCK5oV1tbS3b0dGx3UhwR1pbW1m2trYGsVhcnJqaWpeSkhJg3mbo0KGqgoIC6alTp0pKSkpseDxem8FgoKeeeqr/2LFjA2Uy2fVpGW5ubtrz589bm/fRmS7nnKSFP+J1sx3dLmTdfXn3albgE28i6y7Kin48GFk9gL0w847l3atZC+PD71gW89T6O5YVF7jmjj6LvQGbzaa4uLimuLi4poEDB6q++eYbt+nTp1+ZM2eO/6lTpyRBQUFtaWlpPmq1+vpAo62trcF4rk6n63LOsoeHh04sFkt27drltHbtWo+tW7e6fv/995W3co0meQatVssiIjIYDLR9+/YykUh0w5bOu3fvdoyMjGw2vmcYhqKjo5XR0dHKMWPGKKZPnx7w4YcfVnd2H0uXLuV5enq27dixo0Kv1xOXyx1ifj0Gg4GVmZl5PiEhQdHZNdvb2+tbW1tvavYCj8drnThx4lUioqSkpIbZs2cHdNZWr9fTe++9571z587yF154od+qVasulJaWWi9fvpy3evVqORGRRqNh2dnZtZvK0RlMsQAAAAC4pqCgwMb0n/PPnDnD9fPza1UqlQwRkZeXl7axsZHZu3dvn+76ioqKav72229diYh+//1325KSEjsiopqaGrZOp6OUlJSGDz74QF5YWGhHROTg4KBrbGy0IiISiURquVxuLRaLbYiIsrOz3YYPH97UWRYRUXR0tCIzM5NnnBt94sQJLhHRgQMHnJ966qlGIqLKykrO8ePH7Yzn5Ofn2/n6+nY55aGxsdHK29u7zcrKitasWeOm07VfDOLxxx9v/OyzzzyMP2j866+/bIzTO4w8PDx0Op2OpVQqu/3R45gxYxp++OEHx2vX7+jv76/prO2nn37q9sQTTzTyeDydSqViGIYxWFlZkfH/mV6vp7q6Ok5ISEinfZjDKhYAAADQa7E5jLanl3nr6rhCobB69dVX+ykUCisrKytDQECAZuPGjVXu7u66559/vi40NDTMw8NDKxKJul32be7cuZcmTJgQ2L9//7CgoCC1UChsIfq7SE1NTQ3Q6/UsIqLFixdfICJKTk6uf+WVV/zfeOMNfX5+fvHatWsrExMTB+h0OhKJRMq5c+fWdZWXkZFR/eKLL/YTCARCvV7P6tu3ryY3N7fsxIkTjpmZmXKiv6cuzJ071+/ixYscGxsbg6ura9u6devOd9Xv//3f/11KSEgY8N1337nFxMQ0crncdiOxr7/+en1lZaVNREREqMFgYLm6urYdOHDgnHm7ESNGNB46dMhh3LhxTUREQ4YMCSkvL7dVqVRWPB5v4Jo1ayoTEhIUixcvrn322WcD16xZw7Ozs9OvW7eusqNra2pqYjZt2uR27NixUiKiOXPmXHzyySeDORyOYfPmzeVERMePH7cbNGhQC4fD6eo2b8AyGAydHvxQ/GstEfFuurdbc9H0n82RdftZFs67b7IqcjIslmU+9cCCWe3y7tWs3B9LLZplOtXhXs0iItIummOx5958msO9mrVoj9hiWebTN/T7Uy36fJhO4dhX8bLFsuIC17QbSSwoKKgUiUT1lsi7H507d44zderUAGMB+U87fvy43cqVK3n//e9/K+5U5tSpU/uOGzeuYezYse1G4AsKCtxFIlGA+efd/Y3MUn/4OuobWbefZck8ZN1dWR31jSxkddX3vfDcI8syeZbMAgsbMGBAW28pjomIoqKilPn5+QqtVkvGtZAtLTw8XNVRcdwVTLEAAAAAgDvm//7v/y7fybw5c+bc8r9I4Ed6AAAAAAAmUCADAAAAAJhAgQwAAAAAYAIFMgAAAACACfxIDwAAAHot7fL/iEil7Ll6hWunZc9bUtBVk/Pnz7NffvnlfgUFBXZOTk46d3f3ttWrV8vOnz9vnZmZycvNzS272biEhISAuLi4xqlTp1719fWNyM/PL/b29u50LWa9Xk9Dhw7lHzhwoMzV1VWfmJgY8NNPPzm7ublpS0tLi4ztZsyY4Xfo0CFnDodj8Pf312zZsqXS3d39hh08CgoKbCZNmtS/ra2NtWbNmqrHHnuspa2tjUaOHMnPyckpc3R01BMRxcXF9f/ggw/kERERN72Rxr0OI8gAAADQe/VkcXwT/en1eoqPjw8aMWJEk0wmExcVFRVnZGTIq6urb36Xiduwbds257CwMJWrq6ueiGjatGn1e/bsabdM2xNPPKEoKSkpKikpkQQFBan/85//tNsSfPXq1R4ffvih7ODBg6UrVqzwIiJavny554QJEy4bi2MiopkzZ15aunTpfbeleFdQIAMAAABcs2/fPkc2m22YN2/e9V3rHn30UVVsbGwzEVFLS4tVbGxs/8DAwLD4+PhA47bOc+fO9Q4PDw8NDg4Omzhxor/x8440NzezRowYEZyZmelufuzbb791feaZZxqM78eMGdPs4eHRbsT53//+t8K4M9yjjz7aIpfLrc3bcDgcQ0tLC9PS0sJwOBxDfX291YEDB5xnzZp1wzJrsbGxzXl5eU5tbW3df0H3CRTIAAAAANf89ddfXJFIpOzseHFxMffTTz+VlZWVFZ0/f97mxx9/dCAieuONNy6JxeLi0tLSIpVKxXz33XfOHZ2vUCiY0aNHBz/33HNXOlqf9/Tp0w7Dhg3rdhtrU19//bV7bGxso/nnaWlpdRkZGd7JycmB//nPf2reeust77feeqvWysrqhnZWVlbk7++v/vXXX+1uJfdehgIZAAAA4CZFRES0DBgwoM3KyorCwsKU586dsyYiOnjwoOPAgQMFfD5fePLkSUexWMzt6Pz4+PigpKSk+tmzZ3e4WUZjYyO7T58+nQ8/m0lPT/eysrIyvPTSS1fMjwUHB7f+9ttvZ//880+pvb29vrq62lokEqnGjRsX+NRTT/X/66+/bIxt3d3dtTKZ7I5MI7kboEAGAAAAuCYiIkJVUFDQ6UiqjY2NwfjaysqKtFotS6lUsubMmeO/c+fOcyUlJZLJkyfXq9XqDmushx56qDknJ8e5sykYVlZWBp1O1+Exc1lZWW45OTkuO3furGCYrku69PR034yMDPny5ct5L7zwQt2qVasuzJ8/38d4XKPRMHZ2djddmN/rUCADAAAAXPP00083tba2slauXHl9fvCpU6e4P/zwg0Nn5yiVSoaIyMvLS9vY2Mjs3bu3T2dtV6xYUe3i4qJNTk7u19HxwMBAdXFxsU1Hx0xt377d6eOPP/Y6cOBAmekP7jqyf/9+By8vr7aIiAiNUqlkrKysiGEYg0qlul4HVlRU2AwePFjVXe79AgUyAAAA9F5cu06XRLNEfwzD0J49e84dOXLEqW/fvuFBQUFh6enpvr6+vp3+gs3d3V33/PPP14WGhoZFR0fzRSJRl3OIN2zYIFOr1cxLL73kZ35s9OjRjYcOHXI0vn/66acDo6KiBBUVFTY8Hm/gqlWr3ImI0tLS+rW0tFjFxMTwBQKBcNKkSR0W3Hq9npYuXer9/vvvVxMRzZo1qy4tLa3vk08+GfzGG2/UEhHJZDK2jY2NoV+/fj37Xd/FsA4yAAAA9FrdrVlsCQEBAW0HDhwoN/88IiJCExcX12R8n52dfd74OisrqzorK6va/JwdO3ZUGl/L5fJC4+vt27dXmrclIpo9e3b9xIkTA9LS0uqJiPbu3VvRUbvz58+Lb+ZeGIahkydPXl8mbvDgwWqJRFJs2mbDhg2u06ZNq2t/9v0LI8gAAAAAvYS/v3/btGnT6q9cuXLHajQXFxfd7Nmz262ocT/DCDIAAABALzJ9+vSrdzLvtdde63BFjfsZRpABAAAAAEygQAYAAAAAMIECGQAAAADABApkAAAAAAAT+JEeAAAA9FrLDxaLVG26HqtXuBwr7bwxoV0uHWdnZzdIqVSeueE6li/3sLOz03e2RfTtysrKcouPj1cEBAR0ut5yT6iqquKkpKT45+bmln322WeuH3/8sZfxWElJCff48eOSoUOH3rBhSHx8fKBUKuWOHj268ZNPPpETEc2bN887IiJClZSU1EBEtGXLFudTp07Zf/TRR+2WursbdTeCfNGC2eZ9I+v2syyZh6y7K6ujvpGFrK76vheee2RZJs+SWd3qyeL4dvqbN29enaWKYyKiTZs2uZ8/f55jqf6N3n//fV5qamo9EdHMmTOvSKVSiVQqlWRnZ1f4+vpqzIvjU6dOcblcrr6kpETyxx9/2F2+fNmqqqqKk5+fb28sjomIxo8f35iTk+PS1NR0T8xOYBkMhu5bAQAAANwBBQUFlSKR6PqavIv2iIf0dMbC+PDTXR3vaAQ5LS3Nx8HBQbd48eKLkZGRIREREcpTp045KJVK5quvvqpYunSp99mzZ7ljx469YtwwZM2aNa6fffYZr62tjTV48OCW7OzsKiKi8ePHB/z111/2LBbL8Pzzz9f369evbdasWQGenp5ttra2+vz8/OKffvrJ4c033+yr0+lIJBIps7Ozq7hcrsHX1zfiueeeu5yTk+Os1WpZW7duLR80aJBaoVAwqamp/aRSKVer1bLeeeed6smTJzeY35ufn19EaWmpmMvl3lAAzp4925fFYtHq1avlpp//8ccftu+++673rl27Kh599NGQH3/8sXTGjBl9Z8+eXRcVFaU0bZuamtr30Ucfbb7Ty9TdjoKCAneRSBRg/vk9UeUDAAAA3EnW1tZ6sVhcPHXq1LrExMSgdevWnZdKpUVbt251r62ttfrjjz9st2/f7pqfny+VSqUShmEMa9eudfvll1/sampqOKWlpUUlJSWSWbNmXZ46derV8PBwZXZ2dvm1tjRjxozArVu3nispKZFotVpasWKFhzHb3d1dK5FIiqdNm1aXkZHBIyJ6++23vaOjoxWFhYXFeXl5Z+fPn++nUChuqPOkUqm1s7Oz1rw4JiLavXt3n+Tk5HYj5IMHD1a7u7trw8LChGPGjGkoKiqy0ev1ZF4cExE9+OCDLXl5eQ498w3/szAHGQAAAOAWPfPMMw1ERCKRSBUUFKTy9/dvIyLq27evpry83Pro0aMOYrHYTiQShRIRqdVqxtPTUzt+/PgGmUxmM2XKlL5PP/104zPPPKMw77ugoMDWz89PM3DgQA0RUUpKyuVPP/3Uk4guERFNmjTpKhFRZGSkcs+ePX2IiI4ePeqUk5PjkpWV5UVEpNFoWGVlZdaDBw9WG/uVyWQcV1dXrXnekSNH7Llcrv6hhx5Smx8jItqwYYPM+DomJiZow4YNVenp6V6FhYV2o0aNUsyZM6eeiMjLy0tbW1tr/b98n71NlwXyh+Jfa4mIZ6Hsi2nhj1yfGI6s28+ycN59k1WRk2GxrMAn3rxTWe3y7tWs3B9LLZoV/XjwPZ9l4bx2WdpFcyyWxV6Y+Y9lLdojtljWwvjwG7L0+1Mt+nwwT62/nrev4mWLZcUFrmFZot87wdbW1kBExDAM2djYXB+RZRiGtFoty2AwsBITEy9/+umncvNzxWKxZNeuXU5r16712Lp1q+v3339f+b9ks9lsg1arZRERGQwG2r59e5lIJNJ0dp6dnZ1eo9G0mz3w7bffuv773/++0l3upk2bXB544AGlQqFgysvLbQ8cOFAeFRUV/OKLL15xdHTUq1Qqlq2trf5W7qW36m6KhaX+8HXUN7JuP8uSeci6u7I66htZyOqq73vhuUeWZfIsmXXPio2NVezbt6+PXC5nExFdvHjRqqSkxLqmpoat0+koJSWl4YMPPpAXFhbaERE5ODjoGhsbrYiIRCKRWi6XW4vFYhsiouzsbLfhw4c3dZUXHR2tyMzM5On1f9enJ06c4Jq3iYiI0Mjl8htGeHU6He3du7dPcnJylwWyRqNhrV692nPRokW1LS0tDIvFMhAR6fV6lkajYRERnT171jYsLEzVVT93C8xBBgAAgF6Ly7FqNyXA0v2p1WqGx+MNNP737rvv3vJfEoYMGaKeP3++fNSoUXw+ny+MiYnhy2QyTmVlJScqKipEIBAIk5KS+i9evPgCEVFycnL9K6+84i8QCIR6vZ7Wrl1bmZiYOIDP5wsZhqG5c+fWdZWXkZFRrdVqWQKBQBgUFBQ2f/58X/M2Tk5O+n79+mmMhTcR0cGDBx29vb1bhUJha1f9L1u2zOP555+/7OjoqH/44YdVKpWK4fP5QpFI1OLu7q4jIjp27JjjuHHjGm/1u+qNMAcZAAAAeq3u1iy2BL1e3+UqF7/99ttZ4+u4uLimuLi4po6OvfDCC1dfeOGFdis6SCSSYvPPUlJSGlJSUhqM78eOHds0duxYiXk7uVxeaHw9YsQIpTHPwcHBsHnz5qoub4yIZs6ceemLL75wM660ce36pd2dt2DBgkvG1wzD0N69eytMj8tkMrZarWYiIyPviRFkFMgAAAAA94nk5OSG+vr6Hq//ysvLrTMzM2Xdt7w7oEAGAAAAuI+kpaXVd9/q1owcObLdsm93M8xBBgAAAAAwgQIZAAAAAMAECmQAAAAAABMokAEAAAAATOBHegAAANBr6Q+9KqK2lp6rVzj2WmZ0VpdLx7FYrCHx8fFXdu/eXUFE1NbWRp6enqIHHnigJTc3t+xmo86ePWsdFxcXXFpaWmR+rKqqipOSkuKfm5tbdvbsWWuRSBQeEBCgJiIaPHhw8+bNm8+bnxMfHx8olUq5o0ePbvzkk0/kRETz5s3zjoiIUCUlJTUQEW3ZssX51KlT9h999FH1zV4ntIcCGQAAAHqvniyOb7I/LperP3v2LLe5uZnl4OBg2LVrlxOPx2u7pZi2rpu///77vNTU1OurSfTt21cjlUrbrXtsdOrUKS6Xy9WXlJRIhg4dGnz58mWr5uZmJj8/33758uU1xnbjx49vXLx4sW9TU1Oto6PjPbHt8z8BUywAAAAAzDz22GON33//vQsR0ZYtW1wTEhKub8Wcm5tr98ADDwhCQ0OFgwYNEhQUFNgQEWVlZbnFxMQEPfLII/yhQ4eGdNX//v37+yQkJNz0rnMcDsegUqkYnU5HWq2WYbPZhvT0dJ/FixffMFLMMAwNHTq0aevWrc63dMNwAxTIAAAAAGaSkpKubN26tY9SqWQVFxfbPfrooy3GYyKRSP37779Li4uLJQsXLpTPmzfPz3isqKjIbvfu3ed+//33sx33TCSVSq2dnZ21XC7XYPzswoUL1qGhocKHHnoo5IcffnAwP2fw4MFqd3d3bVhYmHDMmDENRUVFNnq9nqKiotqtP/zggw+25OXltesDbh6mWAAAAACYefjhh1UXLlywWbdunetjjz12w0jvlStXrMaPHx9YWVlpy2KxDG1tbSzjseHDhyt4PJ6uq75lMhnH1dVVa3zfr1+/toqKir+8vLx0eXl5domJiUESiUTs6up6wxSJDRs2XN+pLiYmJmjDhg1V6enpXoWFhXajRo1SzJkzp56IyMvLS1tbW2t9u9/B/QwjyAAAAAAdiI2NbVi4cGHf5OTkK6afp6en+44cObKptLS0aO/evWWtra3X6yk7O7tu5/3a2dnpNRrN9XO4XK7By8tLR0Q0fPhwZb9+/TRisdi2s/M3bdrk8sADDygVCgVTXl5ue+DAgfJdu3b1aWpqYoiIVCoVy9bWFvOPbwNGkAEAAAA6MHPmzHoXFxddZGSkat++fY7GzxUKhZWfn18rEdHnn3/ufqv9RkREaORy+fUR3urqaranp6eWzWaTRCKxrqystAkJCdF0dK5Go2GtXr3a8/Dhw2VisdiGxWIZiIj0ej1Lo9GwHB0d6ezZs7ZhYWGqW79jMMIIMgAAAPReHHtt940s09+AAQPa5s+ff8n88/T09Np3333XLzQ0VKjV3vrlOTk56a+NEtsQER06dMhBIBCECQQC4bPPPjvgo48+qupsmsayZcs8nn/++cuOjo76hx9+WKVSqRg+ny8UiUQt7u7uOiKiY8eOOY4bN+6mfwAI7WEEGQAAAHqt7tYstgSlUnnG/LO4uLimuLi4JiKixx57rKWyslJsPJaVlVVNRPTqq69eJqLLxs9DQkJaO1oDmYho5syZl7744gu3rKys6pSUlIaUlJSGm7m2BQsWXC/YGYahvXv3Vpgel8lkbLVazURGRmIE+TagQAYAAAC4w5KTkxvq6+t7vA4rLy+3zszMlHXfErqCAhkAAADgH5CWllbffatbM3LkyHbLvsGtwxxkAAAAAAATKJABAAAAAEx0VyBftGC2ed/Iuv0sS+Yh6+7K6qhvZCGrq77vheceWZbJs2QWQK/EMhgM3bcCAAAAuAMKCgoqRSJRj8/NBehIQUGBu0gkCjD/HD/SAwAAgF4rp+oNUZu+pcfqFQ5jr33Cf0WnS8fV1tZa/etf/wohIqqvr+cwDGMwbgv9559/Ftva2t4wsjhjxgy/n376yXnUqFGNCxcurI2NjQ1qa2tjVq1adT42NrbZtG1sbGz/Dz/88IJQKGw1fhYTExMkk8lsOloObunSpZ5ff/21h4+PjyYnJ+ecra2tIScnx2Hbtm191q9fLyP6e5OR8ePHB+bl5ZXe3jcDplAgAwAAQK/Vk8XxzfTn5eWlk0qlEiKitLQ0HwcHB93ixYs7nWayefNm96tXr/7JZrPpiy++6BMaGqraunVrlXm7/Px8W51OxzItjjdu3Ohib2/f4YYgRETbtm1zlUqlRW+99Zb3zp07nSZMmNC4ZMkS7x07dpQb2/j4+Gh5PF7boUOH7EePHt3S3f3DzenyIflQ/GstEfEslH0xLfwRL2T1XJaF8+6brIqcDItlBT7x5p3KapeHrNvPyv2x1KJZ0Y8H/yNZFs5rl6VdNMdiWeyFmf9Y1qI9YotlLYwPv1NZ7fL0+1MtlsU8tZ5liX57WkJCQkBcXFzj1KlTrxIR2dnZDVIqlWdiYmKClEqlVXh4uDAhIeHKhg0bPNRqNSMQCOzz8/OLHRwcro82f/31125PP/10g/F9Y2Mjk5WVxfviiy+qJkyYMKCjXIPBQK2trSylUslYW1sbPvvsM9fRo0c3mu+yN27cuIbs7Gw3FMg9p7u/lVnqD19HfSPr9rMsmYesuyuro76Rhayu+r4XnntkWSbPkll3tSNHjpTZ2dkNMo4483i8tvz8fPvs7Ozz5m1PnTrlkJycfMX4Pi0tzfe111676ODgoO+s/xkzZlwaPHhwKJ/PV40aNap5zJgxQT///HO7qRTDhg1rWbx4sU9P3RdgmTcAAAAAi6urq+N4eXm1ERGdPHmSW1FRYZOcnNzQ1TmzZs26UlxcLNm9e3fF0qVLebNmzbq0fft2p9jY2P6pqal9dbq/B5J9fHy0ly5dsrb8Xdw/UCADAAAAdIHNZhuMxahOp6O2trZbnhpiY2OjV6lUDBFRXl6eg1gstvP19Y0YMWKEoLKy0iYyMjKks3MrKys5p0+ftk9KSmr46KOPvPbt21fu4uKi3bNnjxMRkVKpZNnY2HQ6Eg23DgUyAAAAQBf8/f1bT58+bUdEtHnzZhetVnvLBXJwcLC6uLjYhogoPT297tKlS3/J5fLCY8eOSQMCAjS//fbb2c7OnTdvns+SJUuqiYjUajXDYrGIYRhqaWlhiIjEYrEtn89X/W93Bx1BgQwAAAC9Foex1/7T/b3yyit1J0+edAwJCRGePHnSnsvl3vJo7ZgxYxqOHDnieKvnnThxgktEFBUVpSQiSkxMvBwSEhL266+/OiQkJDQSEf3444+OsbGxjbfaN3QOy7wBAABAr9XVmsWW9uGHH1YbXxcUFEiNrz/77DO58bVSqTxjfP3qq69eJqLLHfU1ZcqUq8OGDQvRarXVbPb/L79CQkJaO1oD2WjYsGGqYcOGXV82bsGCBZcWLFhwybTNgQMHXA4ePFh283cG3cEIMgAAAICFOTg4GBYsWFBdUVHRoz+mq66uZr/22msXPTw8Ol1PGW4dRpABAAAA7oCEhARFT/fp4+OjTUpKaujpfu93GEEGAACA3kSv1+vvig1E4O527TnrcD45CmQAAADoTcR1dXXOKJLBkvR6Pauurs6ZiMQdHccUCwAAAOg1tFrt9Nra2i9ra2vDCQN5YDl6IhJrtdrpHR1EgQwAAAC9xpAhQy4RUfw/fR1wf8PfzAAAAAAATKBABgAAAAAwgQIZAAAAAMAECmQAAAAAABMokAEAAAAATKBABgAAAAAwgQIZAAAAAMAECmQAAAAAABMokAEAAAAATKBABgAAAAAwwTIYDP/0NQAAAAAA9BoYQQYAAAAAMIECGQAAAADABApkAAAAAAATKJABAAAAAEygQAYAAAAAMIECGQAAAADAxP8DkVjnAeDKEigAAAAASUVORK5CYII=\n", "text/plain": ["<Waffle size 720x720 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plot_labels = [f'{i} ({str(j)} %)' for i,j in zip(lith_data_df.LITH, \n", "                                                    lith_data_df.Well2)]\n", "\n", "fig = plt.figure(FigureClass=Waffle, figsize=(10,10), rows=5, columns = 20, \n", "                 values=list(lith_data_df['Well2']),\n", "                 colors=colours,\n", "                 labels=plot_labels, \n", "                legend={'loc':'center left', 'bbox_to_anchor': (1.0, 0.5), \n", "                        'ncol':1, 'fontsize':10}, rounding_rule='ceil')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 120, "id": "01907f0e-f219-4c6b-949c-6e3e769a00aa", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Waffle size 1080x720 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plot_labels = [f'{i} ({str(j)} %)' for i,j in zip(lith_data_df.LITH, \n", "                                                    lith_data_df.Well1)]\n", "\n", "fig = plt.figure(FigureClass=Waffle, \n", "                 plots = {211: {'values':list(lith_data_df['Well1']),\n", "                               'labels': [f'{i} ({str(j)} %)' for i,j in zip(lith_data_df.LITH, \n", "                                                    lith_data_df.Well1)],\n", "                               'legend':{'loc':'center left', 'bbox_to_anchor': (1.0, 0.5), \n", "                                          'ncol':1, 'fontsize':12},\n", "                                'title':{'label':'Well 1 Lithology Composition', 'fontsize':18}\n", "                               },\n", "                               \n", "                          212: {\n", "                              'values':list(lith_data_df['Well2']),\n", "                              'labels': [f'{i} ({str(j)} %)' for i,j in zip(lith_data_df.LITH, \n", "                                         lith_data_df.Well2)],\n", "                              'legend':{'loc':'center left', 'bbox_to_anchor': (1.0, 0.5), \n", "                                          'ncol':1, 'fontsize':12},\n", "                              'title':{'label':'Well 2 Lithology Composition', 'fontsize':18}\n", "                          }\n", "                         },\n", "                 figsize=(15,10), \n", "                 rows=5, \n", "                 columns = 20, \n", "                 colors=colours)\n", "                 \n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 50, "id": "814dda78-e0c0-480a-9efa-f36c274a8005", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x1080 with 7 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Set up the colour for unused squares\n", "off_colour = 'lightgrey'\n", "\n", "# Figsize numbers must be equal or the height greater than the width\n", "# othewise the plot will appear distorted\n", "\n", "fig, axs = plt.subplots(len(lith_data_df), 1, figsize=(10, 15))\n", "\n", "for (i, ax), color in zip(enumerate(axs.flatten()), colours):\n", "    plot_colours = [color, off_colour]\n", "    perc = lith_data_df.iloc[i]['Well1']\n", "    values = [perc, (100-perc)]\n", "    lith = lith_data_df.iloc[i]['LITH']\n", "    Waffle.make_waffle(ax=ax, rows=5, columns=20, \n", "                       values=values, colors=plot_colours)\n", "    \n", "    ax.set_title(lith)\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "f28930fe-7df9-4e50-b26c-d7eb5f5117ca", "metadata": {}, "outputs": [], "source": ["fig = plt.figure(\n", "    FigureClass=Waffle,\n", "    plots={\n", "        311: {\n", "            'values': data['Factory A'] / 1000,  # Convert actual number to a reasonable block number\n", "            'labels': [f\"{k} ({v})\" for k, v in data['Factory A'].items()],\n", "            'legend': {'loc': 'upper left', 'bbox_to_anchor': (1.05, 1), 'fontsize': 8},\n", "            'title': {'label': 'Vehicle Production of Factory A', 'loc': 'left', 'fontsize': 12}\n", "        },\n", "        312: {\n", "            'values': data['Factory B'] / 1000,\n", "            'labels': [f\"{k} ({v})\" for k, v in data['Factory B'].items()],\n", "            'legend': {'loc': 'upper left', 'bbox_to_anchor': (1.2, 1), 'fontsize': 8},\n", "            'title': {'label': 'Vehicle Production of Factory B', 'loc': 'left', 'fontsize': 12}\n", "        },\n", "        313: {\n", "            'values': data['Factory C'] / 1000,\n", "            'labels': [f\"{k} ({v})\" for k, v in data['Factory C'].items()],\n", "            'legend': {'loc': 'upper left', 'bbox_to_anchor': (1.3, 1), 'fontsize': 8},\n", "            'title': {'label': 'Vehicle Production of Factory C', 'loc': 'left', 'fontsize': 12}\n", "        },\n", "    },\n", "    rows=5,  # Outside parameter applied to all subplots, same as below\n", "    cmap_name=\"Accent\",  # Change color with cmap\n", "    rounding_rule='ceil',  # Change rounding rule, so value less than 1000 will still have at least 1 block\n", "    figsize=(5, 5)\n", ")\n", "\n", "fig.suptitle('Vehicle Production by Vehicle Type', fontsize=14, fontweight='bold')\n", "fig.supxlabel('1 block = 1000 vehicles', fontsize=8, ha='right')\n"]}, {"cell_type": "code", "execution_count": null, "id": "acd19a80-3546-4821-9a16-58e1d7a21cf1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a8fbddb2-bcfc-4360-b9ae-f51e42bb3ab8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.3"}}, "nbformat": 4, "nbformat_minor": 5}