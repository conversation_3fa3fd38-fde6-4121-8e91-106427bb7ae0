2015-10-19 17:39:39,391 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 17:39:39,610 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 17:39:39,610 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-19 17:39:39,657 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-19 17:39:39,657 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445182159119_0018, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@75a61582)
2015-10-19 17:39:39,891 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-19 17:39:40,610 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445182159119_0018
2015-10-19 17:39:42,516 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-19 17:39:43,485 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-19 17:39:43,516 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@24207655
2015-10-19 17:39:47,516 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/pageinput2.txt:134217728+134217728
2015-10-19 17:39:47,626 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-19 17:39:47,626 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-19 17:39:47,626 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-19 17:39:47,626 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-19 17:39:47,626 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-19 17:39:47,626 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-19 17:40:04,595 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:40:04,595 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 48254386; bufvoid = 104857600
2015-10-19 17:40:04,595 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 17306472(69225888); length = 8907925/6553600
2015-10-19 17:40:04,595 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 57323122 kvi 14330776(57323104)
2015-10-19 17:40:25,252 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-19 17:40:25,252 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 57323122 kv 14330776(57323104) kvi 12127800(48511200)
2015-10-19 17:40:53,737 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:40:53,737 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 57323122; bufend = 699496; bufvoid = 104857600
2015-10-19 17:40:53,737 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14330776(57323104); kvend = 5417752(21671008); length = 8913025/6553600
2015-10-19 17:40:53,737 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 9768248 kvi 2442056(9768224)
2015-10-19 17:41:12,597 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-19 17:41:12,597 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 9768248 kv 2442056(9768224) kvi 241544(966176)
2015-10-19 17:41:21,082 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:41:21,082 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 9768248; bufend = 57981481; bufvoid = 104857600
2015-10-19 17:41:21,082 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 2442056(9768224); kvend = 19738252(78953008); length = 8918205/6553600
2015-10-19 17:41:21,082 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 67050233 kvi 16762552(67050208)
