import{r,aI as f,b4 as l,j as t,b5 as m,bp as B,b3 as b,b6 as p}from"./index.BTGIlECR.js";import{c as h}from"./createDownloadLinkElement.DZMwyjvU.js";function w(n,o,e){return h({enforceDownloadInNewTab:e,url:n.buildMediaURL(o),filename:""})}function D(n){const{disabled:o,element:e,widgetMgr:s,endpoints:a,fragmentId:d}=n,{libConfig:{enforceDownloadInNewTab:c=!1}}=r.useContext(f);let i=l.SECONDARY;e.type==="primary"?i=l.PRIMARY:e.type==="tertiary"&&(i=l.TERTIARY),r.useEffect(()=>{a.checkSourceUrlResponse(e.url,"Download Button")},[e.url,a]);const u=()=>{e.ignoreRerun||s.setTriggerValue(e,{fromUi:!0},d),w(a,e.url,c).click()};return t("div",{className:"stDownloadButton","data-testid":"stDownloadButton",children:t(m,{help:e.help,containerWidth:e.useContainerWidth,children:t(B,{kind:i,size:b.SMALL,disabled:o,onClick:u,containerWidth:e.useContainerWidth,children:t(p,{icon:e.icon,label:e.label})})})})}const g=r.memo(D);export{g as default};
