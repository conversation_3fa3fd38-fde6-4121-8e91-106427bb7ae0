visions-0.8.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
visions-0.8.1.dist-info/LICENSE,sha256=zaKyICqkizk1HYnTKojO9JjD7Q1Hr4O4stTMkQGMjcI,1680
visions-0.8.1.dist-info/METADATA,sha256=7qCEqXL7th0fFSOpzIXj1NnLVIPXwn8gphF4citf6zY,11076
visions-0.8.1.dist-info/RECORD,,
visions-0.8.1.dist-info/WHEEL,sha256=In9FTNxeP60KnTkGw7wk6mJPYd_dQSjEZmXdBdMCI-8,91
visions-0.8.1.dist-info/top_level.txt,sha256=rmFORZR-2SJA9uP91_wlu1k0ym499eo0G9xs7YIKDr8,8
visions/__init__.py,sha256=an9XRgc_RASGU2KhDrLfzktMjYOV5i4bS7uq0ra0QVY,344
visions/__pycache__/__init__.cpython-310.pyc,,
visions/__pycache__/declarative.cpython-310.pyc,,
visions/__pycache__/functional.cpython-310.pyc,,
visions/backends/__init__.py,sha256=q3qe0mkX-tBgYPuSnhFA-uRkTnRQvEdOdEGA4m9xhYE,925
visions/backends/__pycache__/__init__.cpython-310.pyc,,
visions/backends/numpy/__init__.py,sha256=lrsLMJ-bXSHmTRAHYEkxaddaXogOr4vHn_LQfLFQU3s,196
visions/backends/numpy/__pycache__/__init__.cpython-310.pyc,,
visions/backends/numpy/__pycache__/array_utils.cpython-310.pyc,,
visions/backends/numpy/__pycache__/sequences.cpython-310.pyc,,
visions/backends/numpy/__pycache__/test_utils.cpython-310.pyc,,
visions/backends/numpy/array_utils.py,sha256=lxcvTfMbc9j3TTQSndhdy4PdXiupkLOw5x2HF9ScAJU,2019
visions/backends/numpy/sequences.py,sha256=1RxywZ9hdoXC8ts6FqTXAfMSxg6y_DfTuTi2vakfzHo,2071
visions/backends/numpy/test_utils.py,sha256=g7KoTLOmZtxv3M5hzpXalMpz9CTql2cTrYHDfJQBCYc,5412
visions/backends/numpy/types/__init__.py,sha256=4DO6FmU_OmXCWfkqb6l-KI0yVFoKaoLG6mNRzgDxzaA,353
visions/backends/numpy/types/__pycache__/__init__.cpython-310.pyc,,
visions/backends/numpy/types/__pycache__/boolean.cpython-310.pyc,,
visions/backends/numpy/types/__pycache__/complex.cpython-310.pyc,,
visions/backends/numpy/types/__pycache__/date_time.cpython-310.pyc,,
visions/backends/numpy/types/__pycache__/float.cpython-310.pyc,,
visions/backends/numpy/types/__pycache__/integer.cpython-310.pyc,,
visions/backends/numpy/types/__pycache__/object.cpython-310.pyc,,
visions/backends/numpy/types/__pycache__/string.cpython-310.pyc,,
visions/backends/numpy/types/__pycache__/time_delta.cpython-310.pyc,,
visions/backends/numpy/types/boolean.py,sha256=Lbuq7CM1cCg0AQyGHX7JcfOS5i0u3Ph6b5CrN13fSLc,1934
visions/backends/numpy/types/complex.py,sha256=iln5LbO8-Ke3WlwUV1Xk3mtB9Y3y6ms5M1hbxuMhvlM,1305
visions/backends/numpy/types/date_time.py,sha256=tz6lmHPd75GyyKmCanYfFR-38RRg3eFacYnDlocw1xQ,1330
visions/backends/numpy/types/float.py,sha256=TEhEPvn35b_gugb33dmF5OdnphsM5gNYnKoLh_INfa4,1545
visions/backends/numpy/types/integer.py,sha256=LfSrAZ12TVVkoT5hzOqSdgD8h0gkOyaJCjmJGaAFKl4,1132
visions/backends/numpy/types/object.py,sha256=rPiVlHVrwDzI0Pefb29uHD-Ns0gNeryK-bDs5BSy3dU,738
visions/backends/numpy/types/string.py,sha256=-kSH6kiLmotpHCp4tWZxjFtivkFepnKF2PdYcRN5CD4,616
visions/backends/numpy/types/time_delta.py,sha256=BkgXZxX4crpOwTzjfwSSYZtDaHhMeOo7Bon3echaRM4,444
visions/backends/pandas/__init__.py,sha256=54_roagj4nkKi5UfQtgA_63gWk1K97hI7-7DteVNPvw,78
visions/backends/pandas/__pycache__/__init__.cpython-310.pyc,,
visions/backends/pandas/__pycache__/sequences.cpython-310.pyc,,
visions/backends/pandas/__pycache__/series_utils.cpython-310.pyc,,
visions/backends/pandas/__pycache__/test_utils.cpython-310.pyc,,
visions/backends/pandas/__pycache__/traversal.cpython-310.pyc,,
visions/backends/pandas/sequences.py,sha256=-yJbFqKfJPvTvbVRF6l4TnYbAkRLwOfC_0uxjV1s8FA,4680
visions/backends/pandas/series_utils.py,sha256=_86KA1e29CIzqqsJLBRo7P8rC0gmlMElFROV7pDtoKA,2839
visions/backends/pandas/test_utils.py,sha256=kkcaLoSo-v7NSfip6ggm2ZiPZIEJD_x_W0jPYIERVjU,5513
visions/backends/pandas/traversal.py,sha256=q6-mJbC1aw4K3w3vFmpiEn0PogFTl67hTI6gN-hfKmM,1212
visions/backends/pandas/types/__init__.py,sha256=M1C1W_4XEj50731draGRgna_C4k2d3kdcRPuEFYK52w,1026
visions/backends/pandas/types/__pycache__/__init__.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/boolean.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/categorical.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/complex.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/count.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/date.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/date_time.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/email_address.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/file.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/float.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/geometry.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/image.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/integer.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/ip_address.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/numeric.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/object.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/ordinal.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/path.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/sparse.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/string.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/time.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/time_delta.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/url.cpython-310.pyc,,
visions/backends/pandas/types/__pycache__/uuid.cpython-310.pyc,,
visions/backends/pandas/types/boolean.py,sha256=XwdBqxUct7jAKbJQXE5YEkIE_iRlep_ZHR8V0Tppfpg,1873
visions/backends/pandas/types/categorical.py,sha256=lMHiiLM2SSNtipnA5INzTHiSzcuiGdTnmPSx9RR9tRg,375
visions/backends/pandas/types/complex.py,sha256=opj0MOd_ckG-kDdsQZM3qmT1caQoITrmVTENN_zILck,1613
visions/backends/pandas/types/count.py,sha256=-CGIHNDxF-c7GAwRSFek09ckObd5I6xrD1dYYmSUMhQ,356
visions/backends/pandas/types/date.py,sha256=mSNxwVIcdgo_whuIOIPjsu08vukwLsXeAQrl8_Ew6ek,827
visions/backends/pandas/types/date_time.py,sha256=gzvzbVpJF0litivzZdDD2aO_nfNNEd8cWUqR7IWM9Xc,1376
visions/backends/pandas/types/email_address.py,sha256=put2uDgUmQmRykaskU_vovbygXyeJ_xPLRyBEJEsYZ4,1032
visions/backends/pandas/types/file.py,sha256=RMjf9V0c454_RIuZd9QxjTz-Y3H5uP3NcNZC_cbT1K8,362
visions/backends/pandas/types/float.py,sha256=lviS64WUUSF2v8YnRjzYbQ0CPy18toW41hIHSQ14pig,2052
visions/backends/pandas/types/geometry.py,sha256=E7xIz9blgp9I0L4yeOTThHpHlLN4dfoAs3jlhu6gnNI,1538
visions/backends/pandas/types/image.py,sha256=XVfpRZ1wG9cSpGJdr9pbKBF0VBNZeF7Bo495GKIJs0c,448
visions/backends/pandas/types/integer.py,sha256=H8nUayqGETqu11gKb6qyEjYnITJOwGi3JL0dX-wwtNI,1076
visions/backends/pandas/types/ip_address.py,sha256=947r63g2oz3ebkn9MIxUQTHAeN8jRR5HC-FHFSbU27s,932
visions/backends/pandas/types/numeric.py,sha256=tW0tkow_8GqSkkq-yn7GHeghUDxozDaKrQAJqVtXnsA,358
visions/backends/pandas/types/object.py,sha256=N905Qp3tbc0MIRSFaBDc9pVYzFUGrqK6iQBYyKlGlfw,682
visions/backends/pandas/types/ordinal.py,sha256=xjQYS-5U3CFp0M7Kr8SDJ2fTh0xXme3cBNgP41fzdCg,581
visions/backends/pandas/types/path.py,sha256=GfE3pzlC0sGjV6YoU8lMAVmzNTUPEeq9g91GxyLk9bk,1072
visions/backends/pandas/types/sparse.py,sha256=gFvWLwrok7tZCurwwzwb7OmvUllIK6FyVv-fTuMEiQ0,222
visions/backends/pandas/types/string.py,sha256=KeazA3BbvRXzJrp8Klx6Dle5vFs6pkOQTkUgQspdxzs,919
visions/backends/pandas/types/time.py,sha256=Om5_W3IX119J2Ne-QkXphnNrs4Yn_lSyo-EK0kJuTQI,784
visions/backends/pandas/types/time_delta.py,sha256=Rna0aZzlQdeYGgHnR2km2xpchSGEteKyJ2G6pDlRmXw,514
visions/backends/pandas/types/url.py,sha256=k-6tBeS1MdgpWnOcGkVm26Lxy6Ww6RqYze0ssbHGI0s,991
visions/backends/pandas/types/uuid.py,sha256=zvXi8m0SCn_ugkZMh_Gb8DaWfrWCKLD0E3G0BIBLtKM,929
visions/backends/python/__init__.py,sha256=ELTjglzD9tpkuaZQ2VnroALoGoRRVZsARoK9FUxxhec,37
visions/backends/python/__pycache__/__init__.cpython-310.pyc,,
visions/backends/python/__pycache__/sequences.cpython-310.pyc,,
visions/backends/python/__pycache__/series_utils.cpython-310.pyc,,
visions/backends/python/sequences.py,sha256=P10nG3t6xSFl65-WUnn7W7kh8H6g4fvSti4yuhf-x5k,6995
visions/backends/python/series_utils.py,sha256=psAR0A21PMnX9ebflmAKhuCK_4lompocyW1pSXLFJfg,706
visions/backends/python/types/__init__.py,sha256=gIbVLcIsiYZ3nO6ZdvfFu2zQvkyZikZU1ufz680gOZ0,982
visions/backends/python/types/__pycache__/__init__.cpython-310.pyc,,
visions/backends/python/types/__pycache__/boolean.cpython-310.pyc,,
visions/backends/python/types/__pycache__/categorical.cpython-310.pyc,,
visions/backends/python/types/__pycache__/complex.cpython-310.pyc,,
visions/backends/python/types/__pycache__/count.cpython-310.pyc,,
visions/backends/python/types/__pycache__/date.cpython-310.pyc,,
visions/backends/python/types/__pycache__/date_time.cpython-310.pyc,,
visions/backends/python/types/__pycache__/email_address.cpython-310.pyc,,
visions/backends/python/types/__pycache__/file.cpython-310.pyc,,
visions/backends/python/types/__pycache__/float.cpython-310.pyc,,
visions/backends/python/types/__pycache__/geometry.cpython-310.pyc,,
visions/backends/python/types/__pycache__/image.cpython-310.pyc,,
visions/backends/python/types/__pycache__/integer.cpython-310.pyc,,
visions/backends/python/types/__pycache__/ip_address.cpython-310.pyc,,
visions/backends/python/types/__pycache__/numeric.cpython-310.pyc,,
visions/backends/python/types/__pycache__/object.cpython-310.pyc,,
visions/backends/python/types/__pycache__/ordinal.cpython-310.pyc,,
visions/backends/python/types/__pycache__/path.cpython-310.pyc,,
visions/backends/python/types/__pycache__/string.cpython-310.pyc,,
visions/backends/python/types/__pycache__/time.cpython-310.pyc,,
visions/backends/python/types/__pycache__/time_delta.cpython-310.pyc,,
visions/backends/python/types/__pycache__/url.cpython-310.pyc,,
visions/backends/python/types/__pycache__/uuid.cpython-310.pyc,,
visions/backends/python/types/boolean.py,sha256=H_5-h3Xwl4TaPjPi2PaD0mUtT6ERHhoA2EvnAvJCxks,1698
visions/backends/python/types/categorical.py,sha256=vVCQEzGfTe56nw8pymuzVlN2ovzOxq6QvSiwIxjGYL4,199
visions/backends/python/types/complex.py,sha256=BBjNLshaSErHgyesK7hJRZwLM_sLFe1dHtb-aCNlCYU,907
visions/backends/python/types/count.py,sha256=vXX-vaOZ7XAF78sEG8m355ruv-glNiN4FELoAn0VDfI,234
visions/backends/python/types/date.py,sha256=l2WHF72nn35ETnNq70NiNQTc-kr7c1h0N5SubtSaJjE,642
visions/backends/python/types/date_time.py,sha256=PViGNQ8RvRbo-vdLEj2o4lssA5dDskCGrlPGgBbO7lE,953
visions/backends/python/types/email_address.py,sha256=bLCoE97Bw1UUZt5nKnXV7XDSySlvoCGDa8hyXQh8xh8,786
visions/backends/python/types/file.py,sha256=cH_K1TOLMr3NiTdwO8O1z0YerZrU7l01Y2iAhKcUqsA,246
visions/backends/python/types/float.py,sha256=yqQAkY2ocDDBuQ3qsgG-yJb-DKQqpgqMUz9moKC3q74,1351
visions/backends/python/types/geometry.py,sha256=JLbtIbf9GevcHuCgQGYWUrooDuaYLNEtJzRxU_0hoHM,1228
visions/backends/python/types/image.py,sha256=KPdwzCGPPSvBP2v8nVDWfHwOLyHev6RanHd4MsJVN-E,352
visions/backends/python/types/integer.py,sha256=RdLEpkYCyn0aA7BJTReVJWTErXCWMVV0aj0gAsfx-gI,800
visions/backends/python/types/ip_address.py,sha256=fve-k9XsvA4OhZNnSM470JlyJTaGP6YRQbM8KNrlHCg,770
visions/backends/python/types/numeric.py,sha256=YVye9N9plewtV4X4E7RPPf4rsGq7BPHhcGlxmyTKE8I,302
visions/backends/python/types/object.py,sha256=wKgzD15517oLbS-sGtzRHuz3dVZsfAhBnOQBxisqZMg,396
visions/backends/python/types/ordinal.py,sha256=HfStquzo9maK23aemZemeyzk5qSnqFGDXAe-7YhuRew,183
visions/backends/python/types/path.py,sha256=sL9Ve762_7CPkGEJp6Pi32MbE2KmeloHvTQWvWTJuiE,852
visions/backends/python/types/string.py,sha256=HMe2Ca1SDWaX8cQmLk3Ht0vodFcEa_odTL1HucsuST0,360
visions/backends/python/types/time.py,sha256=utg-m3QlBtibvwOdi9ofJFAGI-eVK7mbl6SPo3ISIZw,649
visions/backends/python/types/time_delta.py,sha256=IS30OPCN62CRY1hJmTBXqRSbMwDpdrq_GYgcbDgtPC8,362
visions/backends/python/types/url.py,sha256=A69fb6hV6JnnMYHrTBsQdXtepEJsjYbndkJsVH59txI,726
visions/backends/python/types/uuid.py,sha256=m_l0ZnaC3YOCJYqoXX4Q0rWDidGY_6dNTTQ4DbcpfKo,681
visions/backends/shared/__init__.py,sha256=o5EHXPMi0KjOoNRas7IsMS9RSs6rFLkgFR3D3JeMb_Y,63
visions/backends/shared/__pycache__/__init__.cpython-310.pyc,,
visions/backends/shared/__pycache__/nan_handling.cpython-310.pyc,,
visions/backends/shared/__pycache__/parallelization_engines.cpython-310.pyc,,
visions/backends/shared/__pycache__/utilities.cpython-310.pyc,,
visions/backends/shared/nan_handling.py,sha256=rWenmeVT2gAy3CMwDKX3SfNjI1KdyRpKaPWdGk7amLU,1935
visions/backends/shared/parallelization_engines.py,sha256=Ko0lebYTkfC6xRJNwnY7o6c0uXktczmAHWVl5FDLwO0,2620
visions/backends/shared/utilities.py,sha256=hbgWj3vvHnJinCVpV8t8UxM0EyEL3US5BBfF6CA038c,162
visions/backends/spark/__init__.py,sha256=XY8BmUDIXXEr6VyWW24eM59s6fr4hBvMfcs9w30zRhM,76
visions/backends/spark/__pycache__/__init__.cpython-310.pyc,,
visions/backends/spark/__pycache__/traversal.cpython-310.pyc,,
visions/backends/spark/traversal.py,sha256=80QXQY4oDJLVRSgwMmb_DP9JSiwhqBO9TyU82XFL41g,1071
visions/backends/spark/types/__init__.py,sha256=jj4hQtcbwdKah5bjNVUyUY3FAkPg8CB3d5d7N1Nfsfk,349
visions/backends/spark/types/__pycache__/__init__.cpython-310.pyc,,
visions/backends/spark/types/__pycache__/boolean.cpython-310.pyc,,
visions/backends/spark/types/__pycache__/categorical.cpython-310.pyc,,
visions/backends/spark/types/__pycache__/date.cpython-310.pyc,,
visions/backends/spark/types/__pycache__/datetime.cpython-310.pyc,,
visions/backends/spark/types/__pycache__/float.cpython-310.pyc,,
visions/backends/spark/types/__pycache__/integer.cpython-310.pyc,,
visions/backends/spark/types/__pycache__/numeric.cpython-310.pyc,,
visions/backends/spark/types/__pycache__/object.cpython-310.pyc,,
visions/backends/spark/types/__pycache__/string.cpython-310.pyc,,
visions/backends/spark/types/boolean.py,sha256=7zhT0zkFICbDdqn0LxrQJJ4zgSNREWnTA5V3xWwrAak,363
visions/backends/spark/types/categorical.py,sha256=IC81UpGSFPUWcAZg8XrMdx9i3ZQXtLPmC0T0K-mbmMM,216
visions/backends/spark/types/date.py,sha256=SDws0QZ0TQY2XiDxwr_qVasRD44cNoySz4duvjB05qc,345
visions/backends/spark/types/datetime.py,sha256=y3v3STv8SVr34vykSqR5AEBOvz54vDCO-NZDI_atQsM,372
visions/backends/spark/types/float.py,sha256=DqzT0zPI-ZMcZ7p8RDYsaZlqYLqf5RVyVW1YgO3Zrss,403
visions/backends/spark/types/integer.py,sha256=3YEhUm7EiwcUcdFGtBImRzySlsNviyI9QrMzZsEBwmA,427
visions/backends/spark/types/numeric.py,sha256=41a38jsfLbE--DTlxi8LqRMeov9JNDKUhcOM3DA9MuQ,363
visions/backends/spark/types/object.py,sha256=Tx_0cY2kVe9hITYtNXWjStlYu8usLgCvaDYfZfCt8bo,443
visions/backends/spark/types/string.py,sha256=B5wkKCVjhJNNYTpVfJpoigfXNjY_XVk5XuqA-zcxnH8,357
visions/contrib/README.md,sha256=i_do51bjhl-XEB5Zpm0RUV8QuMz7q1P5xLjsMkNcjkQ,252
visions/contrib/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
visions/contrib/__pycache__/__init__.cpython-310.pyc,,
visions/contrib/relations/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
visions/contrib/relations/__pycache__/__init__.cpython-310.pyc,,
visions/contrib/relations/__pycache__/categorical_to_ordinal.cpython-310.pyc,,
visions/contrib/relations/__pycache__/integer_to_count.cpython-310.pyc,,
visions/contrib/relations/__pycache__/integer_to_datetime.cpython-310.pyc,,
visions/contrib/relations/__pycache__/integer_to_ordinal.cpython-310.pyc,,
visions/contrib/relations/__pycache__/relations_utils.cpython-310.pyc,,
visions/contrib/relations/__pycache__/string_to_categorical.cpython-310.pyc,,
visions/contrib/relations/__pycache__/string_to_datetime.cpython-310.pyc,,
visions/contrib/relations/__pycache__/string_to_ordinal.cpython-310.pyc,,
visions/contrib/relations/categorical_to_ordinal.py,sha256=rhgJ77H-lvm4di1Qoy1suAYvdvSLMI_R3t77kAnziaE,897
visions/contrib/relations/integer_to_count.py,sha256=2FgsO0AFaRhum8rcMZlnkgyZI0CHaExglXLwpbYgeDk,587
visions/contrib/relations/integer_to_datetime.py,sha256=gFrRmGhH7sNWmxz_UbCr4gV_htWEaFwQhC518PGDWRQ,837
visions/contrib/relations/integer_to_ordinal.py,sha256=x0Fkh-1RPQLTWpI8Zc5LWmgJtI4F0yBTtnALLFX8QJ0,692
visions/contrib/relations/relations_utils.py,sha256=BVaKwD_yD1YFoA9HqKwxJMEayvWRIeLTROoSzGzOFSY,163
visions/contrib/relations/string_to_categorical.py,sha256=HWk1cPHV8sheF9W8xnBWbV5WUUp8u0fvZD29xJ3QG0Q,593
visions/contrib/relations/string_to_datetime.py,sha256=kn5X14c1dyDpNMrV004EI4TIAAf82ksVD6X3SAkaUk0,1824
visions/contrib/relations/string_to_ordinal.py,sha256=j75c8KJQ3A9EhyCxa-ToC-YCeuS4CgO0e-oTuJ1Npho,756
visions/contrib/types/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
visions/contrib/types/__pycache__/__init__.cpython-310.pyc,,
visions/contrib/typesets/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
visions/contrib/typesets/__pycache__/__init__.cpython-310.pyc,,
visions/declarative.py,sha256=yWMZt1EuRV01EXMlhvd-LR-CllWMZ5FgftipyJszf-I,1733
visions/dtypes/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
visions/dtypes/__pycache__/__init__.cpython-310.pyc,,
visions/dtypes/__pycache__/boolean.cpython-310.pyc,,
visions/dtypes/boolean.py,sha256=ayd6EJfqbCKZugcnrstT1ud1-WyPfoHpNCGddzo28h8,22362
visions/functional.py,sha256=q5PjBtp9iikka0NrldiHyfgwPxUCJ6HINLGfQrB0Os4,4461
visions/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
visions/relations/__init__.py,sha256=XTano_8ttrByTjGai4d8VJ47JZKCwtSusevcVsAMkd0,243
visions/relations/__pycache__/__init__.cpython-310.pyc,,
visions/relations/__pycache__/relations.cpython-310.pyc,,
visions/relations/relations.py,sha256=_XwajcEELgOpoVwfxoAbMEEPwetYNUCNg1fCPQhd0Ig,2846
visions/test/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
visions/test/__pycache__/__init__.cpython-310.pyc,,
visions/test/__pycache__/series.cpython-310.pyc,,
visions/test/__pycache__/series_geometry.cpython-310.pyc,,
visions/test/__pycache__/series_sparse.cpython-310.pyc,,
visions/test/__pycache__/utils.cpython-310.pyc,,
visions/test/data/__init__.py,sha256=9ab4RHcxUkUU02DQuFXDACTTqz7KFjjshAYXpR47ZIs,42
visions/test/data/__pycache__/__init__.cpython-310.pyc,,
visions/test/data/file.html,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
visions/test/data/img.jpeg,sha256=ee1xWZ12foP9o0E324aQovVXnlSKwmZZGbSNGIs_ZFA,409
visions/test/data/img.jpg,sha256=ee1xWZ12foP9o0E324aQovVXnlSKwmZZGbSNGIs_ZFA,409
visions/test/data/img.png,sha256=GMMnr6kDYz-Gw-_PErd_CYB36sqovhAbsAeEb9dPi5M,95
visions/test/series.py,sha256=mC_Zq4pdfHuR1UZzQuC-kHXYe0YAeeOAvgWMFqoV-UI,695
visions/test/series_geometry.py,sha256=pl8WN1fZSGPNwkTT0DkBbVo8yl1sXtCqUC0kvSOw5Hg,758
visions/test/series_sparse.py,sha256=RnVX8qj7a89QaUmBJZdj38E6AtnXbEfu4M0wxnlZ1m4,1975
visions/test/utils.py,sha256=HvPyR46zk70ImdGZFRgbjZHyEUqJ773HUgqjxAzE8uc,8039
visions/types/__init__.py,sha256=hfaXM1sBpSSDkXy2Hc_qvxQYKy3KIkteVfFEGd_YAb4,1418
visions/types/__pycache__/__init__.cpython-310.pyc,,
visions/types/__pycache__/boolean.cpython-310.pyc,,
visions/types/__pycache__/categorical.cpython-310.pyc,,
visions/types/__pycache__/complex.cpython-310.pyc,,
visions/types/__pycache__/count.cpython-310.pyc,,
visions/types/__pycache__/date.cpython-310.pyc,,
visions/types/__pycache__/date_time.cpython-310.pyc,,
visions/types/__pycache__/email_address.cpython-310.pyc,,
visions/types/__pycache__/file.cpython-310.pyc,,
visions/types/__pycache__/float.cpython-310.pyc,,
visions/types/__pycache__/generic.cpython-310.pyc,,
visions/types/__pycache__/geometry.cpython-310.pyc,,
visions/types/__pycache__/image.cpython-310.pyc,,
visions/types/__pycache__/integer.cpython-310.pyc,,
visions/types/__pycache__/ip_address.cpython-310.pyc,,
visions/types/__pycache__/numeric.cpython-310.pyc,,
visions/types/__pycache__/object.cpython-310.pyc,,
visions/types/__pycache__/ordinal.cpython-310.pyc,,
visions/types/__pycache__/path.cpython-310.pyc,,
visions/types/__pycache__/sparse.cpython-310.pyc,,
visions/types/__pycache__/string.cpython-310.pyc,,
visions/types/__pycache__/time.cpython-310.pyc,,
visions/types/__pycache__/time_delta.cpython-310.pyc,,
visions/types/__pycache__/type.cpython-310.pyc,,
visions/types/__pycache__/url.cpython-310.pyc,,
visions/types/__pycache__/uuid.cpython-310.pyc,,
visions/types/boolean.py,sha256=qHKb8GD1pJR8T5Pew3GCne8n-IrFMNF8lBUjm5jk6Ys,1004
visions/types/categorical.py,sha256=MNnZCXjOJ94Sf67jWXoFytEq09f_Lr1DPOv34cawqwY,784
visions/types/complex.py,sha256=TEuMocpetQ82RHskh3lb8ra6S8ghttU4zObjo_PsEAs,835
visions/types/count.py,sha256=00Z_1396l3RyBxQJp1nBfK5WznVmkcEHgwPdrhaWhwM,695
visions/types/date.py,sha256=gQEJ18AsIg3URQdr50r1eZc9JruhIC2zU1K09U09JBI,948
visions/types/date_time.py,sha256=V03LSajaegHvB720JP_WlWpOuktBAa_qMZlJNtm1lyM,910
visions/types/email_address.py,sha256=kUfn2b95_mRbiM0sw1bXFMkDiwDdjg0zbhWuSVO4RVk,1441
visions/types/file.py,sha256=z714CJidgWJxZdh19U8-_leyswCmWzAzVLUvQReqbl4,777
visions/types/float.py,sha256=dHR749of5ox2Xa7dLK_iiCJruQiTEXUv5dNieYZTfdk,907
visions/types/generic.py,sha256=APEfYkim_6PR_5h9liLQ1rIMiCAAgvRSotcISOd5X28,634
visions/types/geometry.py,sha256=iirzlDYZcrnaglEY757kcFShhexOdK8E2kcx4piqVMQ,926
visions/types/image.py,sha256=VjcrIuaeGOfRZQVvUiFAso11wil2XFb4h-ebk_k4GdQ,815
visions/types/integer.py,sha256=s5WdAr2XiTdRHshWtkT7px3jpmOK_1KqZgZWDb8Iitw,830
visions/types/ip_address.py,sha256=BfptxspKRiK2BrjPfJjnWsL0WnRtATtPVcQTorksdXQ,930
visions/types/numeric.py,sha256=juZR57KaNPFM3iRnlpQCc9WvplXo6JQb4OP8BvPQnw0,783
visions/types/object.py,sha256=fu_7pPjxei4x81t7vGojKpnWVXX56HXGd78S011D3yY,681
visions/types/ordinal.py,sha256=cAZQy8zXuv0mqbnUSlGuqxNUsuMfx2WL5rHfS5cIzW4,783
visions/types/path.py,sha256=e19L9dX-Dx0YQA7ZDSqy25CnwcOU4aUcUx4hc7IsiZw,906
visions/types/sparse.py,sha256=0MD8QFFoDeoiQ9_LH4QhDkq_XAAzTsBdn28GJCNpmow,801
visions/types/string.py,sha256=Ej9NS2elS4jeqcLYIIIvM-8ur8aWHlY2GiABVcQhnx0,693
visions/types/time.py,sha256=e6rQb9BFGTQLc6b-yQ5Fvoi52V5iOkINsO8p01_maIg,762
visions/types/time_delta.py,sha256=rt8QPkEney9tmNjJcMqJDAYeNDprtBF-BIFiZPpBJ68,757
visions/types/type.py,sha256=wWWyoYw3ev7mMMV05nRhwYxDfLsidtqU2MVVHrnHLrM,4214
visions/types/url.py,sha256=45K5SHQcgiFwgIQcgEGJ9hjpmVwx6sLf3-DIEhMurA8,972
visions/types/uuid.py,sha256=0KKEI6R12Loiq-a6ZU-piy5yUdXawFL3ihZLH_-Qj_E,1168
visions/typesets/__init__.py,sha256=XknOafzJEFSAPqJMY4aElIBm_kjIhq6KYR4VVKZPGpM,289
visions/typesets/__pycache__/__init__.cpython-310.pyc,,
visions/typesets/__pycache__/complete_set.cpython-310.pyc,,
visions/typesets/__pycache__/geometry_set.cpython-310.pyc,,
visions/typesets/__pycache__/standard_set.cpython-310.pyc,,
visions/typesets/__pycache__/typeset.cpython-310.pyc,,
visions/typesets/complete_set.py,sha256=u_g52IOmEZ9r4qXxuXnb3XQQ8cRR204r3Danyd9-EmQ,1786
visions/typesets/geometry_set.py,sha256=RZ0Avy9TIRX64koyUXcqnLDx0beEu6q4gY9Is3hTriY,1259
visions/typesets/standard_set.py,sha256=sH85cj5IOnhDLJC6YSN08xhZ8vCZy9wWG4ZRsJXivAA,781
visions/typesets/typeset.py,sha256=vyPqoQMiM3toniJDypDuIT6ybeSakm2VIZFUYZDnhSA,15021
visions/utils/__init__.py,sha256=fiwmFb8b7qUm4yJ8996Qtrit_k5AtsIThdhKC2CNUuo,334
visions/utils/__pycache__/__init__.cpython-310.pyc,,
visions/utils/__pycache__/cache.cpython-310.pyc,,
visions/utils/__pycache__/errors.cpython-310.pyc,,
visions/utils/__pycache__/graph.cpython-310.pyc,,
visions/utils/__pycache__/profiling.cpython-310.pyc,,
visions/utils/__pycache__/warning_handling.cpython-310.pyc,,
visions/utils/cache.py,sha256=DC4PO1ZMVQcdtOJPrtvsKZOuDggZ5e9L7Tg3CTvQCn8,1601
visions/utils/errors.py,sha256=JxQheCpr83HU4iYtXh1DfhefQTAReOC7Dt0MFY440uQ,52
visions/utils/graph.py,sha256=M6CNL5WjAfaBAY-A3V-ATQbJ95ji1eFkHEwMnwtaeMM,1544
visions/utils/images/__init__.py,sha256=gRAPtT_litKvhT4fhJycsX7ypvQAVghtcch1IzldrOo,72
visions/utils/images/__pycache__/__init__.cpython-310.pyc,,
visions/utils/images/__pycache__/image_utils.cpython-310.pyc,,
visions/utils/images/image_utils.py,sha256=B-BMoleA9SCAn6-m7Jd5ez2ze3ngRaEqTK0YonRPgJU,2727
visions/utils/monkeypatches/__init__.py,sha256=WrJxXB3alDsZMde-xRZ9Y_nCkmxxSuZJL3QCTdXMY5s,255
visions/utils/monkeypatches/__pycache__/__init__.cpython-310.pyc,,
visions/utils/monkeypatches/__pycache__/imghdr_patch.cpython-310.pyc,,
visions/utils/monkeypatches/__pycache__/pathlib_patch.cpython-310.pyc,,
visions/utils/monkeypatches/imghdr_patch.py,sha256=A0zmTc328bww5u_4cUWQ9szr8bVbpXxsPWfV9Mn23UE,680
visions/utils/monkeypatches/pathlib_patch.py,sha256=Uayz62nhYQsU65_x0eIZMHgBzaFpNflTA4_ECjXNYmA,307
visions/utils/profiling.py,sha256=UZT7y0GuAHB7nhwO-xYMFMbiBFhn0FMXFxKOyMds3-k,2419
visions/utils/warning_handling.py,sha256=vp5W-_LKGFA5NKGG6tiZvfacgOPxNpPSZ3BJfkea2XQ,926
visions/visualisation/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
visions/visualisation/__pycache__/__init__.cpython-310.pyc,,
visions/visualisation/__pycache__/plot_circular_packing.cpython-310.pyc,,
visions/visualisation/__pycache__/plot_typesets.cpython-310.pyc,,
visions/visualisation/circular_packing.html,sha256=7cYCoK03anIqhk0M66W1isgN0kS78J5GuDYvNJnyics,5006
visions/visualisation/plot_circular_packing.py,sha256=-2Ojz8hOlXSZP91FXzvE9OT5B5Dkn2_GDnrP9pLazyU,1848
visions/visualisation/plot_typesets.py,sha256=0JkANoiMX_S0tQbMhnt6lxfMxd-D_D_Hentu48UeZfA,1157
