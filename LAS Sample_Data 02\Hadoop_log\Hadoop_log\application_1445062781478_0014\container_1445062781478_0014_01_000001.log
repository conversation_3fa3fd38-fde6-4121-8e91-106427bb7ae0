2015-10-17 15:38:02,829 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445062781478_0014_000001
2015-10-17 15:38:04,032 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 15:38:04,032 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 14 cluster_timestamp: 1445062781478 } attemptId: 1 } keyId: 471522253)
2015-10-17 15:38:04,579 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 15:38:06,095 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 15:38:06,204 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 15:38:06,267 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 15:38:06,267 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 15:38:06,267 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 15:38:06,267 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 15:38:06,267 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 15:38:06,282 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 15:38:06,282 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 15:38:06,282 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 15:38:06,360 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 15:38:06,392 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 15:38:06,439 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 15:38:06,751 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 15:38:06,845 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 15:38:07,157 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 15:38:07,220 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 15:38:07,220 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 15:38:07,220 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445062781478_0014 to jobTokenSecretManager
2015-10-17 15:38:07,470 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445062781478_0014 because: not enabled; too many maps; too much input;
2015-10-17 15:38:07,485 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445062781478_0014 = 1256521728. Number of splits = 10
2015-10-17 15:38:07,501 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445062781478_0014 = 1
2015-10-17 15:38:07,501 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0014Job Transitioned from NEW to INITED
2015-10-17 15:38:07,501 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445062781478_0014.
2015-10-17 15:38:07,548 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 15:38:07,595 INFO [Socket Reader #1 for port 49465] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 49465
2015-10-17 15:38:07,611 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 15:38:07,611 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MININT-FNANLI5.fareast.corp.microsoft.com/*************:49465
2015-10-17 15:38:07,642 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 15:38:07,642 INFO [IPC Server listener on 49465] org.apache.hadoop.ipc.Server: IPC Server listener on 49465: starting
2015-10-17 15:38:07,736 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 15:38:07,736 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 15:38:07,751 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 15:38:07,767 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 15:38:07,767 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 15:38:07,767 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 15:38:07,767 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 15:38:07,798 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 49472
2015-10-17 15:38:07,798 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 15:38:07,907 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\Jetty_0_0_0_0_49472_mapreduce____hrelxr\webapp
2015-10-17 15:38:08,126 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:49472
2015-10-17 15:38:08,126 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 49472
2015-10-17 15:38:08,845 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 15:38:08,845 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445062781478_0014
2015-10-17 15:38:08,861 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 15:38:08,861 INFO [Socket Reader #1 for port 49479] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 49479
2015-10-17 15:38:08,861 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 15:38:08,861 INFO [IPC Server listener on 49479] org.apache.hadoop.ipc.Server: IPC Server listener on 49479: starting
2015-10-17 15:38:08,907 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 15:38:08,907 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 15:38:08,907 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 15:38:09,095 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-17 15:38:09,345 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 15:38:09,345 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 15:38:09,345 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 15:38:09,345 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 15:38:09,454 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0014Job Transitioned from INITED to SETUP
2015-10-17 15:38:09,486 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 15:38:09,501 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0014Job Transitioned from SETUP to RUNNING
2015-10-17 15:38:09,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:09,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:09,626 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445062781478_0014, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0014/job_1445062781478_0014_1.jhist
2015-10-17 15:38:09,642 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:09,642 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:09,642 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:09,642 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:09,642 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:09,642 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:09,642 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:09,642 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:09,642 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:09,642 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:09,642 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:09,720 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 15:38:09,736 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 15:38:10,454 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 15:38:10,501 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:9216, vCores:-26> knownNMs=5
2015-10-17 15:38:10,533 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-26>
2015-10-17 15:38:10,533 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:11,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-27>
2015-10-17 15:38:11,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:12,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-28>
2015-10-17 15:38:12,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:13,720 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:5120, vCores:-30>
2015-10-17 15:38:13,720 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:14,752 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:38:14,752 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:14,752 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0014_01_000002 to attempt_1445062781478_0014_m_000000_0
2015-10-17 15:38:14,752 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-32>
2015-10-17 15:38:14,752 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:14,752 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:9 ScheduledReds:0 AssignedMaps:1 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:1 ContRel:0 HostLocal:0 RackLocal:1
2015-10-17 15:38:15,017 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:15,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0014/job.jar
2015-10-17 15:38:15,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0014/job.xml
2015-10-17 15:38:15,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 15:38:15,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 15:38:15,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 15:38:15,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:15,611 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0014_01_000002 taskAttempt attempt_1445062781478_0014_m_000000_0
2015-10-17 15:38:15,611 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0014_m_000000_0
2015-10-17 15:38:15,611 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 15:38:16,142 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0014_m_000000_0 : 13562
2015-10-17 15:38:16,142 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0014_m_000000_0] using containerId: [container_1445062781478_0014_01_000002 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:51951]
2015-10-17 15:38:16,158 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:16,158 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0014_m_000000
2015-10-17 15:38:16,158 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:16,236 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:38:16,236 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:16,236 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:17,267 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:38:17,267 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:18,361 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:38:18,361 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:19,392 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:38:19,392 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:20,408 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:38:20,408 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:21,439 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:38:21,439 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:22,455 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:38:22,455 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:22,455 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0014_01_000003 to attempt_1445062781478_0014_m_000001_0
2015-10-17 15:38:22,471 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:22,471 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:22,471 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:8 ScheduledReds:0 AssignedMaps:2 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:2 ContRel:0 HostLocal:0 RackLocal:2
2015-10-17 15:38:22,471 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:22,471 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:22,596 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0014_01_000003 taskAttempt attempt_1445062781478_0014_m_000001_0
2015-10-17 15:38:22,596 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0014_m_000001_0
2015-10-17 15:38:22,596 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 15:38:23,674 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:38:23,674 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:23,674 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:24,205 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0014_m_000001_0 : 13562
2015-10-17 15:38:24,205 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0014_m_000001_0] using containerId: [container_1445062781478_0014_01_000003 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:51951]
2015-10-17 15:38:24,205 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:24,205 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0014_m_000001
2015-10-17 15:38:24,205 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:24,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:24,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:25,752 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:25,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:27,033 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:27,033 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:28,080 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:28,080 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:29,612 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:29,612 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:30,627 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:30,627 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:31,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:31,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:32,737 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:32,737 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:33,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:33,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:34,284 INFO [Socket Reader #1 for port 49479] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0014 (auth:SIMPLE)
2015-10-17 15:38:34,924 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:34,924 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:34,987 INFO [Socket Reader #1 for port 49479] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0014 (auth:SIMPLE)
2015-10-17 15:38:35,284 INFO [IPC Server handler 23 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0014_m_000003 asked for a task
2015-10-17 15:38:35,284 INFO [IPC Server handler 23 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0014_m_000003 given task: attempt_1445062781478_0014_m_000001_0
2015-10-17 15:38:35,846 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0014_m_000002 asked for a task
2015-10-17 15:38:35,846 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0014_m_000002 given task: attempt_1445062781478_0014_m_000000_0
2015-10-17 15:38:35,987 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:35,987 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:37,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:37,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:38,143 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:38,143 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:39,175 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:39,175 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:40,206 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:40,206 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:41,269 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:41,269 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:42,378 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:42,394 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:43,409 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:43,409 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:44,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:44,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:45,519 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:45,519 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:46,628 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:46,628 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:47,628 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:47,628 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:49,613 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:49,613 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:50,707 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:50,707 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:50,785 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.0026013367
2015-10-17 15:38:51,144 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.006186413
2015-10-17 15:38:51,769 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:51,769 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:52,879 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:52,879 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:53,926 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:53,926 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:54,816 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.011072139
2015-10-17 15:38:55,051 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:55,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:55,816 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.0026013367
2015-10-17 15:38:56,176 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:56,176 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:57,238 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:57,238 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:58,019 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.012698554
2015-10-17 15:38:58,363 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:58,363 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:59,457 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:59,457 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:00,520 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:00,520 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:01,832 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:01,832 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:02,145 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.00813942
2015-10-17 15:39:02,301 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.014654793
2015-10-17 15:39:02,895 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:02,895 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:03,973 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:03,973 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:05,270 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.010419053
2015-10-17 15:39:05,410 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:05,410 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:05,473 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.016609054
2015-10-17 15:39:06,801 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:06,801 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:08,817 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:08,817 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:08,895 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.01334904
2015-10-17 15:39:08,989 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.017910236
2015-10-17 15:39:09,926 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:09,926 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:11,067 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:11,067 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:12,161 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.01595485
2015-10-17 15:39:12,208 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:12,208 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:12,208 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.018889518
2015-10-17 15:39:13,380 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:13,380 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:15,020 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:15,020 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:15,333 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.016281031
2015-10-17 15:39:15,364 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.019864935
2015-10-17 15:39:16,083 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:16,083 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:17,146 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:17,146 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:18,255 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:18,255 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:19,896 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:19,896 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:20,083 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.021819549
2015-10-17 15:39:20,208 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.016281031
2015-10-17 15:39:21,005 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:21,005 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:23,474 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:23,474 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:27,771 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:27,771 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:27,802 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.024099
2015-10-17 15:39:28,318 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.02377111
2015-10-17 15:39:28,849 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:28,849 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:30,021 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:30,021 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:31,084 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:31,084 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:32,131 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:32,131 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:33,162 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:33,162 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:34,256 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:34,256 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:35,146 INFO [Socket Reader #1 for port 49479] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0014 (auth:SIMPLE)
2015-10-17 15:39:35,271 INFO [IPC Server handler 12 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.030288022
2015-10-17 15:39:35,365 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:35,365 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:36,975 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:36,975 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:38,037 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:38,037 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:38,444 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.03256664
2015-10-17 15:39:39,131 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:39,131 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:40,225 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:40,225 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:41,772 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.034845714
2015-10-17 15:39:41,834 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:41,834 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:41,975 INFO [Socket Reader #1 for port 49479] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0014 (auth:SIMPLE)
2015-10-17 15:39:42,100 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.04135924
2015-10-17 15:39:42,881 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:42,881 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:43,944 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:43,944 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:44,960 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.03712723
2015-10-17 15:39:45,053 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:45,053 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:45,366 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.048198067
2015-10-17 15:39:46,131 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:46,131 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:47,272 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:47,272 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:48,350 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:48,350 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:48,460 INFO [IPC Server handler 13 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.038430043
2015-10-17 15:39:48,835 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.049827244
2015-10-17 15:39:49,428 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:49,428 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:50,835 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:50,835 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:51,882 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:51,882 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:51,944 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.046569694
2015-10-17 15:39:52,226 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.049827244
2015-10-17 15:39:52,976 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:52,976 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:54,038 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:54,038 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:55,163 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:55,163 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:55,382 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.05210925
2015-10-17 15:39:56,179 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:56,179 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:57,226 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:57,226 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:58,288 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:58,288 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:39:59,007 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.0556898
2015-10-17 15:39:59,413 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:59,413 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:00,460 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:00,460 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:01,492 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:01,492 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:01,804 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.058945794
2015-10-17 15:40:02,226 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.05959762
2015-10-17 15:40:02,585 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:02,585 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:03,632 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:03,632 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:04,711 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:04,711 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:05,070 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.061878078
2015-10-17 15:40:05,461 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.061552737
2015-10-17 15:40:06,226 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:06,226 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:07,351 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:07,351 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:08,320 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.064809084
2015-10-17 15:40:08,461 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:08,461 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:08,648 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.06220443
2015-10-17 15:40:09,539 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:09,539 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:10,570 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:10,570 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:11,414 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.06871515
2015-10-17 15:40:11,617 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:11,617 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:12,602 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.06285455
2015-10-17 15:40:12,727 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:12,727 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:13,805 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:13,805 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:14,852 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:14,852 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:15,336 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.07440853
2015-10-17 15:40:15,914 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:15,914 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:16,086 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.0664376
2015-10-17 15:40:16,992 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:16,992 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:18,164 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:18,164 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:18,524 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.078487396
2015-10-17 15:40:19,211 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:19,211 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:20,243 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:20,243 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:21,055 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.06969331
2015-10-17 15:40:21,258 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:21,258 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:21,618 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.08239283
2015-10-17 15:40:23,524 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:23,524 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:24,258 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.072625615
2015-10-17 15:40:24,571 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:24,571 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:25,680 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:25,680 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:25,790 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.08565241
2015-10-17 15:40:26,759 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:26,759 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:27,493 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.074905306
2015-10-17 15:40:27,806 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:27,806 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:28,837 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:28,837 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:30,274 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:30,274 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:31,306 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:31,306 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:31,337 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.07816382
2015-10-17 15:40:32,431 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:32,431 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:33,509 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:33,509 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:34,556 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:34,556 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:34,556 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.080118164
2015-10-17 15:40:35,306 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.08728028
2015-10-17 15:40:35,603 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:40:35,603 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0014_01_000004 to attempt_1445062781478_0014_m_000002_0
2015-10-17 15:40:35,603 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:35,603 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:35,603 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:7 ScheduledReds:0 AssignedMaps:3 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:3 ContRel:0 HostLocal:1 RackLocal:2
2015-10-17 15:40:35,603 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:40:35,603 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:40:35,618 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0014_01_000004 taskAttempt attempt_1445062781478_0014_m_000002_0
2015-10-17 15:40:35,618 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0014_m_000002_0
2015-10-17 15:40:35,618 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:40:35,853 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0014_m_000002_0 : 13562
2015-10-17 15:40:35,853 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0014_m_000002_0] using containerId: [container_1445062781478_0014_01_000004 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:40:35,853 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:40:35,853 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0014_m_000002
2015-10-17 15:40:35,853 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:40:36,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:40:36,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:36,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:37,681 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:37,681 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:38,228 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.082397096
2015-10-17 15:40:38,509 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.08988509
2015-10-17 15:40:39,072 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:39,072 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:40,119 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:40,119 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:40,166 INFO [Socket Reader #1 for port 49479] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0014 (auth:SIMPLE)
2015-10-17 15:40:40,259 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0014_m_000004 asked for a task
2015-10-17 15:40:40,259 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0014_m_000004 given task: attempt_1445062781478_0014_m_000002_0
2015-10-17 15:40:41,197 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:41,197 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:42,103 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.08467679
2015-10-17 15:40:42,228 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:42,228 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:42,509 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.09509709
2015-10-17 15:40:43,275 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:43,275 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:44,291 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:44,291 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:46,260 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.09639881
2015-10-17 15:40:46,260 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.08532719
2015-10-17 15:40:46,275 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:46,275 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:47,307 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:47,307 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:49,057 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.033100363
2015-10-17 15:40:49,275 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:49,275 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:49,447 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.08695655
2015-10-17 15:40:49,447 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.101282895
2015-10-17 15:40:50,322 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:50,322 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:51,369 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 15:40:51,369 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0014_01_000005 to attempt_1445062781478_0014_m_000003_0
2015-10-17 15:40:51,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:40:51,369 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0014_01_000006 to attempt_1445062781478_0014_m_000004_0
2015-10-17 15:40:51,369 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:51,369 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:51,369 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:5 ContRel:0 HostLocal:3 RackLocal:2
2015-10-17 15:40:51,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:40:51,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:40:51,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:40:51,494 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0014_01_000005 taskAttempt attempt_1445062781478_0014_m_000003_0
2015-10-17 15:40:51,494 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0014_m_000003_0
2015-10-17 15:40:51,494 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:40:51,619 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0014_01_000006 taskAttempt attempt_1445062781478_0014_m_000004_0
2015-10-17 15:40:51,619 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0014_m_000004_0
2015-10-17 15:40:51,619 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:40:52,119 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.06792809
2015-10-17 15:40:52,229 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0014_m_000004_0 : 13562
2015-10-17 15:40:52,229 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0014_m_000003_0 : 13562
2015-10-17 15:40:52,229 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0014_m_000004_0] using containerId: [container_1445062781478_0014_01_000006 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:40:52,229 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:40:52,229 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0014_m_000003_0] using containerId: [container_1445062781478_0014_01_000005 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:40:52,229 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:40:52,229 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0014_m_000004
2015-10-17 15:40:52,229 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:40:52,229 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0014_m_000003
2015-10-17 15:40:52,229 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:40:52,401 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0014: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:40:52,401 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:40:52,401 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0014_01_000007 to attempt_1445062781478_0014_m_000005_0
2015-10-17 15:40:52,401 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:52,401 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:52,401 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:6 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:4 RackLocal:2
2015-10-17 15:40:52,401 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:40:52,401 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:40:52,494 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0014_01_000007 taskAttempt attempt_1445062781478_0014_m_000005_0
2015-10-17 15:40:52,494 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0014_m_000005_0
2015-10-17 15:40:52,494 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:40:52,729 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0014_m_000005_0 : 13562
2015-10-17 15:40:52,729 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0014_m_000005_0] using containerId: [container_1445062781478_0014_01_000007 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:40:52,729 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:40:52,729 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0014_m_000005
2015-10-17 15:40:52,729 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:40:53,494 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0014: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:40:53,494 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:40:53,494 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0014_01_000008 to attempt_1445062781478_0014_m_000006_0
2015-10-17 15:40:53,494 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:53,494 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:53,494 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:5 RackLocal:2
2015-10-17 15:40:53,494 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:40:53,494 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:40:53,526 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.1113806
2015-10-17 15:40:53,651 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0014_01_000008 taskAttempt attempt_1445062781478_0014_m_000006_0
2015-10-17 15:40:53,651 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0014_m_000006_0
2015-10-17 15:40:53,651 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:40:53,885 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.08858357
2015-10-17 15:40:53,901 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0014_m_000006_0 : 13562
2015-10-17 15:40:53,901 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0014_m_000006_0] using containerId: [container_1445062781478_0014_01_000008 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:40:53,901 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:40:53,901 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0014_m_000006
2015-10-17 15:40:53,901 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:40:54,557 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:40:54,557 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:54,557 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:54,885 INFO [Socket Reader #1 for port 49479] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0014 (auth:SIMPLE)
2015-10-17 15:40:54,948 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0014_m_000006 asked for a task
2015-10-17 15:40:54,948 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0014_m_000006 given task: attempt_1445062781478_0014_m_000004_0
2015-10-17 15:40:55,182 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.08938207
2015-10-17 15:40:55,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:55,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:56,682 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:56,682 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:57,057 INFO [Socket Reader #1 for port 49479] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0014 (auth:SIMPLE)
2015-10-17 15:40:57,104 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0014_m_000005 asked for a task
2015-10-17 15:40:57,104 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0014_m_000005 given task: attempt_1445062781478_0014_m_000003_0
2015-10-17 15:40:57,495 INFO [Socket Reader #1 for port 49479] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0014 (auth:SIMPLE)
2015-10-17 15:40:57,526 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0014_m_000008 asked for a task
2015-10-17 15:40:57,526 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0014_m_000008 given task: attempt_1445062781478_0014_m_000006_0
2015-10-17 15:40:57,713 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:57,713 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:57,760 INFO [Socket Reader #1 for port 49479] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0014 (auth:SIMPLE)
2015-10-17 15:40:57,792 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0014_m_000007 asked for a task
2015-10-17 15:40:57,792 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0014_m_000007 given task: attempt_1445062781478_0014_m_000005_0
2015-10-17 15:40:58,213 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.10660437
2015-10-17 15:40:58,760 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:58,760 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:40:59,276 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.123071566
2015-10-17 15:40:59,776 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:40:59,776 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:00,417 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.10438515
2015-10-17 15:41:01,182 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:01,182 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:01,245 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.10660437
2015-10-17 15:41:02,198 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:02,198 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:02,292 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.056382008
2015-10-17 15:41:02,651 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.13515475
2015-10-17 15:41:03,276 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:03,276 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:04,276 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.10660437
2015-10-17 15:41:04,308 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:04,308 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:05,026 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.0987174
2015-10-17 15:41:05,276 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.093559794
2015-10-17 15:41:05,339 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:05,339 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:05,339 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.09129557
2015-10-17 15:41:05,511 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.09387323
2015-10-17 15:41:06,151 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.14850804
2015-10-17 15:41:06,370 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:06,370 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:07,386 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.1321737
2015-10-17 15:41:07,464 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:07,464 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:08,042 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.106964506
2015-10-17 15:41:08,339 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.106493875
2015-10-17 15:41:08,417 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.10680563
2015-10-17 15:41:08,511 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:08,511 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:08,652 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.10685723
2015-10-17 15:41:09,511 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.16055883
2015-10-17 15:41:09,527 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:09,527 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:10,417 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.1855395
2015-10-17 15:41:10,589 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:10,589 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:11,058 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.106964506
2015-10-17 15:41:11,386 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.106493875
2015-10-17 15:41:11,449 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.10680563
2015-10-17 15:41:11,636 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:11,636 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:11,699 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.10685723
2015-10-17 15:41:12,199 INFO [Socket Reader #1 for port 49479] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0014 (auth:SIMPLE)
2015-10-17 15:41:12,355 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.11782494
2015-10-17 15:41:12,667 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:12,667 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:13,449 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.19212553
2015-10-17 15:41:13,714 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 15:41:13,714 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0014_01_000009 to attempt_1445062781478_0014_m_000007_0
2015-10-17 15:41:13,714 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0014_01_000010 to attempt_1445062781478_0014_m_000008_0
2015-10-17 15:41:13,714 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:13,714 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:41:13,714 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:13,714 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:7 RackLocal:2
2015-10-17 15:41:13,714 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:41:13,714 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:41:13,714 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:41:13,824 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0014_01_000009 taskAttempt attempt_1445062781478_0014_m_000007_0
2015-10-17 15:41:13,824 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0014_m_000007_0
2015-10-17 15:41:13,824 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:41:13,886 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0014_01_000010 taskAttempt attempt_1445062781478_0014_m_000008_0
2015-10-17 15:41:13,886 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0014_m_000008_0
2015-10-17 15:41:13,886 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:41:14,105 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.106964506
2015-10-17 15:41:14,121 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0014_m_000008_0 : 13562
2015-10-17 15:41:14,121 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0014_m_000008_0] using containerId: [container_1445062781478_0014_01_000010 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:41:14,121 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:41:14,121 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0014_m_000008
2015-10-17 15:41:14,121 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:41:14,136 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0014_m_000007_0 : 13562
2015-10-17 15:41:14,136 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0014_m_000007_0] using containerId: [container_1445062781478_0014_01_000009 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:41:14,136 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:41:14,136 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0014_m_000007
2015-10-17 15:41:14,136 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:41:14,433 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.11272242
2015-10-17 15:41:14,496 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.10680563
2015-10-17 15:41:14,746 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.117505655
2015-10-17 15:41:14,761 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:41:14,761 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:14,761 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:15,261 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.18773751
2015-10-17 15:41:16,058 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.1390637
2015-10-17 15:41:16,152 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:16,152 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:16,543 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.19212553
2015-10-17 15:41:17,199 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.11780302
2015-10-17 15:41:17,199 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:17,199 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:17,293 INFO [Socket Reader #1 for port 49479] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0014 (auth:SIMPLE)
2015-10-17 15:41:17,324 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0014_m_000009 asked for a task
2015-10-17 15:41:17,324 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0014_m_000009 given task: attempt_1445062781478_0014_m_000007_0
2015-10-17 15:41:17,464 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.17065884
2015-10-17 15:41:17,527 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.12913415
2015-10-17 15:41:17,824 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.17395099
2015-10-17 15:41:18,246 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:18,246 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:18,840 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.19211523
2015-10-17 15:41:19,168 INFO [Socket Reader #1 for port 49479] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0014 (auth:SIMPLE)
2015-10-17 15:41:19,246 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0014_m_000010 asked for a task
2015-10-17 15:41:19,246 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0014_m_000010 given task: attempt_1445062781478_0014_m_000008_0
2015-10-17 15:41:19,308 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:19,308 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:19,527 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.15078963
2015-10-17 15:41:21,058 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.17839141
2015-10-17 15:41:21,168 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.19247705
2015-10-17 15:41:21,277 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:21,277 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:21,402 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.19209063
2015-10-17 15:41:21,480 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.1661055
2015-10-17 15:41:21,699 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.2139396
2015-10-17 15:41:22,340 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:22,340 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:22,371 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.19211523
2015-10-17 15:41:23,027 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.16996601
2015-10-17 15:41:23,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:23,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:24,121 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.19266446
2015-10-17 15:41:24,246 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.19247705
2015-10-17 15:41:24,496 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.19209063
2015-10-17 15:41:24,527 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:24,527 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:24,559 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.19242907
2015-10-17 15:41:24,574 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.10681946
2015-10-17 15:41:25,559 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:25,559 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:25,887 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.19211523
2015-10-17 15:41:26,543 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.08007775
2015-10-17 15:41:26,543 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.1849882
2015-10-17 15:41:26,606 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:41:26,606 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:27,184 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.19266446
2015-10-17 15:41:27,356 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.1947282
2015-10-17 15:41:27,590 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.19445853
2015-10-17 15:41:27,621 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.10681946
2015-10-17 15:41:27,637 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.19242907
2015-10-17 15:41:27,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:41:27,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0014_01_000011 to attempt_1445062781478_0014_m_000009_0
2015-10-17 15:41:27,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:41:27,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:41:27,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:8 RackLocal:2
2015-10-17 15:41:27,668 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:41:27,668 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:41:27,840 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0014_01_000011 taskAttempt attempt_1445062781478_0014_m_000009_0
2015-10-17 15:41:27,840 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0014_m_000009_0
2015-10-17 15:41:27,840 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:41:28,746 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-34> knownNMs=5
2015-10-17 15:41:29,278 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.27772525
2015-10-17 15:41:29,278 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.19211523
2015-10-17 15:41:29,668 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.10124827
2015-10-17 15:41:30,137 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.19158794
2015-10-17 15:41:30,200 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.19266446
2015-10-17 15:41:31,075 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.2180345
2015-10-17 15:41:31,075 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.10681946
2015-10-17 15:41:31,090 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.21846287
2015-10-17 15:41:31,621 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.19242907
2015-10-17 15:41:32,372 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.27772525
2015-10-17 15:41:32,762 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.106881365
2015-10-17 15:41:32,825 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.19211523
2015-10-17 15:41:33,278 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.24174096
2015-10-17 15:41:33,809 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.19158794
2015-10-17 15:41:34,231 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.12200699
2015-10-17 15:41:34,231 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.27653205
2015-10-17 15:41:34,231 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.27290985
2015-10-17 15:41:34,762 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.19670714
2015-10-17 15:41:35,450 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.27950376
2015-10-17 15:41:35,825 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.106881365
2015-10-17 15:41:36,341 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.2783809
2015-10-17 15:41:38,075 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.19211523
2015-10-17 15:41:38,091 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.23389652
2015-10-17 15:41:38,091 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.17664061
2015-10-17 15:41:38,091 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.27765483
2015-10-17 15:41:38,091 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.27813601
2015-10-17 15:41:38,091 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.19158794
2015-10-17 15:41:38,481 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.32729435
2015-10-17 15:41:38,872 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.106881365
2015-10-17 15:41:39,372 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.2783809
2015-10-17 15:41:41,106 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.19255035
2015-10-17 15:41:41,169 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.26448107
2015-10-17 15:41:41,169 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.27813601
2015-10-17 15:41:41,169 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.27765483
2015-10-17 15:41:41,544 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.36208498
2015-10-17 15:41:41,591 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.19211523
2015-10-17 15:41:41,747 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.19158794
2015-10-17 15:41:41,919 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.1284047
2015-10-17 15:41:42,388 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.2783809
2015-10-17 15:41:44,107 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.19255035
2015-10-17 15:41:44,185 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.28220204
2015-10-17 15:41:44,185 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.282642
2015-10-17 15:41:44,200 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.2781602
2015-10-17 15:41:44,560 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.36317363
2015-10-17 15:41:44,950 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.1818181
2015-10-17 15:41:45,450 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.2783809
2015-10-17 15:41:45,450 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.19211523
2015-10-17 15:41:45,482 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.19158794
2015-10-17 15:41:47,185 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.19255035
2015-10-17 15:41:47,216 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.3357994
2015-10-17 15:41:47,216 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.3373178
2015-10-17 15:41:47,216 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.2781602
2015-10-17 15:41:47,591 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.36317363
2015-10-17 15:41:47,841 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MSRA-SA-39.fareast.corp.microsoft.com/**************:49130. Already tried 0 time(s); maxRetries=45
2015-10-17 15:41:48,310 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.19258286
2015-10-17 15:41:48,326 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0014_m_000009_0 : 13562
2015-10-17 15:41:48,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0014_m_000009_0] using containerId: [container_1445062781478_0014_01_000011 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:41:48,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:41:48,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0014_m_000009
2015-10-17 15:41:48,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:41:48,482 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.32015583
2015-10-17 15:41:49,138 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.19158794
2015-10-17 15:41:49,747 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.20713286
2015-10-17 15:41:50,216 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.2424024
2015-10-17 15:41:50,279 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.36390656
2015-10-17 15:41:50,279 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.2781602
2015-10-17 15:41:50,279 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.36323506
2015-10-17 15:41:50,654 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.37995335
2015-10-17 15:41:51,404 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.19258286
2015-10-17 15:41:51,419 INFO [Socket Reader #1 for port 49479] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0014 (auth:SIMPLE)
2015-10-17 15:41:51,466 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0014_m_000011 asked for a task
2015-10-17 15:41:51,466 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0014_m_000011 given task: attempt_1445062781478_0014_m_000009_0
2015-10-17 15:41:51,529 INFO [IPC Server handler 13 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.36404583
2015-10-17 15:41:52,591 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.19158794
2015-10-17 15:41:53,248 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.2247191
2015-10-17 15:41:53,295 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.27825075
2015-10-17 15:41:53,310 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.36390656
2015-10-17 15:41:53,310 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.36323506
2015-10-17 15:41:53,310 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.30779752
2015-10-17 15:41:53,701 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.43481717
2015-10-17 15:41:54,466 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.20630333
2015-10-17 15:41:54,576 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.36404583
2015-10-17 15:41:56,342 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.27825075
2015-10-17 15:41:56,357 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.36390656
2015-10-17 15:41:56,357 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.33595073
2015-10-17 15:41:56,388 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.36323506
2015-10-17 15:41:56,732 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.44859612
2015-10-17 15:41:56,795 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.19158794
2015-10-17 15:41:56,951 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.25229558
2015-10-17 15:41:57,498 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.2421705
2015-10-17 15:41:57,576 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.36404583
2015-10-17 15:41:58,842 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000009_0 is : 0.295472
2015-10-17 15:41:59,357 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.27825075
2015-10-17 15:41:59,373 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.38907903
2015-10-17 15:41:59,435 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.36388028
2015-10-17 15:41:59,435 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.38725433
2015-10-17 15:41:59,779 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.44859612
2015-10-17 15:42:00,529 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.27372763
2015-10-17 15:42:00,639 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.3667676
2015-10-17 15:42:00,639 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.20564285
2015-10-17 15:42:00,826 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.27776006
2015-10-17 15:42:02,217 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000009_0 is : 0.295472
2015-10-17 15:42:02,420 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.29343957
2015-10-17 15:42:02,498 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.41457453
2015-10-17 15:42:02,498 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.36388028
2015-10-17 15:42:02,498 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.41308898
2015-10-17 15:42:02,873 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.44859612
2015-10-17 15:42:03,576 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.27811313
2015-10-17 15:42:03,686 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.4204561
2015-10-17 15:42:04,201 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.23618867
2015-10-17 15:42:04,420 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.27776006
2015-10-17 15:42:05,295 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000009_0 is : 0.295472
2015-10-17 15:42:05,514 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.3520843
2015-10-17 15:42:05,576 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.44950968
2015-10-17 15:42:05,576 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.36388028
2015-10-17 15:42:05,576 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.4486067
2015-10-17 15:42:05,936 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.44859612
2015-10-17 15:42:06,670 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.27811313
2015-10-17 15:42:06,764 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.448903
2015-10-17 15:42:08,014 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.26825547
2015-10-17 15:42:08,248 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.27776006
2015-10-17 15:42:08,405 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000009_0 is : 0.33377063
2015-10-17 15:42:08,545 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.3638923
2015-10-17 15:42:08,592 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.44950968
2015-10-17 15:42:08,670 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.38486633
2015-10-17 15:42:08,670 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.4486067
2015-10-17 15:42:08,983 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.48342663
2015-10-17 15:42:09,749 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.27934262
2015-10-17 15:42:09,795 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.44980705
2015-10-17 15:42:11,405 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000009_0 is : 0.5323719
2015-10-17 15:42:11,561 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.3638923
2015-10-17 15:42:11,577 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.27696857
2015-10-17 15:42:11,655 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.44950968
2015-10-17 15:42:11,702 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.41618666
2015-10-17 15:42:11,717 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.4486067
2015-10-17 15:42:11,952 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.27776006
2015-10-17 15:42:12,061 INFO [IPC Server handler 23 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.52069014
2015-10-17 15:42:14,436 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000009_0 is : 0.5323719
2015-10-17 15:42:14,593 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.3638923
2015-10-17 15:42:14,702 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.45741966
2015-10-17 15:42:14,749 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.44136488
2015-10-17 15:42:14,764 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.45884338
2015-10-17 15:42:14,921 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.3094447
2015-10-17 15:42:14,936 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.44980705
2015-10-17 15:42:15,077 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.5342037
2015-10-17 15:42:15,171 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.27696857
2015-10-17 15:42:15,608 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.27776006
2015-10-17 15:42:17,468 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000009_0 is : 0.5323719
2015-10-17 15:42:17,593 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.44503257
2015-10-17 15:42:17,749 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.515495
2015-10-17 15:42:17,796 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.44968578
2015-10-17 15:42:17,796 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.5168375
2015-10-17 15:42:17,936 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.48900422
2015-10-17 15:42:17,952 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.3637686
2015-10-17 15:42:18,437 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.5342037
2015-10-17 15:42:19,155 INFO [IPC Server handler 23 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.27696857
2015-10-17 15:42:20,530 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000009_0 is : 0.6206222
2015-10-17 15:42:20,640 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.44964966
2015-10-17 15:42:20,812 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.5352021
2015-10-17 15:42:20,859 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.44968578
2015-10-17 15:42:20,859 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.5343203
2015-10-17 15:42:21,046 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.5299981
2015-10-17 15:42:21,046 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.3637686
2015-10-17 15:42:21,046 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000009_0 is : 0.6206222
2015-10-17 15:42:21,515 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.5342789
2015-10-17 15:42:22,577 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.27776006
2015-10-17 15:42:22,781 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.27696857
2015-10-17 15:42:24,171 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.44968578
2015-10-17 15:42:24,202 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.5343203
2015-10-17 15:42:24,359 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.53543663
2015-10-17 15:42:24,390 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.36834523
2015-10-17 15:42:24,468 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000009_0 is : 0.667
2015-10-17 15:42:25,468 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.5924232
2015-10-17 15:42:26,328 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.27776006
2015-10-17 15:42:26,515 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.27696857
2015-10-17 15:42:27,265 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.47122642
2015-10-17 15:42:27,265 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.5343203
2015-10-17 15:42:27,453 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.53543663
2015-10-17 15:42:27,468 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.4005388
2015-10-17 15:42:27,515 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000009_0 is : 0.667
2015-10-17 15:42:28,375 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.44964966
2015-10-17 15:42:28,375 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.5352021
2015-10-17 15:42:28,546 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.6196791
2015-10-17 15:42:29,859 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.27776006
2015-10-17 15:42:30,343 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.27696857
2015-10-17 15:42:30,343 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.557781
2015-10-17 15:42:30,343 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.49919885
2015-10-17 15:42:30,484 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.53543663
2015-10-17 15:42:30,500 INFO [IPC Server handler 7 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.43165213
2015-10-17 15:42:30,562 INFO [IPC Server handler 12 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000009_0 is : 0.688893
2015-10-17 15:42:31,422 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.5352825
2015-10-17 15:42:31,422 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.57640845
2015-10-17 15:42:31,609 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.6196791
2015-10-17 15:42:33,359 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.6096554
2015-10-17 15:42:33,359 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.529893
2015-10-17 15:42:33,500 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.5852704
2015-10-17 15:42:33,531 INFO [IPC Server handler 7 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.44950172
2015-10-17 15:42:33,562 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000009_0 is : 0.79972637
2015-10-17 15:42:33,859 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.27776006
2015-10-17 15:42:33,906 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.27696857
2015-10-17 15:42:34,437 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.5352825
2015-10-17 15:42:34,437 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.6209487
2015-10-17 15:42:34,625 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.6196791
2015-10-17 15:42:36,422 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.5352028
2015-10-17 15:42:36,422 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.6199081
2015-10-17 15:42:36,578 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.6210422
2015-10-17 15:42:36,594 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.44950172
2015-10-17 15:42:36,656 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000009_0 is : 0.90841556
2015-10-17 15:42:37,344 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.27696857
2015-10-17 15:42:37,484 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.5352825
2015-10-17 15:42:37,516 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.6209487
2015-10-17 15:42:37,531 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.3010337
2015-10-17 15:42:37,594 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:42:37,609 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:42:37,719 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.6440806
2015-10-17 15:42:39,219 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.6440806
2015-10-17 15:42:39,485 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.5352028
2015-10-17 15:42:39,485 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.6199081
2015-10-17 15:42:39,625 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.6210422
2015-10-17 15:42:39,625 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.45141345
2015-10-17 15:42:39,625 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000009_0 is : 1.0
2015-10-17 15:42:39,641 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0014_m_000009_0
2015-10-17 15:42:39,656 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:42:39,656 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0014_01_000011 taskAttempt attempt_1445062781478_0014_m_000009_0
2015-10-17 15:42:39,672 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0014_m_000009_0
2015-10-17 15:42:39,672 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:42:39,938 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:42:39,953 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0014_m_000009_0
2015-10-17 15:42:39,953 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:42:39,953 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-17 15:42:40,125 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0014_m_000000
2015-10-17 15:42:40,141 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 15:42:40,141 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0014_m_000000
2015-10-17 15:42:40,141 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:42:40,141 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:42:40,141 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000000_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:42:40,219 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:8 RackLocal:2
2015-10-17 15:42:40,235 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:42:40,235 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:42:40,235 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-17 15:42:40,235 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:10240, vCores:-25> finalMapResourceLimit:<memory:9216, vCores:-23> finalReduceResourceLimit:<memory:1024, vCores:-2> netScheduledMapResource:<memory:11264, vCores:11> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-17 15:42:40,235 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping up 1
2015-10-17 15:42:40,235 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:8 RackLocal:2
2015-10-17 15:42:40,531 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.5472678
2015-10-17 15:42:40,578 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.6209487
2015-10-17 15:42:41,266 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0014: ask=1 release= 0 newContainers=1 finishedContainers=1 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:42:41,266 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0014_01_000011
2015-10-17 15:42:41,266 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.33752632
2015-10-17 15:42:41,266 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:42:41,266 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0014_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:42:41,266 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0014_01_000012 to attempt_1445062781478_0014_m_000000_1
2015-10-17 15:42:41,266 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:2
2015-10-17 15:42:41,266 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:42:41,266 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000000_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:42:41,266 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0014_01_000012 taskAttempt attempt_1445062781478_0014_m_000000_1
2015-10-17 15:42:41,266 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0014_m_000000_1
2015-10-17 15:42:41,266 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:42:41,407 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0014_m_000000_1 : 13562
2015-10-17 15:42:41,407 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0014_m_000000_1] using containerId: [container_1445062781478_0014_01_000012 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:42:41,407 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000000_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:42:41,407 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0014_m_000000
2015-10-17 15:42:41,485 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.27696857
2015-10-17 15:42:41,703 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.667
2015-10-17 15:42:42,266 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:42:43,407 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.64092004
2015-10-17 15:42:43,407 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.5352028
2015-10-17 15:42:43,563 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.51262444
2015-10-17 15:42:43,563 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.60264087
2015-10-17 15:42:43,579 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.6210422
2015-10-17 15:42:43,625 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.65392905
2015-10-17 15:42:44,782 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.667
2015-10-17 15:42:44,829 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.35489076
2015-10-17 15:42:44,891 INFO [Socket Reader #1 for port 49479] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0014 (auth:SIMPLE)
2015-10-17 15:42:44,985 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0014_m_000012 asked for a task
2015-10-17 15:42:44,985 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0014_m_000012 given task: attempt_1445062781478_0014_m_000000_1
2015-10-17 15:42:45,079 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.65392905
2015-10-17 15:42:45,141 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.29506233
2015-10-17 15:42:45,141 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.64092004
2015-10-17 15:42:46,532 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.5722932
2015-10-17 15:42:46,547 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.667
2015-10-17 15:42:46,641 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.6210422
2015-10-17 15:42:46,641 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.53521925
2015-10-17 15:42:46,641 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.620844
2015-10-17 15:42:46,719 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.667
2015-10-17 15:42:47,532 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:42:47,532 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 15:42:47,532 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0014_01_000013 to attempt_1445062781478_0014_r_000000_0
2015-10-17 15:42:47,532 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:9 RackLocal:2
2015-10-17 15:42:47,563 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:42:47,563 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:42:47,563 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0014_01_000013 taskAttempt attempt_1445062781478_0014_r_000000_0
2015-10-17 15:42:47,563 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0014_r_000000_0
2015-10-17 15:42:47,563 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:42:47,876 INFO [IPC Server handler 7 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.667
2015-10-17 15:42:47,938 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0014_r_000000_0 : 13562
2015-10-17 15:42:47,938 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0014_r_000000_0] using containerId: [container_1445062781478_0014_01_000013 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:42:47,938 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:42:47,938 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0014_r_000000
2015-10-17 15:42:47,938 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:42:48,313 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.36319977
2015-10-17 15:42:48,563 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0014: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:42:48,798 INFO [IPC Server handler 7 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.33216318
2015-10-17 15:42:49,594 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.667
2015-10-17 15:42:49,594 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.59708977
2015-10-17 15:42:49,688 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.66542673
2015-10-17 15:42:49,688 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.620844
2015-10-17 15:42:49,704 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.53521925
2015-10-17 15:42:49,782 INFO [IPC Server handler 7 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.66542673
2015-10-17 15:42:49,798 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.667
2015-10-17 15:42:51,016 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.68190396
2015-10-17 15:42:52,001 INFO [Socket Reader #1 for port 49479] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0014 (auth:SIMPLE)
2015-10-17 15:42:52,001 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.36319977
2015-10-17 15:42:52,110 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0014_r_000013 asked for a task
2015-10-17 15:42:52,110 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0014_r_000013 given task: attempt_1445062781478_0014_r_000000_0
2015-10-17 15:42:52,485 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.10635664
2015-10-17 15:42:52,720 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.667
2015-10-17 15:42:52,720 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.6208445
2015-10-17 15:42:52,751 INFO [IPC Server handler 7 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.667
2015-10-17 15:42:52,751 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.620844
2015-10-17 15:42:52,751 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.36071417
2015-10-17 15:42:52,829 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.53521925
2015-10-17 15:42:52,860 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.667
2015-10-17 15:42:54,360 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 0 maxEvents 10000
2015-10-17 15:42:54,360 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.70016396
2015-10-17 15:42:55,173 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0014_m_000001
2015-10-17 15:42:55,173 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0014_m_000001
2015-10-17 15:42:55,173 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 15:42:55,173 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:42:55,173 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:42:55,173 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000001_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:42:55,251 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:9 RackLocal:2
2015-10-17 15:42:55,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-35> knownNMs=5
2015-10-17 15:42:55,392 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:42:55,564 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.10635664
2015-10-17 15:42:55,798 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.6208445
2015-10-17 15:42:55,798 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.64209974
2015-10-17 15:42:55,798 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.667
2015-10-17 15:42:55,798 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.667
2015-10-17 15:42:55,876 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.53521925
2015-10-17 15:42:55,939 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.6696649
2015-10-17 15:42:56,126 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.36319977
2015-10-17 15:42:56,314 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:42:56,314 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0014_01_000014 to attempt_1445062781478_0014_m_000001_1
2015-10-17 15:42:56,314 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 15:42:56,314 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:42:56,314 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000001_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:42:56,314 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0014_01_000014 taskAttempt attempt_1445062781478_0014_m_000001_1
2015-10-17 15:42:56,314 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0014_m_000001_1
2015-10-17 15:42:56,314 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:42:56,485 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.3624012
2015-10-17 15:42:56,485 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:42:56,501 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0014_m_000001_1 : 13562
2015-10-17 15:42:56,501 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0014_m_000001_1] using containerId: [container_1445062781478_0014_01_000014 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:42:56,501 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000001_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:42:56,501 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0014_m_000001
2015-10-17 15:42:57,298 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.64209974
2015-10-17 15:42:57,376 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-36> knownNMs=5
2015-10-17 15:42:57,423 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.7328752
2015-10-17 15:42:57,564 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:42:58,611 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:42:58,611 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.10635664
2015-10-17 15:42:58,829 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.667
2015-10-17 15:42:58,845 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.6790814
2015-10-17 15:42:58,939 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.68535036
2015-10-17 15:42:58,939 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.6208445
2015-10-17 15:42:58,954 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.5593751
2015-10-17 15:42:59,048 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.6932497
2015-10-17 15:42:59,501 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.36319977
2015-10-17 15:42:59,657 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:42:59,892 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.3624012
2015-10-17 15:43:00,314 INFO [Socket Reader #1 for port 49479] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0014 (auth:SIMPLE)
2015-10-17 15:43:00,345 INFO [IPC Server handler 23 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0014_m_000014 asked for a task
2015-10-17 15:43:00,345 INFO [IPC Server handler 23 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0014_m_000014 given task: attempt_1445062781478_0014_m_000001_1
2015-10-17 15:43:00,392 INFO [IPC Server handler 13 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.033333335
2015-10-17 15:43:00,470 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.74721533
2015-10-17 15:43:00,689 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:01,251 INFO [Thread-54] org.apache.hadoop.hdfs.DFSClient: Exception in createBlockOutputStream
java.io.IOException: Bad connect ack with firstBadLink as ***********:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.createBlockOutputStream(DFSOutputStream.java:1461)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.nextBlockOutputStream(DFSOutputStream.java:1362)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.run(DFSOutputStream.java:589)
2015-10-17 15:43:01,251 INFO [Thread-54] org.apache.hadoop.hdfs.DFSClient: Abandoning BP-1347369012-**************-1444972147527:blk_1073742518_1713
2015-10-17 15:43:01,423 INFO [Thread-54] org.apache.hadoop.hdfs.DFSClient: Excluding datanode ***********:50010
2015-10-17 15:43:01,626 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.10635664
2015-10-17 15:43:01,704 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:01,876 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.667
2015-10-17 15:43:01,876 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.708213
2015-10-17 15:43:01,970 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.7004056
2015-10-17 15:43:01,970 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.6208445
2015-10-17 15:43:02,033 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.5800403
2015-10-17 15:43:02,111 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.707928
2015-10-17 15:43:02,720 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:02,970 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.36319977
2015-10-17 15:43:03,423 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.033333335
2015-10-17 15:43:03,486 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.3624012
2015-10-17 15:43:03,501 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.76165164
2015-10-17 15:43:03,720 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:04,673 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.13672902
2015-10-17 15:43:04,752 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:04,892 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.74120915
2015-10-17 15:43:04,892 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.667
2015-10-17 15:43:05,002 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.7144121
2015-10-17 15:43:05,048 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.63618183
2015-10-17 15:43:05,064 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.60061634
2015-10-17 15:43:05,142 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.7215348
2015-10-17 15:43:05,767 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:06,455 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.033333335
2015-10-17 15:43:06,548 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.7744572
2015-10-17 15:43:06,642 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.36319977
2015-10-17 15:43:06,830 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:07,158 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.3624012
2015-10-17 15:43:07,158 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.63618183
2015-10-17 15:43:07,689 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.19158794
2015-10-17 15:43:07,830 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:07,908 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.7794169
2015-10-17 15:43:07,908 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.67915684
2015-10-17 15:43:08,049 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.7280114
2015-10-17 15:43:08,080 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.667
2015-10-17 15:43:08,095 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.6180898
2015-10-17 15:43:08,189 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.734485
2015-10-17 15:43:08,861 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:08,986 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.03133418
2015-10-17 15:43:09,502 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.033333335
2015-10-17 15:43:09,596 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.7881484
2015-10-17 15:43:09,892 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:10,049 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.36319977
2015-10-17 15:43:10,221 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0014_m_000008
2015-10-17 15:43:10,221 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0014_m_000008
2015-10-17 15:43:10,221 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 15:43:10,221 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:10,221 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:10,221 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000008_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:43:10,705 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.3624012
2015-10-17 15:43:10,721 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.19158794
2015-10-17 15:43:10,877 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 15:43:10,892 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-36> knownNMs=5
2015-10-17 15:43:10,908 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:10,924 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.8334881
2015-10-17 15:43:10,924 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.70871925
2015-10-17 15:43:11,314 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.7434307
2015-10-17 15:43:11,314 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.6207798
2015-10-17 15:43:11,314 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.667
2015-10-17 15:43:11,314 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.74982524
2015-10-17 15:43:11,971 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:12,268 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.058332372
2015-10-17 15:43:12,549 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.033333335
2015-10-17 15:43:12,861 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.80340934
2015-10-17 15:43:13,471 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:13,471 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.36319977
2015-10-17 15:43:13,736 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.19158794
2015-10-17 15:43:13,940 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.74055
2015-10-17 15:43:13,940 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.8720401
2015-10-17 15:43:14,143 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.3624012
2015-10-17 15:43:14,346 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.7624841
2015-10-17 15:43:14,346 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.6700765
2015-10-17 15:43:14,361 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.7675286
2015-10-17 15:43:14,361 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.6207798
2015-10-17 15:43:14,486 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:15,518 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:15,565 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.096319355
2015-10-17 15:43:15,580 INFO [IPC Server handler 13 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.033333335
2015-10-17 15:43:17,252 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.82527757
2015-10-17 15:43:17,268 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.90434563
2015-10-17 15:43:17,268 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.76861143
2015-10-17 15:43:17,393 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.7829501
2015-10-17 15:43:17,393 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.6207798
2015-10-17 15:43:17,393 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.7874152
2015-10-17 15:43:17,393 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.70131755
2015-10-17 15:43:17,408 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:17,549 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.36319977
2015-10-17 15:43:17,674 INFO [IPC Server handler 12 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.19158794
2015-10-17 15:43:17,705 INFO [IPC Server handler 23 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.3624012
2015-10-17 15:43:18,440 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:18,627 INFO [IPC Server handler 12 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.1066108
2015-10-17 15:43:18,627 INFO [IPC Server handler 23 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.033333335
2015-10-17 15:43:20,268 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:20,377 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 0.9460989
2015-10-17 15:43:20,377 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.8673495
2015-10-17 15:43:20,377 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.8001921
2015-10-17 15:43:21,268 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.7284845
2015-10-17 15:43:21,268 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.64056194
2015-10-17 15:43:21,268 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.81997335
2015-10-17 15:43:21,268 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.8231298
2015-10-17 15:43:21,284 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:21,487 INFO [IPC Server handler 7 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.3624012
2015-10-17 15:43:21,674 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.1066108
2015-10-17 15:43:21,674 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.033333335
2015-10-17 15:43:22,096 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.38714764
2015-10-17 15:43:22,346 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:23,346 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:23,378 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.8317922
2015-10-17 15:43:23,393 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 1.0
2015-10-17 15:43:23,409 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.8915977
2015-10-17 15:43:23,456 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000006_0 is : 1.0
2015-10-17 15:43:23,456 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0014_m_000006_0
2015-10-17 15:43:23,456 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:43:23,456 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0014_01_000008 taskAttempt attempt_1445062781478_0014_m_000006_0
2015-10-17 15:43:23,456 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0014_m_000006_0
2015-10-17 15:43:23,456 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:43:23,503 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:43:23,503 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0014_m_000006_0
2015-10-17 15:43:23,503 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:43:23,503 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-17 15:43:23,721 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.64056194
2015-10-17 15:43:24,315 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 15:43:24,346 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.667
2015-10-17 15:43:24,362 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.7755275
2015-10-17 15:43:24,362 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.8489043
2015-10-17 15:43:24,378 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:43:24,378 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.8511439
2015-10-17 15:43:24,737 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.1066108
2015-10-17 15:43:24,737 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.033333335
2015-10-17 15:43:24,971 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.36514875
2015-10-17 15:43:25,471 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:43:25,518 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0014_01_000008
2015-10-17 15:43:25,518 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 15:43:25,534 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0014_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:43:25,784 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.41495058
2015-10-17 15:43:26,487 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.86121905
2015-10-17 15:43:26,518 INFO [IPC Server handler 7 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.90837914
2015-10-17 15:43:26,534 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:43:27,096 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.2687442
2015-10-17 15:43:27,471 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.8746388
2015-10-17 15:43:27,471 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.667
2015-10-17 15:43:27,471 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.81275743
2015-10-17 15:43:27,471 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.8732552
2015-10-17 15:43:27,596 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:43:27,846 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.06666667
2015-10-17 15:43:27,846 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.1066108
2015-10-17 15:43:28,425 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.402765
2015-10-17 15:43:28,706 INFO [IPC Server handler 12 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:43:29,315 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.4304183
2015-10-17 15:43:29,612 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.8919165
2015-10-17 15:43:29,628 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.9363209
2015-10-17 15:43:29,800 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:43:30,206 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.28809068
2015-10-17 15:43:30,597 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.89625597
2015-10-17 15:43:30,644 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.8956702
2015-10-17 15:43:30,644 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.667
2015-10-17 15:43:30,644 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.8512473
2015-10-17 15:43:30,878 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:43:30,940 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.13316931
2015-10-17 15:43:30,956 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.06666667
2015-10-17 15:43:31,956 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:43:32,159 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.42762366
2015-10-17 15:43:32,690 INFO [IPC Server handler 12 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.9218297
2015-10-17 15:43:32,737 INFO [IPC Server handler 23 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.9513903
2015-10-17 15:43:33,003 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:43:33,065 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.448704
2015-10-17 15:43:33,566 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.35395223
2015-10-17 15:43:33,675 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.91182363
2015-10-17 15:43:33,675 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.667
2015-10-17 15:43:33,691 INFO [IPC Server handler 12 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.88915026
2015-10-17 15:43:33,691 INFO [IPC Server handler 23 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.9117501
2015-10-17 15:43:34,003 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.16027956
2015-10-17 15:43:34,081 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.06666667
2015-10-17 15:43:34,081 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:43:35,394 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:43:35,706 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.4457305
2015-10-17 15:43:35,722 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 0.96368766
2015-10-17 15:43:35,769 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 0.96679616
2015-10-17 15:43:36,425 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:43:36,597 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.3624012
2015-10-17 15:43:36,691 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.9335289
2015-10-17 15:43:36,706 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.67275614
2015-10-17 15:43:36,722 INFO [IPC Server handler 12 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.9262697
2015-10-17 15:43:36,738 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.9342991
2015-10-17 15:43:36,784 INFO [IPC Server handler 23 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.448704
2015-10-17 15:43:37,019 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.19211523
2015-10-17 15:43:37,097 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.06666667
2015-10-17 15:43:37,441 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:43:38,081 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000007_0 is : 1.0
2015-10-17 15:43:38,081 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0014_m_000007_0
2015-10-17 15:43:38,081 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:43:38,081 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0014_01_000009 taskAttempt attempt_1445062781478_0014_m_000007_0
2015-10-17 15:43:38,081 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0014_m_000007_0
2015-10-17 15:43:38,081 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:43:38,191 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:43:38,191 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0014_m_000007_0
2015-10-17 15:43:38,191 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:43:38,191 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-17 15:43:38,456 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:43:38,613 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 15:43:38,800 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 1.0
2015-10-17 15:43:38,925 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000002_0 is : 1.0
2015-10-17 15:43:38,925 INFO [IPC Server handler 13 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0014_m_000002_0
2015-10-17 15:43:38,925 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:43:38,925 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0014_01_000004 taskAttempt attempt_1445062781478_0014_m_000002_0
2015-10-17 15:43:38,925 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0014_m_000002_0
2015-10-17 15:43:38,925 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:43:39,050 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:43:39,050 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0014_m_000002_0
2015-10-17 15:43:39,050 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:43:39,050 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-17 15:43:39,378 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.44789755
2015-10-17 15:43:39,488 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:43:39,628 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.3624012
2015-10-17 15:43:39,628 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 15:43:39,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0014_01_000009
2015-10-17 15:43:39,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 15:43:39,644 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0014_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:43:39,738 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.9689033
2015-10-17 15:43:39,738 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.7099232
2015-10-17 15:43:39,769 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 0.9633945
2015-10-17 15:43:39,769 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.97101164
2015-10-17 15:43:40,035 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.19211523
2015-10-17 15:43:40,113 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.10000001
2015-10-17 15:43:40,394 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.448704
2015-10-17 15:43:40,503 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:43:40,660 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0014_01_000004
2015-10-17 15:43:40,660 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:43:40,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0014_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:43:40,660 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0014_01_000015 to attempt_1445062781478_0014_m_000008_1
2015-10-17 15:43:40,660 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 15:43:40,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:40,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000008_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:43:40,660 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0014_01_000015 taskAttempt attempt_1445062781478_0014_m_000008_1
2015-10-17 15:43:40,660 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0014_m_000008_1
2015-10-17 15:43:40,660 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:43:40,738 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0014_m_000008_1 : 13562
2015-10-17 15:43:40,738 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0014_m_000008_1] using containerId: [container_1445062781478_0014_01_000015 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:43:40,738 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000008_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:43:40,738 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0014_m_000008
2015-10-17 15:43:41,535 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:43:41,691 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-36> knownNMs=5
2015-10-17 15:43:42,566 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:43:42,644 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.36285025
2015-10-17 15:43:42,722 INFO [Socket Reader #1 for port 49479] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0014 (auth:SIMPLE)
2015-10-17 15:43:42,753 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0014_m_000015 asked for a task
2015-10-17 15:43:42,753 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0014_m_000015 given task: attempt_1445062781478_0014_m_000008_1
2015-10-17 15:43:42,753 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.73789483
2015-10-17 15:43:42,753 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 0.9952444
2015-10-17 15:43:42,800 INFO [IPC Server handler 12 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 0.99822223
2015-10-17 15:43:42,800 INFO [IPC Server handler 12 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 1.0
2015-10-17 15:43:42,878 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.44789755
2015-10-17 15:43:42,878 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000004_0 is : 1.0
2015-10-17 15:43:42,894 INFO [IPC Server handler 13 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0014_m_000004_0
2015-10-17 15:43:42,894 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:43:42,894 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0014_01_000006 taskAttempt attempt_1445062781478_0014_m_000004_0
2015-10-17 15:43:42,894 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0014_m_000004_0
2015-10-17 15:43:42,894 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:43:43,035 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:43:43,035 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0014_m_000004_0
2015-10-17 15:43:43,035 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:43:43,035 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-17 15:43:43,066 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.19211523
2015-10-17 15:43:43,097 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000003_0 is : 1.0
2015-10-17 15:43:43,097 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0014_m_000003_0
2015-10-17 15:43:43,097 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:43:43,097 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0014_01_000005 taskAttempt attempt_1445062781478_0014_m_000003_0
2015-10-17 15:43:43,097 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0014_m_000003_0
2015-10-17 15:43:43,097 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:43:43,160 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.13333334
2015-10-17 15:43:43,160 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:43:43,160 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0014_m_000003_0
2015-10-17 15:43:43,160 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:43:43,160 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-17 15:43:43,394 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000005_0 is : 1.0
2015-10-17 15:43:43,410 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0014_m_000005_0
2015-10-17 15:43:43,410 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:43:43,410 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0014_01_000007 taskAttempt attempt_1445062781478_0014_m_000005_0
2015-10-17 15:43:43,410 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0014_m_000005_0
2015-10-17 15:43:43,410 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:43:43,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:43:43,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0014_m_000005_0
2015-10-17 15:43:43,535 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:43:43,535 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-17 15:43:43,582 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:43:43,738 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 15:43:43,738 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0014_01_000006
2015-10-17 15:43:43,738 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0014_01_000005
2015-10-17 15:43:43,738 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0014_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:43:43,738 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 15:43:43,738 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0014_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:43:43,863 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.448704
2015-10-17 15:43:44,597 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:43:44,754 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0014_01_000007
2015-10-17 15:43:44,754 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 15:43:44,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0014_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:43:45,629 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:43:45,660 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.41333264
2015-10-17 15:43:45,816 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.7667767
2015-10-17 15:43:46,426 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.19211523
2015-10-17 15:43:46,504 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.20000002
2015-10-17 15:43:46,629 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:43:46,660 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.44789755
2015-10-17 15:43:47,269 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.448704
2015-10-17 15:43:47,644 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:43:48,660 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:43:48,676 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.44789755
2015-10-17 15:43:48,848 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.78300005
2015-10-17 15:43:49,473 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.21517316
2015-10-17 15:43:49,535 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.23333333
2015-10-17 15:43:49,660 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:43:50,254 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_1 is : 0.062864326
2015-10-17 15:43:50,691 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:43:50,957 INFO [IPC Server handler 12 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.44789755
2015-10-17 15:43:51,738 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.44789755
2015-10-17 15:43:51,738 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:43:51,910 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.79891646
2015-10-17 15:43:52,520 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.2407244
2015-10-17 15:43:52,613 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.23333333
2015-10-17 15:43:52,723 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.448704
2015-10-17 15:43:52,754 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:43:53,316 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_1 is : 0.08749987
2015-10-17 15:43:53,785 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:43:54,504 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.44789755
2015-10-17 15:43:54,738 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.44894174
2015-10-17 15:43:54,801 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:43:54,957 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.81525874
2015-10-17 15:43:55,613 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.2663415
2015-10-17 15:43:55,692 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.23333333
2015-10-17 15:43:55,848 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:43:56,395 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_1 is : 0.106881365
2015-10-17 15:43:56,473 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.448704
2015-10-17 15:43:56,864 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:43:57,785 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.5008075
2015-10-17 15:43:57,895 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:43:57,989 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.84418803
2015-10-17 15:43:58,004 INFO [IPC Server handler 7 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.44789755
2015-10-17 15:43:58,660 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.27776006
2015-10-17 15:43:58,817 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.23333333
2015-10-17 15:43:58,942 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:43:59,426 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_1 is : 0.106881365
2015-10-17 15:43:59,973 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:44:00,645 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.448704
2015-10-17 15:44:00,817 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.53341997
2015-10-17 15:44:00,989 INFO [IPC Server handler 7 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:44:01,161 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.88225967
2015-10-17 15:44:01,629 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.44789755
2015-10-17 15:44:01,707 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.27776006
2015-10-17 15:44:01,848 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.23333333
2015-10-17 15:44:02,004 INFO [IPC Server handler 7 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:44:02,458 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_1 is : 0.106881365
2015-10-17 15:44:03,020 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:44:03,817 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.53341997
2015-10-17 15:44:04,036 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:44:04,208 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.9191693
2015-10-17 15:44:04,208 INFO [IPC Server handler 23 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.48878005
2015-10-17 15:44:04,723 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.28294012
2015-10-17 15:44:04,880 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.23333333
2015-10-17 15:44:05,051 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:44:05,239 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.45760798
2015-10-17 15:44:05,489 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_1 is : 0.14739732
2015-10-17 15:44:06,051 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:44:06,833 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.53341997
2015-10-17 15:44:07,239 INFO [IPC Server handler 23 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.9431837
2015-10-17 15:44:07,364 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:44:07,770 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.52247053
2015-10-17 15:44:07,786 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.3152015
2015-10-17 15:44:07,911 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.23333333
2015-10-17 15:44:08,364 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:44:08,520 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_1 is : 0.1816181
2015-10-17 15:44:08,895 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.4905001
2015-10-17 15:44:09,380 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:44:09,849 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.54540855
2015-10-17 15:44:10,302 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.9591969
2015-10-17 15:44:10,708 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:44:10,817 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.34105787
2015-10-17 15:44:10,958 INFO [IPC Server handler 7 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.23333333
2015-10-17 15:44:11,317 INFO [IPC Server handler 23 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.53425497
2015-10-17 15:44:11,567 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_1 is : 0.19258286
2015-10-17 15:44:11,708 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:44:12,442 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.52484185
2015-10-17 15:44:12,708 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:44:12,880 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.59792644
2015-10-17 15:44:13,349 INFO [IPC Server handler 23 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 0.9750538
2015-10-17 15:44:13,724 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:44:13,849 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.36171672
2015-10-17 15:44:14,005 INFO [IPC Server handler 7 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.23333333
2015-10-17 15:44:14,739 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:44:14,739 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.53425497
2015-10-17 15:44:14,896 INFO [IPC Server handler 7 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_1 is : 0.19258286
2015-10-17 15:44:15,755 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:44:15,880 INFO [IPC Server handler 7 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.61898744
2015-10-17 15:44:16,052 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.53341997
2015-10-17 15:44:16,286 INFO [IPC Server handler 23 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000008_0 is : 1.0
2015-10-17 15:44:16,302 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0014_m_000008_0
2015-10-17 15:44:16,302 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:44:16,302 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0014_01_000010 taskAttempt attempt_1445062781478_0014_m_000008_0
2015-10-17 15:44:16,302 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0014_m_000008_0
2015-10-17 15:44:16,302 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:44:16,411 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:44:16,411 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0014_m_000008_0
2015-10-17 15:44:16,411 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0014_m_000008_1
2015-10-17 15:44:16,411 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:44:16,411 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-17 15:44:16,411 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000008_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 15:44:16,411 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0014_01_000015 taskAttempt attempt_1445062781478_0014_m_000008_1
2015-10-17 15:44:16,411 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0014_m_000008_1
2015-10-17 15:44:16,411 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:44:16,505 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000008_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 15:44:16,536 INFO [Socket Reader #1 for port 49479] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 49479: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 15:44:16,536 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 15:44:16,583 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out4/_temporary/1/_temporary/attempt_1445062781478_0014_m_000008_1
2015-10-17 15:44:16,599 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000008_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 15:44:16,755 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:44:16,802 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 15:44:16,802 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0014_01_000010
2015-10-17 15:44:16,802 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 15:44:16,802 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0014_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:44:16,864 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.36319977
2015-10-17 15:44:17,021 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.23333333
2015-10-17 15:44:17,755 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:17,802 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0014_01_000015
2015-10-17 15:44:17,802 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 15:44:17,802 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0014_m_000008_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:44:18,161 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.53425497
2015-10-17 15:44:18,786 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:18,896 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.61898744
2015-10-17 15:44:19,583 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.53341997
2015-10-17 15:44:19,786 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:19,911 INFO [IPC Server handler 7 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.36319977
2015-10-17 15:44:20,052 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.26666668
2015-10-17 15:44:20,802 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:21,708 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.53425497
2015-10-17 15:44:21,802 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:21,912 INFO [IPC Server handler 7 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.61898744
2015-10-17 15:44:22,818 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:22,959 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.37561488
2015-10-17 15:44:23,037 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.53341997
2015-10-17 15:44:23,084 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.26666668
2015-10-17 15:44:23,818 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:24,646 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.61898744
2015-10-17 15:44:24,818 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:24,927 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.667
2015-10-17 15:44:25,521 INFO [IPC Server handler 13 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.53425497
2015-10-17 15:44:25,818 INFO [IPC Server handler 15 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:25,990 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.40191355
2015-10-17 15:44:26,131 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.26666668
2015-10-17 15:44:26,521 INFO [IPC Server handler 13 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.53341997
2015-10-17 15:44:26,834 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:27,834 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:27,927 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.667
2015-10-17 15:44:28,849 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:29,021 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.53425497
2015-10-17 15:44:29,037 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.42923838
2015-10-17 15:44:29,162 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.26666668
2015-10-17 15:44:29,865 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:29,912 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.53341997
2015-10-17 15:44:30,865 INFO [IPC Server handler 8 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:30,943 INFO [IPC Server handler 7 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.667
2015-10-17 15:44:31,881 INFO [IPC Server handler 21 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:32,084 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.448704
2015-10-17 15:44:32,193 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.26666668
2015-10-17 15:44:32,662 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.53425497
2015-10-17 15:44:32,896 INFO [IPC Server handler 7 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:33,537 INFO [IPC Server handler 13 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.53341997
2015-10-17 15:44:33,975 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.667
2015-10-17 15:44:34,147 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:35,100 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.448704
2015-10-17 15:44:35,225 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:35,225 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.26666668
2015-10-17 15:44:35,990 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.53425497
2015-10-17 15:44:36,256 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:36,959 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.53341997
2015-10-17 15:44:37,006 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.69014156
2015-10-17 15:44:37,287 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:38,147 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.448704
2015-10-17 15:44:38,287 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.26666668
2015-10-17 15:44:38,287 INFO [IPC Server handler 12 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:39,303 INFO [IPC Server handler 23 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:39,350 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.5440766
2015-10-17 15:44:40,053 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.7225232
2015-10-17 15:44:40,334 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:40,366 INFO [IPC Server handler 13 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.53341997
2015-10-17 15:44:41,225 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.47701097
2015-10-17 15:44:41,381 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:41,381 INFO [IPC Server handler 13 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.26666668
2015-10-17 15:44:42,397 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:42,631 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.5719
2015-10-17 15:44:43,116 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.75523156
2015-10-17 15:44:43,413 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:43,756 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.55754715
2015-10-17 15:44:44,303 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.50410146
2015-10-17 15:44:44,428 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.26666668
2015-10-17 15:44:44,428 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:45,491 INFO [IPC Server handler 13 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:46,053 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.5927716
2015-10-17 15:44:46,163 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.788906
2015-10-17 15:44:46,507 INFO [IPC Server handler 13 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:47,147 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.5968105
2015-10-17 15:44:47,413 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.52986354
2015-10-17 15:44:47,710 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.26666668
2015-10-17 15:44:47,710 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:48,725 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:49,179 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.8198972
2015-10-17 15:44:49,507 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.6197233
2015-10-17 15:44:49,741 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:50,507 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.53425497
2015-10-17 15:44:50,632 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.61898744
2015-10-17 15:44:50,725 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.26666668
2015-10-17 15:44:50,757 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:51,788 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:52,210 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.85356295
2015-10-17 15:44:52,835 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:52,835 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.6197233
2015-10-17 15:44:53,538 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.53425497
2015-10-17 15:44:53,788 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.26666668
2015-10-17 15:44:53,882 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:54,085 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.61898744
2015-10-17 15:44:54,898 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:55,241 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.888057
2015-10-17 15:44:55,960 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:56,398 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.6197233
2015-10-17 15:44:56,616 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.5483832
2015-10-17 15:44:56,835 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.26666668
2015-10-17 15:44:56,976 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:57,507 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.61898744
2015-10-17 15:44:58,570 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.91764796
2015-10-17 15:44:58,898 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:59,679 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.5774058
2015-10-17 15:44:59,851 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.6197233
2015-10-17 15:44:59,945 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:44:59,945 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.26666668
2015-10-17 15:45:00,898 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.61898744
2015-10-17 15:45:00,960 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:45:01,617 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.95235467
2015-10-17 15:45:02,726 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.6039468
2015-10-17 15:45:02,882 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:45:03,007 INFO [IPC Server handler 13 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.26666668
2015-10-17 15:45:03,445 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.6197233
2015-10-17 15:45:03,898 INFO [IPC Server handler 6 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:45:04,242 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.61898744
2015-10-17 15:45:04,664 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 0.98458403
2015-10-17 15:45:04,929 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:45:05,789 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.6197233
2015-10-17 15:45:06,554 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_1 is : 1.0
2015-10-17 15:45:06,570 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0014_m_000000_1
2015-10-17 15:45:06,570 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000000_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:45:06,570 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0014_01_000012 taskAttempt attempt_1445062781478_0014_m_000000_1
2015-10-17 15:45:06,570 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0014_m_000000_1
2015-10-17 15:45:06,570 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:45:06,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000000_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:45:06,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0014_m_000000_1
2015-10-17 15:45:06,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0014_m_000000_0
2015-10-17 15:45:06,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:45:06,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-17 15:45:06,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000000_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 15:45:06,726 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0014_01_000002 taskAttempt attempt_1445062781478_0014_m_000000_0
2015-10-17 15:45:06,726 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0014_m_000000_0
2015-10-17 15:45:06,726 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 15:45:06,742 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.6197233
2015-10-17 15:45:06,867 INFO [IPC Server handler 12 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:45:06,867 INFO [IPC Server handler 12 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.26666668
2015-10-17 15:45:07,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000000_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 15:45:07,132 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 15:45:07,179 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0014_m_000000
2015-10-17 15:45:07,179 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 15:45:07,226 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out4/_temporary/1/_temporary/attempt_1445062781478_0014_m_000000_0
2015-10-17 15:45:07,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000000_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 15:45:07,679 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 15:45:07,742 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000000_0 is : 0.61898744
2015-10-17 15:45:07,945 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:08,054 INFO [Socket Reader #1 for port 49479] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 49479: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 15:45:10,930 INFO [IPC Server handler 12 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.6197233
2015-10-17 15:45:11,055 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:11,055 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.3
2015-10-17 15:45:11,117 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.6197233
2015-10-17 15:45:11,320 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0014_01_000012
2015-10-17 15:45:11,320 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 15:45:11,320 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0014_m_000000_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:45:12,086 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:12,476 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0014_01_000002
2015-10-17 15:45:12,476 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 15:45:12,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0014_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:45:13,961 INFO [IPC Server handler 12 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.63758135
2015-10-17 15:45:14,023 INFO [IPC Server handler 23 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:14,117 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.3
2015-10-17 15:45:14,367 INFO [IPC Server handler 20 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.63503194
2015-10-17 15:45:15,039 INFO [IPC Server handler 23 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:16,461 INFO [IPC Server handler 28 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:16,992 INFO [IPC Server handler 12 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.63758135
2015-10-17 15:45:17,008 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.667
2015-10-17 15:45:17,180 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.3
2015-10-17 15:45:17,492 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:17,727 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.66048527
2015-10-17 15:45:18,508 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:18,633 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.66048527
2015-10-17 15:45:19,539 INFO [IPC Server handler 9 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:20,086 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.667
2015-10-17 15:45:20,227 INFO [IPC Server handler 11 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.3
2015-10-17 15:45:20,555 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:21,258 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.667
2015-10-17 15:45:21,571 INFO [IPC Server handler 12 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:22,586 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:23,149 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.667
2015-10-17 15:45:23,305 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.3
2015-10-17 15:45:23,602 INFO [IPC Server handler 12 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:24,633 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:24,790 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.667
2015-10-17 15:45:25,665 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:26,305 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.667
2015-10-17 15:45:26,337 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.3
2015-10-17 15:45:26,680 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:27,680 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:28,149 INFO [IPC Server handler 23 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.667
2015-10-17 15:45:28,712 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:29,368 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.7005007
2015-10-17 15:45:29,384 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.3
2015-10-17 15:45:29,727 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:30,743 INFO [IPC Server handler 23 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:31,446 INFO [IPC Server handler 27 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.667
2015-10-17 15:45:32,493 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:32,743 INFO [IPC Server handler 14 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.735057
2015-10-17 15:45:32,837 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.3
2015-10-17 15:45:33,540 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:34,587 INFO [IPC Server handler 13 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:35,149 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.667
2015-10-17 15:45:35,618 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:35,790 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.7770827
2015-10-17 15:45:35,899 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.3
2015-10-17 15:45:36,634 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:38,525 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:38,540 INFO [IPC Server handler 13 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.667
2015-10-17 15:45:38,837 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.8173697
2015-10-17 15:45:38,962 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.3
2015-10-17 15:45:39,556 INFO [IPC Server handler 26 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:40,587 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:41,587 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:41,837 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.667
2015-10-17 15:45:41,869 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.8535298
2015-10-17 15:45:42,009 INFO [IPC Server handler 29 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.3
2015-10-17 15:45:42,634 INFO [IPC Server handler 10 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:43,665 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:44,712 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:44,916 INFO [IPC Server handler 24 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.8881221
2015-10-17 15:45:45,056 INFO [IPC Server handler 18 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.3
2015-10-17 15:45:45,259 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.67123765
2015-10-17 15:45:46,525 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:47,541 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:48,416 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.3
2015-10-17 15:45:48,416 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.92884314
2015-10-17 15:45:48,556 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:49,525 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.68664306
2015-10-17 15:45:49,588 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:50,619 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:51,463 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.3
2015-10-17 15:45:51,463 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.9690827
2015-10-17 15:45:51,635 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:52,666 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:53,541 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_0 is : 0.7023783
2015-10-17 15:45:53,697 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:54,479 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 0.9985604
2015-10-17 15:45:54,479 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.3
2015-10-17 15:45:54,713 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_m_000001_1 is : 1.0
2015-10-17 15:45:54,713 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:54,760 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0014_m_000001_1
2015-10-17 15:45:54,760 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000001_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:45:54,760 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0014_01_000014 taskAttempt attempt_1445062781478_0014_m_000001_1
2015-10-17 15:45:54,760 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0014_m_000001_1
2015-10-17 15:45:54,760 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:45:54,947 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000001_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:45:54,963 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0014_m_000001_1
2015-10-17 15:45:54,963 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0014_m_000001_0
2015-10-17 15:45:54,963 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:45:54,963 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-17 15:45:54,963 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000001_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 15:45:54,963 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0014_01_000003 taskAttempt attempt_1445062781478_0014_m_000001_0
2015-10-17 15:45:54,963 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0014_m_000001_0
2015-10-17 15:45:54,963 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 15:45:55,682 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000001_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 15:45:55,760 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 15:45:55,776 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 15:45:55,776 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:45:55,869 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0014_m_000001
2015-10-17 15:45:55,869 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 15:45:55,885 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out4/_temporary/1/_temporary/attempt_1445062781478_0014_m_000001_0
2015-10-17 15:45:55,885 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_m_000001_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 15:45:56,791 INFO [IPC Server handler 1 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0014_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 15:45:56,807 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0014_01_000003
2015-10-17 15:45:56,807 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0014_01_000014
2015-10-17 15:45:56,807 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 15:45:56,807 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0014_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:45:56,807 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0014_m_000001_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:45:56,901 INFO [Socket Reader #1 for port 49479] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 49479: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 15:45:57,541 INFO [IPC Server handler 3 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.3
2015-10-17 15:45:57,666 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.3
2015-10-17 15:45:57,745 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.3
2015-10-17 15:46:00,604 INFO [IPC Server handler 4 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.67039526
2015-10-17 15:46:03,667 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.6811955
2015-10-17 15:46:06,745 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.7012769
2015-10-17 15:46:09,777 INFO [IPC Server handler 17 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.7172746
2015-10-17 15:46:12,839 INFO [IPC Server handler 2 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.72792006
2015-10-17 15:46:15,886 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.73802686
2015-10-17 15:46:18,933 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.7497747
2015-10-17 15:46:21,949 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.7689908
2015-10-17 15:46:25,012 INFO [IPC Server handler 25 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.7905101
2015-10-17 15:46:28,043 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.81104696
2015-10-17 15:46:31,653 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.82450217
2015-10-17 15:46:34,700 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.835324
2015-10-17 15:46:37,732 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.8429051
2015-10-17 15:46:40,794 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.84852576
2015-10-17 15:46:43,857 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.8582287
2015-10-17 15:46:46,935 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.8712572
2015-10-17 15:46:49,967 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.88436574
2015-10-17 15:46:52,998 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.9052629
2015-10-17 15:46:56,030 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.9271714
2015-10-17 15:46:59,717 INFO [IPC Server handler 7 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.94827574
2015-10-17 15:47:02,780 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.9709817
2015-10-17 15:47:05,843 INFO [IPC Server handler 0 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.9854387
2015-10-17 15:47:08,874 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 0.9913774
2015-10-17 15:47:12,140 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 1.0
2015-10-17 15:47:15,250 INFO [IPC Server handler 5 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 1.0
2015-10-17 15:47:17,000 INFO [IPC Server handler 22 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445062781478_0014_r_000000_0
2015-10-17 15:47:17,000 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_r_000000_0 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-17 15:47:17,000 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445062781478_0014_r_000000_0 given a go for committing the task output.
2015-10-17 15:47:17,000 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445062781478_0014_r_000000_0
2015-10-17 15:47:17,015 INFO [IPC Server handler 19 on 49479] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445062781478_0014_r_000000_0:true
2015-10-17 15:47:17,047 INFO [IPC Server handler 16 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0014_r_000000_0 is : 1.0
2015-10-17 15:47:17,062 INFO [IPC Server handler 7 on 49479] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0014_r_000000_0
2015-10-17 15:47:17,062 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_r_000000_0 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:47:17,062 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0014_01_000013 taskAttempt attempt_1445062781478_0014_r_000000_0
2015-10-17 15:47:17,062 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0014_r_000000_0
2015-10-17 15:47:17,062 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:47:17,265 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0014_r_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:47:17,265 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0014_r_000000_0
2015-10-17 15:47:17,265 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0014_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:47:17,265 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-17 15:47:17,265 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0014Job Transitioned from RUNNING to COMMITTING
2015-10-17 15:47:17,344 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-17 15:47:17,828 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-17 15:47:17,828 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0014Job Transitioned from COMMITTING to SUCCEEDED
2015-10-17 15:47:17,906 INFO [Thread-111] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-17 15:47:17,906 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 15:47:17,906 INFO [Thread-111] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-17 15:47:17,906 INFO [Thread-111] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-17 15:47:17,906 INFO [Thread-111] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-17 15:47:17,906 INFO [Thread-111] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-17 15:47:17,906 INFO [Thread-111] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-17 15:47:17,906 INFO [Thread-111] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-17 15:47:18,953 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0014_01_000013
2015-10-17 15:47:18,953 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 15:47:18,953 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0014_r_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:47:19,766 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0014/job_1445062781478_0014_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0014-1445067476672-msrabi-pagerank-1445068037828-10-1-SUCCEEDED-default-1445067489454.jhist_tmp
2015-10-17 15:47:21,656 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0014-1445067476672-msrabi-pagerank-1445068037828-10-1-SUCCEEDED-default-1445067489454.jhist_tmp
2015-10-17 15:47:21,672 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0014/job_1445062781478_0014_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0014_conf.xml_tmp
2015-10-17 15:47:22,375 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0014_conf.xml_tmp
2015-10-17 15:47:22,422 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0014.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0014.summary
2015-10-17 15:47:22,438 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0014_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0014_conf.xml
2015-10-17 15:47:22,453 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0014-1445067476672-msrabi-pagerank-1445068037828-10-1-SUCCEEDED-default-1445067489454.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0014-1445067476672-msrabi-pagerank-1445068037828-10-1-SUCCEEDED-default-1445067489454.jhist
2015-10-17 15:47:22,453 INFO [Thread-111] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-17 15:47:22,469 INFO [Thread-111] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-17 15:47:22,469 INFO [Thread-111] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MININT-FNANLI5.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445062781478_0014
2015-10-17 15:47:22,485 INFO [Thread-111] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-17 15:47:23,516 INFO [Thread-111] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:14 ContRel:0 HostLocal:11 RackLocal:2
2015-10-17 15:47:23,516 INFO [Thread-111] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0014
2015-10-17 15:47:23,532 INFO [Thread-111] org.apache.hadoop.ipc.Server: Stopping server on 49479
2015-10-17 15:47:23,547 INFO [IPC Server listener on 49479] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 49479
2015-10-17 15:47:23,547 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-17 15:47:23,547 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
