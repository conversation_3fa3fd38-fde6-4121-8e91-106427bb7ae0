2015-10-17 21:47:48,288 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445087491445_0005_000001
2015-10-17 21:47:49,078 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 21:47:49,078 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 5 cluster_timestamp: 1445087491445 } attemptId: 1 } keyId: -1547346236)
2015-10-17 21:47:49,370 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 21:47:50,570 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 21:47:50,635 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 21:47:50,677 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 21:47:50,679 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 21:47:50,680 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 21:47:50,682 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 21:47:50,683 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 21:47:50,692 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 21:47:50,693 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 21:47:50,695 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 21:47:50,742 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 21:47:50,766 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 21:47:50,789 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 21:47:50,800 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 21:47:50,861 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 21:47:51,199 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 21:47:51,283 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 21:47:51,283 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 21:47:51,293 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445087491445_0005 to jobTokenSecretManager
2015-10-17 21:47:51,483 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445087491445_0005 because: not enabled; too many maps; too much input;
2015-10-17 21:47:51,510 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445087491445_0005 = 1751822336. Number of splits = 13
2015-10-17 21:47:51,512 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445087491445_0005 = 1
2015-10-17 21:47:51,512 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0005Job Transitioned from NEW to INITED
2015-10-17 21:47:51,514 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445087491445_0005.
2015-10-17 21:47:51,558 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 21:47:51,571 INFO [Socket Reader #1 for port 32060] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 32060
2015-10-17 21:47:51,602 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 21:47:51,602 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 21:47:51,602 INFO [IPC Server listener on 32060] org.apache.hadoop.ipc.Server: IPC Server listener on 32060: starting
2015-10-17 21:47:51,604 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MSRA-SA-41.fareast.corp.microsoft.com/**************:32060
2015-10-17 21:47:51,724 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 21:47:51,730 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 21:47:51,746 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 21:47:51,754 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 21:47:51,754 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 21:47:51,759 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 21:47:51,759 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 21:47:51,774 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 32067
2015-10-17 21:47:51,774 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 21:47:51,819 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\2\Jetty_0_0_0_0_32067_mapreduce____.dbisl5\webapp
2015-10-17 21:47:52,000 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:32067
2015-10-17 21:47:52,000 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 32067
2015-10-17 21:47:52,427 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 21:47:52,430 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445087491445_0005
2015-10-17 21:47:52,432 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 21:47:52,436 INFO [Socket Reader #1 for port 32070] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 32070
2015-10-17 21:47:52,441 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 21:47:52,441 INFO [IPC Server listener on 32070] org.apache.hadoop.ipc.Server: IPC Server listener on 32070: starting
2015-10-17 21:47:52,461 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 21:47:52,462 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 21:47:52,462 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 21:47:52,521 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at MSRA-SA-41/**************:8030
2015-10-17 21:47:52,604 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 21:47:52,604 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 21:47:52,609 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 21:47:52,612 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 21:47:52,620 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0005Job Transitioned from INITED to SETUP
2015-10-17 21:47:52,622 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 21:47:52,631 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0005Job Transitioned from SETUP to RUNNING
2015-10-17 21:47:52,652 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,652 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,656 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:47:52,657 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,657 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,657 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:47:52,658 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,658 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,658 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:47:52,659 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,659 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,659 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:47:52,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:47:52,661 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,661 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,661 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:47:52,661 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,661 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,662 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:47:52,662 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,662 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,662 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:47:52,663 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,663 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,663 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:47:52,663 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,663 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,664 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:47:52,664 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,664 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,665 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000010 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:47:52,668 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,668 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,668 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000011 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:47:52,668 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,669 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,669 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000012 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:47:52,669 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:47:52,671 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:47:52,671 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:47:52,672 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:47:52,672 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:47:52,672 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:47:52,672 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:47:52,673 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:47:52,673 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:47:52,673 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:47:52,673 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:47:52,673 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000010_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:47:52,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:47:52,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000012_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:47:52,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:47:52,675 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 21:47:52,685 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 21:47:52,720 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445087491445_0005, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0005/job_1445087491445_0005_1.jhist
2015-10-17 21:47:53,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:13 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 21:47:53,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=7 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:13312, vCores:-7> knownNMs=3
2015-10-17 21:47:53,654 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:13312, vCores:-7>
2015-10-17 21:47:53,654 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:47:54,674 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 4
2015-10-17 21:47:54,676 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000002 to attempt_1445087491445_0005_m_000000_0
2015-10-17 21:47:54,678 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000003 to attempt_1445087491445_0005_m_000001_0
2015-10-17 21:47:54,678 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000004 to attempt_1445087491445_0005_m_000010_0
2015-10-17 21:47:54,678 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:54,678 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000005 to attempt_1445087491445_0005_m_000002_0
2015-10-17 21:47:54,679 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-11>
2015-10-17 21:47:54,679 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:47:54,679 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:9 ScheduledReds:0 AssignedMaps:4 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:4 ContRel:0 HostLocal:3 RackLocal:1
2015-10-17 21:47:54,728 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:54,745 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0005/job.jar
2015-10-17 21:47:54,747 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0005/job.xml
2015-10-17 21:47:54,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 21:47:54,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 21:47:54,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 21:47:54,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:47:54,809 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:54,809 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:47:54,810 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:54,810 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000010_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:47:54,810 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:54,811 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:47:54,813 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000002 taskAttempt attempt_1445087491445_0005_m_000000_0
2015-10-17 21:47:54,813 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000003 taskAttempt attempt_1445087491445_0005_m_000001_0
2015-10-17 21:47:54,813 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000004 taskAttempt attempt_1445087491445_0005_m_000010_0
2015-10-17 21:47:54,813 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000005 taskAttempt attempt_1445087491445_0005_m_000002_0
2015-10-17 21:47:54,815 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000001_0
2015-10-17 21:47:54,815 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000010_0
2015-10-17 21:47:54,815 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000002_0
2015-10-17 21:47:54,815 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000000_0
2015-10-17 21:47:54,816 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:47:54,840 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:47:54,841 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:47:54,842 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:47:54,886 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000001_0 : 13562
2015-10-17 21:47:54,886 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000000_0 : 13562
2015-10-17 21:47:54,886 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000010_0 : 13562
2015-10-17 21:47:54,886 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000002_0 : 13562
2015-10-17 21:47:54,888 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000002_0] using containerId: [container_1445087491445_0005_01_000005 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:47:54,892 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:47:54,892 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000010_0] using containerId: [container_1445087491445_0005_01_000004 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:47:54,892 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000010_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:47:54,893 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000001_0] using containerId: [container_1445087491445_0005_01_000003 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 21:47:54,893 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:47:54,893 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000000_0] using containerId: [container_1445087491445_0005_01_000002 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 21:47:54,894 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:47:54,894 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000002
2015-10-17 21:47:54,894 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:47:54,894 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000010
2015-10-17 21:47:54,895 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000010 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:47:54,895 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000001
2015-10-17 21:47:54,895 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:47:54,896 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000000
2015-10-17 21:47:54,896 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:47:55,682 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=6 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:9216, vCores:-11> knownNMs=3
2015-10-17 21:47:55,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-11>
2015-10-17 21:47:55,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:47:56,684 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-11>
2015-10-17 21:47:56,684 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:47:57,635 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:47:57,665 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000002 asked for a task
2015-10-17 21:47:57,666 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000002 given task: attempt_1445087491445_0005_m_000000_0
2015-10-17 21:47:57,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:47:57,690 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:57,691 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000006 to attempt_1445087491445_0005_m_000003_0
2015-10-17 21:47:57,691 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-12>
2015-10-17 21:47:57,691 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:57,691 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:47:57,692 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:8 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:5 ContRel:0 HostLocal:3 RackLocal:2
2015-10-17 21:47:57,693 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:47:57,694 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000006 taskAttempt attempt_1445087491445_0005_m_000003_0
2015-10-17 21:47:57,694 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000003_0
2015-10-17 21:47:57,694 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:47:57,710 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000003_0 : 13562
2015-10-17 21:47:57,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000003_0] using containerId: [container_1445087491445_0005_01_000006 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:47:57,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:47:57,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000003
2015-10-17 21:47:57,712 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:47:58,673 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:47:58,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:8192, vCores:-12> knownNMs=3
2015-10-17 21:47:58,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-12>
2015-10-17 21:47:58,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:47:58,704 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000003 asked for a task
2015-10-17 21:47:58,704 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000003 given task: attempt_1445087491445_0005_m_000001_0
2015-10-17 21:47:59,697 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-12>
2015-10-17 21:47:59,698 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:00,704 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:48:00,704 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:48:00,705 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000007 to attempt_1445087491445_0005_m_000004_0
2015-10-17 21:48:00,705 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-13>
2015-10-17 21:48:00,705 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:48:00,706 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:00,706 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:7 ScheduledReds:0 AssignedMaps:6 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:3 RackLocal:3
2015-10-17 21:48:00,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:48:00,708 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000007 taskAttempt attempt_1445087491445_0005_m_000004_0
2015-10-17 21:48:00,709 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000004_0
2015-10-17 21:48:00,709 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:48:00,731 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000004_0 : 13562
2015-10-17 21:48:00,732 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000004_0] using containerId: [container_1445087491445_0005_01_000007 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:48:00,733 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:48:00,733 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000004
2015-10-17 21:48:00,734 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:48:01,708 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:7168, vCores:-13> knownNMs=3
2015-10-17 21:48:01,709 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-13>
2015-10-17 21:48:01,709 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:02,711 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-13>
2015-10-17 21:48:02,711 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:03,715 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:48:03,715 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:48:03,715 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000008 to attempt_1445087491445_0005_m_000005_0
2015-10-17 21:48:03,716 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:03,716 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:03,716 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:48:03,716 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:6 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:3 RackLocal:4
2015-10-17 21:48:03,717 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:48:03,722 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000008 taskAttempt attempt_1445087491445_0005_m_000005_0
2015-10-17 21:48:03,722 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000005_0
2015-10-17 21:48:03,722 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:48:03,739 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000005_0 : 13562
2015-10-17 21:48:03,739 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000005_0] using containerId: [container_1445087491445_0005_01_000008 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:48:03,740 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:48:03,740 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000005
2015-10-17 21:48:03,741 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:48:04,718 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:6144, vCores:-14> knownNMs=3
2015-10-17 21:48:04,718 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:04,718 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:05,721 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:05,721 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:06,042 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.12453667
2015-10-17 21:48:06,722 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:06,722 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:07,255 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.13104755
2015-10-17 21:48:07,723 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:07,723 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:08,725 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:08,725 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:09,072 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.12453667
2015-10-17 21:48:09,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:09,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:10,277 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.13104755
2015-10-17 21:48:10,727 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:10,727 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:11,523 INFO [Thread-56] org.apache.hadoop.hdfs.DFSClient: Exception in createBlockOutputStream
java.io.IOException: Bad connect ack with firstBadLink as *************:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.createBlockOutputStream(DFSOutputStream.java:1461)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.nextBlockOutputStream(DFSOutputStream.java:1362)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.run(DFSOutputStream.java:589)
2015-10-17 21:48:11,526 INFO [Thread-56] org.apache.hadoop.hdfs.DFSClient: Abandoning BP-1347369012-**************-1444972147527:blk_1073742931_2130
2015-10-17 21:48:11,547 INFO [Thread-56] org.apache.hadoop.hdfs.DFSClient: Excluding datanode *************:50010
2015-10-17 21:48:11,728 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:11,728 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:12,092 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.12780756
2015-10-17 21:48:12,730 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:12,730 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:13,310 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.17341208
2015-10-17 21:48:13,733 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:13,733 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:14,734 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:14,735 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:15,112 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.22738875
2015-10-17 21:48:15,736 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:15,736 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:16,340 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.23926127
2015-10-17 21:48:16,737 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:16,737 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:17,060 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:48:17,084 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000007 asked for a task
2015-10-17 21:48:17,085 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000007 given task: attempt_1445087491445_0005_m_000004_0
2015-10-17 21:48:17,112 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:48:17,132 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000008 asked for a task
2015-10-17 21:48:17,132 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000008 given task: attempt_1445087491445_0005_m_000005_0
2015-10-17 21:48:17,342 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:48:17,365 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:48:17,366 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000005 asked for a task
2015-10-17 21:48:17,366 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000005 given task: attempt_1445087491445_0005_m_000002_0
2015-10-17 21:48:17,367 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:48:17,390 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000004 asked for a task
2015-10-17 21:48:17,390 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000004 given task: attempt_1445087491445_0005_m_000010_0
2015-10-17 21:48:17,390 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000006 asked for a task
2015-10-17 21:48:17,390 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000006 given task: attempt_1445087491445_0005_m_000003_0
2015-10-17 21:48:17,739 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:17,739 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:18,134 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.22738875
2015-10-17 21:48:18,740 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:18,740 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:19,361 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.23926127
2015-10-17 21:48:19,741 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:19,741 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:20,742 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:20,742 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:21,154 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.29636663
2015-10-17 21:48:21,745 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:21,745 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:22,381 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.23926127
2015-10-17 21:48:22,747 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:22,747 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:23,749 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:23,749 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:24,184 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.3302339
2015-10-17 21:48:24,356 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.07092748
2015-10-17 21:48:24,435 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.13101853
2015-10-17 21:48:24,750 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:24,751 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:24,908 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.13105518
2015-10-17 21:48:24,933 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.13101996
2015-10-17 21:48:24,971 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.131026
2015-10-17 21:48:25,401 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.34745386
2015-10-17 21:48:25,753 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:25,753 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:26,754 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:26,754 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:27,204 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.3302339
2015-10-17 21:48:27,358 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.13098624
2015-10-17 21:48:27,436 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.13101853
2015-10-17 21:48:27,755 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:27,755 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:27,921 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.13105518
2015-10-17 21:48:27,936 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.13101996
2015-10-17 21:48:27,984 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.131026
2015-10-17 21:48:28,421 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.34745386
2015-10-17 21:48:28,756 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:28,756 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:29,758 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:29,758 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:30,227 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.3626895
2015-10-17 21:48:30,360 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.13098624
2015-10-17 21:48:30,459 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.13101853
2015-10-17 21:48:30,759 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:30,759 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:30,920 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.13105518
2015-10-17 21:48:30,936 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.13101996
2015-10-17 21:48:30,984 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.131026
2015-10-17 21:48:31,442 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.34745386
2015-10-17 21:48:31,762 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:31,762 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:32,764 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:32,764 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:33,247 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.43306357
2015-10-17 21:48:33,360 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.13098624
2015-10-17 21:48:33,470 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.18994483
2015-10-17 21:48:33,765 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:33,765 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:33,922 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.13105518
2015-10-17 21:48:33,937 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.2319945
2015-10-17 21:48:33,985 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.22999094
2015-10-17 21:48:34,464 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.45563197
2015-10-17 21:48:34,766 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:34,766 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:35,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:35,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:36,265 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.43306357
2015-10-17 21:48:36,359 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.13872962
2015-10-17 21:48:36,469 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.2392399
2015-10-17 21:48:36,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:36,769 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:36,921 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.2116896
2015-10-17 21:48:36,937 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.23923585
2015-10-17 21:48:36,983 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.23922792
2015-10-17 21:48:37,492 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.45563197
2015-10-17 21:48:37,770 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:37,771 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:38,774 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:38,774 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:39,287 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.43306357
2015-10-17 21:48:39,361 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.18863912
2015-10-17 21:48:39,474 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.2392399
2015-10-17 21:48:39,775 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:39,775 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:39,922 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.23924033
2015-10-17 21:48:39,939 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.23923585
2015-10-17 21:48:39,985 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.23922792
2015-10-17 21:48:40,524 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.45563197
2015-10-17 21:48:40,777 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:40,777 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:41,778 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:41,778 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:42,306 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.53591895
2015-10-17 21:48:42,359 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.23919825
2015-10-17 21:48:42,489 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.2392399
2015-10-17 21:48:42,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:42,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:42,922 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.23924033
2015-10-17 21:48:42,938 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.2660185
2015-10-17 21:48:42,985 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.26009074
2015-10-17 21:48:43,545 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.5385628
2015-10-17 21:48:43,781 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:43,781 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:44,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:44,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:45,325 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.53591895
2015-10-17 21:48:45,360 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.23919825
2015-10-17 21:48:45,501 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.34744292
2015-10-17 21:48:45,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:45,785 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:45,922 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.23924033
2015-10-17 21:48:45,937 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.34744066
2015-10-17 21:48:45,986 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.34742332
2015-10-17 21:48:46,575 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.5638331
2015-10-17 21:48:46,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:46,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:47,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:47,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:48,350 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.53591895
2015-10-17 21:48:48,361 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.23919825
2015-10-17 21:48:48,503 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.34744292
2015-10-17 21:48:48,791 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:48,792 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:48,928 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.34109032
2015-10-17 21:48:48,937 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.34744066
2015-10-17 21:48:48,991 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.34742332
2015-10-17 21:48:49,596 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.5638331
2015-10-17 21:48:49,793 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:49,793 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:50,794 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:50,794 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:51,362 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.2776187
2015-10-17 21:48:51,369 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.6387274
2015-10-17 21:48:51,523 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.34744292
2015-10-17 21:48:51,796 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:51,796 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:51,939 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.34744066
2015-10-17 21:48:51,948 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.34744897
2015-10-17 21:48:52,009 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.34742332
2015-10-17 21:48:52,636 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.5638331
2015-10-17 21:48:52,799 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:52,800 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:53,801 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:53,802 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:54,379 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.3473995
2015-10-17 21:48:54,411 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.6387274
2015-10-17 21:48:54,533 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.34744292
2015-10-17 21:48:54,804 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:54,804 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:54,939 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.45387453
2015-10-17 21:48:54,954 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.34744897
2015-10-17 21:48:55,018 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.45562646
2015-10-17 21:48:55,657 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.63755345
2015-10-17 21:48:55,806 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:55,806 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:56,192 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.63755345
2015-10-17 21:48:56,807 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:56,808 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:57,392 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.3473995
2015-10-17 21:48:57,436 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.6387274
2015-10-17 21:48:57,533 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.45563385
2015-10-17 21:48:57,810 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:57,810 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:57,941 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.45562834
2015-10-17 21:48:57,953 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.35219592
2015-10-17 21:48:58,017 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.45562646
2015-10-17 21:48:58,685 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.667
2015-10-17 21:48:58,812 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:58,813 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:48:59,814 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:48:59,814 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:00,011 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.6387274
2015-10-17 21:49:00,392 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.3473995
2015-10-17 21:49:00,454 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.667
2015-10-17 21:49:00,532 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.45563385
2015-10-17 21:49:00,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:49:00,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:00,955 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.45562834
2015-10-17 21:49:00,955 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.45439878
2015-10-17 21:49:01,017 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.45562646
2015-10-17 21:49:01,704 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.667
2015-10-17 21:49:01,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:49:01,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:02,818 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:49:02,818 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:03,403 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.38504407
2015-10-17 21:49:03,482 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.6670522
2015-10-17 21:49:03,543 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.45563385
2015-10-17 21:49:03,820 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:49:03,820 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:03,956 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.54839873
2015-10-17 21:49:03,965 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.455643
2015-10-17 21:49:04,026 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.5638287
2015-10-17 21:49:04,727 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.667
2015-10-17 21:49:04,821 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:49:04,821 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:05,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:49:05,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:06,411 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.45560816
2015-10-17 21:49:06,503 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.7033178
2015-10-17 21:49:06,550 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.49217147
2015-10-17 21:49:06,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:49:06,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:06,962 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.5638227
2015-10-17 21:49:06,970 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.455643
2015-10-17 21:49:07,037 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.5638287
2015-10-17 21:49:07,745 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.667
2015-10-17 21:49:07,824 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:49:07,824 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:08,825 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:49:08,825 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:09,409 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.45560816
2015-10-17 21:49:09,521 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.74206626
2015-10-17 21:49:09,550 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.563847
2015-10-17 21:49:09,826 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-17 21:49:09,826 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:09,971 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.5638227
2015-10-17 21:49:09,975 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.46279538
2015-10-17 21:49:10,052 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.5638287
2015-10-17 21:49:10,763 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.7045539
2015-10-17 21:49:10,828 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:14336, vCores:-14>
2015-10-17 21:49:10,828 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:11,833 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 21:49:11,833 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000009 to attempt_1445087491445_0005_m_000009_0
2015-10-17 21:49:11,833 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000010 to attempt_1445087491445_0005_m_000011_0
2015-10-17 21:49:11,833 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:11264, vCores:-17>
2015-10-17 21:49:11,834 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:11,834 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:9 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:5 RackLocal:4
2015-10-17 21:49:11,834 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:49:11,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:49:11,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:49:11,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:49:11,836 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000009 taskAttempt attempt_1445087491445_0005_m_000009_0
2015-10-17 21:49:11,836 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000010 taskAttempt attempt_1445087491445_0005_m_000011_0
2015-10-17 21:49:11,836 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000009_0
2015-10-17 21:49:11,836 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000011_0
2015-10-17 21:49:11,837 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:49:11,837 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:49:11,958 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000009_0 : 13562
2015-10-17 21:49:11,958 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000009_0] using containerId: [container_1445087491445_0005_01_000009 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 21:49:11,958 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:49:11,959 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000009
2015-10-17 21:49:11,959 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:49:11,998 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000011_0 : 13562
2015-10-17 21:49:11,999 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000011_0] using containerId: [container_1445087491445_0005_01_000010 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 21:49:11,999 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:49:11,999 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000011
2015-10-17 21:49:11,999 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000011 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:49:12,409 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.45560816
2015-10-17 21:49:12,540 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.7817287
2015-10-17 21:49:12,551 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.563847
2015-10-17 21:49:12,835 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:11264, vCores:-17> knownNMs=4
2015-10-17 21:49:12,835 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:11264, vCores:-17>
2015-10-17 21:49:12,835 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:12,975 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.57611114
2015-10-17 21:49:12,988 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.5638264
2015-10-17 21:49:13,068 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.6316091
2015-10-17 21:49:13,783 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.7461594
2015-10-17 21:49:13,836 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:11264, vCores:-17>
2015-10-17 21:49:13,836 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:14,207 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.6316091
2015-10-17 21:49:14,826 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.57611114
2015-10-17 21:49:14,837 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:10240, vCores:-18>
2015-10-17 21:49:14,837 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:15,415 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.46084034
2015-10-17 21:49:15,551 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.563847
2015-10-17 21:49:15,558 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.82154495
2015-10-17 21:49:15,838 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:10240, vCores:-18>
2015-10-17 21:49:15,838 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:15,974 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.667
2015-10-17 21:49:15,987 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.5638264
2015-10-17 21:49:16,067 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.667
2015-10-17 21:49:16,801 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.7880851
2015-10-17 21:49:16,841 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:49:16,841 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:49:16,841 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000011 to attempt_1445087491445_0005_m_000006_0
2015-10-17 21:49:16,841 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-19>
2015-10-17 21:49:16,841 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:16,841 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:5 RackLocal:5
2015-10-17 21:49:16,841 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:49:16,842 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:49:16,843 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000011 taskAttempt attempt_1445087491445_0005_m_000006_0
2015-10-17 21:49:16,843 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000006_0
2015-10-17 21:49:16,843 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:49:17,200 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000006_0 : 13562
2015-10-17 21:49:17,200 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000006_0] using containerId: [container_1445087491445_0005_01_000011 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 21:49:17,201 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:49:17,201 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000006
2015-10-17 21:49:17,201 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:49:17,842 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:9216, vCores:-19> knownNMs=4
2015-10-17 21:49:17,842 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-19>
2015-10-17 21:49:17,842 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:18,434 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.55209094
2015-10-17 21:49:18,551 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.6430008
2015-10-17 21:49:18,575 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.86109155
2015-10-17 21:49:18,774 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:49:18,775 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:49:18,844 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-19>
2015-10-17 21:49:18,844 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:18,854 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000010 asked for a task
2015-10-17 21:49:18,854 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000010 given task: attempt_1445087491445_0005_m_000011_0
2015-10-17 21:49:18,854 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000009 asked for a task
2015-10-17 21:49:18,854 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000009 given task: attempt_1445087491445_0005_m_000009_0
2015-10-17 21:49:18,977 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.667
2015-10-17 21:49:18,986 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.5638264
2015-10-17 21:49:19,070 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.667
2015-10-17 21:49:19,724 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.6430008
2015-10-17 21:49:19,822 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.8301652
2015-10-17 21:49:19,847 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:49:19,847 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:49:19,847 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000012 to attempt_1445087491445_0005_m_000007_0
2015-10-17 21:49:19,847 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-20>
2015-10-17 21:49:19,847 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:19,847 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:49:19,847 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:11 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:5 RackLocal:6
2015-10-17 21:49:19,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:49:19,848 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000012 taskAttempt attempt_1445087491445_0005_m_000007_0
2015-10-17 21:49:19,848 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000007_0
2015-10-17 21:49:19,849 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:49:20,546 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000007_0 : 13562
2015-10-17 21:49:20,546 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000007_0] using containerId: [container_1445087491445_0005_01_000012 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 21:49:20,547 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:49:20,547 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000007
2015-10-17 21:49:20,547 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:49:20,850 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:7168, vCores:-21> knownNMs=4
2015-10-17 21:49:20,850 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:49:20,850 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:49:20,851 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000013 to attempt_1445087491445_0005_m_000008_0
2015-10-17 21:49:20,851 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-21>
2015-10-17 21:49:20,851 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:20,851 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:49:20,851 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:12 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:5 RackLocal:7
2015-10-17 21:49:20,852 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:49:20,852 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000013 taskAttempt attempt_1445087491445_0005_m_000008_0
2015-10-17 21:49:20,852 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000008_0
2015-10-17 21:49:20,852 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:49:20,962 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000008_0 : 13562
2015-10-17 21:49:20,962 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000008_0] using containerId: [container_1445087491445_0005_01_000013 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 21:49:20,962 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:49:20,963 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000008
2015-10-17 21:49:20,963 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:49:21,442 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.5638118
2015-10-17 21:49:21,552 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.667
2015-10-17 21:49:21,615 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.89874566
2015-10-17 21:49:21,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:7168, vCores:-21> knownNMs=4
2015-10-17 21:49:21,854 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-21>
2015-10-17 21:49:21,854 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:21,989 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.667
2015-10-17 21:49:21,990 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.6077303
2015-10-17 21:49:22,083 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.667
2015-10-17 21:49:22,860 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:49:22,860 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.85607255
2015-10-17 21:49:22,860 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:49:22,861 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000014 to attempt_1445087491445_0005_m_000012_0
2015-10-17 21:49:22,861 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-22>
2015-10-17 21:49:22,861 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:22,861 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:49:22,861 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:5 RackLocal:8
2015-10-17 21:49:22,862 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000012_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:49:22,863 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000014 taskAttempt attempt_1445087491445_0005_m_000012_0
2015-10-17 21:49:22,863 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000012_0
2015-10-17 21:49:22,864 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:49:23,228 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000012_0 : 13562
2015-10-17 21:49:23,229 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000012_0] using containerId: [container_1445087491445_0005_01_000014 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 21:49:23,229 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000012_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:49:23,230 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000012
2015-10-17 21:49:23,230 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000012 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:49:23,661 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.6077303
2015-10-17 21:49:23,864 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:6144, vCores:-22> knownNMs=4
2015-10-17 21:49:24,442 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.5638118
2015-10-17 21:49:24,553 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.667
2015-10-17 21:49:24,649 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.9175372
2015-10-17 21:49:24,989 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.6732478
2015-10-17 21:49:25,004 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.667
2015-10-17 21:49:25,083 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.68981385
2015-10-17 21:49:25,896 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.8729832
2015-10-17 21:49:26,372 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:49:26,479 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000011 asked for a task
2015-10-17 21:49:26,480 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000011 given task: attempt_1445087491445_0005_m_000006_0
2015-10-17 21:49:27,444 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.5638118
2015-10-17 21:49:27,552 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.667
2015-10-17 21:49:27,681 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.9396788
2015-10-17 21:49:27,873 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-21>
2015-10-17 21:49:27,873 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:27,991 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.7242781
2015-10-17 21:49:28,011 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.667
2015-10-17 21:49:28,085 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.74450433
2015-10-17 21:49:28,874 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-20>
2015-10-17 21:49:28,874 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:28,923 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.89185774
2015-10-17 21:49:29,875 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-19>
2015-10-17 21:49:29,875 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 21:49:30,460 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.6549402
2015-10-17 21:49:30,553 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.66806614
2015-10-17 21:49:30,702 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 0.974473
2015-10-17 21:49:30,902 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:49:30,987 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000012 asked for a task
2015-10-17 21:49:30,988 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000012 given task: attempt_1445087491445_0005_m_000007_0
2015-10-17 21:49:30,992 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.7663126
2015-10-17 21:49:31,019 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.667
2015-10-17 21:49:31,085 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.7913882
2015-10-17 21:49:31,942 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.92037904
2015-10-17 21:49:32,684 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000000_0 is : 1.0
2015-10-17 21:49:32,685 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0005_m_000000_0
2015-10-17 21:49:32,686 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:49:32,687 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000002 taskAttempt attempt_1445087491445_0005_m_000000_0
2015-10-17 21:49:32,687 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000000_0
2015-10-17 21:49:32,687 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:49:32,703 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:49:32,709 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0005_m_000000_0
2015-10-17 21:49:32,710 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:49:32,712 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-17 21:49:32,806 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:49:32,877 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:5 RackLocal:8
2015-10-17 21:49:32,879 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-19>
2015-10-17 21:49:32,879 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-17 21:49:32,879 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-17 21:49:32,879 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:13 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:5 RackLocal:8
2015-10-17 21:49:33,008 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000014 asked for a task
2015-10-17 21:49:33,008 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000014 given task: attempt_1445087491445_0005_m_000012_0
2015-10-17 21:49:33,458 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.6669973
2015-10-17 21:49:33,554 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.7143454
2015-10-17 21:49:33,885 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=1 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:10240, vCores:-18> knownNMs=4
2015-10-17 21:49:33,885 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000002
2015-10-17 21:49:33,886 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:12 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:5 RackLocal:8
2015-10-17 21:49:33,886 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:49:33,990 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.8117031
2015-10-17 21:49:34,022 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.667
2015-10-17 21:49:34,084 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.84077716
2015-10-17 21:49:34,892 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:49:34,892 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 21:49:34,893 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000015 to attempt_1445087491445_0005_r_000000_0
2015-10-17 21:49:34,893 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:5 RackLocal:8
2015-10-17 21:49:34,912 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:49:34,914 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:49:34,914 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000015 taskAttempt attempt_1445087491445_0005_r_000000_0
2015-10-17 21:49:34,915 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_r_000000_0
2015-10-17 21:49:34,915 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:49:34,974 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.9592762
2015-10-17 21:49:35,637 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_r_000000_0 : 13562
2015-10-17 21:49:35,638 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_r_000000_0] using containerId: [container_1445087491445_0005_01_000015 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 21:49:35,639 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:49:35,639 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_r_000000
2015-10-17 21:49:35,640 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:49:35,895 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:9216, vCores:-19> knownNMs=4
2015-10-17 21:49:36,464 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.6669973
2015-10-17 21:49:36,556 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.7645165
2015-10-17 21:49:36,993 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.855636
2015-10-17 21:49:37,024 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.708393
2015-10-17 21:49:37,088 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.8871951
2015-10-17 21:49:37,317 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.038659796
2015-10-17 21:49:37,453 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:49:37,525 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0005_m_000011
2015-10-17 21:49:37,526 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 21:49:37,526 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445087491445_0005_m_000011
2015-10-17 21:49:37,527 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:49:37,527 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:49:37,528 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:49:37,763 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000013 asked for a task
2015-10-17 21:49:37,763 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000013 given task: attempt_1445087491445_0005_m_000008_0
2015-10-17 21:49:37,898 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:5 RackLocal:8
2015-10-17 21:49:37,900 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:10240, vCores:-18> knownNMs=4
2015-10-17 21:49:38,003 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 0.98724467
2015-10-17 21:49:38,904 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:49:38,905 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000016 to attempt_1445087491445_0005_m_000011_1
2015-10-17 21:49:38,905 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:6 RackLocal:8
2015-10-17 21:49:38,905 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:49:38,907 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:49:38,907 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000016 taskAttempt attempt_1445087491445_0005_m_000011_1
2015-10-17 21:49:38,908 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000011_1
2015-10-17 21:49:38,908 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:49:39,269 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000011_1 : 13562
2015-10-17 21:49:39,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000011_1] using containerId: [container_1445087491445_0005_01_000016 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 21:49:39,270 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:49:39,270 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000011
2015-10-17 21:49:39,462 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000001_0 is : 1.0
2015-10-17 21:49:39,464 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0005_m_000001_0
2015-10-17 21:49:39,465 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:49:39,465 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000003 taskAttempt attempt_1445087491445_0005_m_000001_0
2015-10-17 21:49:39,466 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000001_0
2015-10-17 21:49:39,466 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:49:39,476 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.6669973
2015-10-17 21:49:39,479 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:49:39,480 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0005_m_000001_0
2015-10-17 21:49:39,480 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:49:39,481 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-17 21:49:39,573 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.80759096
2015-10-17 21:49:39,905 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:6 RackLocal:8
2015-10-17 21:49:39,907 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:10240, vCores:-18> knownNMs=4
2015-10-17 21:49:40,005 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.89265954
2015-10-17 21:49:40,040 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.74568313
2015-10-17 21:49:40,099 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.92820525
2015-10-17 21:49:40,303 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.07748995
2015-10-17 21:49:40,488 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.100960016
2015-10-17 21:49:40,909 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000003
2015-10-17 21:49:40,910 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:6 RackLocal:8
2015-10-17 21:49:40,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:49:42,490 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.6669973
2015-10-17 21:49:42,583 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.85757077
2015-10-17 21:49:43,011 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.9432473
2015-10-17 21:49:43,054 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.79534304
2015-10-17 21:49:43,103 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 0.98286295
2015-10-17 21:49:43,572 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.13101342
2015-10-17 21:49:43,729 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.13101514
2015-10-17 21:49:44,175 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:49:44,319 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_r_000015 asked for a task
2015-10-17 21:49:44,319 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_r_000015 given task: attempt_1445087491445_0005_r_000000_0
2015-10-17 21:49:44,496 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000010_0 is : 1.0
2015-10-17 21:49:44,499 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0005_m_000010_0
2015-10-17 21:49:44,500 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000010_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:49:44,500 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000004 taskAttempt attempt_1445087491445_0005_m_000010_0
2015-10-17 21:49:44,501 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000010_0
2015-10-17 21:49:44,502 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:49:44,518 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000010_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:49:44,518 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0005_m_000010_0
2015-10-17 21:49:44,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000010 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:49:44,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-17 21:49:44,916 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:6 RackLocal:8
2015-10-17 21:49:45,492 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.6669973
2015-10-17 21:49:45,591 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.910573
2015-10-17 21:49:45,921 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000004
2015-10-17 21:49:45,921 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:6 RackLocal:8
2015-10-17 21:49:45,922 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000010_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:49:46,025 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 0.97776675
2015-10-17 21:49:46,061 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.8295975
2015-10-17 21:49:46,869 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:49:46,921 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.13101342
2015-10-17 21:49:46,948 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000016 asked for a task
2015-10-17 21:49:46,949 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000016 given task: attempt_1445087491445_0005_m_000011_1
2015-10-17 21:49:46,951 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.13101514
2015-10-17 21:49:48,483 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 0 maxEvents 10000
2015-10-17 21:49:48,497 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000002_0 is : 1.0
2015-10-17 21:49:48,501 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0005_m_000002_0
2015-10-17 21:49:48,501 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:49:48,502 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000005 taskAttempt attempt_1445087491445_0005_m_000002_0
2015-10-17 21:49:48,502 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000002_0
2015-10-17 21:49:48,503 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:49:48,518 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:49:48,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0005_m_000002_0
2015-10-17 21:49:48,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:49:48,520 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-17 21:49:48,607 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 0.9649948
2015-10-17 21:49:48,927 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:6 RackLocal:8
2015-10-17 21:49:49,078 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.8570163
2015-10-17 21:49:49,512 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 21:49:49,703 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.6669973
2015-10-17 21:49:49,931 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000005
2015-10-17 21:49:49,931 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:6 RackLocal:8
2015-10-17 21:49:49,931 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:49:50,091 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.13101342
2015-10-17 21:49:50,114 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.13101514
2015-10-17 21:49:50,592 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 21:49:51,609 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 21:49:51,622 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 1.0
2015-10-17 21:49:52,086 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.88613915
2015-10-17 21:49:52,154 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000005_0 is : 1.0
2015-10-17 21:49:52,156 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0005_m_000005_0
2015-10-17 21:49:52,156 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:49:52,157 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000008 taskAttempt attempt_1445087491445_0005_m_000005_0
2015-10-17 21:49:52,157 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000005_0
2015-10-17 21:49:52,157 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:49:52,168 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:49:52,169 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0005_m_000005_0
2015-10-17 21:49:52,169 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:49:52,169 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-17 21:49:52,528 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0005_m_000009
2015-10-17 21:49:52,529 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 21:49:52,529 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445087491445_0005_m_000009
2015-10-17 21:49:52,529 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:49:52,529 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:49:52,529 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000009_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:49:52,685 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 21:49:52,934 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:6 RackLocal:8
2015-10-17 21:49:52,936 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:13312, vCores:-15> knownNMs=4
2015-10-17 21:49:53,399 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.13101342
2015-10-17 21:49:53,521 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.13101514
2015-10-17 21:49:53,738 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 21:49:53,941 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000008
2015-10-17 21:49:53,941 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:49:53,941 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:49:53,941 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000017 to attempt_1445087491445_0005_m_000009_1
2015-10-17 21:49:53,942 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-17 21:49:53,942 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:49:53,943 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000009_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:49:53,943 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000017 taskAttempt attempt_1445087491445_0005_m_000009_1
2015-10-17 21:49:53,943 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000009_1
2015-10-17 21:49:53,943 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:49:53,953 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000009_1 : 13562
2015-10-17 21:49:53,954 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000009_1] using containerId: [container_1445087491445_0005_01_000017 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 21:49:53,954 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000009_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:49:53,954 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000009
2015-10-17 21:49:54,499 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.71486235
2015-10-17 21:49:54,627 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:49:54,809 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 21:49:54,915 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.019536857
2015-10-17 21:49:54,944 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:13312, vCores:-15> knownNMs=4
2015-10-17 21:49:55,086 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.92956233
2015-10-17 21:49:55,357 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.014651966
2015-10-17 21:49:55,866 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 21:49:56,015 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.08559474
2015-10-17 21:49:56,522 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.069366135
2015-10-17 21:49:56,607 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.13101342
2015-10-17 21:49:56,691 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.13101514
2015-10-17 21:49:56,946 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 21:49:57,514 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.7659111
2015-10-17 21:49:57,941 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:49:58,031 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 21:49:58,056 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:49:58,086 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.9598905
2015-10-17 21:49:58,090 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000017 asked for a task
2015-10-17 21:49:58,090 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000017 given task: attempt_1445087491445_0005_m_000009_1
2015-10-17 21:49:58,227 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.040056143
2015-10-17 21:49:58,491 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.021815024
2015-10-17 21:49:59,485 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.13101514
2015-10-17 21:49:59,582 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 21:49:59,784 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.0983528
2015-10-17 21:49:59,923 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.13101342
2015-10-17 21:49:59,956 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.13101514
2015-10-17 21:50:00,530 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.8177525
2015-10-17 21:50:00,611 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 21:50:01,095 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 0.9949733
2015-10-17 21:50:01,294 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:50:01,572 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.06708712
2015-10-17 21:50:01,743 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000003_0 is : 1.0
2015-10-17 21:50:01,745 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0005_m_000003_0
2015-10-17 21:50:01,745 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:50:01,745 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000006 taskAttempt attempt_1445087491445_0005_m_000003_0
2015-10-17 21:50:01,746 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000003_0
2015-10-17 21:50:01,746 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:50:01,749 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 21:50:01,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:50:01,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0005_m_000003_0
2015-10-17 21:50:01,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:50:01,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-17 21:50:01,955 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-17 21:50:02,428 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.028882042
2015-10-17 21:50:02,819 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 21:50:02,958 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000006
2015-10-17 21:50:02,959 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-17 21:50:02,959 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:50:03,107 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.13101514
2015-10-17 21:50:03,151 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.11496125
2015-10-17 21:50:03,266 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.13101514
2015-10-17 21:50:03,276 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.13101342
2015-10-17 21:50:03,281 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_0 is : 0.015954664
2015-10-17 21:50:03,540 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.85628927
2015-10-17 21:50:03,890 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 21:50:04,690 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:50:04,980 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 21:50:05,016 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.08923178
2015-10-17 21:50:05,938 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.036468506
2015-10-17 21:50:06,084 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.13101342
2015-10-17 21:50:06,107 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 21:50:06,525 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.13101514
2015-10-17 21:50:06,539 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.91267234
2015-10-17 21:50:06,654 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.12831582
2015-10-17 21:50:06,716 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.13101514
2015-10-17 21:50:06,754 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_0 is : 0.038751405
2015-10-17 21:50:06,763 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.13101342
2015-10-17 21:50:07,231 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 21:50:07,531 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0005_m_000006
2015-10-17 21:50:07,531 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 21:50:07,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445087491445_0005_m_000006
2015-10-17 21:50:07,533 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:50:07,534 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:50:07,534 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000006_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:50:07,965 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-17 21:50:07,967 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:14336, vCores:-14> knownNMs=4
2015-10-17 21:50:07,996 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:50:08,311 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 21:50:08,418 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.10681914
2015-10-17 21:50:08,973 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:50:08,974 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000018 to attempt_1445087491445_0005_m_000006_1
2015-10-17 21:50:08,974 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:8 RackLocal:8
2015-10-17 21:50:08,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:50:08,975 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000006_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:50:08,975 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000018 taskAttempt attempt_1445087491445_0005_m_000006_1
2015-10-17 21:50:08,975 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000006_1
2015-10-17 21:50:08,976 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:50:08,985 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000006_1 : 13562
2015-10-17 21:50:08,986 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000006_1] using containerId: [container_1445087491445_0005_01_000018 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 21:50:08,986 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000006_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:50:08,986 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000006
2015-10-17 21:50:09,113 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.13101342
2015-10-17 21:50:09,400 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.041680846
2015-10-17 21:50:09,458 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 21:50:09,541 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 0.9694172
2015-10-17 21:50:09,976 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:13312, vCores:-15> knownNMs=4
2015-10-17 21:50:10,059 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.13101514
2015-10-17 21:50:10,224 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.13101453
2015-10-17 21:50:10,263 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_0 is : 0.059594277
2015-10-17 21:50:10,339 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.13450551
2015-10-17 21:50:10,600 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.13101342
2015-10-17 21:50:10,608 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 21:50:11,518 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:50:11,673 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 21:50:11,746 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.12636223
2015-10-17 21:50:12,136 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.13101342
2015-10-17 21:50:12,549 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 1.0
2015-10-17 21:50:12,561 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000004_0 is : 1.0
2015-10-17 21:50:12,562 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0005_m_000004_0
2015-10-17 21:50:12,563 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:50:12,563 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000007 taskAttempt attempt_1445087491445_0005_m_000004_0
2015-10-17 21:50:12,563 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000004_0
2015-10-17 21:50:12,564 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:50:12,575 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:50:12,575 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0005_m_000004_0
2015-10-17 21:50:12,575 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:50:12,576 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-17 21:50:12,648 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:50:12,671 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000018 asked for a task
2015-10-17 21:50:12,671 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000018 given task: attempt_1445087491445_0005_m_000006_1
2015-10-17 21:50:12,735 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 21:50:12,818 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.04787091
2015-10-17 21:50:12,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:8 RackLocal:8
2015-10-17 21:50:13,408 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.13101514
2015-10-17 21:50:13,751 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.13101453
2015-10-17 21:50:13,795 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.20602982
2015-10-17 21:50:13,795 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:13,954 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_0 is : 0.07620536
2015-10-17 21:50:13,982 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000007
2015-10-17 21:50:13,982 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:8 RackLocal:8
2015-10-17 21:50:13,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:50:13,999 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.17800425
2015-10-17 21:50:14,874 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:14,906 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:50:15,164 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.1757101
2015-10-17 21:50:15,276 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.13104554
2015-10-17 21:50:15,939 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:16,325 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.056988455
2015-10-17 21:50:16,985 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.13101514
2015-10-17 21:50:17,032 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:17,336 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.2392493
2015-10-17 21:50:17,426 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.13101453
2015-10-17 21:50:17,495 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.23920888
2015-10-17 21:50:17,507 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_0 is : 0.09477191
2015-10-17 21:50:18,120 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:18,183 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.23920888
2015-10-17 21:50:18,472 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:50:18,916 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.13104554
2015-10-17 21:50:19,217 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:19,700 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.07362356
2015-10-17 21:50:20,193 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.13102981
2015-10-17 21:50:20,329 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:20,786 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.13101514
2015-10-17 21:50:20,939 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.13101453
2015-10-17 21:50:20,943 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.2392493
2015-10-17 21:50:21,159 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.23920888
2015-10-17 21:50:21,173 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_0 is : 0.114641935
2015-10-17 21:50:21,200 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.23920888
2015-10-17 21:50:21,409 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:21,989 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:50:22,317 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.13104554
2015-10-17 21:50:22,499 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:22,534 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0005_m_000008
2015-10-17 21:50:22,534 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 21:50:22,534 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445087491445_0005_m_000008
2015-10-17 21:50:22,535 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:50:22,535 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:50:22,536 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000008_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:50:22,992 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:8 RackLocal:8
2015-10-17 21:50:22,993 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:14336, vCores:-14> knownNMs=4
2015-10-17 21:50:23,212 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.13102981
2015-10-17 21:50:23,228 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.087022424
2015-10-17 21:50:23,582 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:23,996 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:50:23,996 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000019 to attempt_1445087491445_0005_m_000008_1
2015-10-17 21:50:23,996 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-17 21:50:23,997 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:50:23,997 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000008_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:50:23,997 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000019 taskAttempt attempt_1445087491445_0005_m_000008_1
2015-10-17 21:50:23,998 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000008_1
2015-10-17 21:50:23,998 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:50:24,008 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000008_1 : 13562
2015-10-17 21:50:24,008 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000008_1] using containerId: [container_1445087491445_0005_01_000019 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 21:50:24,009 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000008_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:50:24,009 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000008
2015-10-17 21:50:24,218 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.23920888
2015-10-17 21:50:24,681 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.13101514
2015-10-17 21:50:24,702 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:24,854 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.13101453
2015-10-17 21:50:24,896 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.2392493
2015-10-17 21:50:24,997 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:13312, vCores:-15> knownNMs=4
2015-10-17 21:50:25,023 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_0 is : 0.13101445
2015-10-17 21:50:25,125 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.23920888
2015-10-17 21:50:25,692 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:50:25,804 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:25,926 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.13104554
2015-10-17 21:50:26,018 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:50:26,050 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000019 asked for a task
2015-10-17 21:50:26,050 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000019 given task: attempt_1445087491445_0005_m_000008_1
2015-10-17 21:50:26,241 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.1427087
2015-10-17 21:50:26,820 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.10518839
2015-10-17 21:50:26,878 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:27,237 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.34739637
2015-10-17 21:50:27,953 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:28,429 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.1358542
2015-10-17 21:50:28,532 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.13101453
2015-10-17 21:50:28,704 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.2392493
2015-10-17 21:50:28,752 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_0 is : 0.13101445
2015-10-17 21:50:28,784 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.23920888
2015-10-17 21:50:29,061 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:29,261 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.2392158
2015-10-17 21:50:29,424 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.13104554
2015-10-17 21:50:29,456 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:50:30,167 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:30,255 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.34739637
2015-10-17 21:50:30,508 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.13059545
2015-10-17 21:50:31,281 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:32,277 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.20446521
2015-10-17 21:50:32,279 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.2392158
2015-10-17 21:50:32,355 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.13101453
2015-10-17 21:50:32,358 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:32,550 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_0 is : 0.13101445
2015-10-17 21:50:32,559 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.2392493
2015-10-17 21:50:32,585 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.23920888
2015-10-17 21:50:33,162 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.13104554
2015-10-17 21:50:33,273 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.34739637
2015-10-17 21:50:33,428 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:50:33,439 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:33,799 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.13101445
2015-10-17 21:50:34,364 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.13102981
2015-10-17 21:50:34,568 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:35,299 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.25207433
2015-10-17 21:50:35,647 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:36,154 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.2392493
2015-10-17 21:50:36,209 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.13659258
2015-10-17 21:50:36,291 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.45559853
2015-10-17 21:50:36,368 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.23920888
2015-10-17 21:50:36,371 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_0 is : 0.13101445
2015-10-17 21:50:36,414 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.2392493
2015-10-17 21:50:36,752 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:36,816 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.13101445
2015-10-17 21:50:36,989 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:50:37,018 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.13104554
2015-10-17 21:50:37,536 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0005_m_000007
2015-10-17 21:50:37,536 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 21:50:37,536 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445087491445_0005_m_000007
2015-10-17 21:50:37,537 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:50:37,538 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:50:37,538 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000007_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:50:37,833 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:37,878 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.13102981
2015-10-17 21:50:38,013 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-17 21:50:38,014 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:13312, vCores:-15> knownNMs=4
2015-10-17 21:50:38,317 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.34742883
2015-10-17 21:50:38,913 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:39,017 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:50:39,017 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000020 to attempt_1445087491445_0005_m_000007_1
2015-10-17 21:50:39,017 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-17 21:50:39,017 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:50:39,018 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000007_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:50:39,018 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000020 taskAttempt attempt_1445087491445_0005_m_000007_1
2015-10-17 21:50:39,018 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000007_1
2015-10-17 21:50:39,019 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:50:39,029 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000007_1 : 13562
2015-10-17 21:50:39,029 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000007_1] using containerId: [container_1445087491445_0005_01_000020 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 21:50:39,030 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000007_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:50:39,030 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000007
2015-10-17 21:50:39,308 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.45559853
2015-10-17 21:50:39,731 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.2392493
2015-10-17 21:50:39,835 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.2391939
2015-10-17 21:50:39,894 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.16055505
2015-10-17 21:50:40,003 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:40,019 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:12288, vCores:-16> knownNMs=4
2015-10-17 21:50:40,114 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_0 is : 0.13101445
2015-10-17 21:50:40,119 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.2392493
2015-10-17 21:50:40,143 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.23920888
2015-10-17 21:50:40,841 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:50:40,851 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.13104554
2015-10-17 21:50:41,078 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:50:41,094 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000020 asked for a task
2015-10-17 21:50:41,094 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000020 given task: attempt_1445087491445_0005_m_000007_1
2015-10-17 21:50:41,143 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:41,335 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.34742883
2015-10-17 21:50:41,442 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.13102981
2015-10-17 21:50:42,217 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:42,328 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.45559853
2015-10-17 21:50:42,854 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.2391939
2015-10-17 21:50:43,334 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:43,505 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.2392493
2015-10-17 21:50:43,636 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.2392493
2015-10-17 21:50:43,761 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.20061724
2015-10-17 21:50:43,766 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_0 is : 0.13101445
2015-10-17 21:50:43,771 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.23920888
2015-10-17 21:50:44,369 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.3678606
2015-10-17 21:50:44,403 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:44,442 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.13645977
2015-10-17 21:50:44,473 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:50:44,949 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.13102981
2015-10-17 21:50:45,364 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.5628953
2015-10-17 21:50:45,887 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.2391939
2015-10-17 21:50:46,025 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:47,048 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.2392493
2015-10-17 21:50:47,144 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:47,326 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.2392493
2015-10-17 21:50:47,399 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.4556358
2015-10-17 21:50:47,507 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_0 is : 0.13101445
2015-10-17 21:50:47,525 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.23608091
2015-10-17 21:50:47,632 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.23920888
2015-10-17 21:50:48,217 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.15599987
2015-10-17 21:50:48,265 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:48,346 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.13104554
2015-10-17 21:50:48,394 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.5638114
2015-10-17 21:50:48,502 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.13102981
2015-10-17 21:50:48,506 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:50:48,918 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.34738368
2015-10-17 21:50:49,323 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:50,408 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:50,418 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.4556358
2015-10-17 21:50:50,632 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.2392493
2015-10-17 21:50:50,886 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.29700565
2015-10-17 21:50:51,089 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_0 is : 0.13101445
2015-10-17 21:50:51,224 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.23922452
2015-10-17 21:50:51,225 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.2409076
2015-10-17 21:50:51,376 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.13104554
2015-10-17 21:50:51,423 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.5638114
2015-10-17 21:50:51,499 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:51,754 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.18466008
2015-10-17 21:50:51,936 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.34738368
2015-10-17 21:50:52,004 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:50:52,009 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.13102981
2015-10-17 21:50:52,537 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0005_m_000012
2015-10-17 21:50:52,537 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 21:50:52,537 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445087491445_0005_m_000012
2015-10-17 21:50:52,538 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:50:52,538 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:50:52,538 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000012_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:50:52,609 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:53,035 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-17 21:50:53,037 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:12288, vCores:-16> knownNMs=4
2015-10-17 21:50:53,447 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.4556358
2015-10-17 21:50:53,709 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:53,923 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.5638114
2015-10-17 21:50:54,041 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:50:54,042 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000021 to attempt_1445087491445_0005_m_000012_1
2015-10-17 21:50:54,042 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:20 ContRel:0 HostLocal:11 RackLocal:8
2015-10-17 21:50:54,042 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:50:54,043 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000012_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:50:54,044 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000021 taskAttempt attempt_1445087491445_0005_m_000012_1
2015-10-17 21:50:54,044 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000012_1
2015-10-17 21:50:54,044 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:50:54,061 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000012_1 : 13562
2015-10-17 21:50:54,062 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000012_1] using containerId: [container_1445087491445_0005_01_000021 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 21:50:54,062 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000012_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:50:54,062 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000012
2015-10-17 21:50:54,403 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.1782225
2015-10-17 21:50:54,437 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.2392493
2015-10-17 21:50:54,442 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.667
2015-10-17 21:50:54,510 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.34746152
2015-10-17 21:50:54,811 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_0 is : 0.13101445
2015-10-17 21:50:54,811 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.23922452
2015-10-17 21:50:54,812 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:54,851 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.30140468
2015-10-17 21:50:54,961 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.34738368
2015-10-17 21:50:55,044 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:11264, vCores:-17> knownNMs=4
2015-10-17 21:50:55,542 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.22275786
2015-10-17 21:50:55,823 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:50:55,903 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:56,181 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.13102981
2015-10-17 21:50:56,251 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:50:56,266 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000021 asked for a task
2015-10-17 21:50:56,267 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000021 given task: attempt_1445087491445_0005_m_000012_1
2015-10-17 21:50:56,465 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.56383866
2015-10-17 21:50:57,018 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:57,421 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.23922384
2015-10-17 21:50:57,461 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.667
2015-10-17 21:50:57,979 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.45368624
2015-10-17 21:50:58,112 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:58,256 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.2392493
2015-10-17 21:50:58,365 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.34746152
2015-10-17 21:50:58,474 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.23922452
2015-10-17 21:50:58,592 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.34739637
2015-10-17 21:50:58,724 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_0 is : 0.15292408
2015-10-17 21:50:59,191 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:50:59,210 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.23922384
2015-10-17 21:50:59,367 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:50:59,483 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.56383866
2015-10-17 21:50:59,703 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.13102981
2015-10-17 21:51:00,263 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:51:00,440 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.23922384
2015-10-17 21:51:00,480 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.667
2015-10-17 21:51:00,998 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.4556043
2015-10-17 21:51:01,363 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:51:01,842 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.2392493
2015-10-17 21:51:01,909 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.34746152
2015-10-17 21:51:02,057 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.23922452
2015-10-17 21:51:02,146 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.34739637
2015-10-17 21:51:02,299 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_0 is : 0.17998266
2015-10-17 21:51:02,456 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:51:02,511 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.56383866
2015-10-17 21:51:02,684 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.23922384
2015-10-17 21:51:02,885 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:51:03,332 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.13102981
2015-10-17 21:51:03,467 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.28145298
2015-10-17 21:51:03,497 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.67200994
2015-10-17 21:51:03,511 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.13101453
2015-10-17 21:51:03,557 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:51:04,016 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.4556043
2015-10-17 21:51:04,354 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.56383866
2015-10-17 21:51:04,656 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:51:05,443 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.2392493
2015-10-17 21:51:05,537 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.667
2015-10-17 21:51:05,591 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.34746152
2015-10-17 21:51:05,639 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.23922452
2015-10-17 21:51:05,724 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.34739637
2015-10-17 21:51:05,731 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:51:05,906 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_0 is : 0.21169278
2015-10-17 21:51:06,035 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.23922384
2015-10-17 21:51:06,394 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:51:06,492 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.3474171
2015-10-17 21:51:06,524 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.7044852
2015-10-17 21:51:06,536 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.13101453
2015-10-17 21:51:06,820 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:51:06,897 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.15045905
2015-10-17 21:51:07,039 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.5481149
2015-10-17 21:51:07,911 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:51:08,566 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.667
2015-10-17 21:51:08,998 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:51:09,146 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.282518
2015-10-17 21:51:09,248 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.23922452
2015-10-17 21:51:09,262 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.34739637
2015-10-17 21:51:09,310 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.34746152
2015-10-17 21:51:09,515 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.3474171
2015-10-17 21:51:09,548 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.7347883
2015-10-17 21:51:09,557 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.23189494
2015-10-17 21:51:09,702 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_0 is : 0.2391939
2015-10-17 21:51:09,732 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.23922384
2015-10-17 21:51:10,057 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.56380266
2015-10-17 21:51:10,069 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:51:10,085 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:51:10,537 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.17032824
2015-10-17 21:51:11,187 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:51:11,585 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.667
2015-10-17 21:51:12,064 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0005_m_000009_0 because it is running on unusable node:04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:51:12,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0005_m_000011_0 because it is running on unusable node:04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:51:12,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0005_m_000006_0 because it is running on unusable node:04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:51:12,065 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000009_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:51:12,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0005_m_000007_0 because it is running on unusable node:04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:51:12,065 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:51:12,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0005_m_000008_0 because it is running on unusable node:04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:51:12,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0005_m_000012_0 because it is running on unusable node:04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:51:12,065 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000006_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:51:12,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0005_m_000011_1 because it is running on unusable node:04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:51:12,065 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000009 taskAttempt attempt_1445087491445_0005_m_000009_0
2015-10-17 21:51:12,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0005_r_000000_0 because it is running on unusable node:04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:51:12,065 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000007_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:51:12,065 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000010 taskAttempt attempt_1445087491445_0005_m_000011_0
2015-10-17 21:51:12,066 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000008_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:51:12,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000015
2015-10-17 21:51:12,066 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000009_0
2015-10-17 21:51:12,066 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000011 taskAttempt attempt_1445087491445_0005_m_000006_0
2015-10-17 21:51:12,066 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000012_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:51:12,066 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000011_0
2015-10-17 21:51:12,066 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000006_0
2015-10-17 21:51:12,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000009
2015-10-17 21:51:12,066 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:51:12,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000010
2015-10-17 21:51:12,066 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000012 taskAttempt attempt_1445087491445_0005_m_000007_0
2015-10-17 21:51:12,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000014
2015-10-17 21:51:12,066 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:51:12,066 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_r_000000_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:51:12,067 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000013
2015-10-17 21:51:12,066 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000007_0
2015-10-17 21:51:12,067 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000011
2015-10-17 21:51:12,067 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000014 taskAttempt attempt_1445087491445_0005_m_000012_0
2015-10-17 21:51:12,067 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000013 taskAttempt attempt_1445087491445_0005_m_000008_0
2015-10-17 21:51:12,067 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_r_000000_0: Container released on a *lost* node
2015-10-17 21:51:12,067 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000008_0
2015-10-17 21:51:12,067 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000012_0
2015-10-17 21:51:12,067 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000016 taskAttempt attempt_1445087491445_0005_m_000011_1
2015-10-17 21:51:12,067 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000016
2015-10-17 21:51:12,067 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:51:12,067 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000009_0: Container released on a *lost* node
2015-10-17 21:51:12,067 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000012
2015-10-17 21:51:12,068 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000011_0: Container released on a *lost* node
2015-10-17 21:51:12,068 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:7 CompletedReds:0 ContAlloc:20 ContRel:0 HostLocal:11 RackLocal:8
2015-10-17 21:51:12,068 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000012_0: Container released on a *lost* node
2015-10-17 21:51:12,067 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000011_1
2015-10-17 21:51:12,068 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000008_0: Container released on a *lost* node
2015-10-17 21:51:12,068 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000015 taskAttempt attempt_1445087491445_0005_r_000000_0
2015-10-17 21:51:12,068 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:51:12,068 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000006_0: Container released on a *lost* node
2015-10-17 21:51:12,068 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_r_000000_0
2015-10-17 21:51:12,068 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000011_1: Container released on a *lost* node
2015-10-17 21:51:12,068 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000007_0: Container released on a *lost* node
2015-10-17 21:51:12,069 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:51:12,069 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:51:12,070 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:51:12,070 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:51:12,071 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:51:12,315 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:51:12,532 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.3474171
2015-10-17 21:51:12,566 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.77234423
2015-10-17 21:51:12,574 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.23922452
2015-10-17 21:51:12,817 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.23922452
2015-10-17 21:51:12,836 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.3439185
2015-10-17 21:51:12,967 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:51:12,967 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:51:12,975 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out2/_temporary/1/_temporary/attempt_1445087491445_0005_m_000011_1
2015-10-17 21:51:12,976 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:51:12,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:12,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:12,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_2 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:51:13,068 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:7 CompletedReds:0 ContAlloc:20 ContRel:0 HostLocal:11 RackLocal:8
2015-10-17 21:51:13,069 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:11264, vCores:-9> knownNMs=3
2015-10-17 21:51:13,075 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.56380266
2015-10-17 21:51:13,124 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_0 is : 0.34739637
2015-10-17 21:51:13,127 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_0 is : 0.34746152
2015-10-17 21:51:13,163 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:51:13,163 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:51:13,164 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out2/_temporary/1/_temporary/attempt_1445087491445_0005_m_000011_0
2015-10-17 21:51:13,165 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:51:13,165 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:13,165 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:13,166 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_3 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:51:13,364 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_0 is : 0.23922384
2015-10-17 21:51:13,381 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_0 is : 0.2391939
2015-10-17 21:51:13,418 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 21:51:13,653 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:51:13,756 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000007_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:51:13,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_r_000000_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:51:13,757 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:51:13,757 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:51:13,759 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out2/_temporary/1/_temporary/attempt_1445087491445_0005_m_000007_0
2015-10-17 21:51:13,759 WARN [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out2/_temporary/1/_temporary/attempt_1445087491445_0005_r_000000_0
2015-10-17 21:51:13,759 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000007_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:51:13,759 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_r_000000_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:51:13,760 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:13,760 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:13,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000007_2 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:51:13,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_r_000000_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:51:13,858 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000006_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:51:13,858 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000009_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:51:13,859 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:51:13,859 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:51:13,860 WARN [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out2/_temporary/1/_temporary/attempt_1445087491445_0005_m_000006_0
2015-10-17 21:51:13,860 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out2/_temporary/1/_temporary/attempt_1445087491445_0005_m_000009_0
2015-10-17 21:51:13,861 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000006_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:51:13,861 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000009_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:51:13,861 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:13,861 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:13,861 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:13,862 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:13,862 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000006_2 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:51:13,862 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000009_2 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:51:13,925 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000008_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:51:13,925 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:51:13,927 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out2/_temporary/1/_temporary/attempt_1445087491445_0005_m_000008_0
2015-10-17 21:51:13,927 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000008_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:51:13,928 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:13,928 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:13,928 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000008_2 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:51:14,005 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000012_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:51:14,006 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:51:14,007 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out2/_temporary/1/_temporary/attempt_1445087491445_0005_m_000012_0
2015-10-17 21:51:14,007 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000012_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:51:14,008 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:14,008 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:14,008 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000012_2 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:51:14,069 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:7 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:7 CompletedReds:0 ContAlloc:20 ContRel:0 HostLocal:11 RackLocal:8
2015-10-17 21:51:14,071 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=6 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:11264, vCores:-9> knownNMs=3
2015-10-17 21:51:14,071 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:11264, vCores:-9>
2015-10-17 21:51:14,071 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.53846157 totalResourceLimit:<memory:16384, vCores:-4> finalMapResourceLimit:<memory:8192, vCores:-2> finalReduceResourceLimit:<memory:8192, vCores:-2> netScheduledMapResource:<memory:12288, vCores:12> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-17 21:51:14,071 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping up 1
2015-10-17 21:51:14,071 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:7 ScheduledReds:1 AssignedMaps:5 AssignedReds:0 CompletedMaps:7 CompletedReds:0 ContAlloc:20 ContRel:0 HostLocal:11 RackLocal:8
2015-10-17 21:51:14,139 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_0 is : 0.19384125
2015-10-17 21:51:14,311 INFO [Socket Reader #1 for port 32070] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 32070: readAndProcess from client *********** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:51:14,520 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 21:51:14,604 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.66842353
2015-10-17 21:51:15,072 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:11264, vCores:-9> knownNMs=3
2015-10-17 21:51:15,074 INFO [Socket Reader #1 for port 32070] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 32070: readAndProcess from client *********** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:51:15,551 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.4556043
2015-10-17 21:51:15,584 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.8111178
2015-10-17 21:51:15,590 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 14 maxEvents 10000
2015-10-17 21:51:15,592 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.23922452
2015-10-17 21:51:15,932 INFO [Socket Reader #1 for port 32070] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 32070: readAndProcess from client *********** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:51:16,075 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:51:16,075 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 21:51:16,075 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000022 to attempt_1445087491445_0005_r_000000_1
2015-10-17 21:51:16,075 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:7 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:21 ContRel:0 HostLocal:11 RackLocal:8
2015-10-17 21:51:16,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:16,077 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_r_000000_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:51:16,077 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000022 taskAttempt attempt_1445087491445_0005_r_000000_1
2015-10-17 21:51:16,077 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_r_000000_1
2015-10-17 21:51:16,077 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:51:16,090 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_r_000000_1 : 13562
2015-10-17 21:51:16,090 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_r_000000_1] using containerId: [container_1445087491445_0005_01_000022 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:51:16,090 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_r_000000_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:51:16,090 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_r_000000
2015-10-17 21:51:16,094 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.65375406
2015-10-17 21:51:16,136 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_1 is : 0.34746152
2015-10-17 21:51:16,180 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_0 is : 0.23922452
2015-10-17 21:51:16,412 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.65375406
2015-10-17 21:51:16,556 INFO [Socket Reader #1 for port 32070] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 32070: readAndProcess from client *********** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:51:16,684 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_0. startIndex 14 maxEvents 10000
2015-10-17 21:51:16,686 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_0 is : 0.2391939
2015-10-17 21:51:16,802 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_0 is : 0.0
2015-10-17 21:51:16,882 INFO [Socket Reader #1 for port 32070] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 32070: readAndProcess from client *********** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:51:17,042 INFO [Socket Reader #1 for port 32070] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 32070: readAndProcess from client *********** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:51:17,077 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:10240, vCores:-10> knownNMs=3
2015-10-17 21:51:17,170 INFO [Socket Reader #1 for port 32070] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 32070: readAndProcess from client *********** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:51:17,297 INFO [Socket Reader #1 for port 32070] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 32070: readAndProcess from client *********** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:51:17,624 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.704423
2015-10-17 21:51:18,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:51:18,082 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:18,083 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000023 to attempt_1445087491445_0005_m_000011_2
2015-10-17 21:51:18,083 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:6 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:22 ContRel:0 HostLocal:11 RackLocal:9
2015-10-17 21:51:18,083 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:18,085 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_2 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:51:18,085 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000023 taskAttempt attempt_1445087491445_0005_m_000011_2
2015-10-17 21:51:18,085 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000011_2
2015-10-17 21:51:18,086 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:51:18,107 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000011_2 : 13562
2015-10-17 21:51:18,108 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000011_2] using containerId: [container_1445087491445_0005_01_000023 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:51:18,108 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_2 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:51:18,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000011
2015-10-17 21:51:18,581 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.4556043
2015-10-17 21:51:18,614 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.8472828
2015-10-17 21:51:18,621 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.342399
2015-10-17 21:51:19,085 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:9216, vCores:-11> knownNMs=3
2015-10-17 21:51:19,124 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.667
2015-10-17 21:51:19,526 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:51:19,550 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_r_000022 asked for a task
2015-10-17 21:51:19,551 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_r_000022 given task: attempt_1445087491445_0005_r_000000_1
2015-10-17 21:51:20,654 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.7410565
2015-10-17 21:51:21,091 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:51:21,091 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:21,091 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000024 to attempt_1445087491445_0005_m_000011_3
2015-10-17 21:51:21,092 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:23 ContRel:0 HostLocal:11 RackLocal:10
2015-10-17 21:51:21,092 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:21,093 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_3 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:51:21,094 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000024 taskAttempt attempt_1445087491445_0005_m_000011_3
2015-10-17 21:51:21,094 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000011_3
2015-10-17 21:51:21,094 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:51:21,094 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 0 maxEvents 10000
2015-10-17 21:51:21,114 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000011_3 : 13562
2015-10-17 21:51:21,115 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000011_3] using containerId: [container_1445087491445_0005_01_000024 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:51:21,115 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_3 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:51:21,116 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000011
2015-10-17 21:51:21,413 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:51:21,433 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000023 asked for a task
2015-10-17 21:51:21,433 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000023 given task: attempt_1445087491445_0005_m_000011_2
2015-10-17 21:51:21,610 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.4556043
2015-10-17 21:51:21,642 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.8845787
2015-10-17 21:51:21,649 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.34740257
2015-10-17 21:51:22,094 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:8192, vCores:-12> knownNMs=3
2015-10-17 21:51:22,151 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.667
2015-10-17 21:51:22,885 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 14 maxEvents 10000
2015-10-17 21:51:23,677 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.78056544
2015-10-17 21:51:23,887 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 14 maxEvents 10000
2015-10-17 21:51:24,099 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:51:24,099 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:24,100 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000025 to attempt_1445087491445_0005_m_000007_2
2015-10-17 21:51:24,100 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:24 ContRel:0 HostLocal:11 RackLocal:11
2015-10-17 21:51:24,100 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:24,101 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000007_2 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:51:24,102 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000025 taskAttempt attempt_1445087491445_0005_m_000007_2
2015-10-17 21:51:24,102 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000007_2
2015-10-17 21:51:24,102 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:51:24,131 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000007_2 : 13562
2015-10-17 21:51:24,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000007_2] using containerId: [container_1445087491445_0005_01_000025 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:51:24,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000007_2 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:51:24,133 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000007
2015-10-17 21:51:24,559 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:51:24,581 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000024 asked for a task
2015-10-17 21:51:24,582 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000024 given task: attempt_1445087491445_0005_m_000011_3
2015-10-17 21:51:24,636 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.56379145
2015-10-17 21:51:24,667 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.9247623
2015-10-17 21:51:24,673 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.34740257
2015-10-17 21:51:24,891 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 14 maxEvents 10000
2015-10-17 21:51:25,101 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:7168, vCores:-13> knownNMs=3
2015-10-17 21:51:25,176 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.66908395
2015-10-17 21:51:25,891 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 14 maxEvents 10000
2015-10-17 21:51:26,708 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.8190857
2015-10-17 21:51:26,891 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 14 maxEvents 10000
2015-10-17 21:51:26,935 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:51:26,952 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000025 asked for a task
2015-10-17 21:51:26,952 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000025 given task: attempt_1445087491445_0005_m_000007_2
2015-10-17 21:51:27,041 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.07692308
2015-10-17 21:51:27,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:51:27,108 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:27,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000026 to attempt_1445087491445_0005_m_000006_2
2015-10-17 21:51:27,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:25 ContRel:0 HostLocal:11 RackLocal:12
2015-10-17 21:51:27,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:27,110 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000006_2 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:51:27,110 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000026 taskAttempt attempt_1445087491445_0005_m_000006_2
2015-10-17 21:51:27,111 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000006_2
2015-10-17 21:51:27,111 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:51:27,133 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000006_2 : 13562
2015-10-17 21:51:27,134 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000006_2] using containerId: [container_1445087491445_0005_01_000026 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:51:27,134 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000006_2 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:51:27,135 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000006
2015-10-17 21:51:27,666 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.56379145
2015-10-17 21:51:27,696 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 0.9628177
2015-10-17 21:51:27,700 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.4556311
2015-10-17 21:51:27,894 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 14 maxEvents 10000
2015-10-17 21:51:28,111 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:6144, vCores:-14> knownNMs=3
2015-10-17 21:51:28,205 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.7074603
2015-10-17 21:51:28,748 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.10747195
2015-10-17 21:51:28,896 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 14 maxEvents 10000
2015-10-17 21:51:29,740 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.856994
2015-10-17 21:51:29,895 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 14 maxEvents 10000
2015-10-17 21:51:30,042 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:51:30,053 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.12820514
2015-10-17 21:51:30,069 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000026 asked for a task
2015-10-17 21:51:30,070 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000026 given task: attempt_1445087491445_0005_m_000006_2
2015-10-17 21:51:30,696 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.5672582
2015-10-17 21:51:30,726 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 1.0
2015-10-17 21:51:30,728 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.4556311
2015-10-17 21:51:30,759 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000009_1 is : 1.0
2015-10-17 21:51:30,761 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0005_m_000009_1
2015-10-17 21:51:30,762 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000009_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:51:30,762 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000017 taskAttempt attempt_1445087491445_0005_m_000009_1
2015-10-17 21:51:30,763 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000009_1
2015-10-17 21:51:30,763 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:51:30,786 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000009_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:51:30,787 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0005_m_000009_1
2015-10-17 21:51:30,787 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445087491445_0005_m_000009_2
2015-10-17 21:51:30,788 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:51:30,788 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-17 21:51:30,789 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000009_2 TaskAttempt Transitioned from UNASSIGNED to KILLED
2015-10-17 21:51:30,789 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Processing the event EventType: CONTAINER_DEALLOCATE
2015-10-17 21:51:30,895 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 14 maxEvents 10000
2015-10-17 21:51:31,114 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:25 ContRel:0 HostLocal:11 RackLocal:12
2015-10-17 21:51:31,117 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:6144, vCores:-14> knownNMs=3
2015-10-17 21:51:31,245 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.7441697
2015-10-17 21:51:31,773 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.12535502
2015-10-17 21:51:31,895 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 15 maxEvents 10000
2015-10-17 21:51:31,957 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.13101514
2015-10-17 21:51:32,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000017
2015-10-17 21:51:32,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:51:32,123 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000009_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:51:32,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000027 to attempt_1445087491445_0005_m_000008_2
2015-10-17 21:51:32,124 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:26 ContRel:0 HostLocal:12 RackLocal:12
2015-10-17 21:51:32,124 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:32,125 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000008_2 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:51:32,125 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000027 taskAttempt attempt_1445087491445_0005_m_000008_2
2015-10-17 21:51:32,125 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000008_2
2015-10-17 21:51:32,126 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:51:32,142 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000008_2 : 13562
2015-10-17 21:51:32,143 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000008_2] using containerId: [container_1445087491445_0005_01_000027 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 21:51:32,143 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000008_2 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:51:32,143 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000008
2015-10-17 21:51:32,769 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.8875129
2015-10-17 21:51:32,849 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.5672582
2015-10-17 21:51:32,895 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 15 maxEvents 10000
2015-10-17 21:51:33,066 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.15384616
2015-10-17 21:51:33,126 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-12> knownNMs=2
2015-10-17 21:51:33,724 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.667
2015-10-17 21:51:33,754 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.4556311
2015-10-17 21:51:33,895 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 15 maxEvents 10000
2015-10-17 21:51:34,130 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:51:34,130 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:34,131 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0005_01_000028 to attempt_1445087491445_0005_m_000012_2
2015-10-17 21:51:34,131 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:27 ContRel:0 HostLocal:12 RackLocal:13
2015-10-17 21:51:34,131 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:34,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000012_2 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:51:34,133 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0005_01_000028 taskAttempt attempt_1445087491445_0005_m_000012_2
2015-10-17 21:51:34,133 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0005_m_000012_2
2015-10-17 21:51:34,133 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:51:34,150 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0005_m_000012_2 : 13562
2015-10-17 21:51:34,151 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0005_m_000012_2] using containerId: [container_1445087491445_0005_01_000028 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:51:34,151 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000012_2 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:51:34,152 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0005_m_000012
2015-10-17 21:51:34,273 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.77857196
2015-10-17 21:51:34,300 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_2 is : 0.13104554
2015-10-17 21:51:34,339 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:51:34,354 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000027 asked for a task
2015-10-17 21:51:34,355 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000027 given task: attempt_1445087491445_0005_m_000008_2
2015-10-17 21:51:34,781 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.13101514
2015-10-17 21:51:34,898 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 15 maxEvents 10000
2015-10-17 21:51:34,978 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.13101514
2015-10-17 21:51:35,133 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-13> knownNMs=2
2015-10-17 21:51:35,798 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.9254525
2015-10-17 21:51:35,897 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 15 maxEvents 10000
2015-10-17 21:51:36,082 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.20512822
2015-10-17 21:51:36,753 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.667
2015-10-17 21:51:36,783 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.5638554
2015-10-17 21:51:36,797 INFO [Socket Reader #1 for port 32070] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0005 (auth:SIMPLE)
2015-10-17 21:51:36,813 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0005_m_000028 asked for a task
2015-10-17 21:51:36,813 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0005_m_000028 given task: attempt_1445087491445_0005_m_000012_2
2015-10-17 21:51:36,900 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 15 maxEvents 10000
2015-10-17 21:51:37,305 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.816596
2015-10-17 21:51:37,316 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_2 is : 0.13104554
2015-10-17 21:51:37,559 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_2 is : 0.13102981
2015-10-17 21:51:37,785 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.13101514
2015-10-17 21:51:37,898 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 15 maxEvents 10000
2015-10-17 21:51:37,985 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.13101514
2015-10-17 21:51:38,819 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.95949745
2015-10-17 21:51:38,897 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 15 maxEvents 10000
2015-10-17 21:51:39,096 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.20512822
2015-10-17 21:51:39,783 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.667
2015-10-17 21:51:39,812 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.5638554
2015-10-17 21:51:39,898 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 15 maxEvents 10000
2015-10-17 21:51:40,319 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_2 is : 0.13104554
2015-10-17 21:51:40,336 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.84856087
2015-10-17 21:51:40,579 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_2 is : 0.13102981
2015-10-17 21:51:40,790 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.13101514
2015-10-17 21:51:40,897 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 15 maxEvents 10000
2015-10-17 21:51:40,994 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.13101514
2015-10-17 21:51:41,622 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_2 is : 0.13101445
2015-10-17 21:51:41,849 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 0.9931501
2015-10-17 21:51:41,899 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 15 maxEvents 10000
2015-10-17 21:51:42,098 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.20512822
2015-10-17 21:51:42,820 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.6708218
2015-10-17 21:51:42,847 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.5638554
2015-10-17 21:51:42,875 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000006_1 is : 1.0
2015-10-17 21:51:42,877 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0005_m_000006_1
2015-10-17 21:51:42,878 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000006_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:51:42,878 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000018 taskAttempt attempt_1445087491445_0005_m_000006_1
2015-10-17 21:51:42,879 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000006_1
2015-10-17 21:51:42,879 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:51:42,898 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000006_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:51:42,899 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 15 maxEvents 10000
2015-10-17 21:51:42,899 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0005_m_000006_1
2015-10-17 21:51:42,899 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445087491445_0005_m_000006_2
2015-10-17 21:51:42,900 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:51:42,900 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-17 21:51:42,901 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000006_2 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:51:42,901 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000026 taskAttempt attempt_1445087491445_0005_m_000006_2
2015-10-17 21:51:42,902 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000006_2
2015-10-17 21:51:42,902 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:51:42,917 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000006_2 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:51:42,918 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:51:42,926 WARN [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out2/_temporary/1/_temporary/attempt_1445087491445_0005_m_000006_2
2015-10-17 21:51:42,927 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000006_2 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:51:42,950 INFO [Socket Reader #1 for port 32070] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 32070: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:51:43,143 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:27 ContRel:0 HostLocal:12 RackLocal:13
2015-10-17 21:51:43,337 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_2 is : 0.13104554
2015-10-17 21:51:43,365 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.88182545
2015-10-17 21:51:43,807 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.13101514
2015-10-17 21:51:43,899 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 15 maxEvents 10000
2015-10-17 21:51:44,005 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.14427449
2015-10-17 21:51:44,147 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000026
2015-10-17 21:51:44,147 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000018
2015-10-17 21:51:44,147 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000006_2: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:51:44,147 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:27 ContRel:0 HostLocal:12 RackLocal:13
2015-10-17 21:51:44,148 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000006_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:51:44,552 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_2 is : 0.13101453
2015-10-17 21:51:44,649 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_2 is : 0.13101445
2015-10-17 21:51:44,898 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 16 maxEvents 10000
2015-10-17 21:51:45,097 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.20512822
2015-10-17 21:51:45,593 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.5638554
2015-10-17 21:51:45,843 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.7056344
2015-10-17 21:51:45,876 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.667
2015-10-17 21:51:45,898 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 16 maxEvents 10000
2015-10-17 21:51:46,353 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_2 is : 0.13104554
2015-10-17 21:51:46,386 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.9182548
2015-10-17 21:51:46,815 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.13101514
2015-10-17 21:51:46,898 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 16 maxEvents 10000
2015-10-17 21:51:47,028 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.18433253
2015-10-17 21:51:47,566 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_2 is : 0.13101453
2015-10-17 21:51:47,680 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_2 is : 0.20979296
2015-10-17 21:51:47,899 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 16 maxEvents 10000
2015-10-17 21:51:48,103 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.23076925
2015-10-17 21:51:48,876 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.7455936
2015-10-17 21:51:48,900 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 16 maxEvents 10000
2015-10-17 21:51:48,912 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.667
2015-10-17 21:51:49,413 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.9582379
2015-10-17 21:51:49,899 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 16 maxEvents 10000
2015-10-17 21:51:50,033 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.2313878
2015-10-17 21:51:50,566 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_2 is : 0.13101453
2015-10-17 21:51:50,700 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_2 is : 0.2391939
2015-10-17 21:51:50,898 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 16 maxEvents 10000
2015-10-17 21:51:51,119 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.23076925
2015-10-17 21:51:51,897 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.78810954
2015-10-17 21:51:51,898 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 16 maxEvents 10000
2015-10-17 21:51:51,930 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.667
2015-10-17 21:51:52,438 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 0.9983794
2015-10-17 21:51:52,899 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 16 maxEvents 10000
2015-10-17 21:51:53,034 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.2392493
2015-10-17 21:51:53,324 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000008_1 is : 1.0
2015-10-17 21:51:53,326 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0005_m_000008_1
2015-10-17 21:51:53,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000008_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:51:53,326 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000019 taskAttempt attempt_1445087491445_0005_m_000008_1
2015-10-17 21:51:53,327 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000008_1
2015-10-17 21:51:53,327 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:51:53,337 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000008_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:51:53,337 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0005_m_000008_1
2015-10-17 21:51:53,337 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445087491445_0005_m_000008_2
2015-10-17 21:51:53,338 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:51:53,338 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-17 21:51:53,338 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000008_2 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:51:53,338 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000027 taskAttempt attempt_1445087491445_0005_m_000008_2
2015-10-17 21:51:53,339 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000008_2
2015-10-17 21:51:53,339 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:51:53,346 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000008_2 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:51:53,346 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:51:53,348 WARN [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out2/_temporary/1/_temporary/attempt_1445087491445_0005_m_000008_2
2015-10-17 21:51:53,348 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000008_2 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:51:53,458 INFO [Socket Reader #1 for port 32070] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 32070: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:51:53,874 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_2 is : 0.23855053
2015-10-17 21:51:53,900 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 16 maxEvents 10000
2015-10-17 21:51:54,130 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.23076925
2015-10-17 21:51:54,167 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:27 ContRel:0 HostLocal:12 RackLocal:13
2015-10-17 21:51:54,900 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:51:54,932 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.81263745
2015-10-17 21:51:54,948 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.667
2015-10-17 21:51:55,169 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000019
2015-10-17 21:51:55,169 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000027
2015-10-17 21:51:55,169 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000008_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:51:55,169 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:27 ContRel:0 HostLocal:12 RackLocal:13
2015-10-17 21:51:55,169 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000008_2: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:51:55,902 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:51:56,036 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.2392493
2015-10-17 21:51:56,878 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_2 is : 0.23922452
2015-10-17 21:51:56,900 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:51:57,129 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.25641027
2015-10-17 21:51:57,899 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:51:57,950 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.8476042
2015-10-17 21:51:57,964 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.67505026
2015-10-17 21:51:58,816 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.18400608
2015-10-17 21:51:58,899 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:51:59,035 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.2492724
2015-10-17 21:51:59,879 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_2 is : 0.23922452
2015-10-17 21:51:59,899 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:52:00,128 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.25641027
2015-10-17 21:52:00,899 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:52:00,967 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.8889607
2015-10-17 21:52:00,980 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.71284974
2015-10-17 21:52:01,363 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_2 is : 0.23922384
2015-10-17 21:52:01,825 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.20745635
2015-10-17 21:52:01,899 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:52:02,035 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.27194306
2015-10-17 21:52:02,885 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_2 is : 0.28368443
2015-10-17 21:52:02,899 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:52:03,130 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.25641027
2015-10-17 21:52:03,902 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:52:04,001 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.9104411
2015-10-17 21:52:04,014 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.73469955
2015-10-17 21:52:04,477 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_2 is : 0.23922384
2015-10-17 21:52:04,834 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.23605582
2015-10-17 21:52:04,902 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:52:05,043 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.2987181
2015-10-17 21:52:05,895 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_2 is : 0.34740257
2015-10-17 21:52:05,902 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:52:06,147 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.25641027
2015-10-17 21:52:06,901 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:52:07,027 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.9402774
2015-10-17 21:52:07,038 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.7655215
2015-10-17 21:52:07,489 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_2 is : 0.3474171
2015-10-17 21:52:07,851 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.2392493
2015-10-17 21:52:07,901 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:52:08,052 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.34278914
2015-10-17 21:52:08,894 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_2 is : 0.34740257
2015-10-17 21:52:08,900 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:52:09,161 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.25641027
2015-10-17 21:52:09,901 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:52:10,055 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 0.98040456
2015-10-17 21:52:10,063 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.8066009
2015-10-17 21:52:10,492 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_2 is : 0.3474171
2015-10-17 21:52:10,862 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.2392493
2015-10-17 21:52:10,899 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:52:11,050 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.34746152
2015-10-17 21:52:11,900 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_2 is : 0.34740257
2015-10-17 21:52:11,900 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:52:12,159 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.25641027
2015-10-17 21:52:12,899 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:52:13,084 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 1.0
2015-10-17 21:52:13,089 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.8268097
2015-10-17 21:52:13,502 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_2 is : 0.3474171
2015-10-17 21:52:13,870 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.24200663
2015-10-17 21:52:13,899 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:52:14,051 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.34746152
2015-10-17 21:52:14,899 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:52:14,918 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_2 is : 0.40908936
2015-10-17 21:52:15,167 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.25641027
2015-10-17 21:52:15,900 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:52:16,123 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 1.0
2015-10-17 21:52:16,128 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.84382147
2015-10-17 21:52:16,509 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_2 is : 0.455341
2015-10-17 21:52:16,884 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.26510262
2015-10-17 21:52:16,900 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:52:17,061 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000007_1 is : 1.0
2015-10-17 21:52:17,063 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0005_m_000007_1
2015-10-17 21:52:17,064 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000007_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:52:17,064 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000020 taskAttempt attempt_1445087491445_0005_m_000007_1
2015-10-17 21:52:17,065 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000007_1
2015-10-17 21:52:17,065 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:52:17,070 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.39309585
2015-10-17 21:52:17,083 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000007_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:52:17,083 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0005_m_000007_1
2015-10-17 21:52:17,083 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445087491445_0005_m_000007_2
2015-10-17 21:52:17,084 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:52:17,084 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-17 21:52:17,085 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000007_2 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:52:17,085 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000025 taskAttempt attempt_1445087491445_0005_m_000007_2
2015-10-17 21:52:17,085 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000007_2
2015-10-17 21:52:17,087 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:52:17,103 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000007_2 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:52:17,103 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:52:17,111 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out2/_temporary/1/_temporary/attempt_1445087491445_0005_m_000007_2
2015-10-17 21:52:17,112 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000007_2 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:52:17,137 INFO [Socket Reader #1 for port 32070] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 32070: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:52:17,196 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:11 CompletedReds:0 ContAlloc:27 ContRel:0 HostLocal:12 RackLocal:13
2015-10-17 21:52:17,900 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 17 maxEvents 10000
2015-10-17 21:52:17,935 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_2 is : 0.4556311
2015-10-17 21:52:18,200 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000025
2015-10-17 21:52:18,200 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000020
2015-10-17 21:52:18,200 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000007_2: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:52:18,200 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:11 CompletedReds:0 ContAlloc:27 ContRel:0 HostLocal:12 RackLocal:13
2015-10-17 21:52:18,201 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000007_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:52:18,803 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.25641027
2015-10-17 21:52:18,900 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 18 maxEvents 10000
2015-10-17 21:52:19,155 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.87400746
2015-10-17 21:52:19,900 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 18 maxEvents 10000
2015-10-17 21:52:19,902 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.28627214
2015-10-17 21:52:20,090 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.43553683
2015-10-17 21:52:20,900 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 18 maxEvents 10000
2015-10-17 21:52:20,962 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_2 is : 0.4556311
2015-10-17 21:52:21,804 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.2820513
2015-10-17 21:52:21,901 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 18 maxEvents 10000
2015-10-17 21:52:22,188 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.9043131
2015-10-17 21:52:22,900 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 18 maxEvents 10000
2015-10-17 21:52:22,912 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.30711523
2015-10-17 21:52:23,098 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.4556214
2015-10-17 21:52:23,900 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 18 maxEvents 10000
2015-10-17 21:52:23,975 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_2 is : 0.4556311
2015-10-17 21:52:24,817 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.2820513
2015-10-17 21:52:24,900 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 18 maxEvents 10000
2015-10-17 21:52:25,226 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.92561626
2015-10-17 21:52:25,900 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 18 maxEvents 10000
2015-10-17 21:52:25,911 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.32959095
2015-10-17 21:52:26,098 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.4556214
2015-10-17 21:52:26,900 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 18 maxEvents 10000
2015-10-17 21:52:26,991 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_2 is : 0.5145977
2015-10-17 21:52:27,817 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.2820513
2015-10-17 21:52:27,900 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 18 maxEvents 10000
2015-10-17 21:52:28,265 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.94487935
2015-10-17 21:52:28,901 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 18 maxEvents 10000
2015-10-17 21:52:28,912 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.34746152
2015-10-17 21:52:29,100 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.46279514
2015-10-17 21:52:29,901 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 18 maxEvents 10000
2015-10-17 21:52:30,012 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_2 is : 0.5638554
2015-10-17 21:52:30,817 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.2820513
2015-10-17 21:52:30,901 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 18 maxEvents 10000
2015-10-17 21:52:31,309 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.96225
2015-10-17 21:52:31,901 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 18 maxEvents 10000
2015-10-17 21:52:31,919 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.34746152
2015-10-17 21:52:32,101 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.50155
2015-10-17 21:52:32,901 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 18 maxEvents 10000
2015-10-17 21:52:33,021 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_2 is : 0.5638554
2015-10-17 21:52:33,824 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.2820513
2015-10-17 21:52:33,901 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 18 maxEvents 10000
2015-10-17 21:52:34,341 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 0.98713225
2015-10-17 21:52:34,901 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 18 maxEvents 10000
2015-10-17 21:52:34,927 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.3523846
2015-10-17 21:52:35,106 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.5422601
2015-10-17 21:52:35,584 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000012_1 is : 1.0
2015-10-17 21:52:35,586 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0005_m_000012_1
2015-10-17 21:52:35,587 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000012_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:52:35,587 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000021 taskAttempt attempt_1445087491445_0005_m_000012_1
2015-10-17 21:52:35,588 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000012_1
2015-10-17 21:52:35,589 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:52:35,609 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000012_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:52:35,610 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0005_m_000012_1
2015-10-17 21:52:35,610 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445087491445_0005_m_000012_2
2015-10-17 21:52:35,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000012 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:52:35,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 12
2015-10-17 21:52:35,612 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000012_2 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:52:35,612 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000028 taskAttempt attempt_1445087491445_0005_m_000012_2
2015-10-17 21:52:35,613 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000012_2
2015-10-17 21:52:35,613 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:52:35,628 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000012_2 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:52:35,628 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:52:35,638 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out2/_temporary/1/_temporary/attempt_1445087491445_0005_m_000012_2
2015-10-17 21:52:35,639 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000012_2 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:52:35,654 INFO [Socket Reader #1 for port 32070] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 32070: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:52:35,901 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 18 maxEvents 10000
2015-10-17 21:52:36,230 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:12 CompletedReds:0 ContAlloc:27 ContRel:0 HostLocal:12 RackLocal:13
2015-10-17 21:52:36,833 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.2820513
2015-10-17 21:52:36,900 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:37,233 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000028
2015-10-17 21:52:37,234 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000021
2015-10-17 21:52:37,234 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000012_2: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:52:37,234 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:12 CompletedReds:0 ContAlloc:27 ContRel:0 HostLocal:12 RackLocal:13
2015-10-17 21:52:37,234 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000012_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:52:37,901 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:37,926 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.3803966
2015-10-17 21:52:38,114 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.56380975
2015-10-17 21:52:38,900 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:39,843 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.30769232
2015-10-17 21:52:39,901 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:40,902 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:40,930 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.41133565
2015-10-17 21:52:41,123 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.56380975
2015-10-17 21:52:41,901 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:42,852 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.30769232
2015-10-17 21:52:42,902 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:43,902 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:43,931 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.43901786
2015-10-17 21:52:44,139 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.5737852
2015-10-17 21:52:44,901 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:45,865 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.30769232
2015-10-17 21:52:45,901 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:46,901 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:46,951 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.4556214
2015-10-17 21:52:47,148 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.62042683
2015-10-17 21:52:47,901 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:48,873 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.30769232
2015-10-17 21:52:48,901 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:49,753 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.62042683
2015-10-17 21:52:49,901 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:49,959 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.4556214
2015-10-17 21:52:50,148 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.667
2015-10-17 21:52:50,901 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:51,889 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.30769232
2015-10-17 21:52:51,901 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:52,902 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:52,962 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.4569312
2015-10-17 21:52:53,155 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.667
2015-10-17 21:52:53,902 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:54,899 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.30769232
2015-10-17 21:52:54,902 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:55,902 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:55,967 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.4592092
2015-10-17 21:52:56,162 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.667
2015-10-17 21:52:56,902 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:57,903 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:57,921 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.30769232
2015-10-17 21:52:58,902 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:52:58,976 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.4667034
2015-10-17 21:52:59,171 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.68021524
2015-10-17 21:52:59,902 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:00,902 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:00,932 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.30769232
2015-10-17 21:53:01,902 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:01,984 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.49079993
2015-10-17 21:53:02,186 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.7130046
2015-10-17 21:53:02,902 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:03,902 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:03,951 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.30769232
2015-10-17 21:53:04,903 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:04,998 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.5142518
2015-10-17 21:53:05,203 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.74678195
2015-10-17 21:53:05,903 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:06,903 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:06,967 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.30769232
2015-10-17 21:53:07,902 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:08,015 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.54193485
2015-10-17 21:53:08,211 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.78200066
2015-10-17 21:53:08,902 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:09,902 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:09,982 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.30769232
2015-10-17 21:53:10,903 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:11,035 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.56380975
2015-10-17 21:53:11,219 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.81939995
2015-10-17 21:53:11,902 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:12,902 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:12,995 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.30769232
2015-10-17 21:53:13,903 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:14,039 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.56380975
2015-10-17 21:53:14,227 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.85139865
2015-10-17 21:53:14,903 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:15,903 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:16,006 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.30769232
2015-10-17 21:53:16,903 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:17,045 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.56380975
2015-10-17 21:53:17,227 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.8996417
2015-10-17 21:53:17,903 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:18,903 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:19,017 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.30769232
2015-10-17 21:53:19,903 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:20,062 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.61776674
2015-10-17 21:53:20,227 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.932106
2015-10-17 21:53:20,904 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:21,903 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:21,963 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.61776674
2015-10-17 21:53:22,025 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.30769232
2015-10-17 21:53:22,903 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:23,072 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.667
2015-10-17 21:53:23,233 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 0.9680549
2015-10-17 21:53:23,903 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:24,903 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:25,043 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.30769232
2015-10-17 21:53:25,903 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:26,085 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_2 is : 0.667
2015-10-17 21:53:26,199 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_m_000011_3 is : 1.0
2015-10-17 21:53:26,201 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0005_m_000011_3
2015-10-17 21:53:26,202 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_3 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:53:26,202 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000024 taskAttempt attempt_1445087491445_0005_m_000011_3
2015-10-17 21:53:26,203 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000011_3
2015-10-17 21:53:26,204 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:53:26,220 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_3 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:53:26,220 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0005_m_000011_3
2015-10-17 21:53:26,221 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445087491445_0005_m_000011_2
2015-10-17 21:53:26,221 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_m_000011 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:53:26,222 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 13
2015-10-17 21:53:26,222 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_2 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:53:26,222 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000023 taskAttempt attempt_1445087491445_0005_m_000011_2
2015-10-17 21:53:26,223 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_m_000011_2
2015-10-17 21:53:26,223 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 21:53:26,237 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_2 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:53:26,237 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:53:26,240 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out2/_temporary/1/_temporary/attempt_1445087491445_0005_m_000011_2
2015-10-17 21:53:26,241 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_m_000011_2 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:53:26,298 INFO [Socket Reader #1 for port 32070] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 32070: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:53:26,315 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:13 CompletedReds:0 ContAlloc:27 ContRel:0 HostLocal:12 RackLocal:13
2015-10-17 21:53:26,903 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 19 maxEvents 10000
2015-10-17 21:53:27,320 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000023
2015-10-17 21:53:27,320 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0005_01_000024
2015-10-17 21:53:27,320 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000011_2: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:53:27,321 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:13 CompletedReds:0 ContAlloc:27 ContRel:0 HostLocal:12 RackLocal:13
2015-10-17 21:53:27,321 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0005_m_000011_3: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:53:27,903 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0005_r_000000_1. startIndex 20 maxEvents 10000
2015-10-17 21:53:28,057 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.30769232
2015-10-17 21:53:28,568 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.30769232
2015-10-17 21:53:31,061 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.345256
2015-10-17 21:53:34,077 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.35754353
2015-10-17 21:53:37,096 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.3698946
2015-10-17 21:53:40,110 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.38126525
2015-10-17 21:53:43,122 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.3933237
2015-10-17 21:53:46,143 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.4098779
2015-10-17 21:53:49,157 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.42201963
2015-10-17 21:53:52,173 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.43346977
2015-10-17 21:53:55,195 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.44631818
2015-10-17 21:53:58,198 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.4607639
2015-10-17 21:54:01,205 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.4729471
2015-10-17 21:54:04,221 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.48615146
2015-10-17 21:54:07,237 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.4974948
2015-10-17 21:54:10,252 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.5090921
2015-10-17 21:54:13,268 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.5202243
2015-10-17 21:54:16,280 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.5375776
2015-10-17 21:54:19,299 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.55169684
2015-10-17 21:54:22,311 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.56572306
2015-10-17 21:54:25,331 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.57672894
2015-10-17 21:54:28,348 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.5888653
2015-10-17 21:54:31,363 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.60200906
2015-10-17 21:54:34,371 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.61772954
2015-10-17 21:54:37,370 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.6336707
2015-10-17 21:54:40,378 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.65369284
2015-10-17 21:54:43,390 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.6659689
2015-10-17 21:54:43,745 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.6659689
2015-10-17 21:54:46,411 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.6684697
2015-10-17 21:54:49,419 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.67079854
2015-10-17 21:54:52,427 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.67326474
2015-10-17 21:54:55,438 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.6756341
2015-10-17 21:54:58,456 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.678063
2015-10-17 21:55:01,471 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.68069345
2015-10-17 21:55:04,489 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.6830432
2015-10-17 21:55:07,500 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.6855936
2015-10-17 21:55:10,522 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.68827575
2015-10-17 21:55:13,539 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.6907632
2015-10-17 21:55:16,553 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.6929231
2015-10-17 21:55:19,569 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.69543386
2015-10-17 21:55:22,583 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.6980331
2015-10-17 21:55:25,596 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.7004767
2015-10-17 21:55:28,616 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.70310783
2015-10-17 21:55:31,623 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.7058712
2015-10-17 21:55:34,631 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.70853364
2015-10-17 21:55:37,639 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.7109195
2015-10-17 21:55:40,647 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.7133057
2015-10-17 21:55:43,655 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.7166238
2015-10-17 21:55:46,664 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.7198032
2015-10-17 21:55:49,676 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.7228738
2015-10-17 21:55:52,695 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.72490865
2015-10-17 21:55:55,707 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.72715086
2015-10-17 21:55:58,727 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.7295435
2015-10-17 21:56:01,741 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.7322575
2015-10-17 21:56:04,756 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.73482656
2015-10-17 21:56:07,774 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.7379296
2015-10-17 21:56:10,790 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.7409839
2015-10-17 21:56:13,798 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.7436242
2015-10-17 21:56:16,806 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.74574304
2015-10-17 21:56:19,813 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.7489595
2015-10-17 21:56:22,820 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.7511047
2015-10-17 21:56:25,835 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.75330657
2015-10-17 21:56:28,852 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.7561643
2015-10-17 21:56:31,861 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.75853956
2015-10-17 21:56:34,873 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.7609134
2015-10-17 21:56:37,883 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.7631182
2015-10-17 21:56:40,900 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.76566416
2015-10-17 21:56:43,908 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.76838505
2015-10-17 21:56:46,913 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.7705358
2015-10-17 21:56:49,923 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.77373075
2015-10-17 21:56:52,924 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.77735484
2015-10-17 21:56:55,928 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.78025085
2015-10-17 21:56:58,948 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.78296554
2015-10-17 21:57:01,957 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.7857548
2015-10-17 21:57:04,981 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.78847754
2015-10-17 21:57:07,993 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.79080606
2015-10-17 21:57:11,009 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.7930711
2015-10-17 21:57:14,026 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.7953216
2015-10-17 21:57:17,042 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.79774725
2015-10-17 21:57:20,058 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.799845
2015-10-17 21:57:23,073 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.8027973
2015-10-17 21:57:26,085 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.8055196
2015-10-17 21:57:29,103 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.80804443
2015-10-17 21:57:32,120 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.8106002
2015-10-17 21:57:35,135 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.8133286
2015-10-17 21:57:38,153 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.8157587
2015-10-17 21:57:41,167 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.8180473
2015-10-17 21:57:44,187 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.82017446
2015-10-17 21:57:47,200 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.8221026
2015-10-17 21:57:50,216 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.8243828
2015-10-17 21:57:53,230 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.8270898
2015-10-17 21:57:56,238 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.83080536
2015-10-17 21:57:59,248 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.83335114
2015-10-17 21:58:02,262 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.83542705
2015-10-17 21:58:05,279 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.83770275
2015-10-17 21:58:08,294 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.8396963
2015-10-17 21:58:11,309 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.8420375
2015-10-17 21:58:14,321 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.84421325
2015-10-17 21:58:17,343 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.84631586
2015-10-17 21:58:20,356 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.84849095
2015-10-17 21:58:23,372 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.8507379
2015-10-17 21:58:26,390 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.85307574
2015-10-17 21:58:29,404 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.8553747
2015-10-17 21:58:32,413 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.85778373
2015-10-17 21:58:35,420 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.8603379
2015-10-17 21:58:38,433 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.86279464
2015-10-17 21:58:41,451 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.86500514
2015-10-17 21:58:44,465 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.8676002
2015-10-17 21:58:47,483 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.86989737
2015-10-17 21:58:50,499 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.87194604
2015-10-17 21:58:53,508 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.8741103
2015-10-17 21:58:56,529 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.87628126
2015-10-17 21:58:59,537 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.8788414
2015-10-17 21:59:02,544 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.8817526
2015-10-17 21:59:05,555 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.88437515
2015-10-17 21:59:08,567 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.8863615
2015-10-17 21:59:11,576 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.8884908
2015-10-17 21:59:14,598 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.89101315
2015-10-17 21:59:17,611 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.8933078
2015-10-17 21:59:20,623 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.89646274
2015-10-17 21:59:23,641 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.89856744
2015-10-17 21:59:26,657 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.90126264
2015-10-17 21:59:29,677 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.9036775
2015-10-17 21:59:32,684 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.905637
2015-10-17 21:59:35,703 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.9077408
2015-10-17 21:59:38,716 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.9102632
2015-10-17 21:59:41,735 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.91297376
2015-10-17 21:59:44,751 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.9150733
2015-10-17 21:59:47,764 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.9175357
2015-10-17 21:59:50,777 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.9198362
2015-10-17 21:59:53,782 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.9226535
2015-10-17 21:59:56,790 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.92572635
2015-10-17 21:59:59,796 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.92784995
2015-10-17 22:00:02,812 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.92991763
2015-10-17 22:00:05,829 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.9325303
2015-10-17 22:00:08,837 INFO [IPC Server handler 15 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.935822
2015-10-17 22:00:11,845 INFO [IPC Server handler 0 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.9382085
2015-10-17 22:00:14,855 INFO [IPC Server handler 28 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.9404944
2015-10-17 22:00:17,862 INFO [IPC Server handler 19 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.94334996
2015-10-17 22:00:20,878 INFO [IPC Server handler 8 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.94563043
2015-10-17 22:00:23,893 INFO [IPC Server handler 22 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.94846815
2015-10-17 22:00:26,901 INFO [IPC Server handler 3 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.9509013
2015-10-17 22:00:29,909 INFO [IPC Server handler 24 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.9529029
2015-10-17 22:00:32,918 INFO [IPC Server handler 11 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.95553076
2015-10-17 22:00:35,923 INFO [IPC Server handler 4 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.9579336
2015-10-17 22:00:38,941 INFO [IPC Server handler 7 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.9599835
2015-10-17 22:00:41,957 INFO [IPC Server handler 16 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.9626934
2015-10-17 22:00:44,964 INFO [IPC Server handler 9 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.96582496
2015-10-17 22:00:47,972 INFO [IPC Server handler 27 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.9680419
2015-10-17 22:00:50,984 INFO [IPC Server handler 12 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.9707927
2015-10-17 22:00:54,002 INFO [IPC Server handler 14 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.97401166
2015-10-17 22:00:57,018 INFO [IPC Server handler 23 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.97670907
2015-10-17 22:01:00,034 INFO [IPC Server handler 25 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.979017
2015-10-17 22:01:03,052 INFO [IPC Server handler 20 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.9820512
2015-10-17 22:01:06,066 INFO [IPC Server handler 17 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.984402
2015-10-17 22:01:09,080 INFO [IPC Server handler 18 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.98666817
2015-10-17 22:01:12,095 INFO [IPC Server handler 6 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.9897101
2015-10-17 22:01:15,113 INFO [IPC Server handler 21 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.992361
2015-10-17 22:01:18,128 INFO [IPC Server handler 26 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.9944025
2015-10-17 22:01:21,139 INFO [IPC Server handler 29 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.99681354
2015-10-17 22:01:24,144 INFO [IPC Server handler 10 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 0.99937725
2015-10-17 22:01:25,444 INFO [IPC Server handler 13 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445087491445_0005_r_000000_1
2015-10-17 22:01:25,444 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_r_000000_1 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-17 22:01:25,445 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445087491445_0005_r_000000_1 given a go for committing the task output.
2015-10-17 22:01:25,447 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445087491445_0005_r_000000_1
2015-10-17 22:01:25,447 INFO [IPC Server handler 1 on 32070] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445087491445_0005_r_000000_1:true
2015-10-17 22:01:25,480 INFO [IPC Server handler 5 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0005_r_000000_1 is : 1.0
2015-10-17 22:01:25,483 INFO [IPC Server handler 2 on 32070] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0005_r_000000_1
2015-10-17 22:01:25,483 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_r_000000_1 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:01:25,484 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0005_01_000022 taskAttempt attempt_1445087491445_0005_r_000000_1
2015-10-17 22:01:25,484 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0005_r_000000_1
2015-10-17 22:01:25,486 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 22:01:25,503 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0005_r_000000_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:01:25,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0005_r_000000_1
2015-10-17 22:01:25,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0005_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:01:25,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 14
2015-10-17 22:01:25,506 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0005Job Transitioned from RUNNING to COMMITTING
2015-10-17 22:01:25,507 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-17 22:01:25,576 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-17 22:01:25,578 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0005Job Transitioned from COMMITTING to SUCCEEDED
2015-10-17 22:01:25,580 INFO [Thread-147] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-17 22:01:25,580 INFO [Thread-147] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-17 22:01:25,580 INFO [Thread-147] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-17 22:01:25,580 INFO [Thread-147] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-17 22:01:25,581 INFO [Thread-147] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-17 22:01:25,581 INFO [Thread-147] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-17 22:01:25,583 INFO [Thread-147] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-17 22:01:25,708 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0005/job_1445087491445_0005_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445087491445_0005-1445088243632-msrabi-word+count-1445090485571-13-1-SUCCEEDED-default-1445089672614.jhist_tmp
2015-10-17 22:01:25,779 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445087491445_0005-1445088243632-msrabi-word+count-1445090485571-13-1-SUCCEEDED-default-1445089672614.jhist_tmp
2015-10-17 22:01:25,785 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0005/job_1445087491445_0005_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445087491445_0005_conf.xml_tmp
2015-10-17 22:01:25,854 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445087491445_0005_conf.xml_tmp
2015-10-17 22:01:25,861 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445087491445_0005.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445087491445_0005.summary
2015-10-17 22:01:25,865 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445087491445_0005_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445087491445_0005_conf.xml
2015-10-17 22:01:25,869 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445087491445_0005-1445088243632-msrabi-word+count-1445090485571-13-1-SUCCEEDED-default-1445089672614.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445087491445_0005-1445088243632-msrabi-word+count-1445090485571-13-1-SUCCEEDED-default-1445089672614.jhist
2015-10-17 22:01:25,871 INFO [Thread-147] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-17 22:01:25,877 INFO [Thread-147] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-17 22:01:25,879 INFO [Thread-147] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MSRA-SA-41.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445087491445_0005
2015-10-17 22:01:25,891 INFO [Thread-147] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-17 22:01:26,893 INFO [Thread-147] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:13 CompletedReds:0 ContAlloc:27 ContRel:0 HostLocal:12 RackLocal:13
2015-10-17 22:01:26,896 INFO [Thread-147] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0005
2015-10-17 22:01:26,903 INFO [Thread-147] org.apache.hadoop.ipc.Server: Stopping server on 32070
2015-10-17 22:01:26,906 INFO [IPC Server listener on 32070] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 32070
2015-10-17 22:01:26,907 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-17 22:01:26,908 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
