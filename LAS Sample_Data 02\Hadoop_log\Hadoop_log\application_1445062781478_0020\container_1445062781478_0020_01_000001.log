2015-10-17 17:15:17,514 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445062781478_0020_000001
2015-10-17 17:15:18,195 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 17:15:18,195 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 20 cluster_timestamp: 1445062781478 } attemptId: 1 } keyId: 471522253)
2015-10-17 17:15:18,462 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 17:15:19,664 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 17:15:19,775 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 17:15:19,834 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 17:15:19,836 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 17:15:19,839 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 17:15:19,841 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 17:15:19,842 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 17:15:19,856 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 17:15:19,857 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 17:15:19,859 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 17:15:19,943 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 17:15:19,989 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 17:15:20,034 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 17:15:20,053 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 17:15:20,127 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 17:15:20,340 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 17:15:20,386 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 17:15:20,386 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 17:15:20,393 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445062781478_0020 to jobTokenSecretManager
2015-10-17 17:15:20,522 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445062781478_0020 because: not enabled; too many maps; too much input;
2015-10-17 17:15:20,538 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445062781478_0020 = 1256521728. Number of splits = 10
2015-10-17 17:15:20,539 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445062781478_0020 = 1
2015-10-17 17:15:20,539 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0020Job Transitioned from NEW to INITED
2015-10-17 17:15:20,540 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445062781478_0020.
2015-10-17 17:15:20,570 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 17:15:20,579 INFO [Socket Reader #1 for port 20049] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 20049
2015-10-17 17:15:20,599 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 17:15:20,600 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 17:15:20,600 INFO [IPC Server listener on 20049] org.apache.hadoop.ipc.Server: IPC Server listener on 20049: starting
2015-10-17 17:15:20,601 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MSRA-SA-41.fareast.corp.microsoft.com/**************:20049
2015-10-17 17:15:20,690 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 17:15:20,693 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 17:15:20,703 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 17:15:20,707 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 17:15:20,707 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 17:15:20,710 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 17:15:20,710 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 17:15:20,719 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 20056
2015-10-17 17:15:20,719 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 17:15:20,748 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\2\Jetty_0_0_0_0_20056_mapreduce____cv7jk6\webapp
2015-10-17 17:15:20,885 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:20056
2015-10-17 17:15:20,885 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 20056
2015-10-17 17:15:21,188 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 17:15:21,191 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445062781478_0020
2015-10-17 17:15:21,192 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 17:15:21,195 INFO [Socket Reader #1 for port 20059] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 20059
2015-10-17 17:15:21,200 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 17:15:21,200 INFO [IPC Server listener on 20059] org.apache.hadoop.ipc.Server: IPC Server listener on 20059: starting
2015-10-17 17:15:21,218 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 17:15:21,218 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 17:15:21,218 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 17:15:21,263 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at MSRA-SA-41/**************:8030
2015-10-17 17:15:21,321 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 17:15:21,321 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 17:15:21,324 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 17:15:21,326 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 17:15:21,332 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0020Job Transitioned from INITED to SETUP
2015-10-17 17:15:21,333 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 17:15:21,341 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0020Job Transitioned from SETUP to RUNNING
2015-10-17 17:15:21,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:21,359 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:21,365 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:15:21,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:21,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:21,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:15:21,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:21,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:21,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:15:21,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:21,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:21,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:15:21,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:21,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:21,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:15:21,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:21,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:21,368 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:15:21,368 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:21,368 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:21,368 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:15:21,368 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:21,368 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:21,368 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:15:21,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:21,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:21,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:15:21,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:21,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:21,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:15:21,370 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 17:15:21,371 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:15:21,371 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:15:21,371 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:15:21,371 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:15:21,371 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:15:21,372 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:15:21,372 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:15:21,372 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:15:21,372 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:15:21,372 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:15:21,372 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:15:21,373 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 17:15:21,380 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 17:15:21,408 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445062781478_0020, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0020/job_1445062781478_0020_1.jhist
2015-10-17 17:15:22,323 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 17:15:22,449 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:23552, vCores:-4> knownNMs=4
2015-10-17 17:15:22,454 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:23552, vCores:-4>
2015-10-17 17:15:22,454 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 17:15:23,495 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 10
2015-10-17 17:15:23,498 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0020_01_000002 to attempt_1445062781478_0020_m_000000_0
2015-10-17 17:15:23,502 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0020_01_000003 to attempt_1445062781478_0020_m_000001_0
2015-10-17 17:15:23,502 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0020_01_000004 to attempt_1445062781478_0020_m_000002_0
2015-10-17 17:15:23,503 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0020_01_000005 to attempt_1445062781478_0020_m_000003_0
2015-10-17 17:15:23,503 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0020_01_000006 to attempt_1445062781478_0020_m_000004_0
2015-10-17 17:15:23,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0020_01_000007 to attempt_1445062781478_0020_m_000005_0
2015-10-17 17:15:23,504 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0020_01_000008 to attempt_1445062781478_0020_m_000006_0
2015-10-17 17:15:23,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0020_01_000009 to attempt_1445062781478_0020_m_000007_0
2015-10-17 17:15:23,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0020_01_000010 to attempt_1445062781478_0020_m_000008_0
2015-10-17 17:15:23,506 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0020_01_000011 to attempt_1445062781478_0020_m_000009_0
2015-10-17 17:15:23,506 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:13312, vCores:-14>
2015-10-17 17:15:23,506 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 17:15:23,506 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 17:15:23,602 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:23,635 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0020/job.jar
2015-10-17 17:15:23,641 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0020/job.xml
2015-10-17 17:15:23,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 17:15:23,644 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 17:15:23,644 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 17:15:23,744 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:15:23,751 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:23,752 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:15:23,753 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:23,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:15:23,755 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:23,756 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:15:23,756 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:23,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:15:23,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:23,759 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:15:23,760 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:23,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:15:23,762 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:23,763 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:15:23,764 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:23,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:15:23,766 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:15:23,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:15:23,770 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0020_01_000002 taskAttempt attempt_1445062781478_0020_m_000000_0
2015-10-17 17:15:23,771 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0020_01_000003 taskAttempt attempt_1445062781478_0020_m_000001_0
2015-10-17 17:15:23,771 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0020_01_000004 taskAttempt attempt_1445062781478_0020_m_000002_0
2015-10-17 17:15:23,771 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0020_01_000005 taskAttempt attempt_1445062781478_0020_m_000003_0
2015-10-17 17:15:23,772 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0020_01_000006 taskAttempt attempt_1445062781478_0020_m_000004_0
2015-10-17 17:15:23,772 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0020_01_000007 taskAttempt attempt_1445062781478_0020_m_000005_0
2015-10-17 17:15:23,772 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0020_01_000008 taskAttempt attempt_1445062781478_0020_m_000006_0
2015-10-17 17:15:23,773 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0020_01_000009 taskAttempt attempt_1445062781478_0020_m_000007_0
2015-10-17 17:15:23,773 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0020_01_000010 taskAttempt attempt_1445062781478_0020_m_000008_0
2015-10-17 17:15:23,773 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0020_01_000011 taskAttempt attempt_1445062781478_0020_m_000009_0
2015-10-17 17:15:23,776 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0020_m_000005_0
2015-10-17 17:15:23,776 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0020_m_000007_0
2015-10-17 17:15:23,777 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0020_m_000002_0
2015-10-17 17:15:23,777 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0020_m_000003_0
2015-10-17 17:15:23,777 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0020_m_000006_0
2015-10-17 17:15:23,777 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0020_m_000004_0
2015-10-17 17:15:23,776 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0020_m_000009_0
2015-10-17 17:15:23,776 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0020_m_000008_0
2015-10-17 17:15:23,776 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0020_m_000001_0
2015-10-17 17:15:23,776 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0020_m_000000_0
2015-10-17 17:15:23,778 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:15:23,823 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:15:23,825 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:15:23,827 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 17:15:23,829 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 17:15:23,831 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:15:23,832 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 17:15:23,834 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:15:23,837 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:15:23,839 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 17:15:23,938 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0020_m_000003_0 : 13562
2015-10-17 17:15:23,938 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0020_m_000004_0 : 13562
2015-10-17 17:15:23,938 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0020_m_000007_0 : 13562
2015-10-17 17:15:23,938 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0020_m_000002_0 : 13562
2015-10-17 17:15:23,938 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0020_m_000006_0 : 13562
2015-10-17 17:15:23,938 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0020_m_000005_0 : 13562
2015-10-17 17:15:23,938 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0020_m_000009_0 : 13562
2015-10-17 17:15:23,938 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0020_m_000001_0 : 13562
2015-10-17 17:15:23,938 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0020_m_000000_0 : 13562
2015-10-17 17:15:23,941 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0020_m_000008_0 : 13562
2015-10-17 17:15:23,942 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0020_m_000009_0] using containerId: [container_1445062781478_0020_01_000011 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 17:15:23,948 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:15:23,948 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0020_m_000003_0] using containerId: [container_1445062781478_0020_01_000005 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 17:15:23,949 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:15:23,949 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0020_m_000005_0] using containerId: [container_1445062781478_0020_01_000007 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 17:15:23,950 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:15:23,950 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0020_m_000001_0] using containerId: [container_1445062781478_0020_01_000003 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 17:15:23,951 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:15:23,951 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0020_m_000000_0] using containerId: [container_1445062781478_0020_01_000002 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 17:15:23,952 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:15:23,952 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0020_m_000006_0] using containerId: [container_1445062781478_0020_01_000008 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 17:15:23,953 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:15:23,953 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0020_m_000002_0] using containerId: [container_1445062781478_0020_01_000004 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 17:15:23,954 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:15:23,954 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0020_m_000004_0] using containerId: [container_1445062781478_0020_01_000006 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 17:15:23,955 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:15:23,955 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0020_m_000007_0] using containerId: [container_1445062781478_0020_01_000009 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 17:15:23,956 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:15:23,956 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0020_m_000008_0] using containerId: [container_1445062781478_0020_01_000010 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 17:15:23,957 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:15:23,957 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0020_m_000009
2015-10-17 17:15:23,957 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:15:23,958 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0020_m_000003
2015-10-17 17:15:23,958 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:15:23,958 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0020_m_000005
2015-10-17 17:15:23,959 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:15:23,959 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0020_m_000001
2015-10-17 17:15:23,959 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:15:23,960 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0020_m_000000
2015-10-17 17:15:23,960 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:15:23,960 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0020_m_000006
2015-10-17 17:15:23,961 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:15:23,961 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0020_m_000002
2015-10-17 17:15:23,961 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:15:23,962 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0020_m_000004
2015-10-17 17:15:23,962 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:15:23,962 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0020_m_000007
2015-10-17 17:15:23,962 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:15:23,963 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0020_m_000008
2015-10-17 17:15:23,963 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:15:24,508 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:13312, vCores:-14> knownNMs=4
2015-10-17 17:15:26,605 INFO [Socket Reader #1 for port 20059] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0020 (auth:SIMPLE)
2015-10-17 17:15:26,609 INFO [Socket Reader #1 for port 20059] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0020 (auth:SIMPLE)
2015-10-17 17:15:26,624 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0020_m_000011 asked for a task
2015-10-17 17:15:26,624 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0020_m_000008 asked for a task
2015-10-17 17:15:26,624 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0020_m_000008 given task: attempt_1445062781478_0020_m_000006_0
2015-10-17 17:15:26,624 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0020_m_000011 given task: attempt_1445062781478_0020_m_000009_0
2015-10-17 17:15:26,801 INFO [Socket Reader #1 for port 20059] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0020 (auth:SIMPLE)
2015-10-17 17:15:26,802 INFO [Socket Reader #1 for port 20059] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0020 (auth:SIMPLE)
2015-10-17 17:15:26,812 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0020_m_000010 asked for a task
2015-10-17 17:15:26,812 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0020_m_000010 given task: attempt_1445062781478_0020_m_000008_0
2015-10-17 17:15:26,813 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0020_m_000009 asked for a task
2015-10-17 17:15:26,813 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0020_m_000009 given task: attempt_1445062781478_0020_m_000007_0
2015-10-17 17:15:28,070 INFO [Socket Reader #1 for port 20059] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0020 (auth:SIMPLE)
2015-10-17 17:15:28,073 INFO [Socket Reader #1 for port 20059] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0020 (auth:SIMPLE)
2015-10-17 17:15:28,096 INFO [IPC Server handler 9 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0020_m_000005 asked for a task
2015-10-17 17:15:28,096 INFO [IPC Server handler 9 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0020_m_000005 given task: attempt_1445062781478_0020_m_000003_0
2015-10-17 17:15:28,100 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0020_m_000002 asked for a task
2015-10-17 17:15:28,100 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0020_m_000002 given task: attempt_1445062781478_0020_m_000000_0
2015-10-17 17:15:28,126 INFO [Socket Reader #1 for port 20059] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0020 (auth:SIMPLE)
2015-10-17 17:15:28,151 INFO [IPC Server handler 22 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0020_m_000004 asked for a task
2015-10-17 17:15:28,151 INFO [IPC Server handler 22 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0020_m_000004 given task: attempt_1445062781478_0020_m_000002_0
2015-10-17 17:15:28,183 INFO [Socket Reader #1 for port 20059] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0020 (auth:SIMPLE)
2015-10-17 17:15:28,191 INFO [Socket Reader #1 for port 20059] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0020 (auth:SIMPLE)
2015-10-17 17:15:28,209 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0020_m_000006 asked for a task
2015-10-17 17:15:28,210 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0020_m_000006 given task: attempt_1445062781478_0020_m_000004_0
2015-10-17 17:15:28,211 INFO [Socket Reader #1 for port 20059] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0020 (auth:SIMPLE)
2015-10-17 17:15:28,216 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0020_m_000003 asked for a task
2015-10-17 17:15:28,216 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0020_m_000003 given task: attempt_1445062781478_0020_m_000001_0
2015-10-17 17:15:28,236 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0020_m_000007 asked for a task
2015-10-17 17:15:28,236 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0020_m_000007 given task: attempt_1445062781478_0020_m_000005_0
2015-10-17 17:15:33,653 INFO [IPC Server handler 22 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000009_0 is : 0.295472
2015-10-17 17:15:33,654 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.106964506
2015-10-17 17:15:33,838 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.10681946
2015-10-17 17:15:33,840 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.106881365
2015-10-17 17:15:35,597 INFO [IPC Server handler 9 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.10635664
2015-10-17 17:15:35,599 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.106493875
2015-10-17 17:15:35,628 INFO [IPC Server handler 22 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.10660437
2015-10-17 17:15:35,677 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.10680563
2015-10-17 17:15:35,692 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.1066108
2015-10-17 17:15:35,694 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.10685723
2015-10-17 17:15:36,683 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000009_0 is : 0.295472
2015-10-17 17:15:36,683 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.106964506
2015-10-17 17:15:36,861 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.10681946
2015-10-17 17:15:36,861 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.106881365
2015-10-17 17:15:38,601 INFO [IPC Server handler 9 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.10635664
2015-10-17 17:15:38,618 INFO [IPC Server handler 22 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.106493875
2015-10-17 17:15:38,630 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.10660437
2015-10-17 17:15:38,681 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.10680563
2015-10-17 17:15:38,691 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.1066108
2015-10-17 17:15:38,711 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.10685723
2015-10-17 17:15:39,714 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000009_0 is : 0.324392
2015-10-17 17:15:39,717 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.118691765
2015-10-17 17:15:39,891 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.10681946
2015-10-17 17:15:39,894 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.106881365
2015-10-17 17:15:41,619 INFO [IPC Server handler 22 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.10635664
2015-10-17 17:15:41,632 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.106493875
2015-10-17 17:15:41,635 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.10660437
2015-10-17 17:15:41,695 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.10680563
2015-10-17 17:15:41,697 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.1066108
2015-10-17 17:15:41,725 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.10685723
2015-10-17 17:15:42,744 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.19068728
2015-10-17 17:15:42,745 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000009_0 is : 0.5222586
2015-10-17 17:15:42,914 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.17581606
2015-10-17 17:15:42,934 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.17251138
2015-10-17 17:15:44,635 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.10635664
2015-10-17 17:15:44,645 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.10660437
2015-10-17 17:15:44,649 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.106493875
2015-10-17 17:15:44,708 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.10859275
2015-10-17 17:15:44,713 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.10865837
2015-10-17 17:15:44,740 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.111328624
2015-10-17 17:15:45,767 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000009_0 is : 0.5323719
2015-10-17 17:15:45,767 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.19266446
2015-10-17 17:15:45,935 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.19255035
2015-10-17 17:15:45,955 INFO [IPC Server handler 24 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.19258286
2015-10-17 17:15:47,652 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.14796747
2015-10-17 17:15:47,653 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.14760028
2015-10-17 17:15:47,666 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.15814745
2015-10-17 17:15:47,715 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.18022124
2015-10-17 17:15:47,728 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.18001968
2015-10-17 17:15:47,762 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.18213248
2015-10-17 17:15:48,790 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000009_0 is : 0.5323719
2015-10-17 17:15:48,807 INFO [IPC Server handler 24 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.19266446
2015-10-17 17:15:48,966 INFO [IPC Server handler 15 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.19255035
2015-10-17 17:15:48,983 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.19258286
2015-10-17 17:15:50,661 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.19158794
2015-10-17 17:15:50,661 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.19212553
2015-10-17 17:15:50,676 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.19209063
2015-10-17 17:15:50,724 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.19242907
2015-10-17 17:15:50,739 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.19211523
2015-10-17 17:15:50,770 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.19247705
2015-10-17 17:15:51,822 INFO [IPC Server handler 15 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000009_0 is : 0.62294626
2015-10-17 17:15:51,836 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.23040459
2015-10-17 17:15:52,000 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.19255035
2015-10-17 17:15:52,010 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.19258286
2015-10-17 17:15:52,405 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000009_0 is : 0.62294626
2015-10-17 17:15:53,663 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.19158794
2015-10-17 17:15:53,664 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.19212553
2015-10-17 17:15:53,678 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.19209063
2015-10-17 17:15:53,727 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.19242907
2015-10-17 17:15:53,745 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.19211523
2015-10-17 17:15:53,770 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.19247705
2015-10-17 17:15:54,856 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000009_0 is : 0.667
2015-10-17 17:15:54,868 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.2783809
2015-10-17 17:15:55,034 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.22767149
2015-10-17 17:15:55,041 INFO [IPC Server handler 21 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.22737078
2015-10-17 17:15:56,685 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.19209063
2015-10-17 17:15:56,688 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.19158794
2015-10-17 17:15:56,688 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.19212553
2015-10-17 17:15:56,747 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.19242907
2015-10-17 17:15:56,763 INFO [IPC Server handler 24 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.19211523
2015-10-17 17:15:56,778 INFO [IPC Server handler 15 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.19247705
2015-10-17 17:15:57,878 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000009_0 is : 0.667
2015-10-17 17:15:57,886 INFO [IPC Server handler 21 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.2783809
2015-10-17 17:15:58,055 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.27825075
2015-10-17 17:15:58,058 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.27811313
2015-10-17 17:15:59,698 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.22709332
2015-10-17 17:15:59,705 INFO [IPC Server handler 24 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.23562789
2015-10-17 17:15:59,705 INFO [IPC Server handler 15 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.23281455
2015-10-17 17:15:59,757 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.23688921
2015-10-17 17:15:59,778 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.2376328
2015-10-17 17:15:59,787 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.24027815
2015-10-17 17:16:00,897 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000009_0 is : 0.6673934
2015-10-17 17:16:00,903 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.2783809
2015-10-17 17:16:01,074 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.27825075
2015-10-17 17:16:01,074 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.27811313
2015-10-17 17:16:02,710 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.27765483
2015-10-17 17:16:02,712 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.27772525
2015-10-17 17:16:02,713 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.27696857
2015-10-17 17:16:02,756 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.2781602
2015-10-17 17:16:02,791 INFO [IPC Server handler 21 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.27776006
2015-10-17 17:16:02,791 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.27813601
2015-10-17 17:16:03,917 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000009_0 is : 0.73986816
2015-10-17 17:16:03,920 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.31827462
2015-10-17 17:16:04,097 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.27825075
2015-10-17 17:16:04,097 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.27811313
2015-10-17 17:16:05,709 INFO [IPC Server handler 15 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.27765483
2015-10-17 17:16:05,725 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.27772525
2015-10-17 17:16:05,726 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.27696857
2015-10-17 17:16:05,756 INFO [IPC Server handler 21 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.2781602
2015-10-17 17:16:05,805 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.27813601
2015-10-17 17:16:05,806 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.27776006
2015-10-17 17:16:06,942 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000009_0 is : 0.831289
2015-10-17 17:16:06,943 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.36404583
2015-10-17 17:16:07,117 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.35459784
2015-10-17 17:16:07,121 INFO [IPC Server handler 9 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.3176316
2015-10-17 17:16:08,716 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.27765483
2015-10-17 17:16:08,729 INFO [IPC Server handler 21 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.27772525
2015-10-17 17:16:08,730 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.27696857
2015-10-17 17:16:08,763 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.2781602
2015-10-17 17:16:08,828 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.27776006
2015-10-17 17:16:08,832 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.27813601
2015-10-17 17:16:09,964 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000009_0 is : 0.9200961
2015-10-17 17:16:09,964 INFO [IPC Server handler 9 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.36404583
2015-10-17 17:16:10,136 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.3638923
2015-10-17 17:16:10,137 INFO [IPC Server handler 22 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.3637686
2015-10-17 17:16:11,731 INFO [IPC Server handler 21 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.29395512
2015-10-17 17:16:11,744 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.30158108
2015-10-17 17:16:11,747 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.3119641
2015-10-17 17:16:11,775 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.33034268
2015-10-17 17:16:11,842 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.328011
2015-10-17 17:16:11,844 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.3285037
2015-10-17 17:16:12,993 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.36404583
2015-10-17 17:16:12,994 INFO [IPC Server handler 22 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000009_0 is : 0.9999122
2015-10-17 17:16:13,105 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000009_0 is : 1.0
2015-10-17 17:16:13,107 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0020_m_000009_0
2015-10-17 17:16:13,108 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:16:13,108 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0020_01_000011 taskAttempt attempt_1445062781478_0020_m_000009_0
2015-10-17 17:16:13,109 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0020_m_000009_0
2015-10-17 17:16:13,109 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 17:16:13,127 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:16:13,133 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0020_m_000009_0
2015-10-17 17:16:13,134 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:16:13,136 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-17 17:16:13,162 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.3637686
2015-10-17 17:16:13,165 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.3638923
2015-10-17 17:16:13,286 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0020_m_000003
2015-10-17 17:16:13,287 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 17:16:13,287 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0020_m_000003
2015-10-17 17:16:13,287 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:16:13,287 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:16:13,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000003_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:16:13,579 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 17:16:13,580 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:13312, vCores:-14> knownNMs=4
2015-10-17 17:16:13,580 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:13312, vCores:-14>
2015-10-17 17:16:13,581 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-17 17:16:13,581 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:23552, vCores:-4> finalMapResourceLimit:<memory:11264, vCores:11> finalReduceResourceLimit:<memory:12288, vCores:-15> netScheduledMapResource:<memory:11264, vCores:11> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-17 17:16:13,581 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping up 1
2015-10-17 17:16:13,581 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 17:16:14,588 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0020: ask=1 release= 0 newContainers=1 finishedContainers=1 resourcelimit=<memory:13312, vCores:-14> knownNMs=4
2015-10-17 17:16:14,588 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0020_01_000011
2015-10-17 17:16:14,589 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 17:16:14,589 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0020_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:16:14,590 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:16:14,590 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0020_01_000012 to attempt_1445062781478_0020_m_000003_1
2015-10-17 17:16:14,590 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:10 RackLocal:1
2015-10-17 17:16:14,590 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:16:14,591 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000003_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:16:14,591 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0020_01_000012 taskAttempt attempt_1445062781478_0020_m_000003_1
2015-10-17 17:16:14,591 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0020_m_000003_1
2015-10-17 17:16:14,591 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 17:16:14,690 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0020_m_000003_1 : 13562
2015-10-17 17:16:14,691 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0020_m_000003_1] using containerId: [container_1445062781478_0020_01_000012 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:64642]
2015-10-17 17:16:14,691 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000003_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:16:14,691 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0020_m_000003
2015-10-17 17:16:14,742 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.36323506
2015-10-17 17:16:14,760 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.3624012
2015-10-17 17:16:14,760 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.36317363
2015-10-17 17:16:14,788 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.36388028
2015-10-17 17:16:14,853 INFO [IPC Server handler 9 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.36319977
2015-10-17 17:16:14,853 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.36390656
2015-10-17 17:16:15,593 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0020: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:12288, vCores:-15> knownNMs=4
2015-10-17 17:16:15,593 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 17:16:15,593 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 17:16:15,593 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0020_01_000013 to attempt_1445062781478_0020_r_000000_0
2015-10-17 17:16:15,593 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-17 17:16:15,601 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:16:15,602 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:16:15,602 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0020_01_000013 taskAttempt attempt_1445062781478_0020_r_000000_0
2015-10-17 17:16:15,602 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0020_r_000000_0
2015-10-17 17:16:15,603 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 17:16:15,694 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0020_r_000000_0 : 13562
2015-10-17 17:16:15,694 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0020_r_000000_0] using containerId: [container_1445062781478_0020_01_000013 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:64642]
2015-10-17 17:16:15,694 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:16:15,695 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0020_r_000000
2015-10-17 17:16:15,695 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 17:16:16,025 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.4399499
2015-10-17 17:16:16,181 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.3637686
2015-10-17 17:16:16,181 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.4214182
2015-10-17 17:16:16,594 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0020: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:12288, vCores:-15> knownNMs=4
2015-10-17 17:16:17,741 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.36323506
2015-10-17 17:16:17,772 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.36317363
2015-10-17 17:16:17,773 INFO [IPC Server handler 9 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.3624012
2015-10-17 17:16:17,791 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.36388028
2015-10-17 17:16:17,868 INFO [IPC Server handler 22 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.36319977
2015-10-17 17:16:17,874 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.36390656
2015-10-17 17:16:19,046 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.44980705
2015-10-17 17:16:19,203 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.44950172
2015-10-17 17:16:19,203 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.44964966
2015-10-17 17:16:20,749 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.36323506
2015-10-17 17:16:20,781 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.3624012
2015-10-17 17:16:20,784 INFO [IPC Server handler 22 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.36317363
2015-10-17 17:16:20,809 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.36388028
2015-10-17 17:16:20,872 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.36319977
2015-10-17 17:16:20,888 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.36390656
2015-10-17 17:16:20,910 INFO [Socket Reader #1 for port 20059] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0020 (auth:SIMPLE)
2015-10-17 17:16:20,945 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0020_r_000013 asked for a task
2015-10-17 17:16:20,945 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0020_r_000013 given task: attempt_1445062781478_0020_r_000000_0
2015-10-17 17:16:21,303 INFO [Socket Reader #1 for port 20059] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0020 (auth:SIMPLE)
2015-10-17 17:16:21,618 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0020_m_000012 asked for a task
2015-10-17 17:16:21,618 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0020_m_000012 given task: attempt_1445062781478_0020_m_000003_1
2015-10-17 17:16:22,076 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.44980705
2015-10-17 17:16:22,223 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.44950172
2015-10-17 17:16:22,223 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.44964966
2015-10-17 17:16:23,761 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.3649571
2015-10-17 17:16:23,795 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.3885376
2015-10-17 17:16:23,797 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.37019986
2015-10-17 17:16:23,830 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.4256608
2015-10-17 17:16:23,885 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.4020901
2015-10-17 17:16:23,905 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.41387808
2015-10-17 17:16:24,716 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 0 maxEvents 10000
2015-10-17 17:16:25,107 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.48276442
2015-10-17 17:16:25,248 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.492068
2015-10-17 17:16:25,250 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.44950172
2015-10-17 17:16:25,787 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:26,777 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.44091162
2015-10-17 17:16:26,807 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.44789755
2015-10-17 17:16:26,809 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.44414717
2015-10-17 17:16:26,822 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:26,836 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.44968578
2015-10-17 17:16:26,902 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.448704
2015-10-17 17:16:26,914 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.44950968
2015-10-17 17:16:27,841 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:28,127 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.53543663
2015-10-17 17:16:28,269 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.5352825
2015-10-17 17:16:28,270 INFO [IPC Server handler 15 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.53521925
2015-10-17 17:16:28,289 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0020_m_000002
2015-10-17 17:16:28,289 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0020_m_000002
2015-10-17 17:16:28,290 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:16:28,290 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 17:16:28,290 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:16:28,290 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000002_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:16:28,608 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-17 17:16:28,609 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:12288, vCores:-15> knownNMs=4
2015-10-17 17:16:28,903 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:29,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 17:16:29,612 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:16:29,612 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0020_01_000014 to attempt_1445062781478_0020_m_000002_1
2015-10-17 17:16:29,612 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 17:16:29,612 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:16:29,613 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000002_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:16:29,613 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0020_01_000014 taskAttempt attempt_1445062781478_0020_m_000002_1
2015-10-17 17:16:29,613 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0020_m_000002_1
2015-10-17 17:16:29,613 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 17:16:29,789 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.4486067
2015-10-17 17:16:29,805 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.44789755
2015-10-17 17:16:29,819 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.44859612
2015-10-17 17:16:29,836 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.44968578
2015-10-17 17:16:29,877 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0020_m_000002_1 : 13562
2015-10-17 17:16:29,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0020_m_000002_1] using containerId: [container_1445062781478_0020_01_000014 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:64642]
2015-10-17 17:16:29,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000002_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:16:29,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0020_m_000002
2015-10-17 17:16:29,915 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.44950968
2015-10-17 17:16:29,915 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.448704
2015-10-17 17:16:29,984 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:30,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:11264, vCores:-16> knownNMs=4
2015-10-17 17:16:30,778 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.0
2015-10-17 17:16:31,045 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:31,155 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.53543663
2015-10-17 17:16:31,292 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.5352825
2015-10-17 17:16:31,309 INFO [IPC Server handler 24 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.53521925
2015-10-17 17:16:31,389 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_1 is : 0.06546045
2015-10-17 17:16:32,123 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:32,790 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.4486067
2015-10-17 17:16:32,806 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.44789755
2015-10-17 17:16:32,823 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.44859612
2015-10-17 17:16:32,837 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.45195487
2015-10-17 17:16:32,916 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.44950968
2015-10-17 17:16:32,930 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.448704
2015-10-17 17:16:33,276 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:34,174 INFO [IPC Server handler 15 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.53543663
2015-10-17 17:16:34,312 INFO [IPC Server handler 24 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.5352825
2015-10-17 17:16:34,327 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.53521925
2015-10-17 17:16:34,360 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:34,695 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.0
2015-10-17 17:16:34,965 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_1 is : 0.100270376
2015-10-17 17:16:35,415 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:35,794 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.46088505
2015-10-17 17:16:35,812 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.48774862
2015-10-17 17:16:35,827 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.46827805
2015-10-17 17:16:35,844 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.52721363
2015-10-17 17:16:35,862 INFO [Socket Reader #1 for port 20059] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0020 (auth:SIMPLE)
2015-10-17 17:16:35,922 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.5229624
2015-10-17 17:16:35,934 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0020_m_000014 asked for a task
2015-10-17 17:16:35,935 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0020_m_000014 given task: attempt_1445062781478_0020_m_000002_1
2015-10-17 17:16:35,938 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.51590294
2015-10-17 17:16:36,481 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:37,203 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.6210422
2015-10-17 17:16:37,342 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.6104692
2015-10-17 17:16:37,355 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.5541956
2015-10-17 17:16:37,590 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:38,305 INFO [IPC Server handler 24 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.0
2015-10-17 17:16:38,421 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_1 is : 0.106493875
2015-10-17 17:16:38,659 INFO [IPC Server handler 21 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:38,809 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.5343203
2015-10-17 17:16:38,823 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.53341997
2015-10-17 17:16:38,840 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.5342037
2015-10-17 17:16:38,853 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.5352028
2015-10-17 17:16:38,934 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.5352021
2015-10-17 17:16:38,947 INFO [IPC Server handler 15 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.53425497
2015-10-17 17:16:39,712 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:40,235 INFO [IPC Server handler 24 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.6210422
2015-10-17 17:16:40,373 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.620844
2015-10-17 17:16:40,383 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.6207798
2015-10-17 17:16:40,729 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:41,550 INFO [IPC Server handler 21 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.033333335
2015-10-17 17:16:41,723 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_1 is : 0.106493875
2015-10-17 17:16:41,738 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:41,822 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.5343203
2015-10-17 17:16:41,822 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.53341997
2015-10-17 17:16:41,853 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.5352028
2015-10-17 17:16:41,859 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.5342037
2015-10-17 17:16:41,956 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.5352021
2015-10-17 17:16:41,962 INFO [IPC Server handler 24 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.53425497
2015-10-17 17:16:42,738 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:43,255 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.6210422
2015-10-17 17:16:43,292 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0020_m_000001
2015-10-17 17:16:43,292 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 17:16:43,292 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0020_m_000001
2015-10-17 17:16:43,293 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:16:43,293 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:16:43,293 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000001_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:16:43,393 INFO [IPC Server handler 21 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.620844
2015-10-17 17:16:43,400 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.6207798
2015-10-17 17:16:43,631 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 17:16:43,632 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:11264, vCores:-16> knownNMs=4
2015-10-17 17:16:43,761 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:44,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 17:16:44,636 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:16:44,637 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0020_01_000015 to attempt_1445062781478_0020_m_000001_1
2015-10-17 17:16:44,637 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:10 RackLocal:3
2015-10-17 17:16:44,637 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:16:44,637 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000001_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:16:44,638 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0020_01_000015 taskAttempt attempt_1445062781478_0020_m_000001_1
2015-10-17 17:16:44,638 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0020_m_000001_1
2015-10-17 17:16:44,638 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 17:16:44,805 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.033333335
2015-10-17 17:16:44,806 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:44,826 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.53341997
2015-10-17 17:16:44,829 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.5343203
2015-10-17 17:16:44,855 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.5352028
2015-10-17 17:16:44,873 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.5342037
2015-10-17 17:16:44,966 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.53425497
2015-10-17 17:16:44,972 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0020_m_000001_1 : 13562
2015-10-17 17:16:44,972 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0020_m_000001_1] using containerId: [container_1445062781478_0020_01_000015 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:64642]
2015-10-17 17:16:44,972 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000001_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:16:44,973 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0020_m_000001
2015-10-17 17:16:44,975 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.5352021
2015-10-17 17:16:44,984 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_1 is : 0.106493875
2015-10-17 17:16:45,639 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:10240, vCores:-17> knownNMs=4
2015-10-17 17:16:45,900 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:46,283 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.6252609
2015-10-17 17:16:46,423 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.6395945
2015-10-17 17:16:46,427 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.6207798
2015-10-17 17:16:46,777 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_1 is : 0.065619476
2015-10-17 17:16:46,929 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:47,400 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.6252609
2015-10-17 17:16:47,650 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.6395945
2015-10-17 17:16:47,842 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.5547179
2015-10-17 17:16:47,843 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.55340356
2015-10-17 17:16:47,858 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.61354107
2015-10-17 17:16:47,889 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.56033367
2015-10-17 17:16:47,983 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.6013707
2015-10-17 17:16:47,984 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:47,985 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.6109639
2015-10-17 17:16:48,271 INFO [IPC Server handler 21 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.033333335
2015-10-17 17:16:48,579 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_1 is : 0.106493875
2015-10-17 17:16:49,088 INFO [IPC Server handler 21 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:49,316 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.667
2015-10-17 17:16:49,369 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.6207798
2015-10-17 17:16:49,445 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.667
2015-10-17 17:16:49,461 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.667
2015-10-17 17:16:50,148 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:50,431 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_1 is : 0.099842764
2015-10-17 17:16:50,856 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.6199081
2015-10-17 17:16:50,856 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.61898744
2015-10-17 17:16:50,868 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.6208445
2015-10-17 17:16:50,901 INFO [IPC Server handler 15 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.6196791
2015-10-17 17:16:50,996 INFO [IPC Server handler 21 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.6209487
2015-10-17 17:16:50,996 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.6197233
2015-10-17 17:16:51,239 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:51,836 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.033333335
2015-10-17 17:16:52,202 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_1 is : 0.106493875
2015-10-17 17:16:52,272 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:52,336 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.667
2015-10-17 17:16:52,465 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.667
2015-10-17 17:16:52,480 INFO [IPC Server handler 9 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.667
2015-10-17 17:16:53,358 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:53,855 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.6199081
2015-10-17 17:16:53,855 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.61898744
2015-10-17 17:16:53,872 INFO [IPC Server handler 15 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.6208445
2015-10-17 17:16:53,904 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.6196791
2015-10-17 17:16:53,997 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.6197233
2015-10-17 17:16:54,003 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.6209487
2015-10-17 17:16:54,034 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_1 is : 0.10660437
2015-10-17 17:16:54,386 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:54,753 INFO [Socket Reader #1 for port 20059] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0020 (auth:SIMPLE)
2015-10-17 17:16:54,881 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0020_m_000015 asked for a task
2015-10-17 17:16:54,881 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0020_m_000015 given task: attempt_1445062781478_0020_m_000001_1
2015-10-17 17:16:55,354 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.667
2015-10-17 17:16:55,469 INFO [IPC Server handler 9 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:55,509 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.667
2015-10-17 17:16:55,509 INFO [IPC Server handler 22 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.667
2015-10-17 17:16:55,909 INFO [IPC Server handler 24 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.033333335
2015-10-17 17:16:56,144 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_1 is : 0.106493875
2015-10-17 17:16:56,480 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:56,857 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.6199081
2015-10-17 17:16:56,862 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.61898744
2015-10-17 17:16:56,871 INFO [IPC Server handler 15 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.6298147
2015-10-17 17:16:56,922 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.6196791
2015-10-17 17:16:56,996 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.6197233
2015-10-17 17:16:57,018 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.6292532
2015-10-17 17:16:57,330 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_1 is : 0.10660437
2015-10-17 17:16:57,512 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:58,294 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0020_m_000000
2015-10-17 17:16:58,294 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0020_m_000000
2015-10-17 17:16:58,294 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 17:16:58,294 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:16:58,295 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:16:58,295 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000000_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 17:16:58,382 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.6926627
2015-10-17 17:16:58,429 INFO [IPC Server handler 9 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.6298147
2015-10-17 17:16:58,526 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:58,530 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.67778707
2015-10-17 17:16:58,548 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.6292532
2015-10-17 17:16:58,549 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.69040716
2015-10-17 17:16:58,658 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:10 RackLocal:3
2015-10-17 17:16:58,660 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:10240, vCores:-17> knownNMs=4
2015-10-17 17:16:58,924 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.6197233
2015-10-17 17:16:59,195 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.033333335
2015-10-17 17:16:59,313 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_1 is : 0.106493875
2015-10-17 17:16:59,530 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:16:59,667 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 17:16:59,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0020_01_000016 to attempt_1445062781478_0020_m_000000_1
2015-10-17 17:16:59,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:11 RackLocal:3
2015-10-17 17:16:59,668 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 17:16:59,671 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000000_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 17:16:59,671 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0020_01_000016 taskAttempt attempt_1445062781478_0020_m_000000_1
2015-10-17 17:16:59,671 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0020_m_000000_1
2015-10-17 17:16:59,671 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 17:16:59,693 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0020_m_000000_1 : 13562
2015-10-17 17:16:59,694 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0020_m_000000_1] using containerId: [container_1445062781478_0020_01_000016 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 17:16:59,694 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000000_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 17:16:59,695 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0020_m_000000
2015-10-17 17:16:59,871 INFO [IPC Server handler 15 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.642735
2015-10-17 17:16:59,873 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.63439417
2015-10-17 17:16:59,874 INFO [IPC Server handler 24 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.667
2015-10-17 17:16:59,935 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.6492648
2015-10-17 17:16:59,998 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.667
2015-10-17 17:17:00,035 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.667
2015-10-17 17:17:00,553 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:00,612 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_1 is : 0.10660437
2015-10-17 17:17:00,671 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:9216, vCores:-18> knownNMs=4
2015-10-17 17:17:00,766 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.6492648
2015-10-17 17:17:00,844 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.642735
2015-10-17 17:17:01,202 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.63439417
2015-10-17 17:17:01,422 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.72653395
2015-10-17 17:17:01,569 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.69718146
2015-10-17 17:17:01,578 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:01,587 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.71025634
2015-10-17 17:17:02,356 INFO [Socket Reader #1 for port 20059] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0020 (auth:SIMPLE)
2015-10-17 17:17:02,373 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0020_m_000016 asked for a task
2015-10-17 17:17:02,373 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0020_m_000016 given task: attempt_1445062781478_0020_m_000000_1
2015-10-17 17:17:02,420 INFO [IPC Server handler 22 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.033333335
2015-10-17 17:17:02,518 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_1 is : 0.106493875
2015-10-17 17:17:02,590 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:02,887 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.667
2015-10-17 17:17:02,888 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.667
2015-10-17 17:17:02,892 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.667
2015-10-17 17:17:02,955 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.667
2015-10-17 17:17:02,996 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.667
2015-10-17 17:17:03,051 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.667
2015-10-17 17:17:03,607 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:04,194 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_1 is : 0.10660437
2015-10-17 17:17:04,453 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.74964625
2015-10-17 17:17:04,599 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.72925836
2015-10-17 17:17:04,616 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.74426454
2015-10-17 17:17:04,629 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:05,667 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:05,893 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.667
2015-10-17 17:17:05,894 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.667
2015-10-17 17:17:05,907 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.667
2015-10-17 17:17:05,973 INFO [IPC Server handler 21 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.667
2015-10-17 17:17:06,002 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.667
2015-10-17 17:17:06,023 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.033333335
2015-10-17 17:17:06,066 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.667
2015-10-17 17:17:06,087 INFO [IPC Server handler 9 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_1 is : 0.106493875
2015-10-17 17:17:06,734 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:07,497 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.7772497
2015-10-17 17:17:07,612 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_1 is : 0.10660437
2015-10-17 17:17:07,633 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.7590936
2015-10-17 17:17:07,645 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.7761037
2015-10-17 17:17:07,759 INFO [IPC Server handler 24 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:08,002 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_1 is : 0.038316555
2015-10-17 17:17:08,795 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:08,909 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.667
2015-10-17 17:17:08,910 INFO [IPC Server handler 21 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.6847919
2015-10-17 17:17:08,922 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.667
2015-10-17 17:17:08,989 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.667
2015-10-17 17:17:09,016 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.673317
2015-10-17 17:17:09,081 INFO [IPC Server handler 9 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.68015534
2015-10-17 17:17:09,426 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.033333335
2015-10-17 17:17:09,477 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_1 is : 0.13548234
2015-10-17 17:17:09,787 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_1 is : 0.10635664
2015-10-17 17:17:09,828 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:10,525 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.79608333
2015-10-17 17:17:10,663 INFO [IPC Server handler 15 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.8008257
2015-10-17 17:17:10,673 INFO [IPC Server handler 24 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.8172877
2015-10-17 17:17:10,874 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:11,011 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_1 is : 0.10660437
2015-10-17 17:17:11,731 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_1 is : 0.08962054
2015-10-17 17:17:11,923 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.72243494
2015-10-17 17:17:11,923 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.68729776
2015-10-17 17:17:11,933 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.6896647
2015-10-17 17:17:11,936 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:11,996 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.69483376
2015-10-17 17:17:12,030 INFO [IPC Server handler 9 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.71307385
2015-10-17 17:17:12,090 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.7218813
2015-10-17 17:17:12,821 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_1 is : 0.10635664
2015-10-17 17:17:13,050 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:13,130 INFO [IPC Server handler 22 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_1 is : 0.17886485
2015-10-17 17:17:13,197 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.033333335
2015-10-17 17:17:13,556 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.8151878
2015-10-17 17:17:13,690 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.84266984
2015-10-17 17:17:13,698 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.8609328
2015-10-17 17:17:14,123 INFO [IPC Server handler 22 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:14,548 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_1 is : 0.10660437
2015-10-17 17:17:14,941 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.72854125
2015-10-17 17:17:14,943 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.73011017
2015-10-17 17:17:14,943 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.7617643
2015-10-17 17:17:15,003 INFO [IPC Server handler 22 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.73784685
2015-10-17 17:17:15,029 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.75421786
2015-10-17 17:17:15,099 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.76227516
2015-10-17 17:17:15,139 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_1 is : 0.1066108
2015-10-17 17:17:15,202 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:15,847 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_1 is : 0.10635664
2015-10-17 17:17:16,217 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:16,555 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.033333335
2015-10-17 17:17:16,561 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_1 is : 0.19209063
2015-10-17 17:17:16,588 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.8380622
2015-10-17 17:17:16,721 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.8721353
2015-10-17 17:17:16,723 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.89160633
2015-10-17 17:17:17,246 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:17,899 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_1 is : 0.10660437
2015-10-17 17:17:17,959 INFO [IPC Server handler 22 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.76939386
2015-10-17 17:17:17,964 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.7645177
2015-10-17 17:17:17,964 INFO [IPC Server handler 9 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.7947242
2015-10-17 17:17:18,024 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.77493405
2015-10-17 17:17:18,034 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.7883416
2015-10-17 17:17:18,111 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.7964324
2015-10-17 17:17:18,278 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:18,454 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_1 is : 0.1066108
2015-10-17 17:17:18,886 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_1 is : 0.19158794
2015-10-17 17:17:19,293 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:19,619 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.85705614
2015-10-17 17:17:19,761 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.90842927
2015-10-17 17:17:19,763 INFO [IPC Server handler 21 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.92748094
2015-10-17 17:17:19,907 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_1 is : 0.19209063
2015-10-17 17:17:19,941 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.033333335
2015-10-17 17:17:20,311 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:20,968 INFO [IPC Server handler 9 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.80689293
2015-10-17 17:17:20,969 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.8311372
2015-10-17 17:17:20,969 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.79926544
2015-10-17 17:17:21,028 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.81321025
2015-10-17 17:17:21,043 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.82810265
2015-10-17 17:17:21,122 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.8367236
2015-10-17 17:17:21,287 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_1 is : 0.10660437
2015-10-17 17:17:21,340 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:21,783 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_1 is : 0.1066108
2015-10-17 17:17:21,928 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_1 is : 0.19158794
2015-10-17 17:17:22,390 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:22,656 INFO [IPC Server handler 15 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.88423383
2015-10-17 17:17:22,798 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.95810694
2015-10-17 17:17:22,798 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.93819666
2015-10-17 17:17:23,256 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_1 is : 0.19209063
2015-10-17 17:17:23,315 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.033333335
2015-10-17 17:17:23,435 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:23,971 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.86848557
2015-10-17 17:17:23,973 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.8394517
2015-10-17 17:17:23,985 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.8292054
2015-10-17 17:17:24,034 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.84569126
2015-10-17 17:17:24,047 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.8664425
2015-10-17 17:17:24,128 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.8738835
2015-10-17 17:17:24,469 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:24,644 INFO [IPC Server handler 15 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_1 is : 0.1577693
2015-10-17 17:17:24,970 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_1 is : 0.19158794
2015-10-17 17:17:25,192 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_1 is : 0.1066108
2015-10-17 17:17:25,486 INFO [IPC Server handler 15 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:25,688 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.90345484
2015-10-17 17:17:25,827 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 0.9670727
2015-10-17 17:17:25,830 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 0.98843527
2015-10-17 17:17:26,513 INFO [IPC Server handler 24 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:26,723 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000007_0 is : 1.0
2015-10-17 17:17:26,725 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0020_m_000007_0
2015-10-17 17:17:26,725 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:17:26,725 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0020_01_000009 taskAttempt attempt_1445062781478_0020_m_000007_0
2015-10-17 17:17:26,725 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0020_m_000007_0
2015-10-17 17:17:26,726 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 17:17:26,736 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:17:26,737 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0020_m_000007_0
2015-10-17 17:17:26,737 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:17:26,737 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-17 17:17:26,741 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_1 is : 0.19209063
2015-10-17 17:17:26,783 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.033333335
2015-10-17 17:17:26,984 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.87683386
2015-10-17 17:17:26,989 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.92119133
2015-10-17 17:17:27,005 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.8632102
2015-10-17 17:17:27,047 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.8830246
2015-10-17 17:17:27,051 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.921389
2015-10-17 17:17:27,139 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.9288331
2015-10-17 17:17:27,560 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 17:17:27,705 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:11 RackLocal:3
2015-10-17 17:17:27,996 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_1 is : 0.19158794
2015-10-17 17:17:28,236 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_1 is : 0.19212553
2015-10-17 17:17:28,307 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000008_0 is : 1.0
2015-10-17 17:17:28,308 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0020_m_000008_0
2015-10-17 17:17:28,308 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:17:28,308 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0020_01_000010 taskAttempt attempt_1445062781478_0020_m_000008_0
2015-10-17 17:17:28,309 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0020_m_000008_0
2015-10-17 17:17:28,309 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 17:17:28,318 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:17:28,318 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0020_m_000008_0
2015-10-17 17:17:28,318 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:17:28,318 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-17 17:17:28,625 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 17:17:28,706 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:11 RackLocal:3
2015-10-17 17:17:28,708 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0020_01_000009
2015-10-17 17:17:28,708 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:11 RackLocal:3
2015-10-17 17:17:28,708 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0020_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:17:28,726 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.92723
2015-10-17 17:17:28,874 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_1 is : 0.1066108
2015-10-17 17:17:29,672 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 17:17:29,710 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0020_01_000010
2015-10-17 17:17:29,710 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:11 RackLocal:3
2015-10-17 17:17:29,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0020_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:17:29,983 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.9075622
2015-10-17 17:17:30,001 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.96188486
2015-10-17 17:17:30,020 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.89152
2015-10-17 17:17:30,050 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.9147134
2015-10-17 17:17:30,052 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_1 is : 0.19209063
2015-10-17 17:17:30,066 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 0.9632846
2015-10-17 17:17:30,141 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 0.97013986
2015-10-17 17:17:30,187 INFO [IPC Server handler 15 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.033333335
2015-10-17 17:17:30,723 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 17:17:31,036 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_1 is : 0.26724863
2015-10-17 17:17:31,623 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_1 is : 0.19212553
2015-10-17 17:17:31,751 INFO [IPC Server handler 21 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 0.9586107
2015-10-17 17:17:31,763 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 17:17:32,253 INFO [IPC Server handler 24 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_1 is : 0.1066108
2015-10-17 17:17:32,558 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000005_0 is : 1.0
2015-10-17 17:17:32,561 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0020_m_000005_0
2015-10-17 17:17:32,561 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:17:32,561 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0020_01_000007 taskAttempt attempt_1445062781478_0020_m_000005_0
2015-10-17 17:17:32,562 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0020_m_000005_0
2015-10-17 17:17:32,563 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:17:32,577 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:17:32,578 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0020_m_000005_0
2015-10-17 17:17:32,578 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:17:32,578 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-17 17:17:32,716 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:11 RackLocal:3
2015-10-17 17:17:32,818 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 17:17:32,985 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.9449853
2015-10-17 17:17:33,023 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 0.9982183
2015-10-17 17:17:33,031 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.92587435
2015-10-17 17:17:33,069 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.9528737
2015-10-17 17:17:33,079 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 1.0
2015-10-17 17:17:33,155 INFO [IPC Server handler 15 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000001_0 is : 1.0
2015-10-17 17:17:33,157 INFO [IPC Server handler 24 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0020_m_000001_0
2015-10-17 17:17:33,158 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:17:33,158 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0020_01_000003 taskAttempt attempt_1445062781478_0020_m_000001_0
2015-10-17 17:17:33,159 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0020_m_000001_0
2015-10-17 17:17:33,159 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:17:33,177 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:17:33,177 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0020_m_000001_0
2015-10-17 17:17:33,177 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0020_m_000001_1
2015-10-17 17:17:33,178 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:17:33,178 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-17 17:17:33,179 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000001_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 17:17:33,179 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0020_01_000015 taskAttempt attempt_1445062781478_0020_m_000001_1
2015-10-17 17:17:33,180 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0020_m_000001_1
2015-10-17 17:17:33,181 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 17:17:33,326 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0020_m_000001
2015-10-17 17:17:33,327 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 17:17:33,363 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000004_0 is : 1.0
2015-10-17 17:17:33,366 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0020_m_000004_0
2015-10-17 17:17:33,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:17:33,367 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0020_01_000006 taskAttempt attempt_1445062781478_0020_m_000004_0
2015-10-17 17:17:33,367 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0020_m_000004_0
2015-10-17 17:17:33,367 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:17:33,385 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:17:33,385 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0020_m_000004_0
2015-10-17 17:17:33,385 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:17:33,386 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-17 17:17:33,570 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_1 is : 0.19209063
2015-10-17 17:17:33,620 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.033333335
2015-10-17 17:17:33,623 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000001_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 17:17:33,625 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 17:17:33,640 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/_temporary/attempt_1445062781478_0020_m_000001_1
2015-10-17 17:17:33,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000001_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 17:17:33,718 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:11 RackLocal:3
2015-10-17 17:17:33,720 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0020_01_000007
2015-10-17 17:17:33,720 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0020_01_000003
2015-10-17 17:17:33,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0020_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:17:33,721 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:11 RackLocal:3
2015-10-17 17:17:33,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0020_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:17:33,877 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 17:17:34,067 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_1 is : 0.27696857
2015-10-17 17:17:34,416 INFO [Socket Reader #1 for port 20059] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 20059: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 17:17:34,722 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000006_0 is : 1.0
2015-10-17 17:17:34,723 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0020_01_000006
2015-10-17 17:17:34,723 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:11 RackLocal:3
2015-10-17 17:17:34,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0020_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:17:34,724 INFO [IPC Server handler 21 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0020_m_000006_0
2015-10-17 17:17:34,725 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:17:34,725 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0020_01_000008 taskAttempt attempt_1445062781478_0020_m_000006_0
2015-10-17 17:17:34,726 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0020_m_000006_0
2015-10-17 17:17:34,726 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 17:17:34,739 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:17:34,740 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0020_m_000006_0
2015-10-17 17:17:34,740 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:17:34,741 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-17 17:17:34,890 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_1 is : 0.19212553
2015-10-17 17:17:34,955 INFO [IPC Server handler 22 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 17:17:35,723 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:11 RackLocal:3
2015-10-17 17:17:35,724 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0020_01_000015
2015-10-17 17:17:35,724 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:11 RackLocal:3
2015-10-17 17:17:35,724 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0020_m_000001_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:17:35,984 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 0.9881778
2015-10-17 17:17:36,036 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.9660619
2015-10-17 17:17:36,078 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 0.99586475
2015-10-17 17:17:36,085 INFO [IPC Server handler 15 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 17:17:36,590 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000002_0 is : 1.0
2015-10-17 17:17:36,593 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0020_m_000002_0
2015-10-17 17:17:36,594 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:17:36,594 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0020_01_000004 taskAttempt attempt_1445062781478_0020_m_000002_0
2015-10-17 17:17:36,595 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0020_m_000002_0
2015-10-17 17:17:36,595 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:17:36,614 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:17:36,615 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0020_m_000002_0
2015-10-17 17:17:36,615 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0020_m_000002_1
2015-10-17 17:17:36,615 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:17:36,615 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-17 17:17:36,616 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000002_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 17:17:36,616 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0020_01_000014 taskAttempt attempt_1445062781478_0020_m_000002_1
2015-10-17 17:17:36,616 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0020_m_000002_1
2015-10-17 17:17:36,616 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 17:17:36,724 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:11 RackLocal:3
2015-10-17 17:17:36,725 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0020_01_000008
2015-10-17 17:17:36,725 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:11 RackLocal:3
2015-10-17 17:17:36,725 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0020_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:17:36,881 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_1 is : 0.19209063
2015-10-17 17:17:36,980 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.033333335
2015-10-17 17:17:37,025 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000002_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 17:17:37,025 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 17:17:37,027 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/_temporary/attempt_1445062781478_0020_m_000002_1
2015-10-17 17:17:37,027 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000002_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 17:17:37,087 INFO [IPC Server handler 24 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_1 is : 0.27696857
2015-10-17 17:17:37,140 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 17:17:37,247 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000000_0 is : 1.0
2015-10-17 17:17:37,249 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0020_m_000000_0
2015-10-17 17:17:37,249 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:17:37,249 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0020_01_000002 taskAttempt attempt_1445062781478_0020_m_000000_0
2015-10-17 17:17:37,250 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0020_m_000000_0
2015-10-17 17:17:37,250 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:17:37,263 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:17:37,263 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0020_m_000000_0
2015-10-17 17:17:37,263 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0020_m_000000_1
2015-10-17 17:17:37,263 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:17:37,263 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-17 17:17:37,263 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000000_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 17:17:37,264 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0020_01_000016 taskAttempt attempt_1445062781478_0020_m_000000_1
2015-10-17 17:17:37,264 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0020_m_000000_1
2015-10-17 17:17:37,264 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 17:17:37,275 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000000_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 17:17:37,275 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 17:17:37,277 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/_temporary/attempt_1445062781478_0020_m_000000_1
2015-10-17 17:17:37,277 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000000_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 17:17:37,331 INFO [Socket Reader #1 for port 20059] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 20059: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 17:17:37,378 INFO [Socket Reader #1 for port 20059] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 20059: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 17:17:37,725 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:11 RackLocal:3
2015-10-17 17:17:37,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0020_01_000004
2015-10-17 17:17:37,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:11 RackLocal:3
2015-10-17 17:17:37,726 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0020_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:17:38,203 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 17:17:38,728 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0020_01_000014
2015-10-17 17:17:38,728 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0020_01_000016
2015-10-17 17:17:38,728 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0020_m_000002_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:17:38,728 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0020_01_000002
2015-10-17 17:17:38,729 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0020_m_000000_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:17:38,729 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:11 RackLocal:3
2015-10-17 17:17:38,729 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0020_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:17:39,055 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 0.9941143
2015-10-17 17:17:39,302 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 17:17:39,855 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_0 is : 1.0
2015-10-17 17:17:39,858 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0020_m_000003_0
2015-10-17 17:17:39,858 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:17:39,859 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0020_01_000005 taskAttempt attempt_1445062781478_0020_m_000003_0
2015-10-17 17:17:39,859 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0020_m_000003_0
2015-10-17 17:17:39,860 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 17:17:39,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:17:39,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0020_m_000003_0
2015-10-17 17:17:39,878 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0020_m_000003_1
2015-10-17 17:17:39,878 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:17:39,879 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-17 17:17:39,879 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000003_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 17:17:39,879 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0020_01_000012 taskAttempt attempt_1445062781478_0020_m_000003_1
2015-10-17 17:17:39,880 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0020_m_000003_1
2015-10-17 17:17:39,880 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 17:17:40,019 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_m_000003_1 is : 0.19209063
2015-10-17 17:17:40,135 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.033333335
2015-10-17 17:17:40,163 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000003_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 17:17:40,164 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 17:17:40,167 WARN [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/_temporary/attempt_1445062781478_0020_m_000003_1
2015-10-17 17:17:40,167 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_m_000003_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 17:17:40,388 INFO [Socket Reader #1 for port 20059] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 20059: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 17:17:40,390 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 17:17:40,731 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:11 RackLocal:3
2015-10-17 17:17:41,464 INFO [IPC Server handler 21 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:17:41,736 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0020_01_000012
2015-10-17 17:17:41,736 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0020_01_000005
2015-10-17 17:17:41,736 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0020_m_000003_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:17:41,736 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:11 RackLocal:3
2015-10-17 17:17:41,737 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0020_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 17:17:42,573 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:17:43,275 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.033333335
2015-10-17 17:17:43,644 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:17:44,689 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:17:45,737 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:17:46,373 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.033333335
2015-10-17 17:17:46,783 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:17:47,868 INFO [IPC Server handler 22 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:17:48,911 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:17:49,514 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.06666667
2015-10-17 17:17:49,958 INFO [IPC Server handler 9 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:17:51,033 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:17:52,062 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:17:52,603 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.10000001
2015-10-17 17:17:53,093 INFO [IPC Server handler 24 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:17:54,126 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:17:55,172 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:17:55,682 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.10000001
2015-10-17 17:17:56,224 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:17:57,275 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:17:58,345 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:17:58,779 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.10000001
2015-10-17 17:17:59,409 INFO [IPC Server handler 21 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:00,487 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:01,531 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:01,892 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.10000001
2015-10-17 17:18:02,577 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:03,611 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:04,674 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:04,975 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.10000001
2015-10-17 17:18:05,774 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:06,813 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:07,877 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:08,091 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.13333334
2015-10-17 17:18:08,924 INFO [IPC Server handler 9 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:10,004 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:11,065 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:11,181 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.13333334
2015-10-17 17:18:12,111 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:13,164 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:14,222 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:14,274 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.13333334
2015-10-17 17:18:15,285 INFO [IPC Server handler 21 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:16,332 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:17,367 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:17,377 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.13333334
2015-10-17 17:18:18,424 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:19,486 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:20,491 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.16666667
2015-10-17 17:18:20,552 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:21,580 INFO [IPC Server handler 22 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:22,613 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:23,564 INFO [IPC Server handler 22 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.16666667
2015-10-17 17:18:23,660 INFO [IPC Server handler 9 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:24,720 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:25,788 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:26,648 INFO [IPC Server handler 9 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.16666667
2015-10-17 17:18:26,868 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:27,925 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:28,944 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:29,755 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.20000002
2015-10-17 17:18:29,973 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:30,986 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:32,001 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:32,936 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.23333333
2015-10-17 17:18:33,020 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:34,051 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:35,088 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:36,026 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.23333333
2015-10-17 17:18:36,120 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:37,159 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:38,209 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:39,083 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.23333333
2015-10-17 17:18:39,260 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:40,316 INFO [IPC Server handler 29 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:41,364 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:42,182 INFO [IPC Server handler 17 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.23333333
2015-10-17 17:18:42,431 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:43,492 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:44,555 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:45,294 INFO [IPC Server handler 21 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.26666668
2015-10-17 17:18:45,613 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:46,683 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:47,706 INFO [IPC Server handler 6 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:48,380 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.26666668
2015-10-17 17:18:48,758 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:49,820 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:50,880 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:51,460 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.26666668
2015-10-17 17:18:51,926 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:52,994 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:54,039 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:54,541 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.3
2015-10-17 17:18:55,105 INFO [IPC Server handler 15 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:56,165 INFO [IPC Server handler 23 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:57,206 INFO [IPC Server handler 27 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:57,641 INFO [IPC Server handler 9 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.3
2015-10-17 17:18:58,259 INFO [IPC Server handler 26 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:18:59,333 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:19:00,395 INFO [IPC Server handler 7 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:19:00,735 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.3
2015-10-17 17:19:01,474 INFO [IPC Server handler 3 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:19:02,535 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 17:19:03,002 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.3
2015-10-17 17:19:03,037 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.3
2015-10-17 17:19:03,861 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.6667707
2015-10-17 17:19:06,912 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.67623824
2015-10-17 17:19:09,959 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.6921079
2015-10-17 17:19:13,004 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.7082201
2015-10-17 17:19:16,050 INFO [IPC Server handler 11 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.72405237
2015-10-17 17:19:19,090 INFO [IPC Server handler 16 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.7400066
2015-10-17 17:19:22,131 INFO [IPC Server handler 24 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.7562506
2015-10-17 17:19:25,343 INFO [IPC Server handler 12 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.7726015
2015-10-17 17:19:28,375 INFO [IPC Server handler 8 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.7866131
2015-10-17 17:19:31,446 INFO [IPC Server handler 0 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.8028904
2015-10-17 17:19:34,516 INFO [IPC Server handler 1 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.8189403
2015-10-17 17:19:37,564 INFO [IPC Server handler 22 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.83548474
2015-10-17 17:19:40,629 INFO [IPC Server handler 20 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.85192525
2015-10-17 17:19:43,690 INFO [IPC Server handler 10 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.86731374
2015-10-17 17:19:46,756 INFO [IPC Server handler 28 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.8838906
2015-10-17 17:19:49,818 INFO [IPC Server handler 2 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.9003594
2015-10-17 17:19:52,880 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.9160572
2015-10-17 17:19:55,942 INFO [IPC Server handler 19 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.93260217
2015-10-17 17:19:58,996 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.9489118
2015-10-17 17:20:02,062 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.96544206
2015-10-17 17:20:05,125 INFO [IPC Server handler 14 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.98081887
2015-10-17 17:20:08,169 INFO [IPC Server handler 5 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 0.9972495
2015-10-17 17:20:08,887 INFO [IPC Server handler 13 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445062781478_0020_r_000000_0
2015-10-17 17:20:08,888 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_r_000000_0 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-17 17:20:08,888 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445062781478_0020_r_000000_0 given a go for committing the task output.
2015-10-17 17:20:08,898 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445062781478_0020_r_000000_0
2015-10-17 17:20:08,899 INFO [IPC Server handler 18 on 20059] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445062781478_0020_r_000000_0:true
2015-10-17 17:20:08,980 INFO [IPC Server handler 25 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0020_r_000000_0 is : 1.0
2015-10-17 17:20:08,983 INFO [IPC Server handler 4 on 20059] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0020_r_000000_0
2015-10-17 17:20:08,984 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_r_000000_0 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 17:20:08,984 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0020_01_000013 taskAttempt attempt_1445062781478_0020_r_000000_0
2015-10-17 17:20:08,985 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0020_r_000000_0
2015-10-17 17:20:08,986 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 17:20:09,101 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0020_r_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 17:20:09,102 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0020_r_000000_0
2015-10-17 17:20:09,102 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0020_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 17:20:09,102 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-17 17:20:09,104 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0020Job Transitioned from RUNNING to COMMITTING
2015-10-17 17:20:09,104 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-17 17:20:09,172 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-17 17:20:09,173 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0020Job Transitioned from COMMITTING to SUCCEEDED
2015-10-17 17:20:09,175 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-17 17:20:09,175 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-17 17:20:09,175 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-17 17:20:09,176 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-17 17:20:09,176 INFO [Thread-104] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-17 17:20:09,176 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-17 17:20:09,177 INFO [Thread-104] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-17 17:20:09,354 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0020/job_1445062781478_0020_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0020-1445073079681-msrabi-pagerank-1445073609167-10-1-SUCCEEDED-default-1445073321328.jhist_tmp
2015-10-17 17:20:09,539 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0020-1445073079681-msrabi-pagerank-1445073609167-10-1-SUCCEEDED-default-1445073321328.jhist_tmp
2015-10-17 17:20:09,543 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0020/job_1445062781478_0020_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0020_conf.xml_tmp
2015-10-17 17:20:09,647 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0020_conf.xml_tmp
2015-10-17 17:20:09,652 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0020.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0020.summary
2015-10-17 17:20:09,655 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0020_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0020_conf.xml
2015-10-17 17:20:09,659 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0020-1445073079681-msrabi-pagerank-1445073609167-10-1-SUCCEEDED-default-1445073321328.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0020-1445073079681-msrabi-pagerank-1445073609167-10-1-SUCCEEDED-default-1445073321328.jhist
2015-10-17 17:20:09,660 INFO [Thread-104] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-17 17:20:09,665 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-17 17:20:09,668 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MSRA-SA-41.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445062781478_0020
2015-10-17 17:20:09,678 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-17 17:20:10,680 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:11 RackLocal:3
2015-10-17 17:20:10,683 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0020
2015-10-17 17:20:10,692 INFO [Thread-104] org.apache.hadoop.ipc.Server: Stopping server on 20059
2015-10-17 17:20:10,694 INFO [IPC Server listener on 20059] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 20059
2015-10-17 17:20:10,695 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
2015-10-17 17:20:10,696 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
