{"cells": [{"cell_type": "code", "execution_count": 3, "id": "67c7ecf2", "metadata": {}, "outputs": [], "source": ["from welly import Well\n", "from welly import Curve\n", "\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 4, "id": "b7556252", "metadata": {}, "outputs": [], "source": ["well = Well.from_las(\"C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/L0509_comp.LAS\")"]}, {"cell_type": "code", "execution_count": 5, "id": "9ef868a4", "metadata": {}, "outputs": [{"data": {"text/html": ["<table><tr><th style=\"text-align:center;\" colspan=\"2\">L05-B-01<br><small>L05-B-01</small></th></tr><tr><td><strong>crs</strong></td><td>CRS({})</td></tr><tr><td><strong>location</strong></td><td>NORTH SEA</td></tr><tr><td><strong>country</strong></td><td>NETHERLANDS</td></tr><tr><td><strong>state</strong></td><td>NETHERLANDS</td></tr><tr><td><strong>county</strong></td><td>GLOMAR ADRIATIC XI</td></tr><tr><td><strong>latitude</strong></td><td>53.705031</td></tr><tr><td><strong>longitude</strong></td><td>4.603479</td></tr><tr><td><strong>api</strong></td><td></td></tr><tr><td><strong>ekb</strong></td><td>40.0</td></tr><tr><td><strong>tdd</strong></td><td>4906.0</td></tr><tr><td><strong>td</strong></td><td>None</td></tr><tr><td><strong>data</strong></td><td>DRHO, DT, GR, NPHI, RHOB</td></tr></table>"], "text/plain": ["Well(uwi: 'L05-B-01', name: 'L05-B-01', 5 curves: ['GR', 'DT', 'RHOB', 'DRHO', 'NPHI'])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["well"]}, {"cell_type": "code", "execution_count": 6, "id": "09adb75b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>original_mnemonic</th>\n", "      <th>mnemonic</th>\n", "      <th>unit</th>\n", "      <th>value</th>\n", "      <th>descr</th>\n", "      <th>section</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>VERS</td>\n", "      <td>VERS</td>\n", "      <td></td>\n", "      <td>2.0</td>\n", "      <td>CWLS LOG ASCII STANDARD - VERSION 2.0</td>\n", "      <td>Version</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>WRAP</td>\n", "      <td>WRAP</td>\n", "      <td></td>\n", "      <td>NO</td>\n", "      <td>ONE LINE PER DEPTH STEP</td>\n", "      <td>Version</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>STRT</td>\n", "      <td>STRT</td>\n", "      <td>M</td>\n", "      <td>81.0</td>\n", "      <td>First Index Value</td>\n", "      <td>Well</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>STOP</td>\n", "      <td>STOP</td>\n", "      <td>M</td>\n", "      <td>4879.7006</td>\n", "      <td>Last Index Value</td>\n", "      <td>Well</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>STEP</td>\n", "      <td>STEP</td>\n", "      <td>M</td>\n", "      <td>0.1</td>\n", "      <td>Frame Spacing</td>\n", "      <td>Well</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>NULL</td>\n", "      <td>NULL</td>\n", "      <td></td>\n", "      <td>-999.25</td>\n", "      <td>Absent Value</td>\n", "      <td>Well</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>WELL</td>\n", "      <td>WELL</td>\n", "      <td></td>\n", "      <td>L05-B-01</td>\n", "      <td>Well Name</td>\n", "      <td>Well</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>FLD</td>\n", "      <td>FLD</td>\n", "      <td></td>\n", "      <td>L5</td>\n", "      <td>Field Name</td>\n", "      <td>Well</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>CNTY</td>\n", "      <td>CNTY</td>\n", "      <td></td>\n", "      <td>GLOMAR ADRIATIC XI</td>\n", "      <td>County</td>\n", "      <td>Well</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>STAT</td>\n", "      <td>STAT</td>\n", "      <td></td>\n", "      <td>NETHERLANDS</td>\n", "      <td>State</td>\n", "      <td>Well</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>CTRY</td>\n", "      <td>CTRY</td>\n", "      <td></td>\n", "      <td>NETHERLANDS</td>\n", "      <td>Country</td>\n", "      <td>Well</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>LOC</td>\n", "      <td>LOC</td>\n", "      <td></td>\n", "      <td>NORTH SEA</td>\n", "      <td>Location</td>\n", "      <td>Well</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>API</td>\n", "      <td>API</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>API Number</td>\n", "      <td>Well</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>UWI</td>\n", "      <td>UWI</td>\n", "      <td></td>\n", "      <td>L05-B-01</td>\n", "      <td>UWI Number</td>\n", "      <td>Well</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>DATE</td>\n", "      <td>DATE</td>\n", "      <td></td>\n", "      <td>16-Feb-2002</td>\n", "      <td>Date</td>\n", "      <td>Well</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>COMP</td>\n", "      <td>COMP</td>\n", "      <td></td>\n", "      <td>WIN</td>\n", "      <td>Company Name</td>\n", "      <td>Well</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>SRVC</td>\n", "      <td>SRVC</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>Service Company</td>\n", "      <td>Well</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>LATI</td>\n", "      <td>LATI</td>\n", "      <td>DEG</td>\n", "      <td>53.705031</td>\n", "      <td>LATITUDE</td>\n", "      <td>Well</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>LONG</td>\n", "      <td>LONG</td>\n", "      <td>DEG</td>\n", "      <td>4.603479</td>\n", "      <td>LONGITUDE</td>\n", "      <td>Well</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>DEPT</td>\n", "      <td>DEPT</td>\n", "      <td>M</td>\n", "      <td></td>\n", "      <td>1     Index curve</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>GR</td>\n", "      <td>GR</td>\n", "      <td>GAPI</td>\n", "      <td></td>\n", "      <td>2     Gamma Ray</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>DT</td>\n", "      <td>DT</td>\n", "      <td>US/F</td>\n", "      <td></td>\n", "      <td>3     Acoustic Compressional Slowness - DTC</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>RHOB</td>\n", "      <td>RHOB</td>\n", "      <td>G/C3</td>\n", "      <td></td>\n", "      <td>4     Bulk Density</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>DRHO</td>\n", "      <td>DRHO</td>\n", "      <td>G/C3</td>\n", "      <td></td>\n", "      <td>5     Density Correction</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>NPHI</td>\n", "      <td>NPHI</td>\n", "      <td>V/V</td>\n", "      <td></td>\n", "      <td>6     Neutron Porosity</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>TDD</td>\n", "      <td>TDD</td>\n", "      <td>M</td>\n", "      <td>4906.0</td>\n", "      <td>Total Depth (Driller)</td>\n", "      <td>Parameter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>UBID</td>\n", "      <td>UBID</td>\n", "      <td></td>\n", "      <td>8552</td>\n", "      <td>Unique Borehole Id</td>\n", "      <td>Parameter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>PDAT</td>\n", "      <td>PDAT</td>\n", "      <td></td>\n", "      <td>Mean Sea Level</td>\n", "      <td>Permanent Datum</td>\n", "      <td>Parameter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>EPD</td>\n", "      <td>EPD</td>\n", "      <td>M</td>\n", "      <td>0.0</td>\n", "      <td>Elevation of Permanent Datum (PDAT) above Mean...</td>\n", "      <td>Parameter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>LMF</td>\n", "      <td>LMF</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>Logging Measured From (Name of Logging Elevati...</td>\n", "      <td>Parameter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>APD</td>\n", "      <td>APD</td>\n", "      <td>M</td>\n", "      <td>40.0</td>\n", "      <td>Elevation of Depth Reference (LMF) Above Perma...</td>\n", "      <td>Parameter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>EKB</td>\n", "      <td>EKB</td>\n", "      <td>M</td>\n", "      <td>40.0</td>\n", "      <td>Elevation of Kelly Bushing Above Mean Sea Level</td>\n", "      <td>Parameter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td></td>\n", "      <td>UNKNOWN</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>composite_curve_qc</td>\n", "      <td>Other</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   original_mnemonic mnemonic  unit               value  \\\n", "0               VERS     VERS                       2.0   \n", "1               WRAP     WRAP                        NO   \n", "2               STRT     STRT     M                81.0   \n", "3               STOP     STOP     M           4879.7006   \n", "4               STEP     STEP     M                 0.1   \n", "5               NULL     NULL                   -999.25   \n", "6               WELL     WELL                  L05-B-01   \n", "7                FLD      FLD                        L5   \n", "8               CNTY     CNTY        GLOMAR ADRIATIC XI   \n", "9               STAT     STAT               NETHERLANDS   \n", "10              CTRY     CTRY               NETHERLANDS   \n", "11               LOC      LOC                 NORTH SEA   \n", "12               API      API                             \n", "13               UWI      UWI                  L05-B-01   \n", "14              DATE     DATE               16-Feb-2002   \n", "15              COMP     COMP                       WIN   \n", "16              SRVC     SRVC                             \n", "17              LATI     LATI   DEG           53.705031   \n", "18              LONG     LONG   DEG            4.603479   \n", "19              DEPT     DEPT     M                       \n", "20                GR       GR  GAPI                       \n", "21                DT       DT  US/F                       \n", "22              RHOB     RHOB  G/C3                       \n", "23              DRHO     DRHO  G/C3                       \n", "24              NPHI     NPHI   V/V                       \n", "25               TDD      TDD     M              4906.0   \n", "26              UBID     UBID                      8552   \n", "27              PDAT     PDAT            Mean Sea Level   \n", "28               EPD      EPD     M                 0.0   \n", "29               LMF      LMF                             \n", "30               APD      APD     M                40.0   \n", "31               EKB      EKB     M                40.0   \n", "32                    UNKNOWN                             \n", "\n", "                                                descr    section  \n", "0               CWLS LOG ASCII STANDARD - VERSION 2.0    Version  \n", "1                             ONE LINE PER DEPTH STEP    Version  \n", "2                                   First Index Value       Well  \n", "3                                    Last Index Value       Well  \n", "4                                       Frame Spacing       Well  \n", "5                                        Absent Value       Well  \n", "6                                           Well Name       Well  \n", "7                                          Field Name       Well  \n", "8                                              County       Well  \n", "9                                               State       Well  \n", "10                                            Country       Well  \n", "11                                           Location       Well  \n", "12                                         API Number       Well  \n", "13                                         UWI Number       Well  \n", "14                                               Date       Well  \n", "15                                       Company Name       Well  \n", "16                                    Service Company       Well  \n", "17                                           LATITUDE       Well  \n", "18                                          LONGITUDE       Well  \n", "19                                  1     Index curve     Curves  \n", "20                                    2     Gamma Ray     Curves  \n", "21        3     Acoustic Compressional Slowness - DTC     Curves  \n", "22                                 4     Bulk Density     Curves  \n", "23                           5     Density Correction     Curves  \n", "24                             6     Neutron Porosity     Curves  \n", "25                              Total Depth (Driller)  Parameter  \n", "26                                 Unique Borehole Id  Parameter  \n", "27                                    Permanent Datum  Parameter  \n", "28  Elevation of Permanent Datum (PDAT) above Mean...  Parameter  \n", "29  Logging Measured From (Name of Logging Elevati...  Parameter  \n", "30  Elevation of Depth Reference (LMF) Above Perma...  Parameter  \n", "31    Elevation of Kelly Bushing Above Mean Sea Level  Parameter  \n", "32                                 composite_curve_qc      Other  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["well.header"]}, {"cell_type": "code", "execution_count": 7, "id": "c06c4b5e", "metadata": {}, "outputs": [{"data": {"text/plain": ["Location({'position': None, 'crs': CRS({}), 'location': 'NORTH SEA', 'country': 'NETHERLANDS', 'state': 'NETHERLANDS', 'county': 'GLOMAR ADRIATIC XI', 'latitude': 53.705031, 'longitude': 4.603479, 'api': '', 'ekb': 40.0, 'tdd': 4906.0, 'td': None, 'deviation': None})"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["well.location"]}, {"cell_type": "code", "execution_count": 8, "id": "a74d6c32", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["53.705031\n", "4.603479\n"]}], "source": ["lati = well.location.latitude\n", "long = well.location.longitude\n", "\n", "print(lati)\n", "print(long)"]}, {"cell_type": "code", "execution_count": 9, "id": "5e40cbc1", "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["well.count_curves()"]}, {"cell_type": "code", "execution_count": 10, "id": "a620079e", "metadata": {}, "outputs": [{"data": {"text/plain": ["['GR', 'DT', 'RHOB', 'DRHO', 'NPHI']"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["well._get_curve_mnemonics()"]}, {"cell_type": "code", "execution_count": 11, "id": "92d90962", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\welly\\curve.py:470: FutureWarning: Index.is_numeric is deprecated. Use pandas.api.types.is_any_real_numeric_dtype instead\n", "  if self.df.index.is_numeric() and not self.df.index.empty:\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\welly\\curve.py:470: FutureWarning: Index.is_numeric is deprecated. Use pandas.api.types.is_any_real_numeric_dtype instead\n", "  if self.df.index.is_numeric() and not self.df.index.empty:\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\welly\\curve.py:470: FutureWarning: Index.is_numeric is deprecated. Use pandas.api.types.is_any_real_numeric_dtype instead\n", "  if self.df.index.is_numeric() and not self.df.index.empty:\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\welly\\curve.py:470: FutureWarning: Index.is_numeric is deprecated. Use pandas.api.types.is_any_real_numeric_dtype instead\n", "  if self.df.index.is_numeric() and not self.df.index.empty:\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\welly\\curve.py:470: FutureWarning: Index.is_numeric is deprecated. Use pandas.api.types.is_any_real_numeric_dtype instead\n", "  if self.df.index.is_numeric() and not self.df.index.empty:\n"]}, {"data": {"text/plain": ["{'GR': Curve(mnemonic=GR, units=GAPI, start=81.0000, stop=4879.7006, step=0.0000, count=[47974]),\n", " 'DT': Curve(mnemonic=DT, units=US/F, start=81.0000, stop=4879.7006, step=0.0000, count=[21709]),\n", " 'RHOB': Curve(mnemonic=RHOB, units=G/C3, start=81.0000, stop=4879.7006, step=0.0000, count=[2075]),\n", " 'DRHO': Curve(mnemonic=DRHO, units=G/C3, start=81.0000, stop=4879.7006, step=0.0000, count=[2075]),\n", " 'NPHI': Curve(mnemonic=NPHI, units=V/V, start=81.0000, stop=4879.7006, step=0.0000, count=[3095])}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["well.data"]}, {"cell_type": "code", "execution_count": 12, "id": "e86314ea", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\welly\\curve.py:470: FutureWarning: Index.is_numeric is deprecated. Use pandas.api.types.is_any_real_numeric_dtype instead\n", "  if self.df.index.is_numeric() and not self.df.index.empty:\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\welly\\curve.py:470: FutureWarning: Index.is_numeric is deprecated. Use pandas.api.types.is_any_real_numeric_dtype instead\n", "  if self.df.index.is_numeric() and not self.df.index.empty:\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\welly\\curve.py:342: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  if self.dtypes[0] == float:\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\welly\\curve.py:347: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  rows += s.format(depth, value[0])\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\welly\\curve.py:350: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  rows += s.format(depth, value[0])\n"]}, {"data": {"text/html": ["<table><tr><th style=\"text-align:center;\" colspan=\"2\">GR [GAPI]</th></tr><tr><td style=\"text-align:center;\" colspan=\"2\">81.0000 : 4879.7006 : 0.0000</td></tr><tr><td><strong>index_units</strong></td><td>M</td></tr><tr><td><strong>code</strong></td><td>None</td></tr><tr><td><strong>description</strong></td><td>2     Gamma Ray</td></tr><tr><td><strong>log_type</strong></td><td>None</td></tr><tr><td><strong>api</strong></td><td></td></tr><tr><td><strong>date</strong></td><td>16-Feb-2002</td></tr><tr><td><strong>null</strong></td><td>-999.25</td></tr><tr><td><strong>run</strong></td><td>None</td></tr><tr><td><strong>service_company</strong></td><td></td></tr><tr><th style=\"border-top: 2px solid #000; text-align:center;\" colspan=\"2\"><strong>Stats</strong></th></tr><tr><td><strong>samples (NaNs)</strong></td><td>47988 (14)</td></tr><tr><td><strong><sub>min</sub> mean <sup>max</sup></strong></td><td><sub>12.02</sub> 36.019 <sup>218.20</sup></td></tr><tr><th style=\"border-top: 2px solid #000;\">Depth</th><th style=\"border-top: 2px solid #000;\">Value</th></tr><tr><td>81.0000</td><td>nan</td></tr><tr><td>81.1000</td><td>nan</td></tr><tr><td>81.2000</td><td>nan</td></tr><tr><td>⋮</td><td>⋮</td></tr><tr><td>4879.5006</td><td>104.0882</td></tr><tr><td>4879.6006</td><td>103.3753</td></tr><tr><td>4879.7006</td><td>102.7809</td></tr></table>"], "text/plain": ["Curve(mnemonic=GR, units=GAPI, start=81.0000, stop=4879.7006, step=0.0000, count=[47974])"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["well.data['GR']"]}, {"cell_type": "markdown", "id": "05ffb48e", "metadata": {}, "source": ["# Checking Data Quality"]}, {"cell_type": "code", "execution_count": 13, "id": "131f87aa", "metadata": {}, "outputs": [], "source": ["import welly.quality as wq"]}, {"cell_type": "code", "execution_count": 14, "id": "0e6f2f71", "metadata": {}, "outputs": [], "source": ["tests = {'Each': [wq.no_flat, wq.no_gaps, wq.not_empty],\n", "         'GR': [wq.all_positive, wq.all_between(0,250), wq.check_units(['APT', 'GAPI']),\n", "                ],\n", "                'RHOB': [wq.all_positive, wq.all_between(1.5, 3), wq.check_units(['G/CC', 'g/cm3']),\n", "                ],\n", "                 'NPHI': [wq.all_between(-0.5, 0.5), wq.check_units(['V/V', 'VOL.%']),\n", "                 ]}"]}, {"cell_type": "code", "execution_count": 15, "id": "49e5a337", "metadata": {}, "outputs": [{"data": {"text/html": ["<table><tr><th>Curve</th><th>Passed</th><th>Score</th><th>no_flat</th><th>check_units</th><th>no_gaps</th><th>all_positive</th><th>all_between</th><th>not_empty</th></tr><tr><th>GR</th><td>6 / 6</td><td>1.000</td><td style=\"background-color:#CCEECC;\">True</td><td style=\"background-color:#CCEECC;\">True</td><td style=\"background-color:#CCEECC;\">True</td><td style=\"background-color:#CCEECC;\">True</td><td style=\"background-color:#CCEECC;\">True</td><td style=\"background-color:#CCEECC;\">True</td></tr><tr><th>DT</th><td>2 / 3</td><td>0.667</td><td style=\"background-color:#CCEECC;\">True</td><td style=\"background-color:#EEEEEE;\"></td><td style=\"background-color:#FFCCCC;\">False</td><td style=\"background-color:#EEEEEE;\"></td><td style=\"background-color:#EEEEEE;\"></td><td style=\"background-color:#CCEECC;\">True</td></tr><tr><th>RHOB</th><td>5 / 6</td><td>0.833</td><td style=\"background-color:#CCEECC;\">True</td><td style=\"background-color:#FFCCCC;\">False</td><td style=\"background-color:#CCEECC;\">True</td><td style=\"background-color:#CCEECC;\">True</td><td style=\"background-color:#CCEECC;\">True</td><td style=\"background-color:#CCEECC;\">True</td></tr><tr><th>DRHO</th><td>3 / 3</td><td>1.000</td><td style=\"background-color:#CCEECC;\">True</td><td style=\"background-color:#EEEEEE;\"></td><td style=\"background-color:#CCEECC;\">True</td><td style=\"background-color:#EEEEEE;\"></td><td style=\"background-color:#EEEEEE;\"></td><td style=\"background-color:#CCEECC;\">True</td></tr><tr><th>NPHI</th><td>5 / 5</td><td>1.000</td><td style=\"background-color:#CCEECC;\">True</td><td style=\"background-color:#CCEECC;\">True</td><td style=\"background-color:#CCEECC;\">True</td><td style=\"background-color:#EEEEEE;\"></td><td style=\"background-color:#CCEECC;\">True</td><td style=\"background-color:#CCEECC;\">True</td></tr></table>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import HTML\n", "data_qc_table = well.qc_table_html(tests)\n", "HTML(data_qc_table)"]}, {"cell_type": "code", "execution_count": 16, "id": "c1925cf0", "metadata": {}, "outputs": [{"data": {"text/html": ["<table><tr><th>Curve</th><th>Passed</th><th>Score</th><th>fraction_not_nans</th></tr><tr><th>GR</th><td>0.999708260398433 / 1</td><td>1.000</td><td style=\"background-color:#EEEEEE;\">0.999708260398433</td></tr><tr><th>DT</th><td>0.4523839293156623 / 1</td><td>0.452</td><td style=\"background-color:#EEEEEE;\">0.4523839293156623</td></tr><tr><th>RHOB</th><td>0.0432399766608319 / 1</td><td>0.043</td><td style=\"background-color:#EEEEEE;\">0.0432399766608319</td></tr><tr><th>DRHO</th><td>0.0432399766608319 / 1</td><td>0.043</td><td style=\"background-color:#EEEEEE;\">0.0432399766608319</td></tr><tr><th>NPHI</th><td>0.06449529048928904 / 1</td><td>0.064</td><td style=\"background-color:#EEEEEE;\">0.06449529048928904</td></tr></table>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["tests_nans = {'Each': [wq.fraction_not_nans]}\n", "\n", "data_nans_qc_table = well.qc_table_html(tests_nans)\n", "HTML(data_nans_qc_table)"]}, {"cell_type": "code", "execution_count": 17, "id": "d23e3aea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Curve      % Complete\n", "----       --------\n", "GR         99.97%\n", "DT         45.24%\n", "RHOB       4.32%\n", "DRHO       4.32%\n", "NPHI       6.45%\n"]}], "source": ["print((f'Curve \\t % Complete').expandtabs(10))\n", "print((f'---- \\t --------'.expandtabs(10)))\n", "\n", "for k,v in well.qc_data(tests_nans).items():\n", "    for i,j in v.items():\n", "        values = round(j*100, 2)\n", "    print((f'{k} \\t {values}%').expandtabs(10))"]}, {"cell_type": "code", "execution_count": 18, "id": "a4cb60b4", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\welly\\curve.py:470: FutureWarning: Index.is_numeric is deprecated. Use pandas.api.types.is_any_real_numeric_dtype instead\n", "  if self.df.index.is_numeric() and not self.df.index.empty:\n"]}, {"data": {"image/png": "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**************************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", "text/plain": ["<Figure size 1000x1200 with 5 Axes>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["tracks = ['<PERSON>', 'G<PERSON>', 'RHOB', 'NPHI', 'DT']\n", "well.plot(tracks=tracks)"]}, {"cell_type": "code", "execution_count": 26, "id": "6302baba", "metadata": {}, "outputs": [{"data": {"text/plain": ["(5000.0, 3000.0)"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["track = ['<PERSON>', 'G<PERSON>', 'RHOB', 'NPHI', 'DT']\n", "well.plot(track=track)\n", "plt.ylim(5000, 3000)"]}, {"cell_type": "code", "execution_count": 20, "id": "c6f37835", "metadata": {}, "outputs": [], "source": ["df = well.df()"]}, {"cell_type": "code", "execution_count": 21, "id": "3600f7f2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>GR</th>\n", "      <th>DT</th>\n", "      <th>RHOB</th>\n", "      <th>DRHO</th>\n", "      <th>NPHI</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>4797.0000000000</td>\n", "      <td>2170.0000000000</td>\n", "      <td>207.0000000000</td>\n", "      <td>207.0000000000</td>\n", "      <td>309.0000000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>36.0487982300</td>\n", "      <td>68.3767319116</td>\n", "      <td>2.5594916779</td>\n", "      <td>0.0176576564</td>\n", "      <td>0.1636017228</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>23.2208116824</td>\n", "      <td>5.6885362774</td>\n", "      <td>0.1239327943</td>\n", "      <td>0.0458511985</td>\n", "      <td>0.0791964007</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>12.0199110000</td>\n", "      <td>46.8437516720</td>\n", "      <td>1.9546740000</td>\n", "      <td>-0.1300330000</td>\n", "      <td>0.0335770320</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>22.7054630000</td>\n", "      <td>67.3827693840</td>\n", "      <td>2.4973186500</td>\n", "      <td>-0.0155278720</td>\n", "      <td>0.1012420000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>29.8225670000</td>\n", "      <td>68.6552209255</td>\n", "      <td>2.5655387100</td>\n", "      <td>0.0026197380</td>\n", "      <td>0.1309782360</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>37.4326958240</td>\n", "      <td>69.3066997593</td>\n", "      <td>2.6622683160</td>\n", "      <td>0.0473911640</td>\n", "      <td>0.2308796940</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>217.6526823380</td>\n", "      <td>120.1704100000</td>\n", "      <td>2.7581690000</td>\n", "      <td>0.1279050000</td>\n", "      <td>0.3853830000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    GR               DT            RHOB            DRHO  \\\n", "count  4797.0000000000  2170.0000000000  207.0000000000  207.0000000000   \n", "mean     36.0487982300    68.3767319116    2.5594916779    0.0176576564   \n", "std      23.2208116824     5.6885362774    0.1239327943    0.0458511985   \n", "min      12.0199110000    46.8437516720    1.9546740000   -0.1300330000   \n", "25%      22.7054630000    67.3827693840    2.4973186500   -0.0155278720   \n", "50%      29.8225670000    68.6552209255    2.5655387100    0.0026197380   \n", "75%      37.4326958240    69.3066997593    2.6622683160    0.0473911640   \n", "max     217.6526823380   120.1704100000    2.7581690000    0.1279050000   \n", "\n", "                 NPHI  \n", "count  309.0000000000  \n", "mean     0.1636017228  \n", "std      0.0791964007  \n", "min      0.0335770320  \n", "25%      0.1012420000  \n", "50%      0.1309782360  \n", "75%      0.2308796940  \n", "max      0.3853830000  "]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe()"]}, {"cell_type": "code", "execution_count": 22, "id": "9c9ee1da", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Index: 4799 entries, 81.0 to 4879.0\n", "Data columns (total 5 columns):\n", " #   Column  Non-Null Count  Dtype  \n", "---  ------  --------------  -----  \n", " 0   GR      4797 non-null   float64\n", " 1   DT      2170 non-null   float64\n", " 2   RHOB    207 non-null    float64\n", " 3   DRHO    207 non-null    float64\n", " 4   NPHI    309 non-null    float64\n", "dtypes: float64(5)\n", "memory usage: 225.0 KB\n"]}], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "2a0f8d34", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5b5e3771", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0a9648b3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "668d1022", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "54650458", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f93269a2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}