2015-10-19 15:50:16,106 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 15:50:16,309 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 15:50:16,309 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-19 15:50:16,419 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-19 15:50:16,419 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445182159119_0013, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@3d05ffdb)
2015-10-19 15:50:16,716 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-19 15:50:18,997 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445182159119_0013
2015-10-19 15:50:21,138 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-19 15:50:22,529 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-19 15:50:22,919 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@637205b7
2015-10-19 15:50:34,654 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/pageinput2.txt:268435456+134217728
2015-10-19 15:50:35,451 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-19 15:50:35,451 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-19 15:50:35,451 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-19 15:50:35,451 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-19 15:50:35,451 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-19 15:50:35,748 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-19 15:51:12,125 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 15:51:12,125 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 48249276; bufvoid = 104857600
2015-10-19 15:51:12,125 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 17305200(69220800); length = 8909197/6553600
2015-10-19 15:51:12,125 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 57318028 kvi 14329500(57318000)
2015-10-19 15:52:12,128 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-19 15:52:12,675 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 57318028 kv 14329500(57318000) kvi 12129788(48519152)
2015-10-19 15:52:25,832 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 15:52:25,832 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 57318028; bufend = 686843; bufvoid = 104857600
2015-10-19 15:52:25,832 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14329500(57318000); kvend = 5414592(21658368); length = 8914909/6553600
2015-10-19 15:52:25,832 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 9755595 kvi 2438892(9755568)
2015-10-19 15:53:20,679 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-19 15:53:20,679 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 9755595 kv 2438892(9755568) kvi 240952(963808)
2015-10-19 15:53:34,726 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 15:53:34,726 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 9755595; bufend = 58006021; bufvoid = 104857600
2015-10-19 15:53:34,726 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 2438892(9755568); kvend = 19744380(78977520); length = 8908913/6553600
2015-10-19 15:53:34,726 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 67074757 kvi 16768684(67074736)
2015-10-19 15:54:30,183 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-19 15:54:30,339 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 67074757 kv 16768684(67074736) kvi 14558340(58233360)
2015-10-19 15:54:39,605 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 15:54:39,605 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 67074757; bufend = 10447270; bufvoid = 104857600
2015-10-19 15:54:39,605 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 16768684(67074736); kvend = 7854692(31418768); length = 8913993/6553600
2015-10-19 15:54:39,605 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 19516006 kvi 4878996(19515984)
2015-10-19 15:55:22,295 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-19 15:55:22,388 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 19516006 kv 4878996(19515984) kvi 2677056(10708224)
2015-10-19 15:55:31,061 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 15:55:31,061 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 19516006; bufend = 67756598; bufvoid = 104857600
2015-10-19 15:55:31,061 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 4878996(19515984); kvend = 22182024(88728096); length = 8911373/6553600
2015-10-19 15:55:31,061 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 76825334 kvi 19206328(76825312)
