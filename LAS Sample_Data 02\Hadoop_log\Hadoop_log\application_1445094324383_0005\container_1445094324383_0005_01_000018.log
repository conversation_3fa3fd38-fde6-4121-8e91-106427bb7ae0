2015-10-17 23:13:17,503 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 23:13:17,643 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 23:13:17,643 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-17 23:13:17,768 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 23:13:17,768 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445094324383_0005, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@11628d93)
2015-10-17 23:13:18,112 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 23:13:19,112 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445094324383_0005
2015-10-17 23:13:20,487 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 23:13:21,816 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 23:13:22,003 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@2a405b3a
2015-10-17 23:13:23,738 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/wordcount2.txt:1073741824+134217728
2015-10-17 23:13:23,909 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-17 23:13:23,909 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-17 23:13:23,909 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-17 23:13:23,909 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-17 23:13:23,909 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-17 23:13:23,956 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-17 23:13:46,880 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 23:13:46,880 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 34175830; bufvoid = 104857600
2015-10-17 23:13:46,880 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 13786836(55147344); length = 12427561/6553600
2015-10-17 23:13:46,880 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 44661579 kvi 11165388(44661552)
