"""
Demo script to showcase the ONGC Knowledge Management System capabilities
"""

import time
from semantic_search_engine import SemanticSearchEngine
from tabulate import tabulate

def print_header(title):
    """Print a formatted header"""
    print("\n" + "="*60)
    print(f"🔍 {title}")
    print("="*60)

def print_subheader(title):
    """Print a formatted subheader"""
    print(f"\n📋 {title}")
    print("-"*40)

def demo_search_capabilities():
    """Demonstrate search capabilities"""
    print_header("ONGC Knowledge Management System - Live Demo")
    
    # Initialize search engine
    print("🚀 Initializing search engine...")
    engine = SemanticSearchEngine()
    engine.load_articles()
    engine.load_model()
    engine.generate_embeddings()
    
    print("✅ System ready!")
    
    # Demo queries
    demo_queries = [
        {
            "query": "drilling fluid optimization",
            "description": "Finding articles about drilling fluid optimization"
        },
        {
            "query": "offshore corrosion prevention",
            "description": "Searching for offshore corrosion prevention techniques"
        },
        {
            "query": "reservoir characterization using logs",
            "description": "Looking for reservoir characterization methods"
        },
        {
            "query": "enhanced oil recovery methods",
            "description": "Finding EOR techniques for mature fields"
        },
        {
            "query": "wellbore stability in shale",
            "description": "Searching for wellbore stability solutions"
        }
    ]
    
    print_subheader("Semantic Search Demonstrations")
    
    for i, demo in enumerate(demo_queries, 1):
        print(f"\n🔎 Demo {i}: {demo['description']}")
        print(f"Query: '{demo['query']}'")
        
        start_time = time.time()
        results = engine.search(demo['query'], top_k=3)
        search_time = time.time() - start_time
        
        print(f"⏱️  Search completed in {search_time:.3f} seconds")
        print(f"📊 Found {len(results)} relevant articles")
        
        if results:
            print("\nTop Results:")
            for j, result in enumerate(results, 1):
                print(f"  {j}. {result['title']}")
                print(f"     Category: {result['category']} | Score: {result['similarity_score']:.3f}")
                print(f"     Preview: {result['content'][:100]}...")
        
        print("\n" + "-"*50)
        time.sleep(1)  # Pause for readability

def demo_performance():
    """Demonstrate performance metrics"""
    print_subheader("Performance Demonstration")
    
    engine = SemanticSearchEngine()
    engine.load_articles()
    engine.load_model()
    engine.generate_embeddings()
    
    test_query = "drilling fluid optimization high temperature"
    num_tests = 5
    
    print(f"🔄 Running {num_tests} performance tests...")
    print(f"Query: '{test_query}'")
    
    times = []
    for i in range(num_tests):
        start_time = time.time()
        results = engine.search(test_query, top_k=5)
        search_time = time.time() - start_time
        times.append(search_time)
        print(f"  Test {i+1}: {search_time:.3f}s ({len(results)} results)")
    
    avg_time = sum(times) / len(times)
    min_time = min(times)
    max_time = max(times)
    
    print(f"\n📈 Performance Summary:")
    print(f"  Average: {avg_time:.3f} seconds")
    print(f"  Fastest: {min_time:.3f} seconds")
    print(f"  Slowest: {max_time:.3f} seconds")

def demo_statistics():
    """Show system statistics"""
    print_subheader("System Statistics")
    
    engine = SemanticSearchEngine()
    engine.load_articles()
    engine.load_model()
    engine.generate_embeddings()
    
    stats = engine.get_statistics()
    
    print("📊 Knowledge Base Overview:")
    print(f"  📚 Total Articles: {stats['total_articles']}")
    print(f"  📂 Categories: {stats['total_categories']}")
    print(f"  🔢 Embedding Dimensions: {stats['embedding_dimensions']}")
    print(f"  🤖 AI Model: {stats['model_name']}")
    
    print(f"\n📋 Article Categories:")
    for category in stats['categories']:
        count = sum(1 for article in engine.articles if article['category'] == category)
        print(f"  • {category}: {count} articles")

def demo_comparison():
    """Demonstrate semantic vs keyword search"""
    print_subheader("Semantic vs Keyword Search Comparison")
    
    engine = SemanticSearchEngine()
    engine.load_articles()
    engine.load_model()
    engine.generate_embeddings()
    
    test_cases = [
        {
            "query": "preventing rust in ocean drilling",
            "semantic_explanation": "Understands 'rust' = corrosion, 'ocean' = offshore"
        },
        {
            "query": "rock analysis using electrical measurements",
            "semantic_explanation": "Connects 'electrical measurements' to 'resistivity logs'"
        },
        {
            "query": "increasing oil production from old wells",
            "semantic_explanation": "Links to 'enhanced oil recovery' and 'mature fields'"
        }
    ]
    
    print("🧠 Semantic Understanding Examples:")
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. Query: '{case['query']}'")
        print(f"   Semantic Understanding: {case['semantic_explanation']}")
        
        results = engine.search(case['query'], top_k=2)
        if results:
            print(f"   Best Match: {results[0]['title']} (Score: {results[0]['similarity_score']:.3f})")
        else:
            print("   No matches found")

def main():
    """Main demo function"""
    try:
        print("🎬 Starting ONGC Knowledge Management System Demo")
        print("This demo showcases the AI-powered semantic search capabilities")
        
        # Run demonstrations
        demo_search_capabilities()
        demo_performance()
        demo_statistics()
        demo_comparison()
        
        print_header("Demo Complete!")
        print("🎉 The ONGC Knowledge Management System is ready for use!")
        print("\nNext Steps:")
        print("1. 🌐 Web Interface: streamlit run streamlit_app.py")
        print("2. 💻 CLI Interface: python cli_app.py search --interactive")
        print("3. 🔍 Direct Search: python cli_app.py search 'your query'")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        print("Please ensure all dependencies are installed: python setup.py")

if __name__ == "__main__":
    main()
