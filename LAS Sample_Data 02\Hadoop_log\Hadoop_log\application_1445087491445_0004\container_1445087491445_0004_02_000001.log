2015-10-17 21:47:42,612 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445087491445_0004_000002
2015-10-17 21:47:43,049 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 21:47:43,049 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 4 cluster_timestamp: 1445087491445 } attemptId: 2 } keyId: -1547346236)
2015-10-17 21:47:43,299 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 21:47:44,580 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 21:47:44,705 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 21:47:44,752 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 21:47:44,752 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 21:47:44,752 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 21:47:44,768 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 21:47:44,768 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 21:47:44,768 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 21:47:44,768 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 21:47:44,768 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 21:47:44,846 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 21:47:44,893 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 21:47:44,940 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 21:47:44,955 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 21:47:44,955 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Recovery is enabled. Will try to recover from previous life on best effort basis.
2015-10-17 21:47:45,002 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 21:47:45,002 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Previous history file is at hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0004/job_1445087491445_0004_1.jhist
2015-10-17 21:47:46,502 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445087491445_0004_m_000002
2015-10-17 21:47:46,502 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445087491445_0004_m_000001
2015-10-17 21:47:46,502 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445087491445_0004_m_000000
2015-10-17 21:47:46,502 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445087491445_0004_m_000006
2015-10-17 21:47:46,502 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445087491445_0004_m_000005
2015-10-17 21:47:46,502 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445087491445_0004_m_000004
2015-10-17 21:47:46,502 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445087491445_0004_m_000003
2015-10-17 21:47:46,502 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445087491445_0004_m_000010
2015-10-17 21:47:46,502 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445087491445_0004_m_000009
2015-10-17 21:47:46,502 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445087491445_0004_m_000008
2015-10-17 21:47:46,502 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445087491445_0004_m_000007
2015-10-17 21:47:46,502 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445087491445_0004_m_000012
2015-10-17 21:47:46,502 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445087491445_0004_m_000011
2015-10-17 21:47:46,502 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read completed tasks from history 13
2015-10-17 21:47:46,581 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 21:47:46,674 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 21:47:46,799 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 21:47:46,799 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 21:47:46,815 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445087491445_0004 to jobTokenSecretManager
2015-10-17 21:47:47,127 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445087491445_0004 because: not enabled; too many maps; too much input;
2015-10-17 21:47:47,159 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445087491445_0004 = 1751822336. Number of splits = 13
2015-10-17 21:47:47,159 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445087491445_0004 = 1
2015-10-17 21:47:47,159 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0004Job Transitioned from NEW to INITED
2015-10-17 21:47:47,159 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445087491445_0004.
2015-10-17 21:47:47,206 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 21:47:47,221 INFO [Socket Reader #1 for port 10549] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 10549
2015-10-17 21:47:47,252 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 21:47:47,252 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 21:47:47,252 INFO [IPC Server listener on 10549] org.apache.hadoop.ipc.Server: IPC Server listener on 10549: starting
2015-10-17 21:47:47,252 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MSRA-SA-39.fareast.corp.microsoft.com/**************:10549
2015-10-17 21:47:47,346 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 21:47:47,362 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 21:47:47,377 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 21:47:47,377 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 21:47:47,377 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 21:47:47,377 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 21:47:47,377 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 21:47:47,393 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 10556
2015-10-17 21:47:47,393 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 21:47:47,440 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\2\Jetty_0_0_0_0_10556_mapreduce____.cuyby6\webapp
2015-10-17 21:47:47,768 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:10556
2015-10-17 21:47:47,768 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 10556
2015-10-17 21:47:48,284 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 21:47:48,284 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445087491445_0004
2015-10-17 21:47:48,284 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 21:47:48,299 INFO [Socket Reader #1 for port 10559] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 10559
2015-10-17 21:47:48,299 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 21:47:48,299 INFO [IPC Server listener on 10559] org.apache.hadoop.ipc.Server: IPC Server listener on 10559: starting
2015-10-17 21:47:48,331 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 21:47:48,331 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 21:47:48,331 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 21:47:48,377 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-17 21:47:48,487 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 21:47:48,487 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 21:47:48,487 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 21:47:48,502 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 21:47:48,502 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0004Job Transitioned from INITED to SETUP
2015-10-17 21:47:48,502 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 21:47:48,534 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0004Job Transitioned from SETUP to RUNNING
2015-10-17 21:47:48,534 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445087491445_0004_m_000000 from prior app attempt, status was SUCCEEDED
2015-10-17 21:47:48,627 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,659 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000000_1] using containerId: [container_1445087491445_0004_01_000017 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:47:48,659 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000000_1 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,659 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445087491445_0004, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0004/job_1445087491445_0004_2.jhist
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000000_0] using containerId: [container_1445087491445_0004_01_000004 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:55629]
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000000_0 TaskAttempt Transitioned from NEW to KILLED
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000000_1
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000000 Task Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445087491445_0004_m_000001 from prior app attempt, status was SUCCEEDED
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000001_0] using containerId: [container_1445087491445_0004_01_000002 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000001_0 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000001_0
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000001 Task Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445087491445_0004_m_000002 from prior app attempt, status was SUCCEEDED
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000002_0] using containerId: [container_1445087491445_0004_01_000006 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000002_0 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000002_0
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000002 Task Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445087491445_0004_m_000003 from prior app attempt, status was SUCCEEDED
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000003_0] using containerId: [container_1445087491445_0004_01_000009 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:57365]
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000003_0 TaskAttempt Transitioned from NEW to KILLED
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000003_2] using containerId: [container_1445087491445_0004_01_000023 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000003_2 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000003_1] using containerId: [container_1445087491445_0004_01_000019 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000003_1 TaskAttempt Transitioned from NEW to KILLED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000003_2
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000003 Task Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445087491445_0004_m_000004 from prior app attempt, status was SUCCEEDED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000004_0] using containerId: [container_1445087491445_0004_01_000007 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000004_0 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000004_0
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000004 Task Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445087491445_0004_m_000005 from prior app attempt, status was SUCCEEDED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000005_0] using containerId: [container_1445087491445_0004_01_000005 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:55629]
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000005_0 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000005_0
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000005 Task Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445087491445_0004_m_000006 from prior app attempt, status was SUCCEEDED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000006_0] using containerId: [container_1445087491445_0004_01_000010 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:57365]
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000006_0 TaskAttempt Transitioned from NEW to KILLED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000006_1] using containerId: [container_1445087491445_0004_01_000021 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000006_1 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000006_1
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000006 Task Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445087491445_0004_m_000007 from prior app attempt, status was SUCCEEDED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000007_0] using containerId: [container_1445087491445_0004_01_000011 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:57365]
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000007_0 TaskAttempt Transitioned from NEW to KILLED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000007_1] using containerId: [container_1445087491445_0004_01_000018 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000007_1 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000007_1
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000007 Task Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445087491445_0004_m_000008 from prior app attempt, status was SUCCEEDED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000008_0] using containerId: [container_1445087491445_0004_01_000012 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:57365]
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000008_0 TaskAttempt Transitioned from NEW to KILLED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000008_1] using containerId: [container_1445087491445_0004_01_000020 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000008_1 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000008_1
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000008 Task Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445087491445_0004_m_000009 from prior app attempt, status was SUCCEEDED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000009_0] using containerId: [container_1445087491445_0004_01_000008 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000009_0 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000009_0
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000009 Task Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445087491445_0004_m_000010 from prior app attempt, status was SUCCEEDED
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000010_0] using containerId: [container_1445087491445_0004_01_000003 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000010_0 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000010_0
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000010 Task Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445087491445_0004_m_000011 from prior app attempt, status was SUCCEEDED
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000011_0] using containerId: [container_1445087491445_0004_01_000013 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000011_0 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000011_0
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000011 Task Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445087491445_0004_m_000012 from prior app attempt, status was SUCCEEDED
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000012_0] using containerId: [container_1445087491445_0004_01_000014 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:57365]
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000012_0 TaskAttempt Transitioned from NEW to KILLED
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000012_1] using containerId: [container_1445087491445_0004_01_000022 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000012_1 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000012_1
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000012 Task Transitioned from NEW to SUCCEEDED
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-17 21:47:48,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-17 21:47:48,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-17 21:47:48,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-17 21:47:48,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-17 21:47:48,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-17 21:47:48,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-17 21:47:48,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-17 21:47:48,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 12
2015-10-17 21:47:48,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 13
2015-10-17 21:47:48,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_r_000000_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:47:48,721 INFO [Thread-51] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 21:47:49,487 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:13 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 21:47:49,549 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: TaskAttempt killed because it ran on unusable node 04DN8IQ.fareast.corp.microsoft.com:55452. AttemptId:attempt_1445087491445_0004_m_000002_0
2015-10-17 21:47:49,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: TaskAttempt killed because it ran on unusable node 04DN8IQ.fareast.corp.microsoft.com:55452. AttemptId:attempt_1445087491445_0004_m_000004_0
2015-10-17 21:47:49,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: TaskAttempt killed because it ran on unusable node 04DN8IQ.fareast.corp.microsoft.com:55452. AttemptId:attempt_1445087491445_0004_m_000009_0
2015-10-17 21:47:49,565 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:17408, vCores:-3>
2015-10-17 21:47:49,565 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-17 21:47:49,565 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-17 21:47:49,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000002_0 TaskAttempt Transitioned from SUCCEEDED to KILLED
2015-10-17 21:47:49,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000004_0 TaskAttempt Transitioned from SUCCEEDED to KILLED
2015-10-17 21:47:49,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000009_0 TaskAttempt Transitioned from SUCCEEDED to KILLED
2015-10-17 21:47:49,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:49,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:49,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000002 Task Transitioned from SUCCEEDED to SCHEDULED
2015-10-17 21:47:49,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:49,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:49,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000004 Task Transitioned from SUCCEEDED to SCHEDULED
2015-10-17 21:47:49,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:49,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:49,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000009 Task Transitioned from SUCCEEDED to SCHEDULED
2015-10-17 21:47:49,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000002_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:47:49,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000004_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:47:49,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000009_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:47:49,565 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 21:47:49,565 INFO [Thread-51] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 21:47:50,565 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:3 ScheduledReds:1 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 21:47:50,565 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=5 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:17408, vCores:-3> knownNMs=3
2015-10-17 21:47:51,565 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:47:51,565 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 21:47:51,565 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_02_000002 to attempt_1445087491445_0004_r_000000_1000
2015-10-17 21:47:51,565 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:1 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 21:47:51,628 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:51,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0004/job.jar
2015-10-17 21:47:51,659 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0004/job.xml
2015-10-17 21:47:51,659 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 21:47:51,659 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 21:47:51,659 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 21:47:51,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_r_000000_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:47:51,706 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_02_000002 taskAttempt attempt_1445087491445_0004_r_000000_1000
2015-10-17 21:47:51,721 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_r_000000_1000
2015-10-17 21:47:51,721 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:47:51,815 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_r_000000_1000 : 13562
2015-10-17 21:47:51,831 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_r_000000_1000] using containerId: [container_1445087491445_0004_02_000002 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 21:47:51,831 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_r_000000_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:47:51,831 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_r_000000
2015-10-17 21:47:51,831 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:47:52,565 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=1 release= 0 newContainers=3 finishedContainers=0 resourcelimit=<memory:13312, vCores:-7> knownNMs=3
2015-10-17 21:47:52,565 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 3
2015-10-17 21:47:52,565 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_02_000003 to attempt_1445087491445_0004_m_000002_1000
2015-10-17 21:47:52,565 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_02_000004 to attempt_1445087491445_0004_m_000004_1000
2015-10-17 21:47:52,565 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_02_000005 to attempt_1445087491445_0004_m_000009_1000
2015-10-17 21:47:52,565 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:4 ContRel:0 HostLocal:3 RackLocal:0
2015-10-17 21:47:52,581 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,581 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000002_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:47:52,581 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,581 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000004_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:47:52,581 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:47:52,581 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000009_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:47:52,581 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_02_000003 taskAttempt attempt_1445087491445_0004_m_000002_1000
2015-10-17 21:47:52,581 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000002_1000
2015-10-17 21:47:52,581 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_02_000004 taskAttempt attempt_1445087491445_0004_m_000004_1000
2015-10-17 21:47:52,581 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000004_1000
2015-10-17 21:47:52,581 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_02_000005 taskAttempt attempt_1445087491445_0004_m_000009_1000
2015-10-17 21:47:52,581 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000009_1000
2015-10-17 21:47:52,581 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:47:52,581 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:47:52,581 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:47:52,612 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000004_1000 : 13562
2015-10-17 21:47:52,612 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000004_1000] using containerId: [container_1445087491445_0004_02_000004 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 21:47:52,612 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000004_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:47:52,612 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000004
2015-10-17 21:47:52,612 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:47:52,612 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000009_1000 : 13562
2015-10-17 21:47:52,612 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000009_1000] using containerId: [container_1445087491445_0004_02_000005 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 21:47:52,612 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000009_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:47:52,612 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000009
2015-10-17 21:47:52,612 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:47:52,628 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000002_1000 : 13562
2015-10-17 21:47:52,628 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000002_1000] using containerId: [container_1445087491445_0004_02_000003 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 21:47:52,628 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000002_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:47:52,628 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000002
2015-10-17 21:47:52,628 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:47:53,565 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:13312, vCores:-7> knownNMs=3
2015-10-17 21:47:54,034 INFO [Socket Reader #1 for port 10559] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:47:54,065 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_r_000002 asked for a task
2015-10-17 21:47:54,065 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_r_000002 given task: attempt_1445087491445_0004_r_000000_1000
2015-10-17 21:47:54,768 INFO [Socket Reader #1 for port 10559] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:47:54,784 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000004 asked for a task
2015-10-17 21:47:54,784 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000004 given task: attempt_1445087491445_0004_m_000004_1000
2015-10-17 21:47:54,784 INFO [Socket Reader #1 for port 10559] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:47:54,800 INFO [IPC Server handler 23 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000005 asked for a task
2015-10-17 21:47:54,800 INFO [IPC Server handler 23 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000005 given task: attempt_1445087491445_0004_m_000009_1000
2015-10-17 21:47:55,815 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 0 maxEvents 10000
2015-10-17 21:47:56,550 INFO [Socket Reader #1 for port 10559] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:47:56,581 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000003 asked for a task
2015-10-17 21:47:56,581 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000003 given task: attempt_1445087491445_0004_m_000002_1000
2015-10-17 21:47:56,831 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:47:57,831 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:47:58,831 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:47:59,847 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:00,847 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:01,722 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.025641028
2015-10-17 21:48:01,847 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:02,644 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.13098624
2015-10-17 21:48:02,675 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.13101342
2015-10-17 21:48:02,847 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:03,534 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0004_m_000002
2015-10-17 21:48:03,534 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 21:48:03,534 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445087491445_0004_m_000002
2015-10-17 21:48:03,534 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:48:03,534 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:48:03,534 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000002_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:48:03,566 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:4 ContRel:0 HostLocal:3 RackLocal:0
2015-10-17 21:48:03,566 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:7168, vCores:-13> knownNMs=3
2015-10-17 21:48:03,847 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:04,769 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.051282056
2015-10-17 21:48:04,863 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:04,956 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.13101996
2015-10-17 21:48:05,675 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.13098624
2015-10-17 21:48:05,706 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.13101342
2015-10-17 21:48:05,863 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:06,863 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:07,785 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.10256411
2015-10-17 21:48:07,863 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:07,972 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.13101996
2015-10-17 21:48:08,691 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.13098624
2015-10-17 21:48:08,722 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.13101342
2015-10-17 21:48:08,863 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:09,863 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:10,816 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.15384616
2015-10-17 21:48:10,878 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:11,003 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.16737153
2015-10-17 21:48:11,707 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.23919825
2015-10-17 21:48:11,738 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.23920888
2015-10-17 21:48:11,878 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:12,878 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:13,847 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.1794872
2015-10-17 21:48:13,879 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:14,035 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.23923585
2015-10-17 21:48:14,743 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.23919825
2015-10-17 21:48:14,774 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.23920888
2015-10-17 21:48:14,884 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:15,899 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:16,868 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.20512822
2015-10-17 21:48:16,899 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:17,056 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.23923585
2015-10-17 21:48:17,774 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.23919825
2015-10-17 21:48:17,806 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.23920888
2015-10-17 21:48:17,899 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:18,915 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:19,899 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:48:19,915 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:20,087 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.26060057
2015-10-17 21:48:20,790 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.3473995
2015-10-17 21:48:20,837 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.34739637
2015-10-17 21:48:20,915 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:21,915 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:22,915 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:22,931 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:48:23,103 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.34744066
2015-10-17 21:48:23,821 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.3473995
2015-10-17 21:48:23,868 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.34739637
2015-10-17 21:48:23,915 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:24,947 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:25,947 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:25,962 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:48:26,118 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.34744066
2015-10-17 21:48:26,837 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.3473995
2015-10-17 21:48:26,884 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.34739637
2015-10-17 21:48:26,947 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:27,947 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:28,947 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:28,978 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:48:29,134 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.3543306
2015-10-17 21:48:29,853 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.45560816
2015-10-17 21:48:29,900 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.45559853
2015-10-17 21:48:29,947 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:30,947 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:31,947 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:32,009 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:48:32,166 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.45562834
2015-10-17 21:48:32,884 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.45560816
2015-10-17 21:48:32,931 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.45559853
2015-10-17 21:48:32,947 INFO [IPC Server handler 23 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:33,962 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:34,963 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:35,025 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:48:35,181 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.45562834
2015-10-17 21:48:35,916 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.45560816
2015-10-17 21:48:35,947 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.45559853
2015-10-17 21:48:35,963 INFO [IPC Server handler 23 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:36,963 INFO [IPC Server handler 23 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:37,963 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:38,041 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:48:38,213 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.45562834
2015-10-17 21:48:38,947 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.5638118
2015-10-17 21:48:38,963 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:38,978 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.5467482
2015-10-17 21:48:39,963 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:40,963 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:41,072 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:48:41,228 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.5618482
2015-10-17 21:48:41,963 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.5638118
2015-10-17 21:48:41,963 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:41,994 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.5638114
2015-10-17 21:48:42,963 INFO [IPC Server handler 3 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:43,963 INFO [IPC Server handler 3 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:44,088 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:48:44,244 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.5638227
2015-10-17 21:48:44,979 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:44,979 INFO [IPC Server handler 4 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.5638118
2015-10-17 21:48:45,010 INFO [IPC Server handler 18 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.5638114
2015-10-17 21:48:45,979 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:46,979 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:47,119 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:48:47,276 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.5638227
2015-10-17 21:48:47,697 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.5638118
2015-10-17 21:48:47,979 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:48,010 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.667
2015-10-17 21:48:48,041 INFO [IPC Server handler 24 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.61989284
2015-10-17 21:48:48,697 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.61989284
2015-10-17 21:48:48,979 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:49,979 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:50,135 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:48:50,291 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.58794665
2015-10-17 21:48:50,979 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:51,026 INFO [IPC Server handler 4 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.667
2015-10-17 21:48:51,057 INFO [IPC Server handler 18 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.667
2015-10-17 21:48:51,323 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.58794665
2015-10-17 21:48:51,979 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:52,979 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:53,166 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:48:53,338 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.667
2015-10-17 21:48:53,979 INFO [IPC Server handler 3 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:54,073 INFO [IPC Server handler 9 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.667
2015-10-17 21:48:54,104 INFO [IPC Server handler 24 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.667
2015-10-17 21:48:54,979 INFO [IPC Server handler 3 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:55,995 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:56,198 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:48:56,354 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.667
2015-10-17 21:48:56,995 INFO [IPC Server handler 3 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:57,088 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.667
2015-10-17 21:48:57,120 INFO [IPC Server handler 10 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.667
2015-10-17 21:48:57,995 INFO [IPC Server handler 3 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:58,995 INFO [IPC Server handler 3 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:48:59,214 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:48:59,385 INFO [IPC Server handler 3 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.667
2015-10-17 21:48:59,995 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:00,104 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.6850196
2015-10-17 21:49:00,135 INFO [IPC Server handler 11 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.676457
2015-10-17 21:49:00,995 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:01,995 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:02,245 INFO [IPC Server handler 3 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:49:02,417 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.667
2015-10-17 21:49:02,995 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:03,136 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.7217374
2015-10-17 21:49:03,167 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.713379
2015-10-17 21:49:03,995 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:04,995 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:05,261 INFO [IPC Server handler 3 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:49:05,433 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.6785828
2015-10-17 21:49:05,995 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:06,151 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.76053643
2015-10-17 21:49:06,183 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.7524722
2015-10-17 21:49:07,011 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:08,011 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:08,276 INFO [IPC Server handler 3 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:49:08,448 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.71195084
2015-10-17 21:49:09,011 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:09,167 INFO [IPC Server handler 4 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.8010574
2015-10-17 21:49:09,198 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.7934159
2015-10-17 21:49:10,011 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:11,011 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:11,308 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:49:11,464 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.7537905
2015-10-17 21:49:12,011 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:12,198 INFO [IPC Server handler 9 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.8424829
2015-10-17 21:49:12,214 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.83405846
2015-10-17 21:49:12,573 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:49:12,573 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_02_000006 to attempt_1445087491445_0004_m_000002_1001
2015-10-17 21:49:12,573 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:5 ContRel:0 HostLocal:4 RackLocal:0
2015-10-17 21:49:12,573 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:49:12,573 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000002_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:49:12,573 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_02_000006 taskAttempt attempt_1445087491445_0004_m_000002_1001
2015-10-17 21:49:12,573 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000002_1001
2015-10-17 21:49:12,573 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:49:12,589 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000002_1001 : 13562
2015-10-17 21:49:12,589 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000002_1001] using containerId: [container_1445087491445_0004_02_000006 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 21:49:12,589 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000002_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:49:12,589 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000002
2015-10-17 21:49:13,011 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:13,245 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0004_m_000009
2015-10-17 21:49:13,245 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 21:49:13,245 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445087491445_0004_m_000009
2015-10-17 21:49:13,245 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:49:13,245 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:49:13,245 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000009_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:49:13,573 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:5 ContRel:0 HostLocal:4 RackLocal:0
2015-10-17 21:49:13,573 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:11264, vCores:-17> knownNMs=4
2015-10-17 21:49:14,011 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:14,324 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:49:14,480 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.7958275
2015-10-17 21:49:14,574 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:49:14,574 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_02_000007 to attempt_1445087491445_0004_m_000009_1001
2015-10-17 21:49:14,574 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:5 RackLocal:0
2015-10-17 21:49:14,574 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:49:14,574 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000009_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:49:14,574 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_02_000007 taskAttempt attempt_1445087491445_0004_m_000009_1001
2015-10-17 21:49:14,574 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000009_1001
2015-10-17 21:49:14,574 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:49:14,730 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000009_1001 : 13562
2015-10-17 21:49:14,730 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000009_1001] using containerId: [container_1445087491445_0004_02_000007 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 21:49:14,730 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000009_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:49:14,730 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000009
2015-10-17 21:49:15,011 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:15,214 INFO [IPC Server handler 9 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.8840306
2015-10-17 21:49:15,230 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.874677
2015-10-17 21:49:15,574 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:10240, vCores:-18> knownNMs=4
2015-10-17 21:49:16,011 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:17,011 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:17,339 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:49:17,511 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.83711237
2015-10-17 21:49:18,011 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:18,230 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.9253554
2015-10-17 21:49:18,261 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.91524655
2015-10-17 21:49:18,746 INFO [Socket Reader #1 for port 10559] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:49:18,808 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000006 asked for a task
2015-10-17 21:49:18,808 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000006 given task: attempt_1445087491445_0004_m_000002_1001
2015-10-17 21:49:19,011 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:20,011 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:20,355 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:49:20,527 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.8783798
2015-10-17 21:49:21,027 INFO [IPC Server handler 4 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:21,246 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.9663233
2015-10-17 21:49:21,277 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.95530176
2015-10-17 21:49:22,027 INFO [IPC Server handler 4 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:23,027 INFO [IPC Server handler 4 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:23,386 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:49:23,558 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.8984481
2015-10-17 21:49:24,027 INFO [IPC Server handler 4 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:24,293 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 0.98663956
2015-10-17 21:49:24,308 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.97512245
2015-10-17 21:49:24,965 INFO [Socket Reader #1 for port 10559] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:49:25,027 INFO [IPC Server handler 4 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:25,027 INFO [IPC Server handler 9 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000007 asked for a task
2015-10-17 21:49:25,027 INFO [IPC Server handler 9 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000007 given task: attempt_1445087491445_0004_m_000009_1001
2015-10-17 21:49:26,027 INFO [IPC Server handler 4 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:26,230 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000004_1000 is : 1.0
2015-10-17 21:49:26,230 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0004_m_000004_1000
2015-10-17 21:49:26,230 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000004_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:49:26,230 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_02_000004 taskAttempt attempt_1445087491445_0004_m_000004_1000
2015-10-17 21:49:26,230 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000004_1000
2015-10-17 21:49:26,230 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:49:26,246 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000004_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:49:26,246 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000004_1000
2015-10-17 21:49:26,246 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:49:26,246 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-17 21:49:26,418 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:49:26,574 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:11 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:5 RackLocal:0
2015-10-17 21:49:26,605 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.9150199
2015-10-17 21:49:27,027 INFO [IPC Server handler 4 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 21 maxEvents 10000
2015-10-17 21:49:27,340 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 0.99743974
2015-10-17 21:49:27,574 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_02_000004
2015-10-17 21:49:27,574 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:11 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:5 RackLocal:0
2015-10-17 21:49:27,574 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000004_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:49:27,777 INFO [IPC Server handler 9 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000009_1000 is : 1.0
2015-10-17 21:49:27,777 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0004_m_000009_1000
2015-10-17 21:49:27,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000009_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:49:27,777 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_02_000005 taskAttempt attempt_1445087491445_0004_m_000009_1000
2015-10-17 21:49:27,777 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000009_1000
2015-10-17 21:49:27,777 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:49:27,793 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000009_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:49:27,793 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000009_1000
2015-10-17 21:49:27,793 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445087491445_0004_m_000009_1001
2015-10-17 21:49:27,793 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:49:27,793 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 12
2015-10-17 21:49:27,793 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000009_1001 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:49:27,793 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_02_000007 taskAttempt attempt_1445087491445_0004_m_000009_1001
2015-10-17 21:49:27,793 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000009_1001
2015-10-17 21:49:27,793 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:49:28,027 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 22 maxEvents 10000
2015-10-17 21:49:28,449 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000009_1001 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:49:28,449 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:49:28,465 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out1/_temporary/2/_temporary/attempt_1445087491445_0004_m_000009_1001
2015-10-17 21:49:28,465 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000009_1001 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:49:28,574 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:12 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:5 RackLocal:0
2015-10-17 21:49:29,043 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 23 maxEvents 10000
2015-10-17 21:49:29,449 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.23076925
2015-10-17 21:49:29,512 INFO [Socket Reader #1 for port 10559] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 10559: readAndProcess from client *********** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:49:29,574 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_02_000005
2015-10-17 21:49:29,574 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:12 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:5 RackLocal:0
2015-10-17 21:49:29,574 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000009_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:49:29,621 INFO [IPC Server handler 4 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.93507445
2015-10-17 21:49:30,043 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 23 maxEvents 10000
2015-10-17 21:49:30,574 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_02_000007
2015-10-17 21:49:30,574 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:12 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:5 RackLocal:0
2015-10-17 21:49:30,574 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000009_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:49:31,043 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 23 maxEvents 10000
2015-10-17 21:49:32,043 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 23 maxEvents 10000
2015-10-17 21:49:32,465 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.25641027
2015-10-17 21:49:32,637 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 0.9661628
2015-10-17 21:49:33,043 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 23 maxEvents 10000
2015-10-17 21:49:34,043 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 23 maxEvents 10000
2015-10-17 21:49:35,043 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 23 maxEvents 10000
2015-10-17 21:49:35,496 INFO [IPC Server handler 3 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.2820513
2015-10-17 21:49:35,527 INFO [IPC Server handler 23 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000002_1000 is : 1.0
2015-10-17 21:49:35,527 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0004_m_000002_1000
2015-10-17 21:49:35,527 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000002_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:49:35,527 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_02_000003 taskAttempt attempt_1445087491445_0004_m_000002_1000
2015-10-17 21:49:35,527 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000002_1000
2015-10-17 21:49:35,527 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:49:35,543 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000002_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:49:35,543 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000002_1000
2015-10-17 21:49:35,543 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445087491445_0004_m_000002_1001
2015-10-17 21:49:35,543 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:49:35,543 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 13
2015-10-17 21:49:35,543 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000002_1001 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:49:35,543 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_02_000006 taskAttempt attempt_1445087491445_0004_m_000002_1001
2015-10-17 21:49:35,543 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000002_1001
2015-10-17 21:49:35,543 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 21:49:35,574 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:13 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:5 RackLocal:0
2015-10-17 21:49:35,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000002_1001 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:49:35,996 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:49:35,996 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out1/_temporary/2/_temporary/attempt_1445087491445_0004_m_000002_1001
2015-10-17 21:49:35,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000002_1001 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:49:36,043 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 23 maxEvents 10000
2015-10-17 21:49:36,574 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_02_000003
2015-10-17 21:49:36,574 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:13 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:5 RackLocal:0
2015-10-17 21:49:36,574 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000002_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:49:36,934 INFO [Socket Reader #1 for port 10559] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 10559: readAndProcess from client *********** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:49:37,043 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:38,043 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:38,528 INFO [IPC Server handler 23 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.2820513
2015-10-17 21:49:38,575 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_02_000006
2015-10-17 21:49:38,575 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:13 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:5 RackLocal:0
2015-10-17 21:49:38,575 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000002_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:49:39,059 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:40,059 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:41,059 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:41,559 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.2820513
2015-10-17 21:49:42,059 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:43,059 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:44,059 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:44,590 INFO [IPC Server handler 23 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:49:45,059 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:46,059 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:47,075 INFO [IPC Server handler 14 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:47,637 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:49:48,075 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:49,075 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:50,075 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:50,669 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:49:51,075 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:52,075 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:53,075 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:53,700 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:49:54,075 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:55,075 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:56,075 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:56,716 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:49:57,091 INFO [IPC Server handler 14 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:58,091 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:59,091 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:49:59,747 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:50:00,091 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:01,091 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:02,091 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:02,794 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:50:03,091 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:04,091 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:05,091 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:05,810 INFO [IPC Server handler 23 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:50:06,091 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:07,107 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:08,107 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:08,826 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:50:09,107 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:10,107 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:11,107 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:11,857 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:50:12,107 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:13,107 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:14,107 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:14,873 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:50:15,107 INFO [IPC Server handler 9 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:16,107 INFO [IPC Server handler 9 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:17,107 INFO [IPC Server handler 9 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:17,889 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:50:18,107 INFO [IPC Server handler 9 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:19,107 INFO [IPC Server handler 9 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:20,107 INFO [IPC Server handler 9 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:20,920 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:50:21,123 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:22,123 INFO [IPC Server handler 9 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:23,123 INFO [IPC Server handler 9 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:23,936 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:50:24,123 INFO [IPC Server handler 9 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:25,123 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:26,123 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:26,967 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:50:27,123 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:28,123 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:29,123 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:29,983 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:50:30,123 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:31,124 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:32,124 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:32,999 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:50:33,124 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:34,124 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:35,139 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:36,014 INFO [IPC Server handler 18 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:50:36,139 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:37,139 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:38,139 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:39,030 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:50:39,139 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:40,140 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:41,140 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:42,061 INFO [IPC Server handler 24 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:50:42,140 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:43,140 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:44,140 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:45,093 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:50:45,140 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:46,140 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:47,155 INFO [IPC Server handler 23 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:48,124 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:50:48,155 INFO [IPC Server handler 23 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:49,156 INFO [IPC Server handler 10 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:50,156 INFO [IPC Server handler 10 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:51,156 INFO [IPC Server handler 10 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:51,156 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:50:52,156 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:53,156 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:54,156 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:54,187 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:50:55,156 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:56,171 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:57,171 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:57,203 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:50:58,171 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:50:59,172 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:00,172 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:00,218 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:51:01,172 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:02,172 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:03,172 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:03,250 INFO [IPC Server handler 11 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:51:04,172 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:05,172 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:06,172 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:06,297 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:51:07,172 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:08,188 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:09,188 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:09,328 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:51:10,188 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:11,188 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:11,578 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: TaskAttempt killed because it ran on unusable node 04DN8IQ.fareast.corp.microsoft.com:55452. AttemptId:attempt_1445087491445_0004_m_000002_0
2015-10-17 21:51:11,578 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: TaskAttempt killed because it ran on unusable node 04DN8IQ.fareast.corp.microsoft.com:55452. AttemptId:attempt_1445087491445_0004_m_000004_0
2015-10-17 21:51:11,578 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: TaskAttempt killed because it ran on unusable node 04DN8IQ.fareast.corp.microsoft.com:55452. AttemptId:attempt_1445087491445_0004_m_000009_0
2015-10-17 21:51:12,188 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:12,344 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:51:13,188 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:14,188 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:15,188 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:15,360 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:51:16,188 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:17,191 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:18,191 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:18,394 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:51:19,194 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:20,194 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:21,194 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:21,428 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:51:22,197 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:23,197 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:24,213 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:24,447 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:51:25,217 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:26,217 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:27,217 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:27,483 INFO [IPC Server handler 24 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:51:28,220 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:29,220 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:30,220 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:30,517 INFO [IPC Server handler 4 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:51:31,220 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:32,236 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:32,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: TaskAttempt killed because it ran on unusable node MININT-FNANLI5.fareast.corp.microsoft.com:55629. AttemptId:attempt_1445087491445_0004_m_000005_0
2015-10-17 21:51:32,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000005_0 TaskAttempt Transitioned from SUCCEEDED to KILLED
2015-10-17 21:51:32,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:32,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:32,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000005 Task Transitioned from SUCCEEDED to SCHEDULED
2015-10-17 21:51:32,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000005_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:51:33,236 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 24 maxEvents 10000
2015-10-17 21:51:33,548 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1000 is : 0.30769232
2015-10-17 21:51:33,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:12 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:5 RackLocal:0
2015-10-17 21:51:33,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-12> knownNMs=2
2015-10-17 21:51:34,236 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1000. startIndex 25 maxEvents 10000
2015-10-17 21:51:34,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 21:51:34,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 21:51:34,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Preempting attempt_1445087491445_0004_r_000000_1000
2015-10-17 21:51:34,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_r_000000_1000 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 21:51:34,611 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_02_000002 taskAttempt attempt_1445087491445_0004_r_000000_1000
2015-10-17 21:51:34,611 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_r_000000_1000
2015-10-17 21:51:34,611 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:51:34,627 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_r_000000_1000 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 21:51:34,627 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 21:51:34,642 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out1/_temporary/2/_temporary/attempt_1445087491445_0004_r_000000_1000
2015-10-17 21:51:34,642 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_r_000000_1000 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 21:51:34,642 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_r_000000_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 21:51:34,689 INFO [Socket Reader #1 for port 10559] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 10559: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 21:51:35,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:12 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:5 RackLocal:0
2015-10-17 21:51:35,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-13>
2015-10-17 21:51:35,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.9230769 totalResourceLimit:<memory:1024, vCores:-12> finalMapResourceLimit:<memory:512, vCores:-6> finalReduceResourceLimit:<memory:512, vCores:-6> netScheduledMapResource:<memory:1024, vCores:1> netScheduledReduceResource:<memory:1024, vCores:1>
2015-10-17 21:51:36,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_02_000002
2015-10-17 21:51:36,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce preemption successful attempt_1445087491445_0004_r_000000_1000
2015-10-17 21:51:36,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:51:36,614 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_r_000000_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:51:36,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_02_000008 to attempt_1445087491445_0004_m_000005_1000
2015-10-17 21:51:36,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-13>
2015-10-17 21:51:36,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-17 21:51:36,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:1 AssignedReds:0 CompletedMaps:12 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:6 RackLocal:0
2015-10-17 21:51:36,614 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:36,614 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000005_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:51:36,614 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_02_000008 taskAttempt attempt_1445087491445_0004_m_000005_1000
2015-10-17 21:51:36,614 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_m_000005_1000
2015-10-17 21:51:36,614 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:51:36,645 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_m_000005_1000 : 13562
2015-10-17 21:51:36,645 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_m_000005_1000] using containerId: [container_1445087491445_0004_02_000008 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 21:51:36,645 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000005_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:51:36,645 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_m_000005
2015-10-17 21:51:36,645 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 21:51:37,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=5 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-13> knownNMs=2
2015-10-17 21:51:39,458 INFO [Socket Reader #1 for port 10559] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:51:39,489 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_m_000008 asked for a task
2015-10-17 21:51:39,489 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_m_000008 given task: attempt_1445087491445_0004_m_000005_1000
2015-10-17 21:51:44,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 21:51:44,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 21:51:44,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0004_02_000009 to attempt_1445087491445_0004_r_000000_1001
2015-10-17 21:51:44,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:12 CompletedReds:0 ContAlloc:8 ContRel:0 HostLocal:6 RackLocal:0
2015-10-17 21:51:44,614 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 21:51:44,614 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_r_000000_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 21:51:44,614 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0004_02_000009 taskAttempt attempt_1445087491445_0004_r_000000_1001
2015-10-17 21:51:44,614 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0004_r_000000_1001
2015-10-17 21:51:44,614 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:51:44,630 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0004_r_000000_1001 : 13562
2015-10-17 21:51:44,630 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0004_r_000000_1001] using containerId: [container_1445087491445_0004_02_000009 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 21:51:44,630 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_r_000000_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 21:51:44,630 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0004_r_000000
2015-10-17 21:51:45,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0004: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-12> knownNMs=2
2015-10-17 21:51:46,708 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.13101853
2015-10-17 21:51:47,115 INFO [Socket Reader #1 for port 10559] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0004 (auth:SIMPLE)
2015-10-17 21:51:47,130 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0004_r_000009 asked for a task
2015-10-17 21:51:47,130 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0004_r_000009 given task: attempt_1445087491445_0004_r_000000_1001
2015-10-17 21:51:48,396 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 0 maxEvents 10000
2015-10-17 21:51:49,411 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:51:49,724 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.13101853
2015-10-17 21:51:50,412 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:51:51,412 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:51:52,427 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:51:52,755 INFO [IPC Server handler 23 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.21244207
2015-10-17 21:51:53,427 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:51:54,365 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.051282056
2015-10-17 21:51:54,427 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:51:55,427 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:51:55,771 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.2392399
2015-10-17 21:51:56,427 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:51:57,381 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.10256411
2015-10-17 21:51:57,427 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:51:58,443 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:51:58,802 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.2392399
2015-10-17 21:51:59,443 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:00,396 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.15384616
2015-10-17 21:52:00,443 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:01,443 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:01,834 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.2392399
2015-10-17 21:52:02,443 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:03,443 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.1794872
2015-10-17 21:52:03,443 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:04,443 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:04,881 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.3289236
2015-10-17 21:52:05,459 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:06,459 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:06,475 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.23076925
2015-10-17 21:52:07,490 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:07,912 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.34744292
2015-10-17 21:52:08,490 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:09,490 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:09,522 INFO [IPC Server handler 11 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.2820513
2015-10-17 21:52:10,490 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:10,944 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.34744292
2015-10-17 21:52:11,506 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:12,506 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:12,553 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.2820513
2015-10-17 21:52:13,506 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:13,975 INFO [IPC Server handler 14 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.34744292
2015-10-17 21:52:14,506 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:15,506 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:15,584 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:52:16,506 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:17,006 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.4301272
2015-10-17 21:52:17,522 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:18,522 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:18,616 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:52:19,522 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:20,022 INFO [IPC Server handler 14 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.45563385
2015-10-17 21:52:20,522 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:21,522 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:21,663 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:52:22,522 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:23,069 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.45563385
2015-10-17 21:52:23,522 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:24,522 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:24,694 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:52:25,538 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:26,100 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.45563385
2015-10-17 21:52:26,538 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:27,538 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:27,726 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:52:28,538 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:29,147 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.5008107
2015-10-17 21:52:29,538 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:30,538 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:30,773 INFO [IPC Server handler 10 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:52:31,538 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:32,194 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.563847
2015-10-17 21:52:32,538 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:33,554 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:33,804 INFO [IPC Server handler 9 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:52:34,554 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:35,226 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.563847
2015-10-17 21:52:35,554 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:36,554 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:36,835 INFO [IPC Server handler 10 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:52:37,554 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:38,242 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.563847
2015-10-17 21:52:38,554 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:39,554 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:39,867 INFO [IPC Server handler 24 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:52:40,570 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:41,289 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.563847
2015-10-17 21:52:41,570 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:42,570 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:42,898 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:52:43,570 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:44,320 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.6252317
2015-10-17 21:52:44,570 INFO [IPC Server handler 11 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:45,570 INFO [IPC Server handler 11 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:45,945 INFO [IPC Server handler 4 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:52:46,148 INFO [IPC Server handler 14 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.6252317
2015-10-17 21:52:46,570 INFO [IPC Server handler 11 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:47,351 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.667
2015-10-17 21:52:47,586 INFO [IPC Server handler 11 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:48,586 INFO [IPC Server handler 11 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:48,976 INFO [IPC Server handler 14 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:52:49,586 INFO [IPC Server handler 11 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:50,383 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.667
2015-10-17 21:52:50,586 INFO [IPC Server handler 11 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:51,586 INFO [IPC Server handler 11 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:52,008 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:52:52,586 INFO [IPC Server handler 11 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:53,430 INFO [IPC Server handler 11 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.667
2015-10-17 21:52:53,586 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:54,586 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:55,055 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:52:55,586 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:56,461 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.667
2015-10-17 21:52:56,602 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:57,602 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:58,086 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:52:58,602 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:52:59,508 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.667
2015-10-17 21:52:59,602 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:00,602 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:01,133 INFO [IPC Server handler 23 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:53:01,602 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:02,539 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.68316966
2015-10-17 21:53:02,602 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:03,602 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:04,165 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:53:04,618 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:05,586 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.7028575
2015-10-17 21:53:05,618 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:06,618 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:07,212 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:53:07,618 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:08,618 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.722404
2015-10-17 21:53:08,618 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:09,618 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:10,243 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:53:10,618 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:11,618 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:11,649 INFO [IPC Server handler 14 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.74215287
2015-10-17 21:53:12,618 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:13,290 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:53:13,634 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:14,634 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:14,696 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.7592187
2015-10-17 21:53:15,634 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:16,321 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:53:16,634 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:17,634 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:17,728 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.7794108
2015-10-17 21:53:18,634 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:19,353 INFO [IPC Server handler 18 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:53:19,634 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:20,634 INFO [IPC Server handler 3 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:20,775 INFO [IPC Server handler 10 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.79630226
2015-10-17 21:53:21,650 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:22,400 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:53:22,650 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:23,650 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:23,806 INFO [IPC Server handler 23 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.8125063
2015-10-17 21:53:24,650 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:25,447 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:53:25,650 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:26,650 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:26,853 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.8285643
2015-10-17 21:53:27,650 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:28,494 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:53:28,666 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:29,666 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:29,884 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.84449
2015-10-17 21:53:30,666 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:31,525 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:53:31,666 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:32,666 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:32,916 INFO [IPC Server handler 4 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.86059785
2015-10-17 21:53:33,666 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:34,572 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:53:34,666 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:35,666 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:35,963 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.8767688
2015-10-17 21:53:36,681 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:37,619 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:53:37,682 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:38,682 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:39,010 INFO [IPC Server handler 24 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.8927638
2015-10-17 21:53:39,682 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:40,666 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:53:40,682 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:41,682 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:42,057 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.90881735
2015-10-17 21:53:42,682 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:43,682 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:43,697 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:53:44,697 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:45,104 INFO [IPC Server handler 18 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.9248041
2015-10-17 21:53:45,697 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:46,697 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:46,744 INFO [IPC Server handler 11 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:53:47,698 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:48,135 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.9408325
2015-10-17 21:53:48,698 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:49,698 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:49,776 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:53:50,698 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:51,182 INFO [IPC Server handler 9 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.9567915
2015-10-17 21:53:51,713 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:52,713 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:52,823 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:53:53,713 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:54,213 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.97287226
2015-10-17 21:53:54,713 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:55,713 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:55,854 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:53:56,714 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:57,260 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 0.98907995
2015-10-17 21:53:57,714 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:58,714 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:53:58,901 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:53:59,495 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_m_000005_1000 is : 1.0
2015-10-17 21:53:59,495 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0004_m_000005_1000
2015-10-17 21:53:59,495 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000005_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 21:53:59,495 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_02_000008 taskAttempt attempt_1445087491445_0004_m_000005_1000
2015-10-17 21:53:59,495 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_m_000005_1000
2015-10-17 21:53:59,495 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 21:53:59,511 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_m_000005_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 21:53:59,511 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_m_000005_1000
2015-10-17 21:53:59,526 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 21:53:59,526 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 13
2015-10-17 21:53:59,620 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:13 CompletedReds:0 ContAlloc:8 ContRel:0 HostLocal:6 RackLocal:0
2015-10-17 21:53:59,729 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 25 maxEvents 10000
2015-10-17 21:54:00,620 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0004_02_000008
2015-10-17 21:54:00,620 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:13 CompletedReds:0 ContAlloc:8 ContRel:0 HostLocal:6 RackLocal:0
2015-10-17 21:54:00,620 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0004_m_000005_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 21:54:00,729 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 26 maxEvents 10000
2015-10-17 21:54:01,729 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 26 maxEvents 10000
2015-10-17 21:54:01,932 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:54:02,729 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 26 maxEvents 10000
2015-10-17 21:54:03,729 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 26 maxEvents 10000
2015-10-17 21:54:04,729 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 26 maxEvents 10000
2015-10-17 21:54:04,964 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:54:05,730 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 26 maxEvents 10000
2015-10-17 21:54:06,745 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 26 maxEvents 10000
2015-10-17 21:54:07,745 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0004_r_000000_1001. startIndex 26 maxEvents 10000
2015-10-17 21:54:07,995 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:54:08,058 INFO [IPC Server handler 14 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.30769232
2015-10-17 21:54:11,042 INFO [IPC Server handler 3 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.33728123
2015-10-17 21:54:14,074 INFO [IPC Server handler 14 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.34197232
2015-10-17 21:54:17,121 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.34665045
2015-10-17 21:54:20,168 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.3513231
2015-10-17 21:54:23,199 INFO [IPC Server handler 23 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.35617238
2015-10-17 21:54:26,230 INFO [IPC Server handler 23 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.3611369
2015-10-17 21:54:29,262 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.36584476
2015-10-17 21:54:32,309 INFO [IPC Server handler 24 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.37055942
2015-10-17 21:54:35,340 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.3752732
2015-10-17 21:54:38,387 INFO [IPC Server handler 4 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.37995026
2015-10-17 21:54:41,418 INFO [IPC Server handler 10 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.3846574
2015-10-17 21:54:44,465 INFO [IPC Server handler 18 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.3893556
2015-10-17 21:54:47,497 INFO [IPC Server handler 9 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.3940691
2015-10-17 21:54:50,544 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.39878944
2015-10-17 21:54:53,591 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.40347034
2015-10-17 21:54:56,622 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.40815902
2015-10-17 21:54:59,669 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.41285822
2015-10-17 21:55:02,701 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.41754746
2015-10-17 21:55:05,748 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.42223966
2015-10-17 21:55:08,779 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.4269247
2015-10-17 21:55:11,826 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.43161356
2015-10-17 21:55:14,857 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.4363129
2015-10-17 21:55:17,904 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.44101065
2015-10-17 21:55:20,936 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.44569272
2015-10-17 21:55:23,983 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.45038626
2015-10-17 21:55:27,014 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.45508572
2015-10-17 21:55:30,045 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.46002203
2015-10-17 21:55:33,092 INFO [IPC Server handler 11 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.46485305
2015-10-17 21:55:36,124 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.4695293
2015-10-17 21:55:39,171 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.474209
2015-10-17 21:55:42,202 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.47889143
2015-10-17 21:55:45,249 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.4836096
2015-10-17 21:55:48,281 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.4883365
2015-10-17 21:55:51,328 INFO [IPC Server handler 10 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.4930555
2015-10-17 21:55:54,359 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.49776086
2015-10-17 21:55:57,406 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.50247276
2015-10-17 21:56:00,453 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.50719976
2015-10-17 21:56:03,484 INFO [IPC Server handler 3 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.51192987
2015-10-17 21:56:06,531 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.5166508
2015-10-17 21:56:09,563 INFO [IPC Server handler 4 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.521367
2015-10-17 21:56:12,610 INFO [IPC Server handler 24 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.5260708
2015-10-17 21:56:15,641 INFO [IPC Server handler 24 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.5307827
2015-10-17 21:56:18,688 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.5355253
2015-10-17 21:56:21,735 INFO [IPC Server handler 14 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.54024726
2015-10-17 21:56:24,766 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.54495776
2015-10-17 21:56:27,813 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.5496657
2015-10-17 21:56:30,860 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.5543675
2015-10-17 21:56:33,892 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.5591816
2015-10-17 21:56:36,939 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.5640861
2015-10-17 21:56:39,970 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.56884027
2015-10-17 21:56:43,017 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.57353735
2015-10-17 21:56:46,049 INFO [IPC Server handler 23 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.57822925
2015-10-17 21:56:49,096 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.582925
2015-10-17 21:56:52,143 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.5876592
2015-10-17 21:56:55,174 INFO [IPC Server handler 9 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.59237885
2015-10-17 21:56:58,221 INFO [IPC Server handler 18 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.5970904
2015-10-17 21:57:01,252 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.6017853
2015-10-17 21:57:04,299 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.6064608
2015-10-17 21:57:07,331 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.6111804
2015-10-17 21:57:10,378 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.61591053
2015-10-17 21:57:13,425 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.62061584
2015-10-17 21:57:16,456 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.6252877
2015-10-17 21:57:19,503 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.6299615
2015-10-17 21:57:22,534 INFO [IPC Server handler 11 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.63462245
2015-10-17 21:57:25,581 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.63934517
2015-10-17 21:57:28,628 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.64406395
2015-10-17 21:57:31,660 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.6487409
2015-10-17 21:57:34,707 INFO [IPC Server handler 3 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.6534072
2015-10-17 21:57:37,738 INFO [IPC Server handler 10 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.6580726
2015-10-17 21:57:40,770 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.66301274
2015-10-17 21:57:43,223 INFO [IPC Server handler 24 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.66301274
2015-10-17 21:57:43,817 INFO [IPC Server handler 24 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.6666758
2015-10-17 21:57:46,848 INFO [IPC Server handler 24 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.6695142
2015-10-17 21:57:49,864 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.67285496
2015-10-17 21:57:52,895 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.67622423
2015-10-17 21:57:55,926 INFO [IPC Server handler 14 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.6795752
2015-10-17 21:57:58,942 INFO [IPC Server handler 14 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.68291897
2015-10-17 21:58:01,973 INFO [IPC Server handler 14 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.68627673
2015-10-17 21:58:05,005 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.6896221
2015-10-17 21:58:08,036 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.69296235
2015-10-17 21:58:11,068 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.69631165
2015-10-17 21:58:14,083 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.6996493
2015-10-17 21:58:17,115 INFO [IPC Server handler 14 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.7029911
2015-10-17 21:58:20,146 INFO [IPC Server handler 14 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.70635235
2015-10-17 21:58:23,177 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.709709
2015-10-17 21:58:26,209 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.71305275
2015-10-17 21:58:29,225 INFO [IPC Server handler 3 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.71639323
2015-10-17 21:58:32,256 INFO [IPC Server handler 10 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.7197396
2015-10-17 21:58:35,287 INFO [IPC Server handler 14 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.7230963
2015-10-17 21:58:38,319 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.7264503
2015-10-17 21:58:41,350 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.7297975
2015-10-17 21:58:44,381 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.73260224
2015-10-17 21:58:47,413 INFO [IPC Server handler 24 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.7340774
2015-10-17 21:58:50,460 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.7357312
2015-10-17 21:58:53,476 INFO [IPC Server handler 25 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.73850423
2015-10-17 21:58:56,507 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.7418607
2015-10-17 21:58:59,554 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.7452097
2015-10-17 21:59:02,570 INFO [IPC Server handler 17 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.7485655
2015-10-17 21:59:05,601 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.7519121
2015-10-17 21:59:08,632 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.7552569
2015-10-17 21:59:11,664 INFO [IPC Server handler 23 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.7586159
2015-10-17 21:59:14,695 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.7619279
2015-10-17 21:59:17,727 INFO [IPC Server handler 4 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.7651236
2015-10-17 21:59:20,758 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.76823515
2015-10-17 21:59:23,789 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.7715772
2015-10-17 21:59:26,821 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.7748983
2015-10-17 21:59:29,852 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.778264
2015-10-17 21:59:32,868 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.78162265
2015-10-17 21:59:35,899 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.78497195
2015-10-17 21:59:38,931 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.78831846
2015-10-17 21:59:41,962 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.79167366
2015-10-17 21:59:44,993 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.7950339
2015-10-17 21:59:48,025 INFO [IPC Server handler 14 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.7983955
2015-10-17 21:59:51,056 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.80073583
2015-10-17 21:59:54,087 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.80225277
2015-10-17 21:59:57,119 INFO [IPC Server handler 18 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.80434495
2015-10-17 22:00:00,150 INFO [IPC Server handler 18 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.80753475
2015-10-17 22:00:03,182 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.8108912
2015-10-17 22:00:06,213 INFO [IPC Server handler 4 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.81425047
2015-10-17 22:00:09,244 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.81760657
2015-10-17 22:00:12,276 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.8209565
2015-10-17 22:00:15,307 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.8243045
2015-10-17 22:00:18,323 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.82765245
2015-10-17 22:00:21,354 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.8310121
2015-10-17 22:00:24,386 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.8343706
2015-10-17 22:00:27,417 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.83772266
2015-10-17 22:00:30,433 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.8410632
2015-10-17 22:00:33,464 INFO [IPC Server handler 18 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.84442157
2015-10-17 22:00:36,495 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.8477757
2015-10-17 22:00:39,527 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.8511689
2015-10-17 22:00:42,558 INFO [IPC Server handler 2 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.85453695
2015-10-17 22:00:45,590 INFO [IPC Server handler 9 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.8578761
2015-10-17 22:00:48,621 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.86121505
2015-10-17 22:00:51,652 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.864569
2015-10-17 22:00:54,684 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.86771655
2015-10-17 22:00:57,715 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.86933434
2015-10-17 22:01:00,747 INFO [IPC Server handler 7 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.8708324
2015-10-17 22:01:03,778 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.8730734
2015-10-17 22:01:06,810 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.87614846
2015-10-17 22:01:09,841 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.87949675
2015-10-17 22:01:12,873 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.8828636
2015-10-17 22:01:15,904 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.88621867
2015-10-17 22:01:18,935 INFO [IPC Server handler 22 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.8895749
2015-10-17 22:01:21,967 INFO [IPC Server handler 18 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.89293605
2015-10-17 22:01:24,983 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.8962861
2015-10-17 22:01:28,014 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.8996308
2015-10-17 22:01:31,046 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.9029492
2015-10-17 22:01:34,077 INFO [IPC Server handler 15 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.9062561
2015-10-17 22:01:37,109 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.90958613
2015-10-17 22:01:40,141 INFO [IPC Server handler 18 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.9129406
2015-10-17 22:01:43,176 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.91628474
2015-10-17 22:01:46,208 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.9196352
2015-10-17 22:01:49,239 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.9229791
2015-10-17 22:01:52,255 INFO [IPC Server handler 27 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.92526877
2015-10-17 22:01:55,286 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.92767155
2015-10-17 22:01:58,302 INFO [IPC Server handler 8 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.93112934
2015-10-17 22:02:01,318 INFO [IPC Server handler 20 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.93454564
2015-10-17 22:02:04,365 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.9374285
2015-10-17 22:02:07,381 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.9388636
2015-10-17 22:02:10,428 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.9392351
2015-10-17 22:02:13,459 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.9419843
2015-10-17 22:02:16,478 INFO [IPC Server handler 0 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.94526935
2015-10-17 22:02:19,525 INFO [IPC Server handler 9 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.94825494
2015-10-17 22:02:22,556 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.95105845
2015-10-17 22:02:25,572 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.9543431
2015-10-17 22:02:28,604 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.95765275
2015-10-17 22:02:31,635 INFO [IPC Server handler 16 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.9609972
2015-10-17 22:02:34,651 INFO [IPC Server handler 5 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.96402395
2015-10-17 22:02:37,683 INFO [IPC Server handler 21 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.96731234
2015-10-17 22:02:40,715 INFO [IPC Server handler 19 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.9706268
2015-10-17 22:02:43,755 INFO [IPC Server handler 4 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.97398925
2015-10-17 22:02:46,786 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.9773003
2015-10-17 22:02:49,817 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.98063195
2015-10-17 22:02:52,849 INFO [IPC Server handler 18 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.98395234
2015-10-17 22:02:55,880 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.9866373
2015-10-17 22:02:58,912 INFO [IPC Server handler 13 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.9893665
2015-10-17 22:03:01,943 INFO [IPC Server handler 12 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.99145746
2015-10-17 22:03:04,975 INFO [IPC Server handler 28 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.9945655
2015-10-17 22:03:07,991 INFO [IPC Server handler 1 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.9963511
2015-10-17 22:03:11,038 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 0.9978148
2015-10-17 22:03:14,069 INFO [IPC Server handler 29 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 1.0
2015-10-17 22:03:14,428 INFO [IPC Server handler 26 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445087491445_0004_r_000000_1001
2015-10-17 22:03:14,428 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_r_000000_1001 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-17 22:03:14,428 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445087491445_0004_r_000000_1001 given a go for committing the task output.
2015-10-17 22:03:14,428 INFO [IPC Server handler 3 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445087491445_0004_r_000000_1001
2015-10-17 22:03:14,428 INFO [IPC Server handler 3 on 10559] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445087491445_0004_r_000000_1001:true
2015-10-17 22:03:14,460 INFO [IPC Server handler 6 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0004_r_000000_1001 is : 1.0
2015-10-17 22:03:14,460 INFO [IPC Server handler 11 on 10559] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0004_r_000000_1001
2015-10-17 22:03:14,460 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_r_000000_1001 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:03:14,460 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0004_02_000009 taskAttempt attempt_1445087491445_0004_r_000000_1001
2015-10-17 22:03:14,460 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0004_r_000000_1001
2015-10-17 22:03:14,475 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:03:14,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0004_r_000000_1001 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:03:14,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0004_r_000000_1001
2015-10-17 22:03:14,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0004_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:03:14,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 14
2015-10-17 22:03:14,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0004Job Transitioned from RUNNING to COMMITTING
2015-10-17 22:03:14,491 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-17 22:03:14,569 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-17 22:03:14,585 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0004Job Transitioned from COMMITTING to SUCCEEDED
2015-10-17 22:03:14,585 INFO [Thread-113] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-17 22:03:14,585 INFO [Thread-113] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-17 22:03:14,585 INFO [Thread-113] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-17 22:03:14,585 INFO [Thread-113] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-17 22:03:14,585 INFO [Thread-113] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-17 22:03:14,585 INFO [Thread-113] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-17 22:03:14,585 INFO [Thread-113] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-17 22:03:14,678 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:13 CompletedReds:1 ContAlloc:8 ContRel:0 HostLocal:6 RackLocal:0
2015-10-17 22:03:14,694 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0004/job_1445087491445_0004_2.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445087491445_0004-1445088243078-msrabi-word+count-1445090594569-13-1-SUCCEEDED-default-1445088253210.jhist_tmp
2015-10-17 22:03:14,819 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445087491445_0004-1445088243078-msrabi-word+count-1445090594569-13-1-SUCCEEDED-default-1445088253210.jhist_tmp
2015-10-17 22:03:14,819 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0004/job_1445087491445_0004_2_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445087491445_0004_conf.xml_tmp
2015-10-17 22:03:14,928 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445087491445_0004_conf.xml_tmp
2015-10-17 22:03:14,944 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445087491445_0004.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445087491445_0004.summary
2015-10-17 22:03:14,944 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445087491445_0004_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445087491445_0004_conf.xml
2015-10-17 22:03:14,944 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445087491445_0004-1445088243078-msrabi-word+count-1445090594569-13-1-SUCCEEDED-default-1445088253210.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445087491445_0004-1445088243078-msrabi-word+count-1445090594569-13-1-SUCCEEDED-default-1445088253210.jhist
2015-10-17 22:03:14,944 INFO [Thread-113] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-17 22:03:14,960 INFO [Thread-113] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-17 22:03:14,960 INFO [Thread-113] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MSRA-SA-39.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445087491445_0004
2015-10-17 22:03:14,960 INFO [Thread-113] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-17 22:03:15,960 INFO [Thread-113] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:13 CompletedReds:1 ContAlloc:8 ContRel:0 HostLocal:6 RackLocal:0
2015-10-17 22:03:15,960 INFO [Thread-113] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0004
2015-10-17 22:03:15,975 INFO [Thread-113] org.apache.hadoop.ipc.Server: Stopping server on 10559
2015-10-17 22:03:15,975 INFO [IPC Server listener on 10559] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 10559
2015-10-17 22:03:15,975 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-17 22:03:15,975 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
