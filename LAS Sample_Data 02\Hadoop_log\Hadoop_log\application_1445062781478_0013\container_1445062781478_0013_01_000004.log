2015-10-17 15:38:09,067 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 15:38:09,192 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 15:38:09,192 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-17 15:38:09,223 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 15:38:09,223 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445062781478_0013, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@1623b78d)
2015-10-17 15:38:09,395 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 15:38:10,020 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445062781478_0013
2015-10-17 15:38:10,411 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 15:38:11,192 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 15:38:11,223 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@db57326
2015-10-17 15:38:11,598 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/pageinput2.txt:268435456+134217728
2015-10-17 15:38:11,677 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-17 15:38:11,677 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-17 15:38:11,677 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-17 15:38:11,677 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-17 15:38:11,677 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-17 15:38:11,692 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-17 15:38:19,224 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:38:19,224 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 48249276; bufvoid = 104857600
2015-10-17 15:38:19,224 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 17305200(69220800); length = 8909197/6553600
2015-10-17 15:38:19,224 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 57318028 kvi 14329500(57318000)
2015-10-17 15:38:32,256 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-17 15:38:32,256 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 57318028 kv 14329500(57318000) kvi 12129788(48519152)
2015-10-17 15:38:50,459 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:38:50,459 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 57318028; bufend = 686843; bufvoid = 104857600
2015-10-17 15:38:50,459 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14329500(57318000); kvend = 5414592(21658368); length = 8914909/6553600
2015-10-17 15:38:50,459 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 9755595 kvi 2438892(9755568)
2015-10-17 15:39:01,741 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-17 15:39:01,741 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 9755595 kv 2438892(9755568) kvi 240952(963808)
2015-10-17 15:39:14,679 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:39:14,679 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 9755595; bufend = 58006021; bufvoid = 104857600
2015-10-17 15:39:14,679 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 2438892(9755568); kvend = 19744380(78977520); length = 8908913/6553600
2015-10-17 15:39:14,679 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 67074757 kvi 16768684(67074736)
2015-10-17 15:39:25,773 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-17 15:39:25,789 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 67074757 kv 16768684(67074736) kvi 14558340(58233360)
2015-10-17 15:39:41,340 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:39:41,340 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 67074757; bufend = 10447270; bufvoid = 104857600
2015-10-17 15:39:41,340 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 16768684(67074736); kvend = 7854692(31418768); length = 8913993/6553600
2015-10-17 15:39:41,340 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 19516006 kvi 4878996(19515984)
2015-10-17 15:39:51,699 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-17 15:39:51,699 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 19516006 kv 4878996(19515984) kvi 2677056(10708224)
2015-10-17 15:39:54,449 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:39:54,449 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 19516006; bufend = 67756598; bufvoid = 104857600
2015-10-17 15:39:54,449 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 4878996(19515984); kvend = 22182024(88728096); length = 8911373/6553600
2015-10-17 15:39:54,449 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 76825334 kvi 19206328(76825312)
2015-10-17 15:40:04,544 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-17 15:40:04,544 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 76825334 kv 19206328(76825312) kvi 17012764(68051056)
2015-10-17 15:40:11,669 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:40:11,669 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 76825334; bufend = 20217188; bufvoid = 104857598
2015-10-17 15:40:11,669 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 19206328(76825312); kvend = 10297172(41188688); length = 8909157/6553600
2015-10-17 15:40:11,669 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29285924 kvi 7321476(29285904)
2015-10-17 15:40:21,482 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 5
2015-10-17 15:40:21,482 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29285924 kv 7321476(29285904) kvi 5114320(20457280)
2015-10-17 15:40:24,357 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:40:24,357 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29285924; bufend = 77503154; bufvoid = 104857600
2015-10-17 15:40:24,357 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7321476(29285904); kvend = 24618672(98474688); length = 8917205/6553600
2015-10-17 15:40:24,357 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 86571906 kvi 21642972(86571888)
2015-10-17 15:40:34,967 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 6
2015-10-17 15:40:35,170 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 86571906 kv 21642972(86571888) kvi 19445800(77783200)
2015-10-17 15:40:38,982 INFO [main] org.apache.hadoop.mapred.MapTask: Starting flush of map output
2015-10-17 15:40:38,982 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:40:38,982 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 86571906; bufend = 20324632; bufvoid = 104857600
2015-10-17 15:40:38,982 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 21642972(86571888); kvend = 14513800(58055200); length = 7129173/6553600
2015-10-17 15:40:47,530 INFO [main] org.apache.hadoop.mapred.MapTask: Finished spill 7
2015-10-17 15:40:47,577 INFO [main] org.apache.hadoop.mapred.Merger: Merging 8 sorted segments
2015-10-17 15:40:47,592 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 8 segments left of total size: 288688442 bytes
2015-10-17 15:40:48,327 INFO [communication thread] org.apache.hadoop.mapred.Task: Communication exception: java.io.IOException: Failed on local exception: java.io.IOException: An existing connection was forcibly closed by the remote host; Host Details : local host is: "MSRA-SA-39/**************"; destination host is: "04dn8iq.fareast.corp.microsoft.com":49470; 
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:772)
	at org.apache.hadoop.ipc.Client.call(Client.java:1472)
	at org.apache.hadoop.ipc.Client.call(Client.java:1399)
	at org.apache.hadoop.ipc.WritableRpcEngine$Invoker.invoke(WritableRpcEngine.java:244)
	at com.sun.proxy.$Proxy8.statusUpdate(Unknown Source)
	at org.apache.hadoop.mapred.Task$TaskReporter.run(Task.java:737)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.net.SocketInputStream$Reader.performIO(SocketInputStream.java:57)
	at org.apache.hadoop.net.SocketIOWithTimeout.doIO(SocketIOWithTimeout.java:142)
	at org.apache.hadoop.net.SocketInputStream.read(SocketInputStream.java:161)
	at org.apache.hadoop.net.SocketInputStream.read(SocketInputStream.java:131)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at org.apache.hadoop.ipc.Client$Connection$PingInputStream.read(Client.java:513)
	at java.io.BufferedInputStream.fill(BufferedInputStream.java:246)
	at java.io.BufferedInputStream.read(BufferedInputStream.java:265)
	at java.io.DataInputStream.readInt(DataInputStream.java:387)
	at org.apache.hadoop.ipc.Client$Connection.receiveRpcResponse(Client.java:1071)
	at org.apache.hadoop.ipc.Client$Connection.run(Client.java:966)

2015-10-17 15:41:11,347 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 0 time(s); maxRetries=45
2015-10-17 15:41:15,008 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445062781478_0013_m_000002_0 is done. And is in the process of committing
2015-10-17 15:41:31,352 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 1 time(s); maxRetries=45
2015-10-17 15:41:51,356 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 2 time(s); maxRetries=45
2015-10-17 15:42:11,357 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 3 time(s); maxRetries=45
2015-10-17 15:42:31,358 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 4 time(s); maxRetries=45
2015-10-17 15:42:51,364 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 5 time(s); maxRetries=45
2015-10-17 15:43:11,365 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 6 time(s); maxRetries=45
2015-10-17 15:43:31,369 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 7 time(s); maxRetries=45
2015-10-17 15:43:51,374 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 8 time(s); maxRetries=45
2015-10-17 15:44:11,375 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 9 time(s); maxRetries=45
2015-10-17 15:44:31,375 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 10 time(s); maxRetries=45
2015-10-17 15:44:51,376 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 11 time(s); maxRetries=45
2015-10-17 15:45:11,383 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 12 time(s); maxRetries=45
2015-10-17 15:45:31,384 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 13 time(s); maxRetries=45
2015-10-17 15:45:51,389 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 14 time(s); maxRetries=45
2015-10-17 15:46:11,390 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 15 time(s); maxRetries=45
2015-10-17 15:46:31,391 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 16 time(s); maxRetries=45
2015-10-17 15:46:51,395 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 17 time(s); maxRetries=45
2015-10-17 15:47:11,396 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 18 time(s); maxRetries=45
2015-10-17 15:47:31,400 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 19 time(s); maxRetries=45
2015-10-17 15:47:51,401 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 20 time(s); maxRetries=45
2015-10-17 15:48:11,402 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 21 time(s); maxRetries=45
2015-10-17 15:48:31,405 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 22 time(s); maxRetries=45
2015-10-17 15:48:51,406 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 23 time(s); maxRetries=45
2015-10-17 15:49:11,412 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 24 time(s); maxRetries=45
2015-10-17 15:49:31,415 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 25 time(s); maxRetries=45
2015-10-17 15:49:51,416 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 26 time(s); maxRetries=45
2015-10-17 15:50:11,417 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 27 time(s); maxRetries=45
2015-10-17 15:50:31,420 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 28 time(s); maxRetries=45
2015-10-17 15:50:51,422 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 29 time(s); maxRetries=45
2015-10-17 15:51:11,436 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 30 time(s); maxRetries=45
2015-10-17 15:51:31,437 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 31 time(s); maxRetries=45
2015-10-17 15:51:51,438 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 32 time(s); maxRetries=45
2015-10-17 15:52:11,439 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 33 time(s); maxRetries=45
2015-10-17 15:52:31,440 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 34 time(s); maxRetries=45
2015-10-17 15:52:51,441 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 35 time(s); maxRetries=45
