{"cells": [{"cell_type": "code", "execution_count": 39, "id": "53519c6b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import missingno as msno"]}, {"cell_type": "code", "execution_count": 3, "id": "8726b67b", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(\"C:/Users/<USER>/OneDrive/Desktop/ONGC/Project1/log.csv\")"]}, {"cell_type": "code", "execution_count": 4, "id": "4616e83f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Depth</th>\n", "      <th>RxoRt</th>\n", "      <th>RLL3</th>\n", "      <th>SP</th>\n", "      <th>RILD</th>\n", "      <th>MN</th>\n", "      <th>MI</th>\n", "      <th>MCAL</th>\n", "      <th>DCAL</th>\n", "      <th>RHOB</th>\n", "      <th>RHOC</th>\n", "      <th>DPOR</th>\n", "      <th>CNLS</th>\n", "      <th>GR</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>195.0</td>\n", "      <td>226.0848</td>\n", "      <td>0.4076</td>\n", "      <td>69.8953</td>\n", "      <td>132.5052</td>\n", "      <td>-0.3548</td>\n", "      <td>0.1863</td>\n", "      <td>5.1090</td>\n", "      <td>1.8878</td>\n", "      <td>1.6973</td>\n", "      <td>-0.6303</td>\n", "      <td>59.2216</td>\n", "      <td>30.0657</td>\n", "      <td>60.4576</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>195.5</td>\n", "      <td>223.5031</td>\n", "      <td>0.4063</td>\n", "      <td>69.2303</td>\n", "      <td>123.6225</td>\n", "      <td>-0.3619</td>\n", "      <td>0.1867</td>\n", "      <td>5.1031</td>\n", "      <td>1.8882</td>\n", "      <td>1.6791</td>\n", "      <td>-0.6409</td>\n", "      <td>60.2877</td>\n", "      <td>26.7625</td>\n", "      <td>54.1495</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>196.0</td>\n", "      <td>221.4560</td>\n", "      <td>0.4047</td>\n", "      <td>68.4478</td>\n", "      <td>116.9258</td>\n", "      <td>-0.3668</td>\n", "      <td>0.1860</td>\n", "      <td>5.0872</td>\n", "      <td>1.8878</td>\n", "      <td>1.6585</td>\n", "      <td>-0.6539</td>\n", "      <td>61.4914</td>\n", "      <td>27.6017</td>\n", "      <td>51.9944</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>196.5</td>\n", "      <td>219.8248</td>\n", "      <td>0.4033</td>\n", "      <td>67.4843</td>\n", "      <td>111.7925</td>\n", "      <td>-0.3613</td>\n", "      <td>0.1867</td>\n", "      <td>5.0881</td>\n", "      <td>1.8884</td>\n", "      <td>1.6435</td>\n", "      <td>-0.6649</td>\n", "      <td>62.3711</td>\n", "      <td>31.5870</td>\n", "      <td>52.9645</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>197.0</td>\n", "      <td>218.1438</td>\n", "      <td>0.4023</td>\n", "      <td>66.3013</td>\n", "      <td>106.7821</td>\n", "      <td>-0.3569</td>\n", "      <td>0.1870</td>\n", "      <td>5.0972</td>\n", "      <td>1.8883</td>\n", "      <td>1.6390</td>\n", "      <td>-0.6705</td>\n", "      <td>62.6343</td>\n", "      <td>35.8251</td>\n", "      <td>54.9659</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8734</th>\n", "      <td>4806.0</td>\n", "      <td>1.1354</td>\n", "      <td>398.8031</td>\n", "      <td>-125.7813</td>\n", "      <td>412.4244</td>\n", "      <td>42.3643</td>\n", "      <td>46.5066</td>\n", "      <td>7.8238</td>\n", "      <td>6.2264</td>\n", "      <td>2.5519</td>\n", "      <td>0.2965</td>\n", "      <td>9.2449</td>\n", "      <td>0.0675</td>\n", "      <td>25.8831</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8735</th>\n", "      <td>4806.5</td>\n", "      <td>1.5895</td>\n", "      <td>487.2287</td>\n", "      <td>-126.0632</td>\n", "      <td>434.0304</td>\n", "      <td>41.9099</td>\n", "      <td>43.5448</td>\n", "      <td>7.8055</td>\n", "      <td>6.2264</td>\n", "      <td>2.5503</td>\n", "      <td>0.2977</td>\n", "      <td>9.3399</td>\n", "      <td>0.0704</td>\n", "      <td>9.4402</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8736</th>\n", "      <td>4807.0</td>\n", "      <td>4.1163</td>\n", "      <td>464.9174</td>\n", "      <td>-126.3459</td>\n", "      <td>454.8452</td>\n", "      <td>58.5249</td>\n", "      <td>57.1680</td>\n", "      <td>7.8325</td>\n", "      <td>6.2250</td>\n", "      <td>2.5500</td>\n", "      <td>0.2988</td>\n", "      <td>9.3589</td>\n", "      <td>0.1168</td>\n", "      <td>1.7368</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8737</th>\n", "      <td>4807.5</td>\n", "      <td>8.4264</td>\n", "      <td>353.6349</td>\n", "      <td>-126.6086</td>\n", "      <td>478.5475</td>\n", "      <td>69.1482</td>\n", "      <td>66.7409</td>\n", "      <td>7.8513</td>\n", "      <td>6.2243</td>\n", "      <td>2.5523</td>\n", "      <td>0.2999</td>\n", "      <td>9.2228</td>\n", "      <td>0.3403</td>\n", "      <td>0.1520</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8738</th>\n", "      <td>4808.0</td>\n", "      <td>12.6140</td>\n", "      <td>309.2645</td>\n", "      <td>-126.8335</td>\n", "      <td>506.5828</td>\n", "      <td>67.9119</td>\n", "      <td>66.1706</td>\n", "      <td>7.8118</td>\n", "      <td>6.2243</td>\n", "      <td>2.5563</td>\n", "      <td>0.3008</td>\n", "      <td>8.9912</td>\n", "      <td>1.1912</td>\n", "      <td>0.0058</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8739 rows × 14 columns</p>\n", "</div>"], "text/plain": ["       Depth     RxoRt      RLL3        SP      RILD       MN       MI  \\\n", "0      195.0  226.0848    0.4076   69.8953  132.5052  -0.3548   0.1863   \n", "1      195.5  223.5031    0.4063   69.2303  123.6225  -0.3619   0.1867   \n", "2      196.0  221.4560    0.4047   68.4478  116.9258  -0.3668   0.1860   \n", "3      196.5  219.8248    0.4033   67.4843  111.7925  -0.3613   0.1867   \n", "4      197.0  218.1438    0.4023   66.3013  106.7821  -0.3569   0.1870   \n", "...      ...       ...       ...       ...       ...      ...      ...   \n", "8734  4806.0    1.1354  398.8031 -125.7813  412.4244  42.3643  46.5066   \n", "8735  4806.5    1.5895  487.2287 -126.0632  434.0304  41.9099  43.5448   \n", "8736  4807.0    4.1163  464.9174 -126.3459  454.8452  58.5249  57.1680   \n", "8737  4807.5    8.4264  353.6349 -126.6086  478.5475  69.1482  66.7409   \n", "8738  4808.0   12.6140  309.2645 -126.8335  506.5828  67.9119  66.1706   \n", "\n", "        MCAL    DCAL    RHOB    RHOC     DPOR     CNLS       GR  \n", "0     5.1090  1.8878  1.6973 -0.6303  59.2216  30.0657  60.4576  \n", "1     5.1031  1.8882  1.6791 -0.6409  60.2877  26.7625  54.1495  \n", "2     5.0872  1.8878  1.6585 -0.6539  61.4914  27.6017  51.9944  \n", "3     5.0881  1.8884  1.6435 -0.6649  62.3711  31.5870  52.9645  \n", "4     5.0972  1.8883  1.6390 -0.6705  62.6343  35.8251  54.9659  \n", "...      ...     ...     ...     ...      ...      ...      ...  \n", "8734  7.8238  6.2264  2.5519  0.2965   9.2449   0.0675  25.8831  \n", "8735  7.8055  6.2264  2.5503  0.2977   9.3399   0.0704   9.4402  \n", "8736  7.8325  6.2250  2.5500  0.2988   9.3589   0.1168   1.7368  \n", "8737  7.8513  6.2243  2.5523  0.2999   9.2228   0.3403   0.1520  \n", "8738  7.8118  6.2243  2.5563  0.3008   8.9912   1.1912   0.0058  \n", "\n", "[8739 rows x 14 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 38, "id": "0c407828", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x1000 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(figsize=(10,10))\n", "\n", "ax1 = plt.subplot2grid(shape=(3,3), loc=(0,0), rowspan=3)\n", "ax2 = plt.subplot2grid(shape=(3,3), loc=(0,1), rowspan=3)\n", "ax3 = plt.subplot2grid(shape=(3,3), loc=(0,2))\n", "ax4 = plt.subplot2grid(shape=(3,3), loc=(1,2))\n", "ax5 = plt.subplot2grid(shape=(3,3), loc=(2, 2))\n", "\n", "ax1.scatter(df['DPOR'], df['Depth'], marker='.', c='red')\n", "ax1.set_xlim(0,50)\n", "ax1.set_ylim(4010, 3825)\n", "ax1.set_title('Core Porosity')\n", "ax1.grid()\n", "\n", "ax2.scatter(df['RLL3'], df['Depth'], marker='.', c='blue')\n", "ax2.set_xlim(0.01,10000)\n", "ax2.set_ylim(4010, 3825)\n", "ax2.semilogx()\n", "ax2.set_title('Core Permeability')\n", "ax2.grid()\n", "\n", "ax3.scatter(df['DPOR'], df['RxoRt'], marker='.', alpha=0.5, c='green')\n", "ax3.semilogy()\n", "ax3.set_xlim(0,50)\n", "ax3.set_ylim(0.01, 10000)\n", "ax3.set_title('Poro - Perm Scatter Plot')\n", "ax3.set_xlabel('Core Porosity (%)')\n", "ax3.set_ylabel('Core Permeability (mD)')\n", "ax3.grid()\n", "\n", "ax4.hist(df['DPOR'], bins=30, edgecolor='black', color='red', alpha=0.6)\n", "ax4.set_xlabel('Core Porosity (%)')\n", "\n", "ax5.hist(df['MCAL'], bins=30, edgecolor='black', color='blue', alpha=0.6)\n", "ax5.set_xlabel('Core Porosity (%)')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 40, "id": "10473e72", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Depth</th>\n", "      <th>RxoRt</th>\n", "      <th>RLL3</th>\n", "      <th>SP</th>\n", "      <th>RILD</th>\n", "      <th>MN</th>\n", "      <th>MI</th>\n", "      <th>MCAL</th>\n", "      <th>DCAL</th>\n", "      <th>RHOB</th>\n", "      <th>RHOC</th>\n", "      <th>DPOR</th>\n", "      <th>CNLS</th>\n", "      <th>GR</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>8739.000000</td>\n", "      <td>8739.000000</td>\n", "      <td>8739.000000</td>\n", "      <td>8739.000000</td>\n", "      <td>8739.000000</td>\n", "      <td>8739.000000</td>\n", "      <td>8739.000000</td>\n", "      <td>8739.000000</td>\n", "      <td>8739.000000</td>\n", "      <td>8739.00000</td>\n", "      <td>8739.000000</td>\n", "      <td>8739.0000</td>\n", "      <td>8739.000000</td>\n", "      <td>8739.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>2535.727372</td>\n", "      <td>-18.493591</td>\n", "      <td>37.948919</td>\n", "      <td>-94.299762</td>\n", "      <td>34.511874</td>\n", "      <td>11.714442</td>\n", "      <td>12.180632</td>\n", "      <td>5.906369</td>\n", "      <td>3.669944</td>\n", "      <td>2.03364</td>\n", "      <td>0.126320</td>\n", "      <td>39.5532</td>\n", "      <td>20.129247</td>\n", "      <td>76.948923</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>1358.246302</td>\n", "      <td>31.420924</td>\n", "      <td>81.734581</td>\n", "      <td>39.652558</td>\n", "      <td>251.238360</td>\n", "      <td>27.537012</td>\n", "      <td>28.874862</td>\n", "      <td>1.305561</td>\n", "      <td>2.748873</td>\n", "      <td>0.41571</td>\n", "      <td>0.103907</td>\n", "      <td>24.3105</td>\n", "      <td>11.427909</td>\n", "      <td>33.859411</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>195.000000</td>\n", "      <td>-150.821900</td>\n", "      <td>0.389900</td>\n", "      <td>-201.623000</td>\n", "      <td>0.210400</td>\n", "      <td>-0.527400</td>\n", "      <td>-0.673500</td>\n", "      <td>5.047400</td>\n", "      <td>1.885300</td>\n", "      <td>1.19050</td>\n", "      <td>-0.670500</td>\n", "      <td>-1.8873</td>\n", "      <td>-0.513900</td>\n", "      <td>0.005800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1292.250000</td>\n", "      <td>-31.993950</td>\n", "      <td>6.430800</td>\n", "      <td>-131.421100</td>\n", "      <td>4.356500</td>\n", "      <td>0.306700</td>\n", "      <td>0.097650</td>\n", "      <td>5.089800</td>\n", "      <td>1.919350</td>\n", "      <td>1.68905</td>\n", "      <td>0.049600</td>\n", "      <td>14.8416</td>\n", "      <td>8.888250</td>\n", "      <td>52.336950</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>2623.500000</td>\n", "      <td>-16.509300</td>\n", "      <td>12.138400</td>\n", "      <td>-95.587500</td>\n", "      <td>7.822900</td>\n", "      <td>0.970300</td>\n", "      <td>0.361700</td>\n", "      <td>5.098500</td>\n", "      <td>1.977500</td>\n", "      <td>2.00030</td>\n", "      <td>0.109800</td>\n", "      <td>41.5051</td>\n", "      <td>21.053800</td>\n", "      <td>77.034000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>3715.750000</td>\n", "      <td>-5.941450</td>\n", "      <td>25.696300</td>\n", "      <td>-58.054000</td>\n", "      <td>19.486800</td>\n", "      <td>7.978100</td>\n", "      <td>7.307150</td>\n", "      <td>7.764550</td>\n", "      <td>7.850000</td>\n", "      <td>2.45620</td>\n", "      <td>0.210100</td>\n", "      <td>59.7036</td>\n", "      <td>29.544100</td>\n", "      <td>96.306700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>4808.000000</td>\n", "      <td>247.583200</td>\n", "      <td>880.859400</td>\n", "      <td>69.895300</td>\n", "      <td>11510.641600</td>\n", "      <td>252.124200</td>\n", "      <td>234.871500</td>\n", "      <td>10.308600</td>\n", "      <td>12.334900</td>\n", "      <td>2.74230</td>\n", "      <td>0.357500</td>\n", "      <td>88.8601</td>\n", "      <td>47.581000</td>\n", "      <td>404.288100</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             Depth        RxoRt         RLL3           SP          RILD  \\\n", "count  8739.000000  8739.000000  8739.000000  8739.000000   8739.000000   \n", "mean   2535.727372   -18.493591    37.948919   -94.299762     34.511874   \n", "std    1358.246302    31.420924    81.734581    39.652558    251.238360   \n", "min     195.000000  -150.821900     0.389900  -201.623000      0.210400   \n", "25%    1292.250000   -31.993950     6.430800  -131.421100      4.356500   \n", "50%    2623.500000   -16.509300    12.138400   -95.587500      7.822900   \n", "75%    3715.750000    -5.941450    25.696300   -58.054000     19.486800   \n", "max    4808.000000   247.583200   880.859400    69.895300  11510.641600   \n", "\n", "                MN           MI         MCAL         DCAL        RHOB  \\\n", "count  8739.000000  8739.000000  8739.000000  8739.000000  8739.00000   \n", "mean     11.714442    12.180632     5.906369     3.669944     2.03364   \n", "std      27.537012    28.874862     1.305561     2.748873     0.41571   \n", "min      -0.527400    -0.673500     5.047400     1.885300     1.19050   \n", "25%       0.306700     0.097650     5.089800     1.919350     1.68905   \n", "50%       0.970300     0.361700     5.098500     1.977500     2.00030   \n", "75%       7.978100     7.307150     7.764550     7.850000     2.45620   \n", "max     252.124200   234.871500    10.308600    12.334900     2.74230   \n", "\n", "              RHOC       DPOR         CNLS           GR  \n", "count  8739.000000  8739.0000  8739.000000  8739.000000  \n", "mean      0.126320    39.5532    20.129247    76.948923  \n", "std       0.103907    24.3105    11.427909    33.859411  \n", "min      -0.670500    -1.8873    -0.513900     0.005800  \n", "25%       0.049600    14.8416     8.888250    52.336950  \n", "50%       0.109800    41.5051    21.053800    77.034000  \n", "75%       0.210100    59.7036    29.544100    96.306700  \n", "max       0.357500    88.8601    47.581000   404.288100  "]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe()"]}, {"cell_type": "code", "execution_count": 41, "id": "626d3830", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 8739 entries, 0 to 8738\n", "Data columns (total 14 columns):\n", " #   Column  Non-Null Count  Dtype  \n", "---  ------  --------------  -----  \n", " 0   Depth   8739 non-null   float64\n", " 1   RxoRt   8739 non-null   float64\n", " 2   RLL3    8739 non-null   float64\n", " 3   SP      8739 non-null   float64\n", " 4   RILD    8739 non-null   float64\n", " 5   MN      8739 non-null   float64\n", " 6   MI      8739 non-null   float64\n", " 7   MCAL    8739 non-null   float64\n", " 8   DCAL    8739 non-null   float64\n", " 9   RHOB    8739 non-null   float64\n", " 10  RHOC    8739 non-null   float64\n", " 11  DPOR    8739 non-null   float64\n", " 12  CNLS    8739 non-null   float64\n", " 13  GR      8739 non-null   float64\n", "dtypes: float64(14)\n", "memory usage: 956.0 KB\n"]}], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": 42, "id": "cba54c11", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Depth</th>\n", "      <th>RxoRt</th>\n", "      <th>RLL3</th>\n", "      <th>SP</th>\n", "      <th>RILD</th>\n", "      <th>MN</th>\n", "      <th>MI</th>\n", "      <th>MCAL</th>\n", "      <th>DCAL</th>\n", "      <th>RHOB</th>\n", "      <th>RHOC</th>\n", "      <th>DPOR</th>\n", "      <th>CNLS</th>\n", "      <th>GR</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8734</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8735</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8736</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8737</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8738</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8739 rows × 14 columns</p>\n", "</div>"], "text/plain": ["      Depth  RxoRt   RLL3     SP   RILD     MN     MI   MCAL   DCAL   RHOB  \\\n", "0     False  False  False  False  False  False  False  False  False  False   \n", "1     False  False  False  False  False  False  False  False  False  False   \n", "2     False  False  False  False  False  False  False  False  False  False   \n", "3     False  False  False  False  False  False  False  False  False  False   \n", "4     False  False  False  False  False  False  False  False  False  False   \n", "...     ...    ...    ...    ...    ...    ...    ...    ...    ...    ...   \n", "8734  False  False  False  False  False  False  False  False  False  False   \n", "8735  False  False  False  False  False  False  False  False  False  False   \n", "8736  False  False  False  False  False  False  False  False  False  False   \n", "8737  False  False  False  False  False  False  False  False  False  False   \n", "8738  False  False  False  False  False  False  False  False  False  False   \n", "\n", "       RHOC   DPOR   CNLS     GR  \n", "0     False  False  False  False  \n", "1     False  False  False  False  \n", "2     False  False  False  False  \n", "3     False  False  False  False  \n", "4     False  False  False  False  \n", "...     ...    ...    ...    ...  \n", "8734  False  False  False  False  \n", "8735  False  False  False  False  \n", "8736  False  False  False  False  \n", "8737  False  False  False  False  \n", "8738  False  False  False  False  \n", "\n", "[8739 rows x 14 columns]"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["df.isna()"]}, {"cell_type": "code", "execution_count": 43, "id": "50c086f9", "metadata": {}, "outputs": [{"data": {"text/plain": ["Depth    0\n", "RxoRt    0\n", "RLL3     0\n", "SP       0\n", "RILD     0\n", "MN       0\n", "MI       0\n", "MCAL     0\n", "DCAL     0\n", "RHOB     0\n", "RHOC     0\n", "DPOR     0\n", "CNLS     0\n", "GR       0\n", "dtype: int64"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["df.isna().sum()"]}, {"cell_type": "markdown", "id": "5df3505c", "metadata": {}, "source": ["# Using Missingno"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: >"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 2500x1000 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["msno.bar(df)"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: >"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 2500x1000 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["msno.matrix(df)"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\seaborn\\matrix.py:309: UserWarning: Attempting to set identical low and high xlims makes transformation singular; automatically expanding.\n", "  ax.set(xlim=(0, self.data.shape[1]), ylim=(0, self.data.shape[0]))\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\seaborn\\matrix.py:309: UserWarning: Attempting to set identical low and high ylims makes transformation singular; automatically expanding.\n", "  ax.set(xlim=(0, self.data.shape[1]), ylim=(0, self.data.shape[0]))\n"]}, {"data": {"text/plain": ["<Axes: >"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABZsAAAO+CAYAAABiv8vWAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAAA/8ElEQVR4nO3dfZCV9X0+/vc5i+yKGZ4K7LKJCRjyVZmoWAgr1jZJ2WGJtgmtTSUlRSmBiQmmZjEKmQhGkhAf6lgjU5pUo060GjPRJprZSDA002QLBkvzUOQXHQ2jsotKkAJxwXPO749Otj3hwQOfXc99x9dr5kzce+9z7+ew/1255tpCpVKpBAAAAAAAJCjW+wAAAAAAAOSfsBkAAAAAgGTCZgAAAAAAkgmbAQAAAABIJmwGAAAAACCZsBkAAAAAgGTCZgAAAAAAkgmbAQAAAABIJmwGAAAAACCZsBkAAAAAgGTCZgAAAACAOvrBD34Qf/qnfxqtra1RKBTiwQcffM33bNiwIX7/938/GhsbY9KkSXHHHXcccs+aNWtiwoQJ0dTUFG1tbbFp06aBP/z/IWwGAAAAAKijffv2xVlnnRVr1qyp6f6nn346Lrjggnjve98bW7Zsicsvvzw+8pGPxHe/+93+e+67777o7OyMlStXxuOPPx5nnXVWdHR0xM6dOwfrY0ShUqlUBu3pAAAAAADUrFAoxAMPPBBz5sw54j1XXXVVPPzww/Gzn/2s/9rcuXNj9+7d0dXVFRERbW1t8a53vStuvfXWiIgol8tx8sknx2WXXRbLli0blLNrNgMAAAAA5Eh3d3e0t7dXXevo6Iju7u6IiDhw4EBs3ry56p5isRjt7e399wyGIYP2ZAAAAACAN6i+vr7o6+urutbY2BiNjY3Jz+7p6Ynm5uaqa83NzbFnz5749a9/Hb/61a+iVCod9p4nnngi+ecfibAZAAAAAMisoWf/Tb2PcFw+/YG3xmc/+9mqaytXroxrrrmmPgd6HQibAQAAAAAG2PLly6Ozs7Pq2kC0miMiWlpaore3t+pab29vDB8+PE488cRoaGiIhoaGw97T0tIyIGc4HJvNAAAAAAADrLGxMYYPH171GqiwecaMGbF+/fqqa+vWrYsZM2ZERMTQoUNj6tSpVfeUy+VYv359/z2DQbMZAAAAAMisQrGh3kcYdHv37o0nn3yy/+unn346tmzZEqNHj463vvWtsXz58njuuefirrvuioiIj370o3HrrbfGlVdeGX/zN38Tjz76aHz961+Phx9+uP8ZnZ2dcfHFF8e0adNi+vTpcfPNN8e+fftiwYIFg/Y5hM0AAAAAAHX04x//ON773vf2f/2b+Y2LL7447rjjjtixY0ds3769//sTJ06Mhx9+OD75yU/G3//938db3vKW+Kd/+qfo6Ojov+eiiy6KF154IVasWBE9PT0xZcqU6OrqOuSPBg6kQqVSqQza0wEAAAAAEjROXVTvIxyXvs1fqfcRXnc2mwEAAAAASGZGAwAAAADIrDfCZvPvCs1mAAAAAACSCZsBAAAAAEhmRgMAAAAAyCwzGvmh2QwAAAAAQDJhMwAAAAAAyYTNAAAAAAAks9kMAAAAAGSWzeb80GwGAAAAACCZsBkAAAAAgGTCZgAAAAAAktlsBgAAAAAyq9BgszkvNJsBAAAAAEgmbAYAAAAAIJkZDQAAAAAgs4pFMxp5odkMAAAAAEAyYTMAAAAAAMmEzQAAAAAAJLPZDAAAAABkVsFmc25oNgMAAAAAkEzYDAAAAABAMjMaAAAAAEBmmdHID81mAAAAAACSCZsBAAAAAEgmbAYAAAAAIJnNZgAAAAAgswpFfdm88JsCAAAAACCZsBkAAAAAgGTCZgAAAAAAktlsBgAAAAAyq1BsqPcRqJFmMwAAAAAAyYTNAAAAAAAkM6MBAAAAAGSWGY380GwGAAAAACCZsBkAAAAAgGTCZgAAAAAAktlsBgAAAAAyy2Zzfmg2AwAAAACQTNgMAAAAAEAyMxoAAAAAQGYVGsxo5IVmMwAAAAAAyYTNAAAAAAAkEzYDAAAAAJDMZjMAAAAAkFmFos3mvNBsBgAAAAAgmbAZAAAAAIBkwmYAAAAAAJLZbAYAAAAAMstmc35oNgMAAAAAkEzYDAAAAABAMjMaAAAAAEBmFc1o5IZmMwAAAAAAyYTNAAAAAAAkEzYDAAAAAJDMZjMAAAAAkFkFm825odkMAAAAAEAyYTMAAAAAAMnMaAAAAAAAmWVGIz80mwEAAAAASCZsBgAAAAAgmbAZAAAAAIBkNpsBAAAAgMyy2Zwfms0AAAAAACQTNgMAAAAAkEzYDAAAAABAMpvNAAAAAEBm2WzOD81mAAAAAACSCZsBAAAAAEhmRgMAAAAAyCwzGvmh2QwAAAAAQDJhMwAAAAAAyYTNAAAAAAAks9kMAAAAAGRWocFmc15oNgMAAAAAkEzYDAAAAABAMmEzAAAAAADJbDYDAAAAAJlVKNpszgvNZgAAAAAAkgmbAQAAAABIZkYDAAAAAMgsMxr5odkMAAAAAEAyYTMAAAAAAMmEzQAAAAAAJLPZDAAAAABkls3m/NBsBgAAAAAgmbAZAAAAAIBkZjQAAAAAgMwqFgv1PgI10mwGAAAAACCZsBkAAAAAgGTCZgAAAAAAktlsBgAAAAAyq2CzOTc0mwEAAAAASCZsBgAAAAAgmbAZAAAAAIBkNpsBAAAAgMwqFGw254VmMwAAAAAAyYTNAAAAAAAkM6MBAAAAAGRWsWhGIy80mwEAAAAASCZsBgAAAAAgmbAZAAAAAIBkNpsBAAAAgMwq2GzODc1mAAAAAACSCZsBAAAAAOpszZo1MWHChGhqaoq2trbYtGnTEe99z3veE4VC4ZDXBRdc0H/PJZdccsj3Z8+ePaifwYwGAAAAAJBZb4QZjfvuuy86Oztj7dq10dbWFjfffHN0dHTEtm3bYty4cYfc/81vfjMOHDjQ//VLL70UZ511Vnzwgx+sum/27Nnx1a9+tf/rxsbGwfsQodkMAAAAAFBXN910UyxatCgWLFgQkydPjrVr18awYcPi9ttvP+z9o0ePjpaWlv7XunXrYtiwYYeEzY2NjVX3jRo1alA/h7AZAAAAAKBODhw4EJs3b4729vb+a8ViMdrb26O7u7umZ9x2220xd+7cOOmkk6qub9iwIcaNGxennnpqXHrppfHSSy8N6Nl/mxkNAAAAAIAB1tfXF319fVXXGhsbD5myePHFF6NUKkVzc3PV9ebm5njiiSde8+ds2rQpfvazn8Vtt91WdX327Nnx53/+5zFx4sR46qmn4tOf/nS8733vi+7u7mhoaDjOT3V0ms0AAAAAQGYVC4VcvlavXh0jRoyoeq1evXrA/31uu+22OOOMM2L69OlV1+fOnRvvf//744wzzog5c+bEQw89FI899lhs2LBhwM/wG8JmAAAAAIABtnz58nj55ZerXsuXLz/kvjFjxkRDQ0P09vZWXe/t7Y2Wlpaj/ox9+/bFvffeGwsXLnzN85xyyikxZsyYePLJJ4/tgxwDYTMAAAAAwABrbGyM4cOHV71+e0IjImLo0KExderUWL9+ff+1crkc69evjxkzZhz1Z9x///3R19cXH/7wh1/zPM8++2y89NJLMX78+GP/MDUSNgMAAAAA1FFnZ2d85StfiTvvvDO2bt0al156aezbty8WLFgQERHz588/bCv6tttuizlz5sTv/d7vVV3fu3dvfOpTn4p///d/j2eeeSbWr18fH/jAB2LSpEnR0dExaJ/DHwgEAAAAADKrUCzU+wiD7qKLLooXXnghVqxYET09PTFlypTo6urq/6OB27dvj2Kxuje8bdu2+Ld/+7d45JFHDnleQ0ND/OQnP4k777wzdu/eHa2trTFr1qxYtWrVYdvVA6VQqVQqg/Z0AAAAAIAE77zioXof4bj87MY/qfcRXndmNAAAAAAASGZGAwAAAADIrDfCjMbvCs1mAAAAAACSCZsBAAAAAEgmbAYAAAAAIJnNZgAAAAAgs4o2m3NDsxkAAAAAgGTCZgAAAAAAkpnRAAAAAAAyq6Aumxt+VQAAAAAAJBM2AwAAAACQTNgMAAAAAEAym80AAAAAQGYVCoV6H4EaaTYDAAAAAJBM2AwAAAAAQDJhMwAAAAAAyWw2AwAAAACZVSzabM4LzWYAAAAAAJIJmwEAAAAASGZGAwAAAADIrIIZjdzQbAYAAAAAIJmwGQAAAACAZMJmAAAAAACS2WwGAAAAADLLZnN+aDYDAAAAAJBM2AwAAAAAQDIzGgAAAABAZhULZjTyQrMZAAAAAIBkwmYAAAAAAJIJmwEAAAAASGazGQAAAADIrELRZnNeaDYDAAAAAJBM2AwAAAAAQDJhMwAAAAAAyWw2AwAAAACZZbM5PzSbAQAAAABIJmwGAAAAACCZGQ0AAAAAILOKZjRyQ7MZAAAAAIBkwmYAAAAAAJIJmwEAAAAASGazGQAAAADIrELBZnNeaDYDAAAAAJBM2AwAAAAAQDJhMwAAAAAAyWw2AwAAAACZVVCXzQ2/KgAAAAAAkgmbAQAAAABIZkYDAAAAAMisYrFQ7yNQI81mAAAAAACSCZsBAAAAAEgmbAYAAAAAIJnNZgAAAAAgswo2m3NDsxkAAAAAgGTCZgAAAAAAkpnRAAAAAAAyq1Awo5EXms0AAAAAACQTNgMAAAAAkEzYDAAAAABAMpvNAAAAAEBmFYs2m/NCsxkAAAAAgGTCZgAAAAAAkgmbAQAAAABIZrMZAAAAAMisgs3m3NBsBgAAAAAgmbAZAAAAAIBkZjQAAAAAgMxqMKORG5rNAAAAAAAkEzYDAAAAAJBM2AwAAAAAQDKbzQAAAABAZtlszg/NZgAAAAAAkgmbAQAAAABIZkYDAAAAAMgsMxr5odkMAAAAAEAyYTMAAAAAAMmEzQAAAAAAJLPZDAAAAABkls3m/NBsBgAAAAAgmbAZAAAAAIBkwmYAAAAAAJLZbAYAAAAAMstmc35oNgMAAAAAkEzYDAAAAABAMjMaAAAAAEBmDTGjkRuazQAAAAAAJBM2AwAAAACQTNgMAAAAAEAym80AAAAAQGY12GzODc1mAAAAAACSCZsBAAAAAEhmRgMAAAAAyCwzGvmh2QwAAAAAQDJhMwAAAAAAyYTNAAAAAAAks9kMAAAAAGRWQ1FfNi/8pgAAAAAASCZsBgAAAAAgmbAZAAAAAIBkNpsBAAAAgMxqKBbqfQRqpNkMAAAAAEAyYTMAAAAAAMnMaAAAAAAAmWVGIz80mwEAAAAASCZsBgAAAAAgmbAZAAAAAKDO1qxZExMmTIimpqZoa2uLTZs2HfHeO+64IwqFQtWrqamp6p5KpRIrVqyI8ePHx4knnhjt7e3xi1/8YlA/g7AZAAAAAMishmIhl69jcd9990VnZ2esXLkyHn/88TjrrLOio6Mjdu7cecT3DB8+PHbs2NH/+uUvf1n1/euvvz5uueWWWLt2bWzcuDFOOumk6OjoiFdeeeW4fg+1EDYDAAAAANTRTTfdFIsWLYoFCxbE5MmTY+3atTFs2LC4/fbbj/ieQqEQLS0t/a/m5ub+71Uqlbj55pvjM5/5THzgAx+IM888M+666654/vnn48EHHxy0zyFsBgAAAACokwMHDsTmzZujvb29/1qxWIz29vbo7u4+4vv27t0bb3vb2+Lkk0+OD3zgA/Hzn/+8/3tPP/109PT0VD1zxIgR0dbWdtRnphI2AwAAAAAMsL6+vtizZ0/Vq6+v75D7XnzxxSiVSlXN5IiI5ubm6OnpOeyzTz311Lj99tvjX/7lX+JrX/talMvlOPfcc+PZZ5+NiOh/37E8cyAImwEAAACAzGooFHL5Wr16dYwYMaLqtXr16gH5N5kxY0bMnz8/pkyZEu9+97vjm9/8ZowdOzb+8R//cUCef7yG1PWnAwAAAAD8Dlq+fHl0dnZWXWtsbDzkvjFjxkRDQ0P09vZWXe/t7Y2WlpaaftYJJ5wQZ599djz55JMREf3v6+3tjfHjx1c9c8qUKcfyMY6JZjMAAAAAwABrbGyM4cOHV70OFzYPHTo0pk6dGuvXr++/Vi6XY/369TFjxoyaflapVIqf/vSn/cHyxIkTo6WlpeqZe/bsiY0bN9b8zOOh2QwAAAAAZFZDsVDvIwy6zs7OuPjii2PatGkxffr0uPnmm2Pfvn2xYMGCiIiYP39+vPnNb+6f4bj22mvjnHPOiUmTJsXu3bvjhhtuiF/+8pfxkY98JCIiCoVCXH755fG5z30u3vGOd8TEiRPj6quvjtbW1pgzZ86gfQ5hMwAAAABAHV100UXxwgsvxIoVK6KnpyemTJkSXV1d/X/gb/v27VEs/u9Ixa9+9atYtGhR9PT0xKhRo2Lq1Knxox/9KCZPntx/z5VXXhn79u2LxYsXx+7du+O8886Lrq6uaGpqGrTPUahUKpVBezoAAAAAQIK/feCn9T7Ccfn7Pzuj3kd43dlsBgAAAAAgmRkNAAAAACCz3gibzb8rNJsBAAAAAEgmbAYAAAAAIJkZDQAAAAAgs4aY0cgNzWYAAAAAAJIJmwEAAAAASCZsBgAAAAAgmc1mAAAAACCzGmw254ZmMwAAAAAAyYTNAAAAAAAkEzYDAAAAAJDMZjMAAAAAkFk2m/NDsxkAAAAAgGTCZgAAAAAAkpnRAAAAAAAyy4xGfmg2AwAAAACQTNgMAAAAAEAyYTMAAAAAAMlsNgMAAAAAmWWzOT80mwEAAAAASCZsBgAAAAAgmRkNAAAAACCzzGjkh2YzAAAAAADJhM0AAAAAACQTNgMAAAAAkMxmMwAAAACQWTab80OzGQAAAACAZMJmAAAAAACSCZsBAAAAAEhmsxkAAAAAyCybzfmh2QwAAAAAQDJhMwAAAAAAycxoAAAAAACZZUYjPzSbAQAAAABIJmwGAAAAACCZsBkAAAAAgGQ2mwEAAACAzLLZnB+azQAAAAAAJBM2AwAAAACQzIwGAAAAAJBZZjTyQ7MZAAAAAIBkwmYAAAAAAJIJmwEAAAAASGazGQAAAADIrIaCzea80GwGAAAAACCZsBkAAAAAgGTCZgAAAAAAktlsBgAAAAAyq2izOTc0mwEAAAAASCZsBgAAAAAgmRkNAAAAACCzGqxo5IZmMwAAAAAAyYTNAAAAAAAkEzYDAAAAAJDMZjMAAAAAkFnFotHmvNBsBgAAAAAgmbAZAAAAAIBkZjQAAAAAgMxqKJjRyAvNZgAAAAAAkgmbAQAAAABIJmwGAAAAACCZzWYAAAAAILOKNptzQ7MZAAAAAIBkwmYAAAAAAJIJmwEAAAAASGazGQAAAADIrAaTzbmh2QwAAAAAQDJhMwAAAAAAycxoAAAAAACZVSza0cgLzWYAAAAAAJIJmwEAAAAASCZsBgAAAAAgmc1mAAAAACCzigWbzXmh2QwAAAAAQDJhMwAAAAAAyYTNAAAAAAAks9kMAAAAAGRWg8nm3NBsBgAAAAAgmbAZAAAAAIBkZjQAAAAAgMwqFuxo5IVmMwAAAAAAyYTNAAAAAAAkEzYDAAAAAJDMZjMAAAAAkFkNRZvNeaHZDAAAAABAMmEzAAAAAADJzGgAAAAAAJlVLJjRyAvNZgAAAAAAkgmbAQAAAABIJmwGAAAAACCZzWYAAAAAILMaTDbnhmYzAAAAAADJhM0AAAAAACQTNgMAAAAAkMxmMwAAAACQWcWC0ea80GwGAAAAACCZsBkAAAAAgGRmNAAAAACAzGoomtHIC81mAAAAAACSCZsBAAAAAEgmbAYAAAAAIJnNZgAAAAAgs0w254dmMwAAAAAAyYTNAAAAAAAkEzYDAAAAAJnVUCjk8nWs1qxZExMmTIimpqZoa2uLTZs2HfHer3zlK/GHf/iHMWrUqBg1alS0t7cfcv8ll1wShUKh6jV79uxjPtexEDYDAAAAANTRfffdF52dnbFy5cp4/PHH46yzzoqOjo7YuXPnYe/fsGFDfOhDH4rvf//70d3dHSeffHLMmjUrnnvuuar7Zs+eHTt27Oh//fM///Ogfo5CpVKpDOpPAAAAAAA4Tv/61Iv1PsJxeffbx9R8b1tbW7zrXe+KW2+9NSIiyuVynHzyyXHZZZfFsmXLXvP9pVIpRo0aFbfeemvMnz8/Iv6n2bx79+548MEHj+v8x0OzGQAAAABggPX19cWePXuqXn19fYfcd+DAgdi8eXO0t7f3XysWi9He3h7d3d01/az9+/fHwYMHY/To0VXXN2zYEOPGjYtTTz01Lr300njppZfSPtRrEDYDAAAAAJlVLBRy+Vq9enWMGDGi6rV69epDPt+LL74YpVIpmpubq643NzdHT09PTf9GV111VbS2tlYF1rNnz4677ror1q9fH9ddd13867/+a7zvfe+LUqmU9gs5iiGD9mQAAAAAgDeo5cuXR2dnZ9W1xsbGAf85X/ziF+Pee++NDRs2RFNTU//1uXPn9v/3GWecEWeeeWa8/e1vjw0bNsTMmTMH/BwRms0AAAAAAAOusbExhg8fXvU6XNg8ZsyYaGhoiN7e3qrrvb290dLSctSfceONN8YXv/jFeOSRR+LMM8886r2nnHJKjBkzJp588slj/zA1EjYDAAAAANTJ0KFDY+rUqbF+/fr+a+VyOdavXx8zZsw44vuuv/76WLVqVXR1dcW0adNe8+c8++yz8dJLL8X48eMH5NyHY0YDAAAAAMishjdAXbazszMuvvjimDZtWkyfPj1uvvnm2LdvXyxYsCAiIubPnx9vfvOb+zefr7vuulixYkXcc889MWHChP5t5ze96U3xpje9Kfbu3Ruf/exn48ILL4yWlpZ46qmn4sorr4xJkyZFR0fHoH0OYTMAAAAAQB1ddNFF8cILL8SKFSuip6cnpkyZEl1dXf1/NHD79u1RLP5v6v4P//APceDAgfiLv/iLquesXLkyrrnmmmhoaIif/OQnceedd8bu3bujtbU1Zs2aFatWrRqU3ejfKFQqlcqgPR0AAAAAIMGPnnmp3kc4LudO+L16H+F1p9kMAAAAAGRWsVCo9xGo0Rtg8QQAAAAAgMEmbAYAAAAAIJmwGQAAAACAZDabAQAAAIDMarDZnBuazQAAAAAAJBM2AwAAAACQzIwGAAAAAJBZRTMauaHZDAAAAABAMmEzAAAAAADJhM0AAAAAACSz2QwAAAAAZFaDumxu+FUBAAAAAJBM2AwAAAAAQDJhMwAAAAAAyWw2AwAAAACZVSwU6n0EaqTZDAAAAABAMmEzAAAAAADJzGgAAAAAAJllRSM/NJsBAAAAAEgmbAYAAAAAIJmwGQAAAACAZDabAQAAAIDMKobR5rzQbAYAAAAAIJmwGQAAAACAZGY0AAAAAIDMKljRyA3NZgAAAAAAkgmbAQAAAABIJmwGAAAAACCZzWYAAAAAILOKNptzQ7MZAAAAAIBkwmYAAAAAAJIJmwEAAAAASGazGQAAAADIrILN5tzQbAYAAAAAIJmwGQAAAACAZGY0AAAAAIDMKoYdjbzQbAYAAAAAIJmwGQAAAACAZMJmAAAAAACS2WwGAAAAADKrYLI5NzSbAQAAAABIJmwGAAAAACCZsBkAAAAAgGQ2mwEAAACAzCrabM4NzWYAAAAAAJIJmwEAAAAASGZGAwAAAADILCsa+aHZDAAAAABAMmEzAAAAAADJhM0AAAAAACSz2QwAAAAAZFaxYLU5LzSbAQAAAABIJmwGAAAAACCZGQ0AAAAAILOsaOSHZjMAAAAAAMmEzQAAAAAAJBM2AwAAAACQzGYzAAAAAJBZ2rL54XcFAAAAAEAyYTMAAAAAAMmEzQAAAAAAJLPZDAAAAABkVqFQqPcRqJFmMwAAAAAAyYTNAAAAAAAkM6MBAAAAAGRW0YpGbmg2AwAAAACQTNgMAAAAAEAyYTMAAAAAAMlsNgMAAAAAmVWw2Zwbms0AAAAAACQTNgMAAAAAkMyMBgAAAACQWdqy+eF3BQAAAABAMmEzAAAAAADJhM0AAAAAACSz2QwAAAAAZFahUKj3EaiRZjMAAAAAAMmEzQAAAAAAJBM2AwAAAACQzGYzAAAAAJBZRZPNuaHZDAAAAABAMmEzAAAAAADJzGgAAAAAAJllRSM/NJsBAAAAAEgmbAYAAAAAIJmwGQAAAACAZDabAQAAAIDMKhptzg3NZgAAAAAAkgmbAQAAAABIZkYDAAAAAMisQsGORl5oNgMAAAAAkEzYDAAAAABAMmEzAAAAAADJbDYDAAAAAJlVNNmcG5rNAAAAAAAkEzYDAAAAAJBM2AwAAAAAQDKbzQAAAABAZplszg/NZgAAAAAAkgmbAQAAAABIZkYDAAAAAMisYsGQRl5oNgMAAAAAkEzYDAAAAABAMmEzAAAAAECdrVmzJiZMmBBNTU3R1tYWmzZtOur9999/f5x22mnR1NQUZ5xxRnznO9+p+n6lUokVK1bE+PHj48QTT4z29vb4xS9+MZgfQdgMAAAAAGRXoZDP17G47777orOzM1auXBmPP/54nHXWWdHR0RE7d+487P0/+tGP4kMf+lAsXLgw/uM//iPmzJkTc+bMiZ/97Gf991x//fVxyy23xNq1a2Pjxo1x0kknRUdHR7zyyispv46jKlQqlcqgPR0AAAAAIMGvBzEcHUwnNjXVfG9bW1u8613viltvvTUiIsrlcpx88slx2WWXxbJlyw65/6KLLop9+/bFQw891H/tnHPOiSlTpsTatWujUqlEa2trLF26NK644oqIiHj55Zejubk57rjjjpg7d27ipzs8zWYAAAAAgDo5cOBAbN68Odrb2/uvFYvFaG9vj+7u7sO+p7u7u+r+iIiOjo7++59++uno6empumfEiBHR1tZ2xGcOhCGD9mQAAAAAgDeovr6+6Ovrq7rW2NgYjY2NVddefPHFKJVK0dzcXHW9ubk5nnjiicM+u6en57D39/T09H//N9eOdM9g0GwGAAAAADKrUKnk8rV69eoYMWJE1Wv16tX1/uccVJrNAAAAAAADbPny5dHZ2Vl17bdbzRERY8aMiYaGhujt7a263tvbGy0tLYd9dktLy1Hv/83/9vb2xvjx46vumTJlyjF/llppNgMAAAAADLDGxsYYPnx41etwYfPQoUNj6tSpsX79+v5r5XI51q9fHzNmzDjss2fMmFF1f0TEunXr+u+fOHFitLS0VN2zZ8+e2Lhx4xGfORA0mwEAAACA7KqU632CQdfZ2RkXX3xxTJs2LaZPnx4333xz7Nu3LxYsWBAREfPnz483v/nN/TMcf/u3fxvvfve74+/+7u/iggsuiHvvvTd+/OMfx5e//OWIiCgUCnH55ZfH5z73uXjHO94REydOjKuvvjpaW1tjzpw5g/Y5hM0AAAAAAHV00UUXxQsvvBArVqyInp6emDJlSnR1dfX/gb/t27dHsfi/IxXnnntu3HPPPfGZz3wmPv3pT8c73vGOePDBB+Od73xn/z1XXnll7Nu3LxYvXhy7d++O8847L7q6uqKpqWnQPkehUqlUBu3pAAAAAAAJXtm/r95HOC5Nw06q9xFedzabAQAAAABIZkYDAAAAAMiswhtgs/l3hWYzAAAAAADJhM0AAAAAACQzowEAAAAAZJcZjdzQbAYAAAAAIJmwGQAAAACAZMJmAAAAAACS2WwGAAAAALKrUqn3CaiRZjMAAAAAAMmEzQAAAAAAJBM2AwAAAACQzGYzAAAAAJBdlXK9T0CNNJsBAAAAAEgmbAYAAAAAIJkZDQAAAAAgswpmNHJDsxkAAAAAgGTCZgAAAAAAkgmbAQAAAABIZrMZAAAAAMgum825odkMAAAAAEAyYTMAAAAAAMnMaAAAAAAA2WVGIzc0mwEAAAAASCZsBgAAAAAgmbAZAAAAAIBkNpsBAAAAgOyy2Zwbms0AAAAAACQTNgMAAAAAkEzYDAAAAABAMpvNAAAAAEB2lW0254VmMwAAAAAAyYTNAAAAAAAkM6MBAAAAAGRWoWJGIy80mwEAAAAASCZsBgAAAAAgmbAZAAAAAIBkNpsBAAAAgOyy2Zwbms0AAAAAACQTNgMAAAAAkMyMBgAAAACQXZVKvU9AjTSbAQAAAABIJmwGAAAAACCZsBkAAAAAgGQ2mwEAAACA7KqU630CaqTZDAAAAABAMmEzAAAAAADJhM0AAAAAACSz2QwAAAAAZFbBZnNuaDYDAAAAAJBM2AwAAAAAQDIzGgAAAABAdpnRyA3NZgAAAAAAkgmbAQAAAABIJmwGAAAAACCZzWYAAAAAILtsNueGZjMAAAAAAMmEzQAAAAAAJDOjAQAAAABklxmN3NBsBgAAAAAgmbAZAAAAAIBkwmYAAAAAAJLZbAYAAAAAMqtgszk3NJsBAAAAAEgmbAYAAAAAIJmwGQAAAACAZDabAQAAAIDsKttszgvNZgAAAAAAkgmbAQAAAABIZkYDAAAAAMiuSqXeJ6BGms0AAAAAACQTNgMAAAAAkEzYDAAAAABAMpvNAAAAAEB2Vcr1PgE10mwGAAAAACCZsBkAAAAAgGTCZgAAAAAAktlsBgAAAAAyq2CzOTc0mwEAAAAASCZsBgAAAAAgmRkNAAAAACC7zGjkhmYzAAAAAADJhM0AAAAAACQTNgMAAAAAkMxmMwAAAACQXTabc0OzGQAAAACAZMJmAAAAAACSmdEAAAAAALKrXKr3CaiRZjMAAAAAAMmEzQAAAAAAJBM2AwAAAACQzGYzAAAAAJBZlXK53kegRprNAAAAAAAkEzYDAAAAAJBM2AwAAAAAQDKbzQAAAABAdpVL9T4BNdJsBgAAAAAgmbAZAAAAAIBkZjQAAAAAgOwyo5Ebms0AAAAAACQTNgMAAAAAkEzYDAAAAABAMpvNAAAAAEBmVUo2m/NCsxkAAAAAgGTCZgAAAACAnNi1a1fMmzcvhg8fHiNHjoyFCxfG3r17j3r/ZZddFqeeemqceOKJ8da3vjU+8YlPxMsvv1x1X6FQOOR17733HtPZzGgAAAAAANlVLtf7BJkyb9682LFjR6xbty4OHjwYCxYsiMWLF8c999xz2Puff/75eP755+PGG2+MyZMnxy9/+cv46Ec/Gs8//3x84xvfqLr3q1/9asyePbv/65EjRx7T2QqVSqVyzJ8IAAAAAOB18Op/dNX7CMdlyNmzX/umY7R169aYPHlyPPbYYzFt2rSIiOjq6orzzz8/nn322Whtba3pOffff398+MMfjn379sWQIf/TRy4UCvHAAw/EnDlzjvt8ZjQAAAAAAHKgu7s7Ro4c2R80R0S0t7dHsViMjRs31vycl19+OYYPH94fNP/Gxz/+8RgzZkxMnz49br/99jjWnrIZDQAAAACAAdbX1xd9fX1V1xobG6OxsfG4n9nT0xPjxo2rujZkyJAYPXp09PT01PSMF198MVatWhWLFy+uun7ttdfGH//xH8ewYcPikUceiY997GOxd+/e+MQnPlHz+TSbAQAAAIDsKpdy+Vq9enWMGDGi6rV69erDfsRly5Yd9g/0/d/XE088kfxPuWfPnrjgggti8uTJcc0111R97+qrr44/+IM/iLPPPjuuuuqquPLKK+OGG244pufbbAYAAAAAMuvVzQ/X+wjHpfTO9pqbzS+88EK89NJLR33eKaecEl/72tdi6dKl8atf/ar/+quvvhpNTU1x//33x5/92Z8d8f3//d//HR0dHTFs2LB46KGHoqmp6ag/7+GHH44/+ZM/iVdeeaXmNrYZDQAAAACAAXYskxljx46NsWPHvuZ9M2bMiN27d8fmzZtj6tSpERHx6KOPRrlcjra2tiO+b8+ePdHR0RGNjY3xrW996zWD5oiILVu2xKhRo45p9kPYDAAAAACQA6effnrMnj07Fi1aFGvXro2DBw/GkiVLYu7cudHa2hoREc8991zMnDkz7rrrrpg+fXrs2bMnZs2aFfv374+vfe1rsWfPntizZ09E/E/I3dDQEN/+9rejt7c3zjnnnGhqaop169bFF77whbjiiiuO6XzCZgAAAAAgsyrlUr2PkCl33313LFmyJGbOnBnFYjEuvPDCuOWWW/q/f/Dgwdi2bVvs378/IiIef/zx2LhxY0RETJo0qepZTz/9dEyYMCFOOOGEWLNmTXzyk5+MSqUSkyZNiptuuikWLVp0TGez2QwAAAAAZNbBx75V7yMclxPe9f56H+F1V6z3AQAAAAAAyD8zGgAAAABAdpXL9T4BNdJsBgAAAAAgmbAZAAAAAIBkwmYAAAAAAJLZbAYAAAAAMqtSLtX7CNRIsxkAAAAAgGTCZgAAAAAAkpnRAAAAAACyy4xGbmg2AwAAAACQTNgMAAAAAEAyYTMAAAAAAMlsNgMAAAAA2VUu1/sE1EizGQAAAACAZMJmAAAAAACSCZsBAAAAAEhmsxkAAAAAyKxKqVTvI1AjzWYAAAAAAJIJmwEAAAAASGZGAwAAAADIrrIZjbzQbAYAAAAAIJmwGQAAAACAZMJmAAAAAACS2WwGAAAAALLLZnNuaDYDAAAAAJBM2AwAAAAAQDIzGgAAAABAZlXK5XofgRppNgMAAAAAkEzYDAAAAABAMmEzAAAAAADJbDYDAAAAANlVLtX7BNRIsxkAAAAAgGTCZgAAAAAAkgmbAQAAAABIZrMZAAAAAMgum825odkMAAAAAEAyYTMAAAAAAMnMaAAAAAAAmVUpl+t9BGqk2QwAAAAAQDJhMwAAAAAAyYTNAAAAAAAks9kMAAAAAGRXuVTvE1AjzWYAAAAAAJIJmwEAAAAASCZsBgAAAAAgmc1mAAAAACC7bDbnhmYzAAAAAADJhM0AAAAAACQzowEAAAAAZFalZEYjLzSbAQAAAABIJmwGAAAAACCZsBkAAAAAgGQ2mwEAAACA7CqX630CaqTZDAAAAABAMmEzAAAAAADJzGgAAAAAANlVLtX7BNRIsxkAAAAAgGTCZgAAAAAAkgmbAQAAAABIZrMZAAAAAMisis3m3NBsBgAAAAAgmbAZAAAAAIBkwmYAAAAAAJLZbAYAAAAAMqtSLtf7CNRIsxkAAAAAgGTCZgAAAAAAkpnRAAAAAAAyq1Iyo5EXms0AAAAAACQTNgMAAAAAkEzYDAAAAABAMpvNAAAAAEBm2WzOD81mAAAAAACSCZsBAAAAAEhmRgMAAAAAyKxK2YxGXmg2AwAAAACQTNgMAAAAAEAyYTMAAAAAAMlsNgMAAAAAmVUp2WzOC81mAAAAAACSCZsBAAAAAEgmbAYAAAAAIJnNZgAAAAAgs2w254dmMwAAAAAAyYTNAAAAAAAkM6MBAAAAAGRWuVSq9xGokWYzAAAAAADJhM0AAAAAACQTNgMAAAAAkMxmMwAAAACQWZVyud5HoEaazQAAAAAAJBM2AwAAAACQzIwGAAAAAJBZlZIZjbzQbAYAAAAAIJmwGQAAAACAZMJmAAAAAACS2WwGAAAAADLLZnN+aDYDAAAAAJBM2AwAAAAAQDJhMwAAAAAAyWw2AwAAAACZVSnbbM4LzWYAAAAAAJIJmwEAAAAASGZGAwAAAADIrHLJjEZeaDYDAAAAAJBM2AwAAAAAQDJhMwAAAABATuzatSvmzZsXw4cPj5EjR8bChQtj7969R33Pe97znigUClWvj370o1X3bN++PS644IIYNmxYjBs3Lj71qU/Fq6++ekxns9kMAAAAAGRWxWZzlXnz5sWOHTti3bp1cfDgwViwYEEsXrw47rnnnqO+b9GiRXHttdf2fz1s2LD+/y6VSnHBBRdES0tL/OhHP4odO3bE/Pnz44QTTogvfOELNZ9N2AwAAAAAkANbt26Nrq6ueOyxx2LatGkREfGlL30pzj///LjxxhujtbX1iO8dNmxYtLS0HPZ7jzzySPzXf/1XfO9734vm5uaYMmVKrFq1Kq666qq45pprYujQoTWdz4wGAAAAAEAOdHd3x8iRI/uD5oiI9vb2KBaLsXHjxqO+9+67744xY8bEO9/5zli+fHns37+/6rlnnHFGNDc391/r6OiIPXv2xM9//vOaz6fZDAAAAAAwwPr6+qKvr6/qWmNjYzQ2Nh73M3t6emLcuHFV14YMGRKjR4+Onp6eI77vr/7qr+Jtb3tbtLa2xk9+8pO46qqrYtu2bfHNb36z/7n/N2iOiP6vj/bc36bZDAAAAABkVqVUzuVr9erVMWLEiKrX6tWrD/sZly1bdsgf8Pvt1xNPPHHc/4aLFy+Ojo6OOOOMM2LevHlx1113xQMPPBBPPfXUcT/zcDSbAQAAAAAG2PLly6Ozs7Pq2pFazUuXLo1LLrnkqM875ZRToqWlJXbu3Fl1/dVXX41du3YdcY/5cNra2iIi4sknn4y3v/3t0dLSEps2baq6p7e3NyLimJ4rbAYAAAAAGGDHMpkxduzYGDt27GveN2PGjNi9e3ds3rw5pk6dGhERjz76aJTL5f4AuRZbtmyJiIjx48f3P/fzn/987Ny5s3+mY926dTF8+PCYPHlyzc81owEAAAAAZFalXM7lazCcfvrpMXv27Fi0aFFs2rQpfvjDH8aSJUti7ty50draGhERzz33XJx22mn9TeWnnnoqVq1aFZs3b45nnnkmvvWtb8X8+fPjj/7oj+LMM8+MiIhZs2bF5MmT46//+q/jP//zP+O73/1ufOYzn4mPf/zjx7QxLWwGAAAAAMiJu+++O0477bSYOXNmnH/++XHeeefFl7/85f7vHzx4MLZt2xb79++PiIihQ4fG9773vZg1a1acdtppsXTp0rjwwgvj29/+dv97Ghoa4qGHHoqGhoaYMWNGfPjDH4758+fHtddee0xnK1QqlcrAfEwAAAAAgIH17MpF9T7CcXnLZ79S7yO87jSbAQAAAABI5g8EAgAAAACZVSkNzv4xA0+zGQAAAACAZMJmAAAAAACSmdEAAAAAADLLjEZ+aDYDAAAAAJBM2AwAAAAAQDJhMwAAAAAAyWw2AwAAAACZVS7bbM4LzWYAAAAAAJIJmwEAAAAASCZsBgAAAAAgmc1mAAAAACCzKiWbzXmh2QwAAAAAQDJhMwAAAAAAycxoAAAAAACZVSmV6n0EaqTZDAAAAABAMmEzAAAAAADJhM0AAAAAACSz2QwAAAAAZFalXK73EaiRZjMAAAAAAMmEzQAAAAAAJDOjAQAAAABkVqVkRiMvNJsBAAAAAEgmbAYAAAAAIJmwGQAAAACAZDabAQAAAIDMstmcH5rNAAAAAAAkEzYDAAAAAJBM2AwAAAAAQDKbzQAAAABAZpVtNueGZjMAAAAAAMmEzQAAAAAAJDOjAQAAAABkVqVsRiMvNJsBAAAAAEgmbAYAAAAAIJmwGQAAAACAZDabAQAAAIDMqpRsNueFZjMAAAAAAMmEzQAAAAAAJDOjAQAAAABkVqVUqfcRqJFmMwAAAAAAyYTNAAAAAAAkEzYDAAAAAJDMZjMAAAAAkFnlUrneR6BGms0AAAAAACQTNgMAAAAAkEzYDAAAAABAMpvNAAAAAEBmVcqVeh+BGmk2AwAAAACQTNgMAAAAAEAyMxoAAAAAQGaVS2Y08kKzGQAAAACAZMJmAAAAAACSCZsBAAAAAEhmsxkAAAAAyKxKqVzvI1AjzWYAAAAAAJIJmwEAAAAASGZGAwAAAADIrEqpUu8jUCPNZgAAAAAAkgmbAQAAAABIJmwGAAAAACCZzWYAAAAAILPKNptzQ7MZAAAAAIBkwmYAAAAAAJIJmwEAAAAASGazGQAAAADIrEqpXO8jUCPNZgAAAAAAkgmbAQAAAABIZkYDAAAAAMiscrlS7yNQI81mAAAAAACSCZsBAAAAAEgmbAYAAAAAIJnNZgAAAAAgsyolm815odkMAAAAAEAyYTMAAAAAAMmEzQAAAAAAJLPZDAAAAABkVrlUrvcRqJFmMwAAAAAAyYTNAAAAAAAkM6MBAAAAAGRWpVSp9xGokWYzAAAAAADJhM0AAAAAACQTNgMAAAAAkMxmMwAAAACQWTab80OzGQAAAACAZMJmAAAAAACSmdEAAAAAADKrXCrX+wjUSLMZAAAAAIBkwmYAAAAAAJIJmwEAAAAASGazGQAAAADIrEq5Uu8jUCPNZgAAAAAAkgmbAQAAAABIJmwGAAAAACCZzWYAAAAAILPKJZvNeaHZDAAAAABAMmEzAAAAAADJzGgAAAAAAJlVKZXrfQRqpNkMAAAAAEAyYTMAAAAAAMmEzQAAAAAAJLPZDAAAAABkVqVUqfcRqJFmMwAAAAAAyYTNAAAAAAAkEzYDAAAAAJlVLlVy+Rosu3btinnz5sXw4cNj5MiRsXDhwti7d+8R73/mmWeiUCgc9nX//ff333e47997773HdDabzQAAAAAAOTFv3rzYsWNHrFu3Lg4ePBgLFiyIxYsXxz333HPY+08++eTYsWNH1bUvf/nLccMNN8T73ve+qutf/epXY/bs2f1fjxw58pjOJmwGAAAAAMiBrVu3RldXVzz22GMxbdq0iIj40pe+FOeff37ceOON0draesh7GhoaoqWlperaAw88EH/5l38Zb3rTm6qujxw58pB7j4UZDQAAAACAAdbX1xd79uypevX19SU9s7u7O0aOHNkfNEdEtLe3R7FYjI0bN9b0jM2bN8eWLVti4cKFh3zv4x//eIwZMyamT58et99+e1QqxzYHImwGAAAAADKrUi7n8rV69eoYMWJE1Wv16tVJ/xY9PT0xbty4qmtDhgyJ0aNHR09PT03PuO222+L000+Pc889t+r6tddeG1//+tdj3bp1ceGFF8bHPvax+NKXvnRM5zOjAQAAAAAwwJYvXx6dnZ1V1xobGw9777Jly+K666476vO2bt2afKZf//rXcc8998TVV199yPf+77Wzzz479u3bFzfccEN84hOfqPn5wmYAAAAAgAHW2Nh4xHD5ty1dujQuueSSo95zyimnREtLS+zcubPq+quvvhq7du2qaWv5G9/4Ruzfvz/mz5//mve2tbXFqlWroq+vr+bPIWwGAAAAAKijsWPHxtixY1/zvhkzZsTu3btj8+bNMXXq1IiIePTRR6NcLkdbW9trvv+2226L97///TX9rC1btsSoUaNqDpojhM0AAAAAQIaVS8f2R+p+l51++ukxe/bsWLRoUaxduzYOHjwYS5Ysiblz50Zra2tERDz33HMxc+bMuOuuu2L69On9733yySfjBz/4QXznO9855Lnf/va3o7e3N84555xoamqKdevWxRe+8IW44oorjul8wmYAAAAAgJy4++67Y8mSJTFz5swoFotx4YUXxi233NL//YMHD8a2bdti//79Ve+7/fbb4y1veUvMmjXrkGeecMIJsWbNmvjkJz8ZlUolJk2aFDfddFMsWrTomM5WqFQq/q8BAAAAACCTvvv/fr/eRzguHf/f4/U+wutOsxkAAAAAyKyKGY3cKNb7AAAAAAAA5J+wGQAAAACAZMJmAAAAAACS2WwGAAAAADKrUirX+wjUSLMZAAAAAIBkwmYAAAAAAJKZ0QAAAAAAMqtcqtT7CNRIsxkAAAAAgGTCZgAAAAAAkgmbAQAAAABIZrMZAAAAAMisis3m3NBsBgAAAAAgmbAZAAAAAIBkwmYAAAAAAJLZbAYAAAAAMqtcsdmcF5rNAAAAAAAkEzYDAAAAAJDMjAYAAAAAkFklMxq5odkMAAAAAEAyYTMAAAAAAMmEzQAAAAAAJLPZDAAAAABkVslkc25oNgMAAAAAkEzYDAAAAABAMjMaAAAAAEBmlSp2NPJCsxkAAAAAgGTCZgAAAAAAkgmbAQAAAABIZrMZAAAAAMisksnm3NBsBgAAAAAgmbAZAAAAAIBkwmYAAAAAAJLZbAYAAAAAMqtUMdqcF5rNAAAAAAAkEzYDAAAAAJDMjAYAAAAAkFklKxq5odkMAAAAAEAyYTMAAAAAAMmEzQAAAAAAJLPZDAAAAABkVqlitDkvNJsBAAAAAEgmbAYAAAAAIJmwGQAAAACAZDabAQAAAIDMKplszg3NZgAAAAAAkgmbAQAAAABIZkYDAAAAAMgsMxr5odkMAAAAAEAyYTMAAAAAAMmEzQAAAAAAJLPZDAAAAABkVqlitDkvNJsBAAAAAEgmbAYAAAAAIJkZDQAAAAAgs0pWNHJDsxkAAAAAgGTCZgAAAAAAkgmbAQAAAABIZrMZAAAAAMisUsVoc15oNgMAAAAAkEzYDAAAAABAMmEzAAAAAADJbDYDAAAAAJlVMtmcG5rNAAAAAAAkEzYDAAAAAJDMjAYAAAAAkFmlih2NvNBsBgAAAAAgmbAZAAAAAIBkwmYAAAAAAJLZbAYAAAAAMqtksjk3NJsBAAAAAEgmbAYAAAAAIJkZDQAAAAAgs0oVOxp5odkMAAAAAEAyYTMAAAAAAMmEzQAAAAAAJLPZDAAAAABkVrneB6Bmms0AAAAAACQTNgMAAAAAkEzYDAAAAABAMpvNAAAAAEBmlSqVeh+BGmk2AwAAAACQTNgMAAAAAEAyMxoAAAAAQGaVrGjkhmYzAAAAAADJhM0AAAAAACQTNgMAAAAAkMxmMwAAAACQWaWK0ea80GwGAAAAACCZsBkAAAAAgGRmNAAAAACAzCpZ0cgNzWYAAAAAAJIJmwEAAAAASCZsBgAAAAAgmc1mAAAAACCzShWjzXmh2QwAAAAAQDJhMwAAAAAAyYTNAAAAAAAks9kMAAAAAGRWyWRzbmg2AwAAAACQTNgMAAAAAEAyMxoAAAAAQGaVKnY08kKzGQAAAACAZMJmAAAAAACSCZsBAAAAAHLi85//fJx77rkxbNiwGDlyZE3vqVQqsWLFihg/fnyceOKJ0d7eHr/4xS+q7tm1a1fMmzcvhg8fHiNHjoyFCxfG3r17j+lswmYAAAAAILNKlXy+BsuBAwfigx/8YFx66aU1v+f666+PW265JdauXRsbN26Mk046KTo6OuKVV17pv2fevHnx85//PNatWxcPPfRQ/OAHP4jFixcf09kKlYqFbQAAAAAgmz5amFDvIxyXtZVnBvX5d9xxR1x++eWxe/fuo95XqVSitbU1li5dGldccUVERLz88svR3Nwcd9xxR8ydOze2bt0akydPjsceeyymTZsWERFdXV1x/vnnx7PPPhutra01nUmzGQAAAADgd9TTTz8dPT090d7e3n9txIgR0dbWFt3d3RER0d3dHSNHjuwPmiMi2tvbo1gsxsaNG2v+WUMG7tgAAAAAAERE9PX1RV9fX9W1xsbGaGxsfF3P0dPTExERzc3NVdebm5v7v9fT0xPjxo2r+v6QIUNi9OjR/ffUQtgMAAAAAGTWYM9RDJZrrrkmPvvZz1ZdW7lyZVxzzTWH3Lts2bK47rrrjvq8rVu3xmmnnTaQRxxwwmYAAAAAgAG2fPny6OzsrLp2pFbz0qVL45JLLjnq80455ZTjOkdLS0tERPT29sb48eP7r/f29saUKVP679m5c2fV+1599dXYtWtX//trIWwGAAAAABhgxzKZMXbs2Bg7duygnGPixInR0tIS69ev7w+X9+zZExs3boxLL700IiJmzJgRu3fvjs2bN8fUqVMjIuLRRx+NcrkcbW1tNf8sfyAQAAAAACAntm/fHlu2bInt27dHqVSKLVu2xJYtW2Lv3r3995x22mnxwAMPREREoVCIyy+/PD73uc/Ft771rfjpT38a8+fPj9bW1pgzZ05ERJx++ukxe/bsWLRoUWzatCl++MMfxpIlS2Lu3LnR2tpa89k0mwEAAAAAcmLFihVx55139n999tlnR0TE97///XjPe94TERHbtm2Ll19+uf+eK6+8Mvbt2xeLFy+O3bt3x3nnnRddXV3R1NTUf8/dd98dS5YsiZkzZ0axWIwLL7wwbrnllmM6W6FSqVQSPhsAAAAAAJjRAAAAAAAgnbAZAAAAAIBkwmYAAAAAAJIJmwEAAAAASCZsBgAAAAAgmbAZAAAAAIBkwmYAAAAAAJIJmwEAAAAASCZsBgAAAAAgmbAZAAAAAIBkwmYAAAAAAJIJmwEAAAAASPb/A3uteMpHfcVrAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 2000x1200 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["msno.heatmap(df)"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\scipy\\cluster\\hierarchy.py:2821: UserWarning: Attempting to set identical low and high ylims makes transformation singular; automatically expanding.\n", "  ax.set_ylim([dvw, 0])\n"]}, {"data": {"text/plain": ["<Axes: >"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 2500x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["msno.dendrogram(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.isnull().sum()\n", "df[df.isnull().any(axis=1)]\n", "df.dropna()\n", "df.dropna(axis=1)\n", "# df.fillna(1000)\n", "df.fillna(method='ffill')\n", "df.fillna(method='bfill')\n", "\n", "df.interpolate(method='linear')\n", "\n", "df.fillna(value=df.mean())\n", "\n", "df.describe() "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "59b5f8ef", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "abd35621", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9d2e7857", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d7059456", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "00f68925", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "783891be", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "476750b1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}