{"cells": [{"cell_type": "code", "execution_count": 1, "id": "cd2fa6c4", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-12 17:50:18.482 WARNING streamlit.runtime.scriptrunner_utils.script_run_context: Thread 'MainThread': missing ScriptRunContext! This warning can be ignored when running in bare mode.\n", "2025-07-12 17:50:19.648 \n", "  \u001b[33m\u001b[1mWarning:\u001b[0m to view this Streamlit app on a browser, run it with the following\n", "  command:\n", "\n", "    streamlit run C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\ipykernel_launcher.py [ARGUMENTS]\n", "2025-07-12 17:50:19.649 Thread 'MainThread': missing <PERSON><PERSON>tRunContext! This warning can be ignored when running in bare mode.\n", "2025-07-12 17:50:19.650 Thread 'MainThread': missing <PERSON><PERSON>tRunContext! This warning can be ignored when running in bare mode.\n", "2025-07-12 17:50:19.651 Thread 'MainThread': missing <PERSON><PERSON>tRunContext! This warning can be ignored when running in bare mode.\n", "2025-07-12 17:50:19.652 Thread 'MainThread': missing <PERSON><PERSON>tRunContext! This warning can be ignored when running in bare mode.\n", "2025-07-12 17:50:19.654 Thread 'MainThread': missing <PERSON><PERSON>tRunContext! This warning can be ignored when running in bare mode.\n", "2025-07-12 17:50:19.655 Thread 'MainThread': missing <PERSON><PERSON>tRunContext! This warning can be ignored when running in bare mode.\n", "2025-07-12 17:50:19.657 Thread 'MainThread': missing <PERSON><PERSON>tRunContext! This warning can be ignored when running in bare mode.\n", "2025-07-12 17:50:19.658 Thread 'MainThread': missing <PERSON><PERSON>tRunContext! This warning can be ignored when running in bare mode.\n", "2025-07-12 17:50:19.659 Thread 'MainThread': missing <PERSON><PERSON>tRunContext! This warning can be ignored when running in bare mode.\n", "2025-07-12 17:50:19.660 Thread 'MainThread': missing <PERSON><PERSON>tRunContext! This warning can be ignored when running in bare mode.\n", "2025-07-12 17:50:19.661 Thread 'MainThread': missing <PERSON><PERSON>tRunContext! This warning can be ignored when running in bare mode.\n"]}], "source": ["#Import the required Libraries\n", "\n", "import streamlit as st\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "# Add a title and intro text\n", "st.title('Earthquake Data Explorer')\n", "st.text('This is a web app to allow exploration of Earthquake Data')\n", "\n", "# Create file uploader object\n", "upload_file = st.file_uploader(\"C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/CSV Data/kaggle_significant_earthquakes_database.csv\")\n", "\n", "# Check to see if a file has been uploaded\n", "if upload_file is not None:\n", "    # If it has then do the following:\n", "\n", "    # Read the file to a dataframe using pandas\n", "    df = pd.read_csv(upload_file)\n", "\n", "    # Create a section for the dataframe statistics\n", "    st.header('Statistics of Dataframe')\n", "    st.write(df.describe())\n", "\n", "    # Create a section for the dataframe header\n", "    st.header('Header of Dataframe')\n", "    st.write(df.head())\n", "\n", "    # Create a section for matplotlib figure\n", "    st.header('Plot of Data')\n", "    \n", "    fig, ax = plt.subplots(1,1)\n", "    ax.scatter(x=df['Depth'], y=df['Magnitude'])\n", "    ax.set_xlabel('Depth')\n", "    ax.set_ylabel('Magnitude')\n", "    \n", "    st.pyplot(fig)"]}, {"cell_type": "code", "execution_count": null, "id": "f3de92fe", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}