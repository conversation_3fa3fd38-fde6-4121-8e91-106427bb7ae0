//
// Generated by NVIDIA NVVM Compiler
//
// Compiler Build ID: CL-27506705
// Cuda compilation tools, release 10.2, V10.2.89
// Based on LLVM 3.4svn
//

.version 6.5
.target sm_50
.address_size 64

	// .globl	bar

.visible .func  (.param .b32 func_retval0) bar(
	.param .b64 bar_param_0,
	.param .b32 bar_param_1
)
{
	.reg .b32 	%r<4>;
	.reg .b64 	%rd<2>;


	ld.param.u64 	%rd1, [bar_param_0];
	ld.param.u32 	%r1, [bar_param_1];
	shl.b32 	%r2, %r1, 1;
	st.u32 	[%rd1], %r2;
	mov.u32 	%r3, 0;
	st.param.b32	[func_retval0+0], %r3;
	ret;
}

	// .globl	array_mutator
.visible .func  (.param .b32 func_retval0) array_mutator(
	.param .b64 array_mutator_param_0,
	.param .b64 array_mutator_param_1
)
{
	.reg .b32 	%r<3>;
	.reg .b64 	%rd<2>;


	ld.param.u64 	%rd1, [array_mutator_param_1];
	ld.u32 	%r1, [%rd1+4];
	st.u32 	[%rd1], %r1;
	mov.u32 	%r2, 0;
	st.param.b32	[func_retval0+0], %r2;
	ret;
}


