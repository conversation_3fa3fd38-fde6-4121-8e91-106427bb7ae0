2015-10-17 18:09:20,787 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445076437777_0003_000001
2015-10-17 18:09:21,334 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 18:09:21,334 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 3 cluster_timestamp: 1445076437777 } attemptId: 1 } keyId: 291674728)
2015-10-17 18:09:21,600 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 18:09:22,350 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 18:09:22,443 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 18:09:22,475 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 18:09:22,475 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 18:09:22,475 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 18:09:22,475 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 18:09:22,490 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 18:09:22,490 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 18:09:22,490 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 18:09:22,490 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 18:09:22,537 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 18:09:22,553 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 18:09:22,584 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 18:09:22,600 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 18:09:22,646 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 18:09:22,975 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 18:09:23,037 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 18:09:23,037 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 18:09:23,053 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445076437777_0003 to jobTokenSecretManager
2015-10-17 18:09:23,240 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445076437777_0003 because: not enabled; too many maps; too much input;
2015-10-17 18:09:23,256 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445076437777_0003 = 1256521728. Number of splits = 10
2015-10-17 18:09:23,256 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445076437777_0003 = 1
2015-10-17 18:09:23,256 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445076437777_0003Job Transitioned from NEW to INITED
2015-10-17 18:09:23,256 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445076437777_0003.
2015-10-17 18:09:23,303 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 18:09:23,318 INFO [Socket Reader #1 for port 53655] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 53655
2015-10-17 18:09:23,334 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 18:09:23,334 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 18:09:23,334 INFO [IPC Server listener on 53655] org.apache.hadoop.ipc.Server: IPC Server listener on 53655: starting
2015-10-17 18:09:23,350 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MININT-75DGDAM1.fareast.corp.microsoft.com/************:53655
2015-10-17 18:09:23,412 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 18:09:23,412 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 18:09:23,428 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 18:09:23,428 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 18:09:23,428 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 18:09:23,443 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 18:09:23,443 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 18:09:23,443 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 53662
2015-10-17 18:09:23,443 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 18:09:23,490 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\Jetty_0_0_0_0_53662_mapreduce____.wrtujf\webapp
2015-10-17 18:09:23,709 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:53662
2015-10-17 18:09:23,709 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 53662
2015-10-17 18:09:24,193 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 18:09:24,193 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445076437777_0003
2015-10-17 18:09:24,193 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 18:09:24,193 INFO [Socket Reader #1 for port 53665] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 53665
2015-10-17 18:09:24,209 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 18:09:24,209 INFO [IPC Server listener on 53665] org.apache.hadoop.ipc.Server: IPC Server listener on 53665: starting
2015-10-17 18:09:24,225 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 18:09:24,225 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 18:09:24,225 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 18:09:24,271 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-17 18:09:24,381 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 18:09:24,381 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 18:09:24,381 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 18:09:24,396 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 18:09:24,396 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445076437777_0003Job Transitioned from INITED to SETUP
2015-10-17 18:09:24,412 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 18:09:24,412 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445076437777_0003Job Transitioned from SETUP to RUNNING
2015-10-17 18:09:24,443 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,443 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,443 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,459 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 18:09:24,475 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 18:09:24,506 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445076437777_0003, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0003/job_1445076437777_0003_1.jhist
2015-10-17 18:09:25,381 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 18:09:25,443 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:32768, vCores:-3> knownNMs=5
2015-10-17 18:09:25,443 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:32768, vCores:-3>
2015-10-17 18:09:25,443 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:26,474 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 4
2015-10-17 18:09:26,474 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0003_01_000002 to attempt_1445076437777_0003_m_000000_0
2015-10-17 18:09:26,474 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0003_01_000003 to attempt_1445076437777_0003_m_000001_0
2015-10-17 18:09:26,474 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0003_01_000004 to attempt_1445076437777_0003_m_000002_0
2015-10-17 18:09:26,474 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0003_01_000005 to attempt_1445076437777_0003_m_000003_0
2015-10-17 18:09:26,474 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:18432, vCores:-17>
2015-10-17 18:09:26,474 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:26,474 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:6 ScheduledReds:0 AssignedMaps:4 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:4 ContRel:0 HostLocal:4 RackLocal:0
2015-10-17 18:09:26,553 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,584 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0003/job.jar
2015-10-17 18:09:26,584 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0003/job.xml
2015-10-17 18:09:26,584 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 18:09:26,584 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 18:09:26,584 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 18:09:26,631 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:26,631 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,631 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:26,631 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,631 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:26,646 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,646 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:26,646 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0003_01_000002 taskAttempt attempt_1445076437777_0003_m_000000_0
2015-10-17 18:09:26,646 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0003_01_000003 taskAttempt attempt_1445076437777_0003_m_000001_0
2015-10-17 18:09:26,646 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0003_01_000004 taskAttempt attempt_1445076437777_0003_m_000002_0
2015-10-17 18:09:26,646 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0003_01_000005 taskAttempt attempt_1445076437777_0003_m_000003_0
2015-10-17 18:09:26,646 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0003_m_000000_0
2015-10-17 18:09:26,646 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0003_m_000002_0
2015-10-17 18:09:26,646 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0003_m_000001_0
2015-10-17 18:09:26,646 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0003_m_000003_0
2015-10-17 18:09:26,646 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:09:26,662 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:09:26,662 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:09:26,678 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:09:27,490 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0003_m_000000_0 : 13562
2015-10-17 18:09:27,490 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0003_m_000003_0 : 13562
2015-10-17 18:09:27,490 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0003_m_000002_0 : 13562
2015-10-17 18:09:27,490 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0003_m_000001_0 : 13562
2015-10-17 18:09:27,506 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0003_m_000000_0] using containerId: [container_1445076437777_0003_01_000002 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:09:27,506 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:27,506 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0003_m_000002_0] using containerId: [container_1445076437777_0003_01_000004 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:09:27,506 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:27,506 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0003_m_000001_0] using containerId: [container_1445076437777_0003_01_000003 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:09:27,506 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:27,506 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0003_m_000003_0] using containerId: [container_1445076437777_0003_01_000005 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:09:27,506 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:27,506 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0003_m_000000
2015-10-17 18:09:27,506 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:27,506 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0003_m_000002
2015-10-17 18:09:27,506 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:27,506 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0003_m_000001
2015-10-17 18:09:27,506 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:27,506 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0003_m_000003
2015-10-17 18:09:27,506 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:27,521 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0003: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:17408, vCores:-18> knownNMs=5
2015-10-17 18:09:27,521 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:09:27,521 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:27,521 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0003_01_000006 to attempt_1445076437777_0003_m_000004_0
2015-10-17 18:09:27,521 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:17408, vCores:-18>
2015-10-17 18:09:27,521 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:27,521 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:5 ContRel:0 HostLocal:4 RackLocal:1
2015-10-17 18:09:27,521 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:27,521 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:27,521 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0003_01_000006 taskAttempt attempt_1445076437777_0003_m_000004_0
2015-10-17 18:09:27,521 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0003_m_000004_0
2015-10-17 18:09:27,521 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:53425
2015-10-17 18:09:27,537 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0003_m_000004_0 : 13562
2015-10-17 18:09:27,537 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0003_m_000004_0] using containerId: [container_1445076437777_0003_01_000006 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:53425]
2015-10-17 18:09:27,553 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:27,553 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0003_m_000004
2015-10-17 18:09:27,553 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:28,537 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0003: ask=4 release= 0 newContainers=2 finishedContainers=0 resourcelimit=<memory:14336, vCores:-21> knownNMs=5
2015-10-17 18:09:28,537 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 18:09:28,537 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:28,537 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0003_01_000007 to attempt_1445076437777_0003_m_000005_0
2015-10-17 18:09:28,537 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:28,537 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:28,537 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0003_01_000007 taskAttempt attempt_1445076437777_0003_m_000005_0
2015-10-17 18:09:28,537 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0003_m_000005_0
2015-10-17 18:09:28,537 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:28,537 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 18:09:28,537 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0003_01_000008 to attempt_1445076437777_0003_m_000006_0
2015-10-17 18:09:28,537 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:14336, vCores:-21>
2015-10-17 18:09:28,537 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:28,537 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:4 RackLocal:3
2015-10-17 18:09:28,537 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:28,537 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:28,537 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0003_01_000008 taskAttempt attempt_1445076437777_0003_m_000006_0
2015-10-17 18:09:28,537 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0003_m_000006_0
2015-10-17 18:09:28,537 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 18:09:28,584 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0003_m_000005_0 : 13562
2015-10-17 18:09:28,599 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0003_m_000005_0] using containerId: [container_1445076437777_0003_01_000007 on NM: [04DN8IQ.fareast.corp.microsoft.com:52150]
2015-10-17 18:09:28,599 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:28,599 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0003_m_000005
2015-10-17 18:09:28,599 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:28,646 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0003_m_000006_0 : 13562
2015-10-17 18:09:28,646 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0003_m_000006_0] using containerId: [container_1445076437777_0003_01_000008 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:64642]
2015-10-17 18:09:28,646 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:28,646 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0003_m_000006
2015-10-17 18:09:28,646 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:29,537 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0003: ask=4 release= 0 newContainers=2 finishedContainers=0 resourcelimit=<memory:11264, vCores:-24> knownNMs=5
2015-10-17 18:09:29,537 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 18:09:29,537 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:29,537 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0003_01_000009 to attempt_1445076437777_0003_m_000007_0
2015-10-17 18:09:29,537 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:29,537 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0003_01_000010 to attempt_1445076437777_0003_m_000008_0
2015-10-17 18:09:29,537 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:29,537 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:11264, vCores:-24>
2015-10-17 18:09:29,537 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:29,537 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:4 RackLocal:5
2015-10-17 18:09:29,553 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:29,553 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:29,553 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:29,553 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0003_01_000009 taskAttempt attempt_1445076437777_0003_m_000007_0
2015-10-17 18:09:29,553 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0003_m_000007_0
2015-10-17 18:09:29,553 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 18:09:29,553 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0003_01_000010 taskAttempt attempt_1445076437777_0003_m_000008_0
2015-10-17 18:09:29,553 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0003_m_000008_0
2015-10-17 18:09:29,553 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:53425
2015-10-17 18:09:29,584 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0003_m_000008_0 : 13562
2015-10-17 18:09:29,584 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0003_m_000008_0] using containerId: [container_1445076437777_0003_01_000010 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:53425]
2015-10-17 18:09:29,584 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:29,584 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0003_m_000008
2015-10-17 18:09:29,584 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:29,771 INFO [Socket Reader #1 for port 53665] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0003 (auth:SIMPLE)
2015-10-17 18:09:29,818 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0003_m_000006 asked for a task
2015-10-17 18:09:29,818 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0003_m_000006 given task: attempt_1445076437777_0003_m_000004_0
2015-10-17 18:09:29,834 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0003_m_000007_0 : 13562
2015-10-17 18:09:29,834 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0003_m_000007_0] using containerId: [container_1445076437777_0003_01_000009 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:64642]
2015-10-17 18:09:29,834 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:29,834 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0003_m_000007
2015-10-17 18:09:29,834 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:30,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0003: ask=4 release= 0 newContainers=2 finishedContainers=0 resourcelimit=<memory:8192, vCores:-27> knownNMs=5
2015-10-17 18:09:30,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 18:09:30,568 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:30,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0003_01_000011 to attempt_1445076437777_0003_m_000009_0
2015-10-17 18:09:30,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Releasing unassigned and invalid container Container: [ContainerId: container_1445076437777_0003_01_000012, NodeId: MININT-FNANLI5.fareast.corp.microsoft.com:64642, NodeHttpAddress: MININT-FNANLI5.fareast.corp.microsoft.com:8042, Resource: <memory:1024, vCores:1>, Priority: 20, Token: Token { kind: ContainerToken, service: *************:64642 }, ]. RM may have assignment issues
2015-10-17 18:09:30,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-27>
2015-10-17 18:09:30,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:30,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:4 RackLocal:6
2015-10-17 18:09:30,568 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:30,568 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:30,631 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0003_01_000011 taskAttempt attempt_1445076437777_0003_m_000009_0
2015-10-17 18:09:30,631 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0003_m_000009_0
2015-10-17 18:09:30,646 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 18:09:30,771 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0003_m_000009_0 : 13562
2015-10-17 18:09:30,771 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0003_m_000009_0] using containerId: [container_1445076437777_0003_01_000011 on NM: [04DN8IQ.fareast.corp.microsoft.com:52150]
2015-10-17 18:09:30,771 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:30,771 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0003_m_000009
2015-10-17 18:09:30,771 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:31,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0003: ask=4 release= 1 newContainers=1 finishedContainers=1 resourcelimit=<memory:5120, vCores:-30> knownNMs=5
2015-10-17 18:09:31,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0003_01_000012
2015-10-17 18:09:31,615 ERROR [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Container complete event for unknown container id container_1445076437777_0003_01_000012
2015-10-17 18:09:31,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:09:31,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Cannot assign container Container: [ContainerId: container_1445076437777_0003_01_000013, NodeId: 04DN8IQ.fareast.corp.microsoft.com:52150, NodeHttpAddress: 04DN8IQ.fareast.corp.microsoft.com:8042, Resource: <memory:1024, vCores:1>, Priority: 20, Token: Token { kind: ContainerToken, service: ***********:52150 }, ] for a map as either  container memory less than required <memory:1024, vCores:1> or no pending map tasks - maps.isEmpty=true
2015-10-17 18:09:31,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:5120, vCores:-30>
2015-10-17 18:09:31,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:31,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:12 ContRel:2 HostLocal:4 RackLocal:6
2015-10-17 18:09:31,724 INFO [Socket Reader #1 for port 53665] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0003 (auth:SIMPLE)
2015-10-17 18:09:31,756 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0003_m_000007 asked for a task
2015-10-17 18:09:31,756 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0003_m_000007 given task: attempt_1445076437777_0003_m_000005_0
2015-10-17 18:09:32,662 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0003: ask=0 release= 1 newContainers=0 finishedContainers=1 resourcelimit=<memory:4096, vCores:-31> knownNMs=5
2015-10-17 18:09:32,662 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0003_01_000013
2015-10-17 18:09:32,662 ERROR [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Container complete event for unknown container id container_1445076437777_0003_01_000013
2015-10-17 18:09:32,662 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-31>
2015-10-17 18:09:32,662 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:33,631 INFO [Socket Reader #1 for port 53665] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0003 (auth:SIMPLE)
2015-10-17 18:09:33,693 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0003_m_000010 asked for a task
2015-10-17 18:09:33,693 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0003_m_000010 given task: attempt_1445076437777_0003_m_000008_0
2015-10-17 18:09:34,724 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-33>
2015-10-17 18:09:34,724 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:34,818 INFO [Socket Reader #1 for port 53665] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0003 (auth:SIMPLE)
2015-10-17 18:09:34,834 INFO [Socket Reader #1 for port 53665] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0003 (auth:SIMPLE)
2015-10-17 18:09:34,849 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0003_m_000004 asked for a task
2015-10-17 18:09:34,849 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0003_m_000004 given task: attempt_1445076437777_0003_m_000002_0
2015-10-17 18:09:34,865 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0003_m_000002 asked for a task
2015-10-17 18:09:34,865 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0003_m_000002 given task: attempt_1445076437777_0003_m_000000_0
2015-10-17 18:09:34,912 INFO [Socket Reader #1 for port 53665] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0003 (auth:SIMPLE)
2015-10-17 18:09:35,006 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0003_m_000011 asked for a task
2015-10-17 18:09:35,006 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0003_m_000011 given task: attempt_1445076437777_0003_m_000009_0
2015-10-17 18:09:35,240 INFO [Socket Reader #1 for port 53665] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0003 (auth:SIMPLE)
2015-10-17 18:09:35,318 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0003_m_000009 asked for a task
2015-10-17 18:09:35,318 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0003_m_000009 given task: attempt_1445076437777_0003_m_000007_0
2015-10-17 18:09:35,568 INFO [Socket Reader #1 for port 53665] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0003 (auth:SIMPLE)
2015-10-17 18:09:35,584 INFO [Socket Reader #1 for port 53665] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0003 (auth:SIMPLE)
2015-10-17 18:09:35,615 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0003_m_000003 asked for a task
2015-10-17 18:09:35,615 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0003_m_000003 given task: attempt_1445076437777_0003_m_000001_0
2015-10-17 18:09:35,630 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0003_m_000005 asked for a task
2015-10-17 18:09:35,630 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0003_m_000005 given task: attempt_1445076437777_0003_m_000003_0
2015-10-17 18:09:35,771 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 18:09:35,771 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:37,912 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:37,912 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:41,833 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.029655661
2015-10-17 18:09:41,927 INFO [Socket Reader #1 for port 53665] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0003 (auth:SIMPLE)
2015-10-17 18:09:42,130 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0003_m_000008 asked for a task
2015-10-17 18:09:42,130 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0003_m_000008 given task: attempt_1445076437777_0003_m_000006_0
2015-10-17 18:09:43,677 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.09972117
2015-10-17 18:09:43,677 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.10186296
2015-10-17 18:09:44,677 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.09672602
2015-10-17 18:09:44,755 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.102750935
2015-10-17 18:09:45,552 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.06722589
2015-10-17 18:09:46,724 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.10660437
2015-10-17 18:09:46,724 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.10635664
2015-10-17 18:09:47,693 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.106493875
2015-10-17 18:09:47,818 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.1066108
2015-10-17 18:09:48,068 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.047874123
2015-10-17 18:09:48,865 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.096806146
2015-10-17 18:09:49,802 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.10635664
2015-10-17 18:09:49,802 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.10660437
2015-10-17 18:09:50,771 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.106493875
2015-10-17 18:09:50,802 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.037777647
2015-10-17 18:09:50,927 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.1066108
2015-10-17 18:09:51,099 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.05503785
2015-10-17 18:09:52,364 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.10685723
2015-10-17 18:09:52,833 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.11312911
2015-10-17 18:09:52,833 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.109317794
2015-10-17 18:09:53,833 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.0436392
2015-10-17 18:09:54,130 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.067413874
2015-10-17 18:09:54,364 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.12477529
2015-10-17 18:09:54,724 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.106493875
2015-10-17 18:09:54,989 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.030078541
2015-10-17 18:09:55,536 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.10685723
2015-10-17 18:09:55,880 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.19212553
2015-10-17 18:09:55,880 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.19158794
2015-10-17 18:09:56,864 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.05568746
2015-10-17 18:09:57,161 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.07653509
2015-10-17 18:09:57,442 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.19211523
2015-10-17 18:09:57,755 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.19209063
2015-10-17 18:09:58,130 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.06390381
2015-10-17 18:09:58,942 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.19212553
2015-10-17 18:09:58,942 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.19158794
2015-10-17 18:09:59,020 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.10685723
2015-10-17 18:09:59,911 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.06643759
2015-10-17 18:10:00,192 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.09021357
2015-10-17 18:10:00,489 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.19211523
2015-10-17 18:10:01,224 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.08280729
2015-10-17 18:10:01,474 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.0577152
2015-10-17 18:10:01,708 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.19209063
2015-10-17 18:10:02,005 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.19158794
2015-10-17 18:10:02,005 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.19212553
2015-10-17 18:10:02,161 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.10685723
2015-10-17 18:10:02,927 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.07946373
2015-10-17 18:10:03,208 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.09444502
2015-10-17 18:10:03,552 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.19211523
2015-10-17 18:10:03,911 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.01921069
2015-10-17 18:10:04,286 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.09091126
2015-10-17 18:10:04,802 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.1951197
2015-10-17 18:10:05,223 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.10685723
2015-10-17 18:10:05,364 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.2456138
2015-10-17 18:10:05,364 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.23412894
2015-10-17 18:10:05,911 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.08630316
2015-10-17 18:10:05,989 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.08727966
2015-10-17 18:10:06,255 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.10389169
2015-10-17 18:10:06,630 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.24370016
2015-10-17 18:10:08,161 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.10171276
2015-10-17 18:10:08,161 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.040057663
2015-10-17 18:10:08,161 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.27367863
2015-10-17 18:10:08,286 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.119198576
2015-10-17 18:10:08,395 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.27696857
2015-10-17 18:10:08,426 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.27772525
2015-10-17 18:10:09,036 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.09477099
2015-10-17 18:10:09,348 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.10680563
2015-10-17 18:10:09,551 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.10681946
2015-10-17 18:10:09,708 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.27776006
2015-10-17 18:10:11,208 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.27765483
2015-10-17 18:10:11,239 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.11971541
2015-10-17 18:10:11,442 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.123431735
2015-10-17 18:10:11,489 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.27696857
2015-10-17 18:10:11,505 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.27772525
2015-10-17 18:10:11,708 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.06024888
2015-10-17 18:10:12,129 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.106881365
2015-10-17 18:10:12,442 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.10680563
2015-10-17 18:10:13,629 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.27776006
2015-10-17 18:10:13,770 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.10681946
2015-10-17 18:10:14,270 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.27765483
2015-10-17 18:10:14,317 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.12781532
2015-10-17 18:10:14,536 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.1289698
2015-10-17 18:10:14,567 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.27772525
2015-10-17 18:10:14,583 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.27696857
2015-10-17 18:10:15,708 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.106881365
2015-10-17 18:10:15,911 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.107800335
2015-10-17 18:10:16,692 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.27776006
2015-10-17 18:10:16,895 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.0906864
2015-10-17 18:10:17,379 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.30860466
2015-10-17 18:10:17,489 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.13411696
2015-10-17 18:10:17,676 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.35174778
2015-10-17 18:10:17,676 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.35229892
2015-10-17 18:10:17,723 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.13222575
2015-10-17 18:10:17,739 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.10681946
2015-10-17 18:10:19,176 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.106881365
2015-10-17 18:10:19,286 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.12180473
2015-10-17 18:10:19,770 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.35306296
2015-10-17 18:10:20,411 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.36323506
2015-10-17 18:10:20,629 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.14761677
2015-10-17 18:10:20,739 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.106964506
2015-10-17 18:10:20,739 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.36317363
2015-10-17 18:10:20,739 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.3624012
2015-10-17 18:10:20,879 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.14036779
2015-10-17 18:10:21,660 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.10681946
2015-10-17 18:10:22,520 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.106881365
2015-10-17 18:10:22,567 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.14287353
2015-10-17 18:10:22,817 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.36319977
2015-10-17 18:10:23,457 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.36323506
2015-10-17 18:10:24,395 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.14785944
2015-10-17 18:10:24,520 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.106964506
2015-10-17 18:10:24,692 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.3624012
2015-10-17 18:10:24,692 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.36317363
2015-10-17 18:10:24,723 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.16381802
2015-10-17 18:10:25,301 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.10681946
2015-10-17 18:10:25,973 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.106881365
2015-10-17 18:10:25,988 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.16218708
2015-10-17 18:10:26,176 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.36319977
2015-10-17 18:10:26,535 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.36323506
2015-10-17 18:10:27,488 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.15241942
2015-10-17 18:10:27,801 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.36317363
2015-10-17 18:10:27,801 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.3624012
2015-10-17 18:10:27,942 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.19622828
2015-10-17 18:10:28,285 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.106964506
2015-10-17 18:10:29,254 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.37785006
2015-10-17 18:10:29,395 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.10681946
2015-10-17 18:10:29,629 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.43145126
2015-10-17 18:10:29,785 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.106881365
2015-10-17 18:10:29,848 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.18164527
2015-10-17 18:10:30,863 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.38384512
2015-10-17 18:10:30,895 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.37810132
2015-10-17 18:10:31,223 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.16088627
2015-10-17 18:10:31,254 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.20612393
2015-10-17 18:10:32,098 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.106964506
2015-10-17 18:10:32,316 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.448704
2015-10-17 18:10:32,723 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.4486067
2015-10-17 18:10:33,238 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.10681946
2015-10-17 18:10:33,285 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.1291798
2015-10-17 18:10:33,395 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.19242907
2015-10-17 18:10:33,895 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.44355696
2015-10-17 18:10:34,254 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.44240847
2015-10-17 18:10:35,129 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.17610462
2015-10-17 18:10:35,129 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.24392761
2015-10-17 18:10:35,394 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.448704
2015-10-17 18:10:36,285 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.106964506
2015-10-17 18:10:36,754 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.16609496
2015-10-17 18:10:36,769 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.19242907
2015-10-17 18:10:37,238 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.4486067
2015-10-17 18:10:37,254 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.44789755
2015-10-17 18:10:37,301 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.44859612
2015-10-17 18:10:37,379 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.10681946
2015-10-17 18:10:38,426 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.448704
2015-10-17 18:10:38,832 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.18655252
2015-10-17 18:10:38,879 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.2903803
2015-10-17 18:10:40,144 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.18694025
2015-10-17 18:10:40,269 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.19242907
2015-10-17 18:10:40,301 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.4486067
2015-10-17 18:10:40,316 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.44789755
2015-10-17 18:10:40,363 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.44859612
2015-10-17 18:10:40,379 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.106964506
2015-10-17 18:10:41,066 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.10681946
2015-10-17 18:10:41,472 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.448704
2015-10-17 18:10:42,379 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.19247705
2015-10-17 18:10:42,582 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.295472
2015-10-17 18:10:43,347 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.47934672
2015-10-17 18:10:43,363 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.44789755
2015-10-17 18:10:43,426 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.44859612
2015-10-17 18:10:43,847 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.19258286
2015-10-17 18:10:43,863 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.19242907
2015-10-17 18:10:44,035 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.106964506
2015-10-17 18:10:44,519 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.46418303
2015-10-17 18:10:45,410 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.10681946
2015-10-17 18:10:45,910 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.19247705
2015-10-17 18:10:46,066 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.295472
2015-10-17 18:10:46,441 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.5312925
2015-10-17 18:10:46,441 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.44789755
2015-10-17 18:10:46,472 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.44859612
2015-10-17 18:10:47,160 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.19258286
2015-10-17 18:10:47,175 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.19242907
2015-10-17 18:10:47,535 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.53425497
2015-10-17 18:10:48,160 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.106964506
2015-10-17 18:10:49,097 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.10681946
2015-10-17 18:10:49,519 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.5343203
2015-10-17 18:10:49,519 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.46694997
2015-10-17 18:10:49,550 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.45989418
2015-10-17 18:10:49,582 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.19247705
2015-10-17 18:10:49,769 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.295472
2015-10-17 18:10:50,410 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.19258286
2015-10-17 18:10:50,566 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.19242907
2015-10-17 18:10:50,597 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.53425497
2015-10-17 18:10:51,957 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.106964506
2015-10-17 18:10:52,597 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.53341997
2015-10-17 18:10:52,597 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.5343203
2015-10-17 18:10:53,300 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.295472
2015-10-17 18:10:53,347 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.13016962
2015-10-17 18:10:53,472 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.5342037
2015-10-17 18:10:53,550 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.19258286
2015-10-17 18:10:53,644 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.53425497
2015-10-17 18:10:53,675 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.19247705
2015-10-17 18:10:53,738 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.19242907
2015-10-17 18:10:55,628 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.53341997
2015-10-17 18:10:55,628 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.5500805
2015-10-17 18:10:55,910 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.106964506
2015-10-17 18:10:56,503 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.5342037
2015-10-17 18:10:56,691 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.5728454
2015-10-17 18:10:56,785 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.295472
2015-10-17 18:10:57,050 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.16929153
2015-10-17 18:10:57,300 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.19258286
2015-10-17 18:10:57,300 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.19247705
2015-10-17 18:10:57,550 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.19242907
2015-10-17 18:10:58,644 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.6199081
2015-10-17 18:10:58,644 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.53341997
2015-10-17 18:10:59,581 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.53646135
2015-10-17 18:10:59,722 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.6197233
2015-10-17 18:10:59,941 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.106964506
2015-10-17 18:11:00,159 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.295472
2015-10-17 18:11:00,644 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.19247705
2015-10-17 18:11:00,659 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.19258286
2015-10-17 18:11:01,066 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.19255035
2015-10-17 18:11:01,066 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.21769437
2015-10-17 18:11:01,706 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.60506225
2015-10-17 18:11:01,706 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.6199081
2015-10-17 18:11:02,659 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.6196791
2015-10-17 18:11:02,784 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.6197233
2015-10-17 18:11:03,519 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.13515526
2015-10-17 18:11:03,691 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.295472
2015-10-17 18:11:04,237 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.19258286
2015-10-17 18:11:04,534 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.25245658
2015-10-17 18:11:04,628 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.19247705
2015-10-17 18:11:04,628 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.19255035
2015-10-17 18:11:04,784 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.6199081
2015-10-17 18:11:04,784 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.61898744
2015-10-17 18:11:05,753 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.6196791
2015-10-17 18:11:05,816 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.6197233
2015-10-17 18:11:06,706 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.6199081
2015-10-17 18:11:06,847 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.295472
2015-10-17 18:11:07,159 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.16349196
2015-10-17 18:11:07,503 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.6197233
2015-10-17 18:11:07,737 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.19540702
2015-10-17 18:11:07,737 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.19258286
2015-10-17 18:11:07,831 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.61898744
2015-10-17 18:11:07,831 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.667
2015-10-17 18:11:08,128 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.2781602
2015-10-17 18:11:08,597 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.19255035
2015-10-17 18:11:08,784 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.6196791
2015-10-17 18:11:08,878 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.667
2015-10-17 18:11:09,909 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.33664805
2015-10-17 18:11:11,128 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.21429667
2015-10-17 18:11:11,409 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.2781602
2015-10-17 18:11:11,737 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.20583145
2015-10-17 18:11:11,768 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.667
2015-10-17 18:11:11,784 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.61898744
2015-10-17 18:11:11,815 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.6227687
2015-10-17 18:11:11,909 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.667
2015-10-17 18:11:13,315 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.6227687
2015-10-17 18:11:13,315 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.35104954
2015-10-17 18:11:13,878 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.19266446
2015-10-17 18:11:13,893 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.19255035
2015-10-17 18:11:14,487 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.23779202
2015-10-17 18:11:14,768 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.2781602
2015-10-17 18:11:15,331 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.22048686
2015-10-17 18:11:15,753 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.667
2015-10-17 18:11:15,846 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.667
2015-10-17 18:11:16,393 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.3726485
2015-10-17 18:11:16,909 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.66878754
2015-10-17 18:11:17,706 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.19266446
2015-10-17 18:11:17,815 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.19255035
2015-10-17 18:11:17,893 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.26655734
2015-10-17 18:11:18,237 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.2781602
2015-10-17 18:11:18,503 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.2243937
2015-10-17 18:11:18,784 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.667
2015-10-17 18:11:18,878 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.6809367
2015-10-17 18:11:19,518 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.61898744
2015-10-17 18:11:19,518 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.667
2015-10-17 18:11:19,596 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.39179757
2015-10-17 18:11:19,956 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.69730496
2015-10-17 18:11:21,440 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.27811313
2015-10-17 18:11:21,565 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.23090862
2015-10-17 18:11:21,565 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.19266446
2015-10-17 18:11:21,721 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.19255035
2015-10-17 18:11:21,737 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.2781602
2015-10-17 18:11:21,831 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.67834073
2015-10-17 18:11:21,940 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.7015723
2015-10-17 18:11:22,581 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.667
2015-10-17 18:11:22,690 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.42666236
2015-10-17 18:11:23,331 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.72029006
2015-10-17 18:11:24,752 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.23774756
2015-10-17 18:11:24,831 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.27811313
2015-10-17 18:11:24,862 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.7154013
2015-10-17 18:11:25,112 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.2781602
2015-10-17 18:11:25,362 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.7255788
2015-10-17 18:11:25,362 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.19266446
2015-10-17 18:11:25,424 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.19255035
2015-10-17 18:11:25,612 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.667
2015-10-17 18:11:25,799 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.4563609
2015-10-17 18:11:26,377 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.75073534
2015-10-17 18:11:27,893 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.75430983
2015-10-17 18:11:27,971 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.25109905
2015-10-17 18:11:28,377 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.27811313
2015-10-17 18:11:28,440 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.7673675
2015-10-17 18:11:28,721 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.690412
2015-10-17 18:11:28,737 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.2781602
2015-10-17 18:11:29,409 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.79014003
2015-10-17 18:11:29,455 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.50767225
2015-10-17 18:11:29,455 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.19255035
2015-10-17 18:11:29,455 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.19266446
2015-10-17 18:11:30,940 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.7936346
2015-10-17 18:11:31,487 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.80689746
2015-10-17 18:11:31,565 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.26803976
2015-10-17 18:11:31,674 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.27811313
2015-10-17 18:11:31,768 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.72934777
2015-10-17 18:11:31,955 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.2781602
2015-10-17 18:11:32,487 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.8293322
2015-10-17 18:11:32,877 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.5323719
2015-10-17 18:11:33,987 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.8305916
2015-10-17 18:11:34,205 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.19255035
2015-10-17 18:11:34,377 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.19266446
2015-10-17 18:11:34,549 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.8448944
2015-10-17 18:11:34,768 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.27811313
2015-10-17 18:11:34,846 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.7600318
2015-10-17 18:11:35,002 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.27813601
2015-10-17 18:11:35,002 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.28073618
2015-10-17 18:11:35,596 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.8651998
2015-10-17 18:11:36,658 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.5323719
2015-10-17 18:11:37,080 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.8462174
2015-10-17 18:11:37,612 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.8709696
2015-10-17 18:11:37,830 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.27811313
2015-10-17 18:11:37,924 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.77513987
2015-10-17 18:11:38,049 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.29278708
2015-10-17 18:11:38,065 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.19255035
2015-10-17 18:11:38,362 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.19266446
2015-10-17 18:11:38,408 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.27813601
2015-10-17 18:11:39,533 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.89395446
2015-10-17 18:11:39,908 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.5323719
2015-10-17 18:11:40,127 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.86438125
2015-10-17 18:11:40,877 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.27811313
2015-10-17 18:11:41,096 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.29962713
2015-10-17 18:11:41,565 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.8969946
2015-10-17 18:11:41,768 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.19255035
2015-10-17 18:11:41,799 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.27813601
2015-10-17 18:11:41,893 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.79596055
2015-10-17 18:11:42,143 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.19266446
2015-10-17 18:11:43,346 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.9183208
2015-10-17 18:11:43,471 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.8953182
2015-10-17 18:11:43,658 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.5323719
2015-10-17 18:11:44,143 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.2888783
2015-10-17 18:11:44,236 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.30483717
2015-10-17 18:11:44,674 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.9184482
2015-10-17 18:11:44,986 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.84007514
2015-10-17 18:11:45,158 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.27813601
2015-10-17 18:11:45,643 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.22188964
2015-10-17 18:11:46,408 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.9418907
2015-10-17 18:11:46,502 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.9355308
2015-10-17 18:11:47,424 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.2937625
2015-10-17 18:11:47,455 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.31102765
2015-10-17 18:11:47,736 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.9408908
2015-10-17 18:11:48,049 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.8776036
2015-10-17 18:11:48,064 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.5323719
2015-10-17 18:11:48,314 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.19266446
2015-10-17 18:11:48,408 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.27813601
2015-10-17 18:11:49,471 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 0.96745825
2015-10-17 18:11:49,533 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 0.9732309
2015-10-17 18:11:49,752 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.26189533
2015-10-17 18:11:50,564 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.31558463
2015-10-17 18:11:50,658 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.31688872
2015-10-17 18:11:50,799 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 0.9692384
2015-10-17 18:11:51,377 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.9154448
2015-10-17 18:11:51,580 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.5323719
2015-10-17 18:11:52,377 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000002_0 is : 1.0
2015-10-17 18:11:52,377 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.27813601
2015-10-17 18:11:52,377 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.19728623
2015-10-17 18:11:52,392 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0003_m_000002_0
2015-10-17 18:11:52,408 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:11:52,408 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0003_01_000004 taskAttempt attempt_1445076437777_0003_m_000002_0
2015-10-17 18:11:52,408 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0003_m_000002_0
2015-10-17 18:11:52,424 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000003_0 is : 1.0
2015-10-17 18:11:52,470 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0003_m_000003_0
2015-10-17 18:11:52,470 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:11:52,470 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0003_01_000005 taskAttempt attempt_1445076437777_0003_m_000003_0
2015-10-17 18:11:52,470 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0003_m_000003_0
2015-10-17 18:11:52,486 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:11:52,486 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:11:52,658 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:11:52,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0003_m_000002_0
2015-10-17 18:11:52,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:11:52,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:11:52,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-17 18:11:52,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0003_m_000003_0
2015-10-17 18:11:52,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:11:52,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-17 18:11:53,299 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445076437777_0003_m_000006
2015-10-17 18:11:53,299 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 18:11:53,299 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445076437777_0003_m_000006
2015-10-17 18:11:53,299 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:11:53,299 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:11:53,299 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000006_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:11:53,377 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:12 ContRel:2 HostLocal:4 RackLocal:6
2015-10-17 18:11:53,377 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0003: ask=4 release= 0 newContainers=0 finishedContainers=2 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:11:53,377 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0003_01_000004
2015-10-17 18:11:53,377 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0003_01_000005
2015-10-17 18:11:53,377 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0003_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:11:53,377 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:53,377 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-17 18:11:53,377 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0003_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:11:53,377 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.2 totalResourceLimit:<memory:8192, vCores:-27> finalMapResourceLimit:<memory:6554, vCores:-22> finalReduceResourceLimit:<memory:1638, vCores:-5> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-17 18:11:53,377 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping up 1
2015-10-17 18:11:53,377 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:8 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:12 ContRel:2 HostLocal:4 RackLocal:6
2015-10-17 18:11:53,470 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000001_0 is : 1.0
2015-10-17 18:11:53,486 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0003_m_000001_0
2015-10-17 18:11:53,486 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:11:53,486 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0003_01_000003 taskAttempt attempt_1445076437777_0003_m_000001_0
2015-10-17 18:11:53,486 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0003_m_000001_0
2015-10-17 18:11:53,486 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:11:53,517 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.27825075
2015-10-17 18:11:53,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:11:53,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0003_m_000001_0
2015-10-17 18:11:53,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:11:53,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-17 18:11:53,705 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.32209963
2015-10-17 18:11:53,752 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.329916
2015-10-17 18:11:54,377 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:8 AssignedReds:0 CompletedMaps:3 CompletedReds:0 ContAlloc:12 ContRel:2 HostLocal:4 RackLocal:6
2015-10-17 18:11:54,408 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0003: ask=1 release= 0 newContainers=1 finishedContainers=1 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:11:54,408 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0003_01_000003
2015-10-17 18:11:54,408 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:11:54,408 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0003_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:11:54,408 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0003_01_000014 to attempt_1445076437777_0003_m_000006_1
2015-10-17 18:11:54,408 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:8 AssignedReds:0 CompletedMaps:3 CompletedReds:0 ContAlloc:13 ContRel:2 HostLocal:5 RackLocal:6
2015-10-17 18:11:54,408 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:11:54,408 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000006_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:11:54,408 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0003_01_000014 taskAttempt attempt_1445076437777_0003_m_000006_1
2015-10-17 18:11:54,408 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0003_m_000006_1
2015-10-17 18:11:54,408 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:11:54,408 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.95579743
2015-10-17 18:11:54,470 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0003_m_000006_1 : 13562
2015-10-17 18:11:54,470 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0003_m_000006_1] using containerId: [container_1445076437777_0003_01_000014 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:11:54,470 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000006_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:11:54,470 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0003_m_000006
2015-10-17 18:11:55,439 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0003: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:11:55,439 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:11:55,439 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 18:11:55,439 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0003_01_000015 to attempt_1445076437777_0003_r_000000_0
2015-10-17 18:11:55,439 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:14 ContRel:2 HostLocal:5 RackLocal:6
2015-10-17 18:11:55,455 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:11:55,455 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:11:55,455 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0003_01_000015 taskAttempt attempt_1445076437777_0003_r_000000_0
2015-10-17 18:11:55,455 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0003_r_000000_0
2015-10-17 18:11:55,455 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:11:55,533 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.5323719
2015-10-17 18:11:55,564 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0003_r_000000_0 : 13562
2015-10-17 18:11:55,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0003_r_000000_0] using containerId: [container_1445076437777_0003_01_000015 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:11:55,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:11:55,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0003_r_000000
2015-10-17 18:11:55,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:11:56,470 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0003: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:11:56,845 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.3247026
2015-10-17 18:11:56,892 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.3406642
2015-10-17 18:11:56,908 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.27813601
2015-10-17 18:11:57,455 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.9751554
2015-10-17 18:11:57,673 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.27825075
2015-10-17 18:11:58,986 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.61860925
2015-10-17 18:11:59,939 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.33610204
2015-10-17 18:12:00,002 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.3514121
2015-10-17 18:12:00,533 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 0.9968456
2015-10-17 18:12:01,408 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000000_0 is : 1.0
2015-10-17 18:12:01,439 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0003_m_000000_0
2015-10-17 18:12:01,439 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:12:01,439 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0003_01_000002 taskAttempt attempt_1445076437777_0003_m_000000_0
2015-10-17 18:12:01,439 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0003_m_000000_0
2015-10-17 18:12:01,439 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:12:01,517 INFO [Socket Reader #1 for port 53665] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0003 (auth:SIMPLE)
2015-10-17 18:12:01,595 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0003_m_000014 asked for a task
2015-10-17 18:12:01,595 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0003_m_000014 given task: attempt_1445076437777_0003_m_000006_1
2015-10-17 18:12:01,626 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:12:01,626 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.61860925
2015-10-17 18:12:01,626 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0003_m_000000_0
2015-10-17 18:12:01,626 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:12:01,626 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-17 18:12:01,830 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:14 ContRel:2 HostLocal:5 RackLocal:6
2015-10-17 18:12:02,126 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.27825075
2015-10-17 18:12:02,580 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.667
2015-10-17 18:12:02,595 INFO [Socket Reader #1 for port 53665] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0003 (auth:SIMPLE)
2015-10-17 18:12:02,658 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0003_r_000015 asked for a task
2015-10-17 18:12:02,658 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0003_r_000015 given task: attempt_1445076437777_0003_r_000000_0
2015-10-17 18:12:02,908 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0003_01_000002
2015-10-17 18:12:02,908 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:14 ContRel:2 HostLocal:5 RackLocal:6
2015-10-17 18:12:02,908 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0003_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:12:02,986 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.22699717
2015-10-17 18:12:03,033 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.2826914
2015-10-17 18:12:03,220 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.35010734
2015-10-17 18:12:03,298 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.36388028
2015-10-17 18:12:04,423 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 0 maxEvents 10000
2015-10-17 18:12:05,439 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:05,814 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.27825075
2015-10-17 18:12:05,970 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.667
2015-10-17 18:12:06,314 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.3582504
2015-10-17 18:12:06,408 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.36388028
2015-10-17 18:12:06,454 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:06,454 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.30581617
2015-10-17 18:12:06,986 INFO [Socket Reader #1 for port 53665] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0003 (auth:SIMPLE)
2015-10-17 18:12:07,158 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.2783809
2015-10-17 18:12:07,517 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:08,314 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445076437777_0003_m_000007
2015-10-17 18:12:08,548 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:08,767 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 18:12:08,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445076437777_0003_m_000007
2015-10-17 18:12:08,783 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:12:08,783 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:12:08,783 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000007_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:12:09,329 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.667
2015-10-17 18:12:09,470 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.3637686
2015-10-17 18:12:09,517 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:14 ContRel:2 HostLocal:5 RackLocal:6
2015-10-17 18:12:09,548 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.36388028
2015-10-17 18:12:09,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:3072, vCores:-32> knownNMs=5
2015-10-17 18:12:09,611 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.106964506
2015-10-17 18:12:09,720 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.27825075
2015-10-17 18:12:09,829 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:09,876 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.32861444
2015-10-17 18:12:10,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:12:10,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0003_01_000016 to attempt_1445076437777_0003_m_000007_1
2015-10-17 18:12:10,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:15 ContRel:2 HostLocal:6 RackLocal:6
2015-10-17 18:12:10,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:12:10,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000007_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:12:10,611 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0003_01_000016 taskAttempt attempt_1445076437777_0003_m_000007_1
2015-10-17 18:12:10,611 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0003_m_000007_1
2015-10-17 18:12:10,611 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:12:12,423 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-33> knownNMs=5
2015-10-17 18:12:12,423 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0003_m_000007_1 : 13562
2015-10-17 18:12:12,439 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0003_m_000007_1] using containerId: [container_1445076437777_0003_01_000016 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:12:12,439 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000007_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:12:12,439 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0003_m_000007
2015-10-17 18:12:12,595 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.667
2015-10-17 18:12:12,626 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.3637686
2015-10-17 18:12:12,657 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.106964506
2015-10-17 18:12:12,751 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.36388028
2015-10-17 18:12:12,876 INFO [Socket Reader #1 for port 53665] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0003 (auth:SIMPLE)
2015-10-17 18:12:12,939 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0003_m_000016 asked for a task
2015-10-17 18:12:12,939 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0003_m_000016 given task: attempt_1445076437777_0003_m_000007_1
2015-10-17 18:12:13,157 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.3354504
2015-10-17 18:12:13,204 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.2783809
2015-10-17 18:12:13,517 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.27825075
2015-10-17 18:12:14,579 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:12:14,579 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:15,626 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:15,735 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.14810295
2015-10-17 18:12:15,892 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.3637686
2015-10-17 18:12:16,095 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.36388028
2015-10-17 18:12:16,548 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.667
2015-10-17 18:12:16,642 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:16,735 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.36226246
2015-10-17 18:12:16,970 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.2783809
2015-10-17 18:12:17,454 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.27825075
2015-10-17 18:12:17,595 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:12:17,657 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:18,720 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:18,829 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.19266446
2015-10-17 18:12:18,985 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.3637686
2015-10-17 18:12:19,204 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.3722555
2015-10-17 18:12:19,798 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:20,079 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.667
2015-10-17 18:12:20,188 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.36390656
2015-10-17 18:12:20,532 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.10681946
2015-10-17 18:12:20,642 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:12:20,735 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.2783809
2015-10-17 18:12:20,829 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:21,501 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.27825075
2015-10-17 18:12:21,876 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.19266446
2015-10-17 18:12:21,892 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:22,251 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.3637686
2015-10-17 18:12:22,360 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.39375034
2015-10-17 18:12:22,907 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:23,579 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.667
2015-10-17 18:12:23,610 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.10681946
2015-10-17 18:12:23,610 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.36390656
2015-10-17 18:12:23,720 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:12:23,782 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445076437777_0003_m_000005
2015-10-17 18:12:23,782 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445076437777_0003_m_000005
2015-10-17 18:12:23,782 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:12:23,782 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:12:23,782 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000005_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:12:23,782 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 18:12:23,954 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:15 ContRel:2 HostLocal:6 RackLocal:6
2015-10-17 18:12:23,954 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:24,016 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-33> knownNMs=5
2015-10-17 18:12:24,501 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.2783809
2015-10-17 18:12:24,938 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.19345176
2015-10-17 18:12:25,001 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:25,063 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:12:25,079 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0003_01_000017 to attempt_1445076437777_0003_m_000005_1
2015-10-17 18:12:25,079 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:16 ContRel:2 HostLocal:7 RackLocal:6
2015-10-17 18:12:25,079 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:12:25,079 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000005_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:12:25,079 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0003_01_000017 taskAttempt attempt_1445076437777_0003_m_000005_1
2015-10-17 18:12:25,079 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0003_m_000005_1
2015-10-17 18:12:25,079 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:12:25,298 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.27825075
2015-10-17 18:12:25,313 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0003_m_000005_1 : 13562
2015-10-17 18:12:25,313 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0003_m_000005_1] using containerId: [container_1445076437777_0003_01_000017 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:12:25,313 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000005_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:12:25,313 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0003_m_000005
2015-10-17 18:12:25,391 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.37876907
2015-10-17 18:12:25,516 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.4165488
2015-10-17 18:12:26,048 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:26,141 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-34> knownNMs=5
2015-10-17 18:12:26,720 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.10681946
2015-10-17 18:12:26,813 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:12:26,923 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.6784536
2015-10-17 18:12:27,048 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.36390656
2015-10-17 18:12:27,891 INFO [Socket Reader #1 for port 53665] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0003 (auth:SIMPLE)
2015-10-17 18:12:27,970 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0003_m_000017 asked for a task
2015-10-17 18:12:27,970 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0003_m_000017 given task: attempt_1445076437777_0003_m_000005_1
2015-10-17 18:12:28,048 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:28,048 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.2783809
2015-10-17 18:12:28,610 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.2783809
2015-10-17 18:12:28,891 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.391144
2015-10-17 18:12:29,001 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.43120423
2015-10-17 18:12:29,094 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:29,391 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.27825075
2015-10-17 18:12:29,782 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.19255035
2015-10-17 18:12:29,891 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:12:30,126 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:31,548 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.71380556
2015-10-17 18:12:32,001 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.2783809
2015-10-17 18:12:32,048 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.36390656
2015-10-17 18:12:32,110 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:32,313 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.40742803
2015-10-17 18:12:32,423 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.44292864
2015-10-17 18:12:32,782 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.2783809
2015-10-17 18:12:32,876 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.19255035
2015-10-17 18:12:32,954 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:12:33,126 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.27825075
2015-10-17 18:12:33,141 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:34,922 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.76290405
2015-10-17 18:12:35,047 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.2783809
2015-10-17 18:12:35,063 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:35,219 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.10685723
2015-10-17 18:12:35,422 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.36390656
2015-10-17 18:12:35,594 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.42110503
2015-10-17 18:12:35,719 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.44968578
2015-10-17 18:12:35,954 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.19255035
2015-10-17 18:12:36,016 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:12:36,079 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:36,766 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.30003586
2015-10-17 18:12:37,094 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:37,485 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.2783809
2015-10-17 18:12:38,500 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:38,563 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.10685723
2015-10-17 18:12:38,829 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445076437777_0003_m_000008
2015-10-17 18:12:38,829 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 18:12:38,829 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445076437777_0003_m_000008
2015-10-17 18:12:38,829 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:12:38,829 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:12:38,829 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000008_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:12:38,860 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.79254633
2015-10-17 18:12:38,985 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.36404583
2015-10-17 18:12:38,985 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.23480271
2015-10-17 18:12:39,047 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:12:39,110 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.44357696
2015-10-17 18:12:39,219 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.44968578
2015-10-17 18:12:39,219 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.36390656
2015-10-17 18:12:39,532 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:16 ContRel:2 HostLocal:7 RackLocal:6
2015-10-17 18:12:39,547 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:39,563 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:12:40,485 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.33861795
2015-10-17 18:12:40,594 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:41,407 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.2783809
2015-10-17 18:12:41,641 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:41,641 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.10685723
2015-10-17 18:12:42,016 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.36404583
2015-10-17 18:12:42,094 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.27825075
2015-10-17 18:12:42,125 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:12:42,532 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.8242491
2015-10-17 18:12:42,688 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:42,735 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.44950172
2015-10-17 18:12:42,782 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.44968578
2015-10-17 18:12:42,985 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.36390656
2015-10-17 18:12:43,766 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:44,063 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.36280957
2015-10-17 18:12:44,703 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.16119638
2015-10-17 18:12:44,828 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:45,125 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.30158082
2015-10-17 18:12:45,157 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.27825075
2015-10-17 18:12:45,250 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:12:45,391 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.36404583
2015-10-17 18:12:45,875 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:46,063 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.44950172
2015-10-17 18:12:46,172 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.44968578
2015-10-17 18:12:46,282 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.84543467
2015-10-17 18:12:46,688 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.3676965
2015-10-17 18:12:46,938 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:47,516 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.3638923
2015-10-17 18:12:47,969 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:48,047 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.19247705
2015-10-17 18:12:48,219 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.27825075
2015-10-17 18:12:48,328 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:12:48,469 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.40210414
2015-10-17 18:12:48,625 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.33317372
2015-10-17 18:12:49,031 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:49,469 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.44950172
2015-10-17 18:12:49,610 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.44968578
2015-10-17 18:12:49,750 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.879107
2015-10-17 18:12:50,078 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:50,891 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.3676965
2015-10-17 18:12:51,125 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.19247705
2015-10-17 18:12:51,250 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:51,281 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.34104082
2015-10-17 18:12:51,438 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:12:51,641 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.44980705
2015-10-17 18:12:51,953 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.3638923
2015-10-17 18:12:52,391 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:52,719 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.44950172
2015-10-17 18:12:52,813 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.44968578
2015-10-17 18:12:53,172 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.3590887
2015-10-17 18:12:53,297 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 0.9152862
2015-10-17 18:12:53,484 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:54,203 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.19247705
2015-10-17 18:12:54,359 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.3638923
2015-10-17 18:12:54,531 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:12:54,531 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:54,688 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.44980705
2015-10-17 18:12:55,594 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:55,734 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.3638923
2015-10-17 18:12:55,984 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.44950172
2015-10-17 18:12:56,125 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.44968578
2015-10-17 18:12:56,656 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:57,187 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.37518483
2015-10-17 18:12:57,266 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.27813601
2015-10-17 18:12:57,281 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.36404583
2015-10-17 18:12:57,359 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000009_0 is : 1.0
2015-10-17 18:12:57,437 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.3638923
2015-10-17 18:12:57,500 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0003_m_000009_0
2015-10-17 18:12:57,500 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:12:57,500 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0003_01_000011 taskAttempt attempt_1445076437777_0003_m_000009_0
2015-10-17 18:12:57,500 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0003_m_000009_0
2015-10-17 18:12:57,516 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 18:12:57,609 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:12:57,766 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:57,797 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.4556577
2015-10-17 18:12:58,219 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:12:58,219 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0003_m_000009_0
2015-10-17 18:12:58,219 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:12:58,219 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-17 18:12:58,844 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:12:58,922 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:16 ContRel:2 HostLocal:7 RackLocal:6
2015-10-17 18:12:59,047 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.44950172
2015-10-17 18:12:59,172 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.45595512
2015-10-17 18:12:59,469 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.3638923
2015-10-17 18:13:00,594 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.27813601
2015-10-17 18:13:00,719 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.3794209
2015-10-17 18:13:00,781 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:00,781 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.3638923
2015-10-17 18:13:00,781 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:13:00,828 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.53543663
2015-10-17 18:13:01,000 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.36404583
2015-10-17 18:13:01,781 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:02,078 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.4562815
2015-10-17 18:13:02,187 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.46181765
2015-10-17 18:13:03,625 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.27813601
2015-10-17 18:13:03,640 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.3638923
2015-10-17 18:13:03,703 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:03,765 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.38430545
2015-10-17 18:13:03,844 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:13:03,844 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.44964966
2015-10-17 18:13:03,875 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.53543663
2015-10-17 18:13:04,719 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.36404583
2015-10-17 18:13:04,719 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:05,125 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.45660633
2015-10-17 18:13:05,203 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.46800712
2015-10-17 18:13:05,781 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:06,359 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0003_01_000011
2015-10-17 18:13:06,359 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:13:06,359 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:13:06,359 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0003_01_000018 to attempt_1445076437777_0003_m_000008_1
2015-10-17 18:13:06,359 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:17 ContRel:2 HostLocal:7 RackLocal:7
2015-10-17 18:13:06,359 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0003_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:13:06,359 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:13:06,359 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000008_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:13:06,359 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0003_01_000018 taskAttempt attempt_1445076437777_0003_m_000008_1
2015-10-17 18:13:06,359 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0003_m_000008_1
2015-10-17 18:13:06,359 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 18:13:06,718 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.3567104
2015-10-17 18:13:06,828 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:06,953 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.39635628
2015-10-17 18:13:06,953 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.44964966
2015-10-17 18:13:06,953 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:13:06,953 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.53543663
2015-10-17 18:13:07,015 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445076437777_0003_m_000004
2015-10-17 18:13:07,015 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 18:13:07,015 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445076437777_0003_m_000004
2015-10-17 18:13:07,015 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:13:07,015 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:13:07,015 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000004_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:13:07,359 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:17 ContRel:2 HostLocal:7 RackLocal:7
2015-10-17 18:13:07,578 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.3638923
2015-10-17 18:13:07,843 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:07,906 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0003_m_000008_1 : 13562
2015-10-17 18:13:07,906 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0003_m_000008_1] using containerId: [container_1445076437777_0003_01_000018 on NM: [04DN8IQ.fareast.corp.microsoft.com:52150]
2015-10-17 18:13:07,906 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000008_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:13:07,906 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0003_m_000008
2015-10-17 18:13:08,172 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.46181658
2015-10-17 18:13:08,265 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.47358426
2015-10-17 18:13:08,484 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.36404583
2015-10-17 18:13:08,922 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:09,781 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.36390656
2015-10-17 18:13:10,078 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:13:10,078 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.44964966
2015-10-17 18:13:10,078 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.6210422
2015-10-17 18:13:10,187 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.40124115
2015-10-17 18:13:10,250 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:11,218 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.47354314
2015-10-17 18:13:11,265 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.3638923
2015-10-17 18:13:11,312 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.48266274
2015-10-17 18:13:11,578 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:12,593 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:13,687 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.40417305
2015-10-17 18:13:13,718 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.36390656
2015-10-17 18:13:14,046 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:13:14,046 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.5285427
2015-10-17 18:13:14,046 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:14,046 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.6210422
2015-10-17 18:13:14,249 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.47516906
2015-10-17 18:13:14,359 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.49113014
2015-10-17 18:13:14,484 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.36404583
2015-10-17 18:13:15,109 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:15,437 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.3638923
2015-10-17 18:13:16,171 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:16,749 INFO [Socket Reader #1 for port 53665] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0003 (auth:SIMPLE)
2015-10-17 18:13:17,281 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.48103228
2015-10-17 18:13:17,421 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.50676185
2015-10-17 18:13:17,796 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0003_m_000018 asked for a task
2015-10-17 18:13:17,796 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0003_m_000018 given task: attempt_1445076437777_0003_m_000008_1
2015-10-17 18:13:17,874 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.40905836
2015-10-17 18:13:18,015 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:13:18,015 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:18,374 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.36404583
2015-10-17 18:13:18,890 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.40617704
2015-10-17 18:13:19,046 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:19,109 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.3638923
2015-10-17 18:13:19,202 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.6210422
2015-10-17 18:13:19,202 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.5352825
2015-10-17 18:13:19,265 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.6210422
2015-10-17 18:13:20,093 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:20,390 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.4852679
2015-10-17 18:13:20,499 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:13:20,624 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.5321652
2015-10-17 18:13:21,093 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:13:21,562 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:21,921 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.44950968
2015-10-17 18:13:22,577 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:23,437 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.487871
2015-10-17 18:13:23,609 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:23,655 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.5352028
2015-10-17 18:13:23,999 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.41361648
2015-10-17 18:13:24,062 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.3638923
2015-10-17 18:13:24,155 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:13:24,343 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.667
2015-10-17 18:13:24,562 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.36404583
2015-10-17 18:13:24,640 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:24,968 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.44950968
2015-10-17 18:13:25,702 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:26,499 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.489501
2015-10-17 18:13:26,702 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.5352028
2015-10-17 18:13:26,749 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:27,109 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.4158968
2015-10-17 18:13:27,218 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:13:27,702 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.38918212
2015-10-17 18:13:27,765 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:28,780 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:28,937 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.50914997
2015-10-17 18:13:29,530 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.4976413
2015-10-17 18:13:29,733 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.5352028
2015-10-17 18:13:30,593 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:30,593 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.42110637
2015-10-17 18:13:30,921 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:13:31,358 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.00944148
2015-10-17 18:13:31,515 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.42721748
2015-10-17 18:13:31,640 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:31,905 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.6738919
2015-10-17 18:13:32,062 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.5352021
2015-10-17 18:13:32,577 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.51066995
2015-10-17 18:13:32,671 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:32,796 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.5352028
2015-10-17 18:13:32,968 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.36404583
2015-10-17 18:13:33,702 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:33,718 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.42371392
2015-10-17 18:13:34,874 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:34,874 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:13:34,968 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.7642859
2015-10-17 18:13:35,061 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.44964966
2015-10-17 18:13:35,124 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.5352021
2015-10-17 18:13:35,921 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:36,171 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.51946455
2015-10-17 18:13:36,374 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.54161173
2015-10-17 18:13:36,390 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.3882134
2015-10-17 18:13:36,983 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:37,436 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.013024793
2015-10-17 18:13:37,811 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.4285979
2015-10-17 18:13:37,983 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:13:38,046 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:38,093 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.80119383
2015-10-17 18:13:38,233 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.55535257
2015-10-17 18:13:38,655 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.44964966
2015-10-17 18:13:39,108 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:39,327 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.5285815
2015-10-17 18:13:39,655 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.55528814
2015-10-17 18:13:39,749 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.41361535
2015-10-17 18:13:40,139 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:40,593 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.021167655
2015-10-17 18:13:41,030 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.43511158
2015-10-17 18:13:41,046 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:13:41,171 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.8373101
2015-10-17 18:13:41,171 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.5374971
2015-10-17 18:13:41,171 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.5374971
2015-10-17 18:13:41,186 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:41,327 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.6209487
2015-10-17 18:13:42,217 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:42,608 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.53521925
2015-10-17 18:13:42,733 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.44964966
2015-10-17 18:13:43,046 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.5810173
2015-10-17 18:13:43,921 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.026376778
2015-10-17 18:13:44,139 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.44184232
2015-10-17 18:13:44,139 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:44,139 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:13:44,202 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.87328905
2015-10-17 18:13:44,202 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.667
2015-10-17 18:13:44,264 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.43967146
2015-10-17 18:13:44,358 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.6209487
2015-10-17 18:13:45,155 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:45,889 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.53521925
2015-10-17 18:13:46,186 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:46,405 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.602149
2015-10-17 18:13:46,670 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.44964966
2015-10-17 18:13:47,155 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.03451765
2015-10-17 18:13:47,217 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:13:47,249 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:47,264 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.667
2015-10-17 18:13:47,264 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.89196897
2015-10-17 18:13:47,405 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.44292763
2015-10-17 18:13:47,436 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.6209487
2015-10-17 18:13:48,077 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.44980705
2015-10-17 18:13:48,311 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:49,342 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:49,342 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.53521925
2015-10-17 18:13:50,030 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.6208445
2015-10-17 18:13:50,280 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:13:50,327 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.667
2015-10-17 18:13:50,327 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.9077102
2015-10-17 18:13:50,389 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:50,436 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.039730407
2015-10-17 18:13:50,467 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.44964966
2015-10-17 18:13:50,514 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.6209487
2015-10-17 18:13:50,624 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.44553408
2015-10-17 18:13:51,483 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:51,827 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.44980705
2015-10-17 18:13:52,498 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:52,905 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.53521925
2015-10-17 18:13:53,342 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:13:53,358 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.9303429
2015-10-17 18:13:53,373 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.67229664
2015-10-17 18:13:53,436 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.6208445
2015-10-17 18:13:53,530 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:53,561 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.6250129
2015-10-17 18:13:53,842 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.05227937
2015-10-17 18:13:53,952 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.44950968
2015-10-17 18:13:54,139 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.44964966
2015-10-17 18:13:54,561 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:54,608 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.6250129
2015-10-17 18:13:55,545 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.44980705
2015-10-17 18:13:55,592 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:56,405 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:13:56,405 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.70550996
2015-10-17 18:13:56,405 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 0.964139
2015-10-17 18:13:56,467 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.53521925
2015-10-17 18:13:56,639 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:56,639 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.667
2015-10-17 18:13:56,905 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.6208445
2015-10-17 18:13:57,248 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.06773745
2015-10-17 18:13:57,358 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.44950968
2015-10-17 18:13:57,655 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:57,967 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.44964966
2015-10-17 18:13:58,701 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:59,326 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_1 is : 1.0
2015-10-17 18:13:59,342 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0003_m_000006_1
2015-10-17 18:13:59,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000006_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:13:59,342 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0003_01_000014 taskAttempt attempt_1445076437777_0003_m_000006_1
2015-10-17 18:13:59,342 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0003_m_000006_1
2015-10-17 18:13:59,342 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:13:59,358 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000006_0 is : 0.44980705
2015-10-17 18:13:59,530 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000006_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:13:59,530 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0003_m_000006_1
2015-10-17 18:13:59,530 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445076437777_0003_m_000006_0
2015-10-17 18:13:59,530 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:13:59,530 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-17 18:13:59,530 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000006_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 18:13:59,530 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0003_01_000008 taskAttempt attempt_1445076437777_0003_m_000006_0
2015-10-17 18:13:59,530 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0003_m_000006_0
2015-10-17 18:13:59,530 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 18:13:59,561 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.13333334
2015-10-17 18:13:59,561 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.74339634
2015-10-17 18:13:59,655 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.667
2015-10-17 18:13:59,795 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:13:59,889 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.53521925
2015-10-17 18:14:00,326 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.6208445
2015-10-17 18:14:00,326 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:17 ContRel:2 HostLocal:7 RackLocal:7
2015-10-17 18:14:00,545 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000006_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 18:14:00,623 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 18:14:00,842 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:00,842 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.44950968
2015-10-17 18:14:00,842 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/_temporary/attempt_1445076437777_0003_m_000006_0
2015-10-17 18:14:00,842 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000006_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 18:14:00,904 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.085359104
2015-10-17 18:14:01,373 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0003_01_000014
2015-10-17 18:14:01,373 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:14:01,373 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0003_m_000006_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:14:01,373 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0003_01_000019 to attempt_1445076437777_0003_m_000004_1
2015-10-17 18:14:01,373 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:18 ContRel:2 HostLocal:8 RackLocal:7
2015-10-17 18:14:01,373 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:14:01,373 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000004_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:14:01,373 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0003_01_000019 taskAttempt attempt_1445076437777_0003_m_000004_1
2015-10-17 18:14:01,373 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0003_m_000004_1
2015-10-17 18:14:01,373 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:14:01,498 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0003_m_000004_1 : 13562
2015-10-17 18:14:01,498 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0003_m_000004_1] using containerId: [container_1445076437777_0003_01_000019 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:14:01,498 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000004_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:14:01,498 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0003_m_000004
2015-10-17 18:14:01,873 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:02,404 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:14:02,404 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.44964966
2015-10-17 18:14:02,592 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.16666667
2015-10-17 18:14:02,608 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.782021
2015-10-17 18:14:02,670 INFO [Socket Reader #1 for port 53665] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 53665: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 18:14:02,701 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.68046004
2015-10-17 18:14:02,889 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:03,170 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.5403086
2015-10-17 18:14:03,670 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.6208445
2015-10-17 18:14:03,936 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:04,373 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.44950968
2015-10-17 18:14:04,514 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0003_01_000008
2015-10-17 18:14:04,514 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:18 ContRel:2 HostLocal:8 RackLocal:7
2015-10-17 18:14:04,514 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0003_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:14:04,576 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.09183913
2015-10-17 18:14:05,014 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:05,780 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.7163219
2015-10-17 18:14:05,936 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.16666667
2015-10-17 18:14:05,936 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.8184311
2015-10-17 18:14:06,014 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:06,139 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.44964966
2015-10-17 18:14:06,295 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.5533361
2015-10-17 18:14:06,717 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.6208445
2015-10-17 18:14:07,030 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:07,905 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.45204732
2015-10-17 18:14:08,061 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:08,108 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.096724786
2015-10-17 18:14:08,842 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.7363248
2015-10-17 18:14:08,983 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.16666667
2015-10-17 18:14:08,983 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.8402939
2015-10-17 18:14:09,092 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:09,358 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.5533361
2015-10-17 18:14:09,748 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.6208445
2015-10-17 18:14:09,889 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_0 is : 0.44964966
2015-10-17 18:14:10,123 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:11,155 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:11,280 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.45758426
2015-10-17 18:14:11,655 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.10486763
2015-10-17 18:14:11,905 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.7686974
2015-10-17 18:14:12,092 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.16666667
2015-10-17 18:14:12,092 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.8730782
2015-10-17 18:14:12,186 INFO [Socket Reader #1 for port 53665] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 53665: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 18:14:12,217 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:12,670 INFO [Socket Reader #1 for port 53665] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0003 (auth:SIMPLE)
2015-10-17 18:14:12,764 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0003_m_000019 asked for a task
2015-10-17 18:14:12,764 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0003_m_000019 given task: attempt_1445076437777_0003_m_000004_1
2015-10-17 18:14:12,780 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.638011
2015-10-17 18:14:13,264 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:14,311 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:14,983 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.80574733
2015-10-17 18:14:15,186 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.16666667
2015-10-17 18:14:15,186 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.9103883
2015-10-17 18:14:15,373 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.4725656
2015-10-17 18:14:15,389 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.56668824
2015-10-17 18:14:15,389 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:15,670 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.106881365
2015-10-17 18:14:15,795 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.64810884
2015-10-17 18:14:16,702 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:17,717 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:18,030 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.8437685
2015-10-17 18:14:18,264 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.9482957
2015-10-17 18:14:18,264 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.16666667
2015-10-17 18:14:18,405 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.56896806
2015-10-17 18:14:18,733 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:18,795 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.48331425
2015-10-17 18:14:18,827 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.6507133
2015-10-17 18:14:19,092 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.106881365
2015-10-17 18:14:19,733 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:19,983 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.10680563
2015-10-17 18:14:21,358 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:21,436 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.5725507
2015-10-17 18:14:21,670 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.8810693
2015-10-17 18:14:21,670 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 0.98621655
2015-10-17 18:14:21,842 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.6507133
2015-10-17 18:14:22,295 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.16666667
2015-10-17 18:14:22,967 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:22,983 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.5061103
2015-10-17 18:14:23,045 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.10680563
2015-10-17 18:14:23,358 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.106881365
2015-10-17 18:14:23,998 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:24,452 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.5751561
2015-10-17 18:14:24,795 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.92589194
2015-10-17 18:14:25,014 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:25,608 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.16666667
2015-10-17 18:14:26,077 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:26,842 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.6507133
2015-10-17 18:14:26,983 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.13277757
2015-10-17 18:14:27,092 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:27,217 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.106881365
2015-10-17 18:14:27,342 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.5264552
2015-10-17 18:14:27,717 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.5842741
2015-10-17 18:14:27,842 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.9620718
2015-10-17 18:14:28,077 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000007_1 is : 1.0
2015-10-17 18:14:28,139 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:28,155 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0003_m_000007_1
2015-10-17 18:14:28,155 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000007_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:14:28,155 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0003_01_000016 taskAttempt attempt_1445076437777_0003_m_000007_1
2015-10-17 18:14:28,155 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0003_m_000007_1
2015-10-17 18:14:28,155 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:14:28,327 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.667
2015-10-17 18:14:28,499 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000007_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:14:28,499 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0003_m_000007_1
2015-10-17 18:14:28,499 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445076437777_0003_m_000007_0
2015-10-17 18:14:28,499 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:14:28,499 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-17 18:14:28,499 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000007_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 18:14:28,499 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0003_01_000009 taskAttempt attempt_1445076437777_0003_m_000007_0
2015-10-17 18:14:28,499 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0003_m_000007_0
2015-10-17 18:14:28,499 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 18:14:28,702 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.16666667
2015-10-17 18:14:29,233 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:14:29,327 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445076437777_0003_m_000007
2015-10-17 18:14:29,327 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 18:14:29,514 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:18 ContRel:2 HostLocal:8 RackLocal:7
2015-10-17 18:14:29,624 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0003_01_000016
2015-10-17 18:14:29,624 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:18 ContRel:2 HostLocal:8 RackLocal:7
2015-10-17 18:14:29,624 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0003_m_000007_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:14:30,045 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.19242907
2015-10-17 18:14:30,264 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 18:14:30,764 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.106881365
2015-10-17 18:14:30,983 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 0.9901314
2015-10-17 18:14:31,155 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.59730256
2015-10-17 18:14:31,155 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.5352021
2015-10-17 18:14:31,327 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 18:14:31,592 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.667
2015-10-17 18:14:31,811 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.20000002
2015-10-17 18:14:32,358 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 18:14:32,874 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_1 is : 1.0
2015-10-17 18:14:32,920 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0003_m_000005_1
2015-10-17 18:14:32,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000005_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:14:32,920 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0003_01_000017 taskAttempt attempt_1445076437777_0003_m_000005_1
2015-10-17 18:14:32,936 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0003_m_000005_1
2015-10-17 18:14:32,936 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:14:33,280 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.19242907
2015-10-17 18:14:33,327 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000005_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:14:33,327 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0003_m_000005_1
2015-10-17 18:14:33,327 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445076437777_0003_m_000005_0
2015-10-17 18:14:33,327 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:14:33,327 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-17 18:14:33,327 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000005_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 18:14:33,342 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0003_01_000007 taskAttempt attempt_1445076437777_0003_m_000005_0
2015-10-17 18:14:33,342 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0003_m_000005_0
2015-10-17 18:14:33,342 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 18:14:33,420 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 18:14:34,342 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:18 ContRel:2 HostLocal:8 RackLocal:7
2015-10-17 18:14:34,702 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.6060945
2015-10-17 18:14:34,749 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0003_01_000017
2015-10-17 18:14:34,749 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:34,749 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:18 ContRel:2 HostLocal:8 RackLocal:7
2015-10-17 18:14:34,749 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0003_m_000005_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:14:34,795 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000005_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 18:14:34,858 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.20000002
2015-10-17 18:14:34,936 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 18:14:35,061 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/_temporary/attempt_1445076437777_0003_m_000005_0
2015-10-17 18:14:35,061 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000005_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 18:14:35,061 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.667
2015-10-17 18:14:35,202 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000005_0 is : 0.5352021
2015-10-17 18:14:35,327 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.106881365
2015-10-17 18:14:35,530 INFO [Socket Reader #1 for port 53665] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 53665: readAndProcess from client *********** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 18:14:35,795 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:36,342 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.19242907
2015-10-17 18:14:36,858 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:36,874 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0003_01_000007
2015-10-17 18:14:36,874 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:18 ContRel:2 HostLocal:8 RackLocal:7
2015-10-17 18:14:36,874 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0003_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:14:37,920 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:37,920 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.23333333
2015-10-17 18:14:38,217 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.6207798
2015-10-17 18:14:38,483 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.667
2015-10-17 18:14:38,670 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.11007847
2015-10-17 18:14:38,999 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:39,421 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.2781602
2015-10-17 18:14:40,030 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:40,967 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.23333333
2015-10-17 18:14:41,046 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:41,592 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.6207798
2015-10-17 18:14:41,858 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.667
2015-10-17 18:14:41,874 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.12049934
2015-10-17 18:14:42,077 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:42,467 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.2781602
2015-10-17 18:14:43,124 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:44,124 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.23333333
2015-10-17 18:14:44,155 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:44,342 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445076437777_0003_m_000007
2015-10-17 18:14:44,342 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 18:14:44,983 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.6207798
2015-10-17 18:14:45,061 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.14753199
2015-10-17 18:14:45,202 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:45,296 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.667
2015-10-17 18:14:45,546 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.2781602
2015-10-17 18:14:46,233 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:47,452 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.23333333
2015-10-17 18:14:47,452 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:48,202 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.6207798
2015-10-17 18:14:48,296 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.17326213
2015-10-17 18:14:48,483 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:48,499 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 0 time(s); maxRetries=45
2015-10-17 18:14:48,592 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.30055612
2015-10-17 18:14:48,655 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.667
2015-10-17 18:14:49,499 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:50,546 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:50,546 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.26666668
2015-10-17 18:14:51,436 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.6207798
2015-10-17 18:14:51,546 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:51,655 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.36388028
2015-10-17 18:14:51,749 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.19258286
2015-10-17 18:14:51,936 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.6731531
2015-10-17 18:14:52,561 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:53,593 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:53,639 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.26666668
2015-10-17 18:14:54,608 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:54,624 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.6207798
2015-10-17 18:14:54,702 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.36388028
2015-10-17 18:14:55,139 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.68768555
2015-10-17 18:14:55,155 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.19258286
2015-10-17 18:14:55,639 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:56,968 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:56,968 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.26666668
2015-10-17 18:14:57,702 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.6207798
2015-10-17 18:14:57,764 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.36388028
2015-10-17 18:14:58,186 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.70434415
2015-10-17 18:14:58,874 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:14:58,983 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.19258286
2015-10-17 18:14:59,905 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:00,030 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.26666668
2015-10-17 18:15:00,733 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.6305203
2015-10-17 18:15:00,811 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.43744037
2015-10-17 18:15:00,921 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:01,218 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.7257465
2015-10-17 18:15:01,952 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:02,968 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:03,077 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.26666668
2015-10-17 18:15:03,765 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.6601592
2015-10-17 18:15:03,858 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.44968578
2015-10-17 18:15:03,983 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:04,280 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.7487052
2015-10-17 18:15:04,311 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.6601592
2015-10-17 18:15:04,999 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:05,671 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.19258286
2015-10-17 18:15:06,015 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:06,108 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.26666668
2015-10-17 18:15:06,811 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.667
2015-10-17 18:15:06,890 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.44968578
2015-10-17 18:15:07,015 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:07,327 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.77105474
2015-10-17 18:15:08,015 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:08,499 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 1 time(s); maxRetries=45
2015-10-17 18:15:09,030 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:09,124 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.19258286
2015-10-17 18:15:09,140 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.26666668
2015-10-17 18:15:09,843 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.667
2015-10-17 18:15:09,921 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.46186832
2015-10-17 18:15:10,061 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:10,374 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.7947416
2015-10-17 18:15:11,061 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:12,061 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:12,171 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.26666668
2015-10-17 18:15:12,593 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.19258286
2015-10-17 18:15:12,890 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.667
2015-10-17 18:15:12,968 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.5352028
2015-10-17 18:15:13,077 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:13,421 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.81341225
2015-10-17 18:15:14,077 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:15,077 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:15,218 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.26666668
2015-10-17 18:15:15,968 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.667
2015-10-17 18:15:15,999 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.19258286
2015-10-17 18:15:15,999 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.5352028
2015-10-17 18:15:16,108 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:16,468 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.8354002
2015-10-17 18:15:17,124 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:18,140 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:18,249 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.26666668
2015-10-17 18:15:19,015 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.6757073
2015-10-17 18:15:19,030 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.5352028
2015-10-17 18:15:19,155 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:19,280 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.23851041
2015-10-17 18:15:19,546 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.85683876
2015-10-17 18:15:20,171 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:21,187 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:21,296 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.26666668
2015-10-17 18:15:22,062 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.6970345
2015-10-17 18:15:22,077 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.6208445
2015-10-17 18:15:22,202 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:22,593 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.8790363
2015-10-17 18:15:22,687 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.27158552
2015-10-17 18:15:23,218 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:24,233 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:24,327 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.26666668
2015-10-17 18:15:25,093 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.6208445
2015-10-17 18:15:25,109 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.7168231
2015-10-17 18:15:25,249 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:25,640 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.8994096
2015-10-17 18:15:26,280 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:26,734 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.27811313
2015-10-17 18:15:27,280 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:27,374 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.26666668
2015-10-17 18:15:28,124 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.6208445
2015-10-17 18:15:28,155 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.73838127
2015-10-17 18:15:28,296 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:28,499 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 2 time(s); maxRetries=45
2015-10-17 18:15:28,687 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.92152965
2015-10-17 18:15:29,296 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:30,312 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:30,327 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.27811313
2015-10-17 18:15:30,421 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.26666668
2015-10-17 18:15:31,171 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.64134794
2015-10-17 18:15:31,202 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.759585
2015-10-17 18:15:31,327 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:31,734 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.94350684
2015-10-17 18:15:32,327 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:32,390 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.64134794
2015-10-17 18:15:33,343 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:33,452 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.26666668
2015-10-17 18:15:33,734 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.27811313
2015-10-17 18:15:34,234 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.667
2015-10-17 18:15:34,249 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.78037405
2015-10-17 18:15:34,343 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:34,780 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.9666081
2015-10-17 18:15:35,359 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:36,359 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:36,499 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.26666668
2015-10-17 18:15:37,124 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.27811313
2015-10-17 18:15:37,281 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.667
2015-10-17 18:15:37,296 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.79721504
2015-10-17 18:15:37,359 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:37,827 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 0.98304975
2015-10-17 18:15:38,374 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:39,374 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:39,546 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.26666668
2015-10-17 18:15:40,296 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_0 is : 1.0
2015-10-17 18:15:40,296 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0003_m_000004_0
2015-10-17 18:15:40,296 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:15:40,296 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0003_01_000006 taskAttempt attempt_1445076437777_0003_m_000004_0
2015-10-17 18:15:40,296 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0003_m_000004_0
2015-10-17 18:15:40,296 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:53425
2015-10-17 18:15:40,312 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.27811313
2015-10-17 18:15:40,312 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000004_1 is : 0.667
2015-10-17 18:15:40,327 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:15:40,327 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0003_m_000004_0
2015-10-17 18:15:40,327 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445076437777_0003_m_000004_1
2015-10-17 18:15:40,327 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:15:40,327 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-17 18:15:40,327 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000004_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 18:15:40,327 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0003_01_000019 taskAttempt attempt_1445076437777_0003_m_000004_1
2015-10-17 18:15:40,327 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0003_m_000004_1
2015-10-17 18:15:40,327 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:15:40,343 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.8188077
2015-10-17 18:15:40,390 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:15:40,421 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000004_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 18:15:40,452 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 18:15:40,468 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/_temporary/attempt_1445076437777_0003_m_000004_1
2015-10-17 18:15:40,468 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000004_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 18:15:40,515 INFO [Socket Reader #1 for port 53665] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 53665: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 18:15:41,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:18 ContRel:2 HostLocal:8 RackLocal:7
2015-10-17 18:15:41,421 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:15:42,187 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0003_01_000006
2015-10-17 18:15:42,187 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0003_01_000019
2015-10-17 18:15:42,187 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0003_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:15:42,187 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:18 ContRel:2 HostLocal:8 RackLocal:7
2015-10-17 18:15:42,187 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0003_m_000004_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:15:42,515 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:15:42,624 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.26666668
2015-10-17 18:15:43,468 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.8371619
2015-10-17 18:15:43,593 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:15:43,624 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.27811313
2015-10-17 18:15:44,640 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:15:45,702 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:15:45,702 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.26666668
2015-10-17 18:15:46,515 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.8564411
2015-10-17 18:15:46,781 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:15:46,999 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.27811313
2015-10-17 18:15:47,828 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:15:48,499 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 3 time(s); maxRetries=45
2015-10-17 18:15:48,781 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.26666668
2015-10-17 18:15:48,968 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:15:49,578 INFO [IPC Server handler 23 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.87505865
2015-10-17 18:15:49,984 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:15:50,359 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.29278818
2015-10-17 18:15:51,078 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:15:51,859 INFO [IPC Server handler 25 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.3
2015-10-17 18:15:52,109 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:15:52,609 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.89780915
2015-10-17 18:15:53,124 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:15:53,781 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.30874667
2015-10-17 18:15:54,140 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:15:54,921 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.3
2015-10-17 18:15:55,156 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:15:55,640 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.92693114
2015-10-17 18:15:56,171 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:15:57,187 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:15:57,203 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.34160018
2015-10-17 18:15:57,953 INFO [IPC Server handler 16 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.3
2015-10-17 18:15:58,187 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:15:58,671 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.9540404
2015-10-17 18:15:59,187 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:16:00,203 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:16:00,281 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.3637686
2015-10-17 18:16:01,000 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.3
2015-10-17 18:16:01,203 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:16:01,718 INFO [IPC Server handler 20 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 0.98310345
2015-10-17 18:16:02,203 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:16:03,203 INFO [IPC Server handler 4 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:16:03,625 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_1 is : 0.3637686
2015-10-17 18:16:04,031 INFO [IPC Server handler 26 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.3
2015-10-17 18:16:04,218 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:16:04,218 INFO [IPC Server handler 22 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_m_000008_0 is : 1.0
2015-10-17 18:16:04,218 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0003_m_000008_0
2015-10-17 18:16:04,218 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:16:04,218 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0003_01_000010 taskAttempt attempt_1445076437777_0003_m_000008_0
2015-10-17 18:16:04,218 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0003_m_000008_0
2015-10-17 18:16:04,218 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:53425
2015-10-17 18:16:04,234 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:16:04,234 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0003_m_000008_0
2015-10-17 18:16:04,234 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445076437777_0003_m_000008_1
2015-10-17 18:16:04,234 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:16:04,234 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-17 18:16:04,234 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000008_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 18:16:04,234 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0003_01_000018 taskAttempt attempt_1445076437777_0003_m_000008_1
2015-10-17 18:16:04,234 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0003_m_000008_1
2015-10-17 18:16:04,234 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 18:16:04,390 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000008_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 18:16:04,390 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 18:16:04,406 WARN [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/_temporary/attempt_1445076437777_0003_m_000008_1
2015-10-17 18:16:04,406 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_m_000008_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 18:16:04,765 INFO [Socket Reader #1 for port 53665] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 53665: readAndProcess from client *********** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 18:16:04,828 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:18 ContRel:2 HostLocal:8 RackLocal:7
2015-10-17 18:16:05,218 INFO [IPC Server handler 13 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:16:05,875 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0003_01_000018
2015-10-17 18:16:05,875 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0003_01_000010
2015-10-17 18:16:05,875 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0003_m_000008_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:16:05,875 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:18 ContRel:2 HostLocal:8 RackLocal:7
2015-10-17 18:16:05,875 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0003_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:16:06,250 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:16:07,109 INFO [IPC Server handler 15 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.3
2015-10-17 18:16:07,281 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:16:08,328 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:16:08,500 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 4 time(s); maxRetries=45
2015-10-17 18:16:09,375 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:16:10,156 INFO [IPC Server handler 10 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.3
2015-10-17 18:16:10,422 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:16:12,343 INFO [IPC Server handler 17 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:16:13,218 INFO [IPC Server handler 6 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.3
2015-10-17 18:16:13,375 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:16:14,390 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:16:15,437 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:16:16,297 INFO [IPC Server handler 0 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.3
2015-10-17 18:16:16,484 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:16:17,531 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:16:18,625 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:16:19,375 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.3
2015-10-17 18:16:19,656 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:16:20,718 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:16:21,594 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.3
2015-10-17 18:16:21,672 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.3
2015-10-17 18:16:22,422 INFO [IPC Server handler 12 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.66672045
2015-10-17 18:16:25,453 INFO [IPC Server handler 8 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.6834755
2015-10-17 18:16:28,469 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.6954033
2015-10-17 18:16:28,500 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 5 time(s); maxRetries=45
2015-10-17 18:16:31,500 INFO [IPC Server handler 9 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.6954033
2015-10-17 18:16:48,500 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 6 time(s); maxRetries=45
2015-10-17 18:16:50,500 INFO [IPC Server handler 7 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.7252363
2015-10-17 18:16:53,531 INFO [IPC Server handler 11 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.7506641
2015-10-17 18:16:56,610 INFO [IPC Server handler 5 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.76689637
2015-10-17 18:16:59,656 INFO [IPC Server handler 1 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.7815165
2015-10-17 18:17:02,719 INFO [IPC Server handler 3 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.7973026
2015-10-17 18:17:05,844 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.80658937
2015-10-17 18:17:08,500 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 7 time(s); maxRetries=45
2015-10-17 18:17:08,891 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.8202279
2015-10-17 18:17:11,922 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.8365239
2015-10-17 18:17:15,016 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.8565279
2015-10-17 18:17:18,110 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.8764551
2015-10-17 18:17:21,172 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.8963853
2015-10-17 18:17:25,110 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.906633
2015-10-17 18:17:28,157 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.9287646
2015-10-17 18:17:28,501 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 8 time(s); maxRetries=45
2015-10-17 18:17:31,188 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.94801646
2015-10-17 18:17:35,032 INFO [IPC Server handler 28 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.9646504
2015-10-17 18:17:38,094 INFO [IPC Server handler 2 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.97329646
2015-10-17 18:17:41,141 INFO [IPC Server handler 27 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.98216
2015-10-17 18:17:44,188 INFO [IPC Server handler 21 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.9908834
2015-10-17 18:17:47,235 INFO [IPC Server handler 29 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 0.9995786
2015-10-17 18:17:48,298 INFO [IPC Server handler 18 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445076437777_0003_r_000000_0
2015-10-17 18:17:48,298 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_r_000000_0 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-17 18:17:48,298 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445076437777_0003_r_000000_0 given a go for committing the task output.
2015-10-17 18:17:48,329 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445076437777_0003_r_000000_0
2015-10-17 18:17:48,329 INFO [IPC Server handler 24 on 53665] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445076437777_0003_r_000000_0:true
2015-10-17 18:17:48,407 INFO [IPC Server handler 14 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0003_r_000000_0 is : 1.0
2015-10-17 18:17:48,438 INFO [IPC Server handler 19 on 53665] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0003_r_000000_0
2015-10-17 18:17:48,438 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_r_000000_0 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:17:48,438 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0003_01_000015 taskAttempt attempt_1445076437777_0003_r_000000_0
2015-10-17 18:17:48,438 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0003_r_000000_0
2015-10-17 18:17:48,438 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:17:48,501 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 9 time(s); maxRetries=45
2015-10-17 18:17:48,548 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0003_r_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:17:48,548 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0003_r_000000_0
2015-10-17 18:17:48,548 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0003_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:17:48,548 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-17 18:17:48,548 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445076437777_0003Job Transitioned from RUNNING to COMMITTING
2015-10-17 18:17:48,548 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-17 18:17:48,938 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-17 18:17:48,938 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445076437777_0003Job Transitioned from COMMITTING to SUCCEEDED
2015-10-17 18:17:48,938 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-17 18:17:48,938 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-17 18:17:48,938 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-17 18:17:48,938 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-17 18:17:48,938 INFO [Thread-112] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-17 18:17:48,938 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-17 18:17:48,954 INFO [Thread-112] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-17 18:17:49,360 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:18 ContRel:2 HostLocal:8 RackLocal:7
2015-10-17 18:17:49,829 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0003/job_1445076437777_0003_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0003-1445076558237-msrabi-pagerank-1445077068938-10-1-SUCCEEDED-default-1445076564396.jhist_tmp
2015-10-17 18:17:51,048 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0003_01_000015
2015-10-17 18:17:51,048 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:18 ContRel:2 HostLocal:8 RackLocal:7
2015-10-17 18:17:51,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0003_r_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:18:08,501 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 10 time(s); maxRetries=45
2015-10-17 18:18:11,204 INFO [Thread-115] org.apache.hadoop.hdfs.DFSClient: Exception in createBlockOutputStream
java.io.IOException: Bad connect ack with firstBadLink as *************:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.createBlockOutputStream(DFSOutputStream.java:1461)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.nextBlockOutputStream(DFSOutputStream.java:1362)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.run(DFSOutputStream.java:589)
2015-10-17 18:18:11,204 INFO [Thread-115] org.apache.hadoop.hdfs.DFSClient: Abandoning BP-1347369012-**************-1444972147527:blk_1073742660_1855
2015-10-17 18:18:11,267 INFO [Thread-115] org.apache.hadoop.hdfs.DFSClient: Excluding datanode *************:50010
2015-10-17 18:18:12,251 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0003-1445076558237-msrabi-pagerank-1445077068938-10-1-SUCCEEDED-default-1445076564396.jhist_tmp
2015-10-17 18:18:12,376 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0003/job_1445076437777_0003_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0003_conf.xml_tmp
2015-10-17 18:18:13,532 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0003_conf.xml_tmp
2015-10-17 18:18:13,673 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0003.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0003.summary
2015-10-17 18:18:13,751 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0003_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0003_conf.xml
2015-10-17 18:18:13,829 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0003-1445076558237-msrabi-pagerank-1445077068938-10-1-SUCCEEDED-default-1445076564396.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0003-1445076558237-msrabi-pagerank-1445077068938-10-1-SUCCEEDED-default-1445076564396.jhist
2015-10-17 18:18:13,829 INFO [Thread-112] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-17 18:18:28,501 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 11 time(s); maxRetries=45
2015-10-17 18:18:48,501 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 12 time(s); maxRetries=45
2015-10-17 18:19:08,502 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 13 time(s); maxRetries=45
2015-10-17 18:19:28,502 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 14 time(s); maxRetries=45
2015-10-17 18:19:48,503 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 15 time(s); maxRetries=45
2015-10-17 18:20:08,504 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 16 time(s); maxRetries=45
2015-10-17 18:20:28,504 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 17 time(s); maxRetries=45
2015-10-17 18:20:48,505 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 18 time(s); maxRetries=45
2015-10-17 18:21:08,505 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 19 time(s); maxRetries=45
2015-10-17 18:21:28,506 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 20 time(s); maxRetries=45
2015-10-17 18:21:48,507 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 21 time(s); maxRetries=45
2015-10-17 18:22:08,507 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 22 time(s); maxRetries=45
2015-10-17 18:22:28,508 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 23 time(s); maxRetries=45
2015-10-17 18:22:48,555 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 24 time(s); maxRetries=45
2015-10-17 18:23:08,556 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 25 time(s); maxRetries=45
2015-10-17 18:23:28,557 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 26 time(s); maxRetries=45
2015-10-17 18:23:48,557 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 27 time(s); maxRetries=45
2015-10-17 18:24:08,558 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 28 time(s); maxRetries=45
2015-10-17 18:24:28,558 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 29 time(s); maxRetries=45
2015-10-17 18:24:48,559 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 30 time(s); maxRetries=45
2015-10-17 18:25:08,560 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 31 time(s); maxRetries=45
2015-10-17 18:25:28,560 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 32 time(s); maxRetries=45
2015-10-17 18:25:48,561 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 33 time(s); maxRetries=45
2015-10-17 18:26:08,561 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 34 time(s); maxRetries=45
2015-10-17 18:26:28,561 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 35 time(s); maxRetries=45
2015-10-17 18:26:48,576 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 36 time(s); maxRetries=45
2015-10-17 18:27:08,576 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 37 time(s); maxRetries=45
2015-10-17 18:27:18,685 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 0 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 18:27:18,888 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445076437777_0003_m_000007_0 because it is running on unusable node:MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 18:27:18,888 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0003_01_000009
2015-10-17 18:27:18,888 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:18 ContRel:2 HostLocal:8 RackLocal:7
2015-10-17 18:27:18,888 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0003_m_000007_0: Container released on a *lost* node
2015-10-17 18:27:38,216 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 1 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 18:27:57,746 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 2 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 18:28:17,277 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 3 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 18:28:36,793 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 4 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 18:28:56,323 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 5 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 18:29:15,854 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 6 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
