2015-10-19 17:47:22,862 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445182159119_0020_000001
2015-10-19 17:47:23,440 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-19 17:47:23,440 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 20 cluster_timestamp: 1445182159119 } attemptId: 1 } keyId: 1694045684)
2015-10-19 17:47:23,659 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-19 17:47:24,503 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-19 17:47:24,596 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-19 17:47:24,628 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-19 17:47:24,628 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-19 17:47:24,628 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-19 17:47:24,628 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-19 17:47:24,643 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-19 17:47:24,643 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-19 17:47:24,643 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-19 17:47:24,643 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-19 17:47:24,690 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 17:47:24,721 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 17:47:24,737 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 17:47:24,753 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-19 17:47:24,815 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-19 17:47:25,143 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 17:47:25,190 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 17:47:25,190 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-19 17:47:25,206 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445182159119_0020 to jobTokenSecretManager
2015-10-19 17:47:25,409 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445182159119_0020 because: not enabled; too many maps; too much input;
2015-10-19 17:47:25,440 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445182159119_0020 = 1256521728. Number of splits = 10
2015-10-19 17:47:25,440 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445182159119_0020 = 1
2015-10-19 17:47:25,440 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0020Job Transitioned from NEW to INITED
2015-10-19 17:47:25,440 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445182159119_0020.
2015-10-19 17:47:25,487 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 17:47:25,503 INFO [Socket Reader #1 for port 52517] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 52517
2015-10-19 17:47:25,581 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-19 17:47:25,581 INFO [IPC Server listener on 52517] org.apache.hadoop.ipc.Server: IPC Server listener on 52517: starting
2015-10-19 17:47:25,581 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 17:47:25,596 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at 04DN8IQ.fareast.corp.microsoft.com/*************:52517
2015-10-19 17:47:25,675 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-19 17:47:25,675 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-19 17:47:25,690 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-19 17:47:25,690 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-19 17:47:25,690 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-19 17:47:25,690 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-19 17:47:25,690 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-19 17:47:25,706 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 52526
2015-10-19 17:47:25,706 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-19 17:47:25,753 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\Jetty_0_0_0_0_52526_mapreduce____x7ixtp\webapp
2015-10-19 17:47:26,003 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:52526
2015-10-19 17:47:26,003 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 52526
2015-10-19 17:47:26,456 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-19 17:47:26,456 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445182159119_0020
2015-10-19 17:47:26,456 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 17:47:26,471 INFO [Socket Reader #1 for port 52529] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 52529
2015-10-19 17:47:26,471 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 17:47:26,471 INFO [IPC Server listener on 52529] org.apache.hadoop.ipc.Server: IPC Server listener on 52529: starting
2015-10-19 17:47:26,487 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-19 17:47:26,487 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-19 17:47:26,487 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-19 17:47:26,534 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-19 17:47:26,643 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-19 17:47:26,643 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-19 17:47:26,643 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-19 17:47:26,643 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-19 17:47:26,659 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0020Job Transitioned from INITED to SETUP
2015-10-19 17:47:26,659 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-19 17:47:26,675 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0020Job Transitioned from SETUP to RUNNING
2015-10-19 17:47:26,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:47:26,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:47:26,737 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:47:26,737 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:47:26,737 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:47:26,737 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:47:26,737 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:47:26,737 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:47:26,737 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:47:26,737 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:47:26,737 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:47:26,737 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:47:26,737 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:47:26,737 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-19 17:47:26,737 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-19 17:47:26,784 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445182159119_0020, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0020/job_1445182159119_0020_1.jhist
2015-10-19 17:47:27,643 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-19 17:47:27,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:5120, vCores:-30> knownNMs=5
2015-10-19 17:47:27,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:5120, vCores:-30>
2015-10-19 17:47:27,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:28,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:5120, vCores:-30>
2015-10-19 17:47:28,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:29,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:5120, vCores:-30>
2015-10-19 17:47:29,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:30,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:5120, vCores:-30>
2015-10-19 17:47:30,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:31,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:5120, vCores:-30>
2015-10-19 17:47:31,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:32,706 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:5120, vCores:-30>
2015-10-19 17:47:32,706 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:33,925 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:47:33,925 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:33,925 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0020_01_000002 to attempt_1445182159119_0020_m_000000_0
2015-10-19 17:47:33,940 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-31>
2015-10-19 17:47:33,940 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:33,940 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:9 ScheduledReds:0 AssignedMaps:1 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:1 ContRel:0 HostLocal:0 RackLocal:1
2015-10-19 17:47:34,018 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:34,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0020/job.jar
2015-10-19 17:47:34,534 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0020/job.xml
2015-10-19 17:47:34,534 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-19 17:47:34,534 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-19 17:47:34,534 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-19 17:47:34,597 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:47:34,597 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0020_01_000002 taskAttempt attempt_1445182159119_0020_m_000000_0
2015-10-19 17:47:34,612 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0020_m_000000_0
2015-10-19 17:47:34,612 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:47:34,675 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0020_m_000000_0 : 13562
2015-10-19 17:47:34,675 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0020_m_000000_0] using containerId: [container_1445182159119_0020_01_000002 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 17:47:34,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:47:34,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0020_m_000000
2015-10-19 17:47:34,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:47:34,940 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:4096, vCores:-31> knownNMs=5
2015-10-19 17:47:34,940 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-31>
2015-10-19 17:47:34,940 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:35,940 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-31>
2015-10-19 17:47:35,940 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:36,940 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-31>
2015-10-19 17:47:36,940 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:36,940 INFO [Socket Reader #1 for port 52529] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0020 (auth:SIMPLE)
2015-10-19 17:47:36,972 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0020_m_000002 asked for a task
2015-10-19 17:47:36,972 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0020_m_000002 given task: attempt_1445182159119_0020_m_000000_0
2015-10-19 17:47:37,940 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-31>
2015-10-19 17:47:37,940 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:38,956 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:47:38,956 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:38,956 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0020_01_000003 to attempt_1445182159119_0020_m_000001_0
2015-10-19 17:47:38,956 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-32>
2015-10-19 17:47:38,956 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:38,956 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:8 ScheduledReds:0 AssignedMaps:2 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:2 ContRel:0 HostLocal:0 RackLocal:2
2015-10-19 17:47:38,956 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:38,956 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:47:38,956 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0020_01_000003 taskAttempt attempt_1445182159119_0020_m_000001_0
2015-10-19 17:47:38,956 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0020_m_000001_0
2015-10-19 17:47:38,956 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:47:39,019 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0020_m_000001_0 : 13562
2015-10-19 17:47:39,019 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0020_m_000001_0] using containerId: [container_1445182159119_0020_01_000003 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 17:47:39,019 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:47:39,019 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0020_m_000001
2015-10-19 17:47:39,019 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:47:39,987 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:3072, vCores:-32> knownNMs=5
2015-10-19 17:47:39,987 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-32>
2015-10-19 17:47:39,987 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:41,034 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-32>
2015-10-19 17:47:41,034 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:42,097 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-32>
2015-10-19 17:47:42,097 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:42,222 INFO [Socket Reader #1 for port 52529] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0020 (auth:SIMPLE)
2015-10-19 17:47:42,237 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0020_m_000003 asked for a task
2015-10-19 17:47:42,237 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0020_m_000003 given task: attempt_1445182159119_0020_m_000001_0
2015-10-19 17:47:43,112 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:47:43,112 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:43,112 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0020_01_000004 to attempt_1445182159119_0020_m_000002_0
2015-10-19 17:47:43,112 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-33>
2015-10-19 17:47:43,112 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:43,112 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:7 ScheduledReds:0 AssignedMaps:3 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:3 ContRel:0 HostLocal:0 RackLocal:3
2015-10-19 17:47:43,112 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:43,128 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:47:43,144 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0020_01_000004 taskAttempt attempt_1445182159119_0020_m_000002_0
2015-10-19 17:47:43,144 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0020_m_000002_0
2015-10-19 17:47:43,144 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:47:43,253 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0020_m_000002_0 : 13562
2015-10-19 17:47:43,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0020_m_000002_0] using containerId: [container_1445182159119_0020_01_000004 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 17:47:43,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:47:43,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0020_m_000002
2015-10-19 17:47:43,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:47:44,128 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0020: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:1024, vCores:-34> knownNMs=5
2015-10-19 17:47:44,128 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:47:44,128 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:44,128 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0020_01_000005 to attempt_1445182159119_0020_m_000003_0
2015-10-19 17:47:44,128 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-19 17:47:44,128 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:44,128 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:6 ScheduledReds:0 AssignedMaps:4 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:4 ContRel:0 HostLocal:0 RackLocal:4
2015-10-19 17:47:44,128 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:44,128 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:47:44,144 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0020_01_000005 taskAttempt attempt_1445182159119_0020_m_000003_0
2015-10-19 17:47:44,144 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0020_m_000003_0
2015-10-19 17:47:44,144 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:47:44,237 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0020_m_000003_0 : 13562
2015-10-19 17:47:44,237 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0020_m_000003_0] using containerId: [container_1445182159119_0020_01_000005 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 17:47:44,237 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:47:44,237 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0020_m_000003
2015-10-19 17:47:44,237 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:47:44,909 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.096839
2015-10-19 17:47:45,128 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-34> knownNMs=5
2015-10-19 17:47:45,128 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-19 17:47:45,128 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:46,144 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-19 17:47:46,144 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:47,191 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:47:47,191 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:47,191 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0020_01_000006 to attempt_1445182159119_0020_m_000004_0
2015-10-19 17:47:47,191 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:47:47,191 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:47,191 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:5 ContRel:0 HostLocal:0 RackLocal:5
2015-10-19 17:47:47,191 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:47:47,191 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:47:47,222 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0020_01_000006 taskAttempt attempt_1445182159119_0020_m_000004_0
2015-10-19 17:47:47,222 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0020_m_000004_0
2015-10-19 17:47:47,222 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:47:47,456 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0020_m_000004_0 : 13562
2015-10-19 17:47:47,456 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0020_m_000004_0] using containerId: [container_1445182159119_0020_01_000006 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 17:47:47,456 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:47:47,456 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0020_m_000004
2015-10-19 17:47:47,456 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:47:48,206 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-19 17:47:48,206 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:47:48,206 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:48,284 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.10635664
2015-10-19 17:47:48,534 INFO [Socket Reader #1 for port 52529] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0020 (auth:SIMPLE)
2015-10-19 17:47:48,644 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0020_m_000004 asked for a task
2015-10-19 17:47:48,644 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0020_m_000004 given task: attempt_1445182159119_0020_m_000002_0
2015-10-19 17:47:49,253 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:47:49,253 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:49,691 INFO [Socket Reader #1 for port 52529] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0020 (auth:SIMPLE)
2015-10-19 17:47:49,816 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0020_m_000005 asked for a task
2015-10-19 17:47:49,816 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0020_m_000005 given task: attempt_1445182159119_0020_m_000003_0
2015-10-19 17:47:50,362 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:47:50,362 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:51,425 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:47:51,425 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:52,066 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.10635664
2015-10-19 17:47:52,488 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:47:52,488 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:53,503 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:47:53,503 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:54,519 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:47:54,519 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:55,331 INFO [Socket Reader #1 for port 52529] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0020 (auth:SIMPLE)
2015-10-19 17:47:55,363 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.10635664
2015-10-19 17:47:55,394 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0020_m_000006 asked for a task
2015-10-19 17:47:55,394 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0020_m_000006 given task: attempt_1445182159119_0020_m_000004_0
2015-10-19 17:47:55,597 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:47:55,597 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:56,675 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:47:56,675 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:57,581 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.057023466
2015-10-19 17:47:57,706 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:47:57,706 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:58,753 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.10635664
2015-10-19 17:47:58,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:47:58,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:47:59,863 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:47:59,863 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:00,175 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.046975013
2015-10-19 17:48:00,894 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:00,894 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:00,941 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.10253199
2015-10-19 17:48:01,816 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.10635664
2015-10-19 17:48:01,910 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:48:01,910 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:48:01,910 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0020_01_000007 to attempt_1445182159119_0020_m_000005_0
2015-10-19 17:48:01,910 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-19 17:48:01,910 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:01,910 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:6 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:0 RackLocal:6
2015-10-19 17:48:01,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:48:01,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:48:01,910 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0020_01_000007 taskAttempt attempt_1445182159119_0020_m_000005_0
2015-10-19 17:48:01,910 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0020_m_000005_0
2015-10-19 17:48:01,910 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 17:48:02,956 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0020: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-19 17:48:02,956 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:48:02,956 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:48:02,956 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0020_01_000008 to attempt_1445182159119_0020_m_000006_0
2015-10-19 17:48:02,956 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:02,956 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:02,956 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:0 RackLocal:7
2015-10-19 17:48:02,956 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:48:02,956 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:48:03,050 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0020_01_000008 taskAttempt attempt_1445182159119_0020_m_000006_0
2015-10-19 17:48:03,050 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0020_m_000006_0
2015-10-19 17:48:03,050 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 17:48:03,472 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0020_m_000005_0 : 13562
2015-10-19 17:48:03,472 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0020_m_000005_0] using containerId: [container_1445182159119_0020_01_000007 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 17:48:03,472 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:48:03,472 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0020_m_000005
2015-10-19 17:48:03,472 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:48:03,503 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.098416634
2015-10-19 17:48:03,800 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0020_m_000006_0 : 13562
2015-10-19 17:48:03,800 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0020_m_000006_0] using containerId: [container_1445182159119_0020_01_000008 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 17:48:03,800 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:48:03,800 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0020_m_000006
2015-10-19 17:48:03,800 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:48:04,003 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-19 17:48:04,003 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:04,003 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:04,191 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.1066108
2015-10-19 17:48:05,035 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:05,035 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:05,050 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.10635664
2015-10-19 17:48:06,050 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:06,050 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:06,550 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.106493875
2015-10-19 17:48:07,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:07,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:07,394 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.1066108
2015-10-19 17:48:08,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:08,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:08,363 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.10635664
2015-10-19 17:48:09,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:09,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:09,613 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.106493875
2015-10-19 17:48:10,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:10,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:10,441 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.1066108
2015-10-19 17:48:11,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:11,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:11,488 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.1080365
2015-10-19 17:48:12,097 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:12,097 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:12,691 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.106493875
2015-10-19 17:48:13,113 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:13,113 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:13,503 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.1066108
2015-10-19 17:48:14,128 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:14,128 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:14,660 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.1731385
2015-10-19 17:48:15,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:15,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:15,988 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.106493875
2015-10-19 17:48:16,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:16,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:16,754 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.1066108
2015-10-19 17:48:17,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:17,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:17,847 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.19158794
2015-10-19 17:48:18,144 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:18,144 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:19,144 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.106493875
2015-10-19 17:48:19,207 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:19,207 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:19,957 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.1066108
2015-10-19 17:48:20,269 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:20,269 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:21,082 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.19158794
2015-10-19 17:48:21,316 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:21,316 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:22,316 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.106493875
2015-10-19 17:48:22,332 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:22,332 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:23,129 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.08956437
2015-10-19 17:48:23,379 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:23,379 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:23,488 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.12080302
2015-10-19 17:48:24,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:24,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:24,519 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.19158794
2015-10-19 17:48:25,457 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:25,457 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:25,660 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.106493875
2015-10-19 17:48:26,457 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.10660437
2015-10-19 17:48:26,551 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:26,551 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:26,863 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.15860589
2015-10-19 17:48:27,894 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:27,894 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:27,988 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.19158794
2015-10-19 17:48:28,926 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.14434907
2015-10-19 17:48:28,972 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:28,972 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:29,301 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.042823363
2015-10-19 17:48:29,691 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.10660437
2015-10-19 17:48:30,051 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:30,051 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:30,051 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.18140295
2015-10-19 17:48:31,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:31,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:31,160 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.19158794
2015-10-19 17:48:32,223 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:32,223 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:32,332 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.17249157
2015-10-19 17:48:32,754 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.07458101
2015-10-19 17:48:33,019 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.10660437
2015-10-19 17:48:33,238 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:33,238 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:33,254 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.19211523
2015-10-19 17:48:34,301 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:34,301 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:34,785 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.19158794
2015-10-19 17:48:35,348 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:35,348 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:35,551 INFO [Socket Reader #1 for port 52529] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0020 (auth:SIMPLE)
2015-10-19 17:48:35,551 INFO [Socket Reader #1 for port 52529] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0020 (auth:SIMPLE)
2015-10-19 17:48:35,613 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0020_m_000007 asked for a task
2015-10-19 17:48:35,613 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0020_m_000007 given task: attempt_1445182159119_0020_m_000005_0
2015-10-19 17:48:35,613 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0020_m_000008 asked for a task
2015-10-19 17:48:35,613 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0020_m_000008 given task: attempt_1445182159119_0020_m_000006_0
2015-10-19 17:48:35,754 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.19209063
2015-10-19 17:48:36,238 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.10364375
2015-10-19 17:48:36,379 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:36,379 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:36,473 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.10660437
2015-10-19 17:48:36,707 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.19211523
2015-10-19 17:48:37,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:37,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:38,316 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.19158794
2015-10-19 17:48:38,426 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:38,426 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:39,113 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.19209063
2015-10-19 17:48:39,566 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.10680563
2015-10-19 17:48:39,660 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:39,660 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:39,801 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.10660437
2015-10-19 17:48:40,160 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.19211523
2015-10-19 17:48:40,895 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:40,895 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:41,613 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.19516522
2015-10-19 17:48:41,910 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:41,910 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:42,691 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.19209063
2015-10-19 17:48:42,957 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:42,957 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:43,051 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.10680563
2015-10-19 17:48:43,238 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.10660437
2015-10-19 17:48:43,520 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.19211523
2015-10-19 17:48:44,004 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:44,004 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:44,895 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.24024123
2015-10-19 17:48:45,035 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:45,035 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:45,457 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.051643852
2015-10-19 17:48:45,629 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.020190079
2015-10-19 17:48:46,004 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.19209063
2015-10-19 17:48:46,051 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:46,051 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:46,488 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.10680563
2015-10-19 17:48:46,535 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.10660437
2015-10-19 17:48:46,926 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.19211523
2015-10-19 17:48:47,067 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:47,067 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:48,113 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:48,113 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:48,285 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.27696857
2015-10-19 17:48:48,801 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.0990047
2015-10-19 17:48:49,020 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.043965917
2015-10-19 17:48:49,113 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:49,113 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:49,301 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.19209063
2015-10-19 17:48:49,738 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.10680563
2015-10-19 17:48:49,848 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.10660437
2015-10-19 17:48:50,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:50,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:50,223 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.19211523
2015-10-19 17:48:51,145 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:51,145 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:51,598 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.27696857
2015-10-19 17:48:52,176 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:52,176 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:52,629 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.19209063
2015-10-19 17:48:52,723 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.106964506
2015-10-19 17:48:52,770 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.09564919
2015-10-19 17:48:53,098 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.10680563
2015-10-19 17:48:53,207 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:53,207 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:53,207 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.1129883
2015-10-19 17:48:53,473 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.19211523
2015-10-19 17:48:54,254 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:54,254 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:55,098 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.27696857
2015-10-19 17:48:55,332 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:55,332 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:56,004 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.19209063
2015-10-19 17:48:56,192 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.106964506
2015-10-19 17:48:56,395 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.10685723
2015-10-19 17:48:56,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:56,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:56,582 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.10680563
2015-10-19 17:48:56,645 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.16077039
2015-10-19 17:48:56,817 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.19211523
2015-10-19 17:48:57,473 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:57,473 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:58,489 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.27696857
2015-10-19 17:48:58,551 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:58,551 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:59,504 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.19209063
2015-10-19 17:48:59,629 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:48:59,629 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:48:59,723 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.106964506
2015-10-19 17:48:59,895 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.10685723
2015-10-19 17:48:59,973 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.10680563
2015-10-19 17:49:00,145 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.19212553
2015-10-19 17:49:00,254 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.21817048
2015-10-19 17:49:00,707 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:00,707 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:01,739 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:01,739 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:01,786 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.27696857
2015-10-19 17:49:02,801 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:02,801 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:02,832 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.21403891
2015-10-19 17:49:03,364 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.10680563
2015-10-19 17:49:03,489 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.106964506
2015-10-19 17:49:03,614 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.10685723
2015-10-19 17:49:03,692 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.19212553
2015-10-19 17:49:03,817 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.26681364
2015-10-19 17:49:03,832 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:03,832 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:04,879 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:04,879 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:05,161 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.27696857
2015-10-19 17:49:05,926 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:05,926 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:06,395 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.2618457
2015-10-19 17:49:06,833 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.13044627
2015-10-19 17:49:06,958 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.106964506
2015-10-19 17:49:07,004 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:07,004 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:07,036 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.10685723
2015-10-19 17:49:07,301 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.27776006
2015-10-19 17:49:07,348 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.19212553
2015-10-19 17:49:08,067 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:08,067 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:08,473 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.27696857
2015-10-19 17:49:09,114 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:09,114 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:09,676 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.27765483
2015-10-19 17:49:10,192 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:10,192 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:10,301 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.11724466
2015-10-19 17:49:10,301 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.17640047
2015-10-19 17:49:10,333 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.10685723
2015-10-19 17:49:10,801 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.27776006
2015-10-19 17:49:10,817 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.19212553
2015-10-19 17:49:11,223 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:11,223 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:11,833 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.27696857
2015-10-19 17:49:12,270 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:12,270 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:13,067 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.27765483
2015-10-19 17:49:13,270 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:13,270 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:13,661 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.19242907
2015-10-19 17:49:14,098 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.17927597
2015-10-19 17:49:14,145 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.27776006
2015-10-19 17:49:14,161 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.19212553
2015-10-19 17:49:14,286 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:14,286 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:14,364 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.11757089
2015-10-19 17:49:15,130 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.27696857
2015-10-19 17:49:15,301 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:15,301 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:16,317 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:16,317 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:16,380 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.27765483
2015-10-19 17:49:16,989 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.19242907
2015-10-19 17:49:17,317 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:17,317 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:17,458 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.27776006
2015-10-19 17:49:17,505 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.19212553
2015-10-19 17:49:17,567 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.19266446
2015-10-19 17:49:17,755 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.1550234
2015-10-19 17:49:18,364 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:18,364 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:18,583 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.31914788
2015-10-19 17:49:19,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:19,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:19,676 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.27765483
2015-10-19 17:49:20,411 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.19242907
2015-10-19 17:49:20,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:20,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:20,786 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.27776006
2015-10-19 17:49:21,067 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.19212553
2015-10-19 17:49:21,473 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:21,473 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:21,567 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.19247705
2015-10-19 17:49:21,817 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.19266446
2015-10-19 17:49:21,927 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.35976455
2015-10-19 17:49:22,489 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:22,489 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:23,145 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.27765483
2015-10-19 17:49:23,520 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:23,520 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:23,802 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.19242907
2015-10-19 17:49:24,005 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.27776006
2015-10-19 17:49:24,333 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.19212553
2015-10-19 17:49:24,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:24,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:25,083 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.19247705
2015-10-19 17:49:25,192 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.3624012
2015-10-19 17:49:25,302 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.19266446
2015-10-19 17:49:25,567 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:25,567 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:26,411 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.27765483
2015-10-19 17:49:26,598 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:26,598 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:27,067 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.19242907
2015-10-19 17:49:27,270 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.27776006
2015-10-19 17:49:27,614 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.20216738
2015-10-19 17:49:27,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:27,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:28,583 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.19247705
2015-10-19 17:49:28,583 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.3624012
2015-10-19 17:49:28,692 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:28,692 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:28,739 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.19266446
2015-10-19 17:49:29,739 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:29,739 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:29,755 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.27765483
2015-10-19 17:49:30,458 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.19242907
2015-10-19 17:49:30,645 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.27776006
2015-10-19 17:49:30,802 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:30,802 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:31,020 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.25044334
2015-10-19 17:49:31,817 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:31,817 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:32,020 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.3624012
2015-10-19 17:49:32,255 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.19266446
2015-10-19 17:49:32,255 INFO [IPC Server handler 13 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.19247705
2015-10-19 17:49:32,864 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-19 17:49:32,864 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:33,255 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.27765483
2015-10-19 17:49:33,880 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.19242907
2015-10-19 17:49:33,911 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:49:33,911 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:49:33,911 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0020_01_000009 to attempt_1445182159119_0020_m_000007_0
2015-10-19 17:49:33,911 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:33,911 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:33,911 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:8 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:8 ContRel:0 HostLocal:0 RackLocal:8
2015-10-19 17:49:33,911 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:49:33,911 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:49:34,020 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0020_01_000009 taskAttempt attempt_1445182159119_0020_m_000007_0
2015-10-19 17:49:34,020 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0020_m_000007_0
2015-10-19 17:49:34,020 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 17:49:34,052 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.30446273
2015-10-19 17:49:34,411 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.27772525
2015-10-19 17:49:34,927 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0020_m_000007_0 : 13562
2015-10-19 17:49:34,927 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0020_m_000007_0] using containerId: [container_1445182159119_0020_01_000009 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 17:49:34,927 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:49:34,927 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0020_m_000007
2015-10-19 17:49:34,927 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:49:34,927 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-19 17:49:34,927 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:34,927 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:35,317 INFO [IPC Server handler 13 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.3624012
2015-10-19 17:49:35,755 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.19247705
2015-10-19 17:49:35,755 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.20452598
2015-10-19 17:49:35,974 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:35,974 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:36,599 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.29488924
2015-10-19 17:49:37,286 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.19242907
2015-10-19 17:49:37,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:37,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:37,646 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.35233036
2015-10-19 17:49:38,052 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.27772525
2015-10-19 17:49:38,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:38,505 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:38,739 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.3624012
2015-10-19 17:49:39,192 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.19247705
2015-10-19 17:49:39,224 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.23351319
2015-10-19 17:49:39,521 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:39,521 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:39,896 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.33572692
2015-10-19 17:49:40,599 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:40,599 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:40,708 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.2102543
2015-10-19 17:49:41,146 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.36319977
2015-10-19 17:49:41,536 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.27772525
2015-10-19 17:49:41,661 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:41,661 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:42,130 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.3624012
2015-10-19 17:49:42,677 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:42,677 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:42,708 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.21299551
2015-10-19 17:49:42,755 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.2504489
2015-10-19 17:49:43,286 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.36323506
2015-10-19 17:49:43,693 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:43,693 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:43,958 INFO [Socket Reader #1 for port 52529] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0020 (auth:SIMPLE)
2015-10-19 17:49:44,099 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.25523436
2015-10-19 17:49:44,130 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0020_m_000009 asked for a task
2015-10-19 17:49:44,130 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0020_m_000009 given task: attempt_1445182159119_0020_m_000007_0
2015-10-19 17:49:44,599 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.36319977
2015-10-19 17:49:44,771 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:44,771 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:45,052 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.27772525
2015-10-19 17:49:45,505 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.3624012
2015-10-19 17:49:45,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:45,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:46,583 INFO [IPC Server handler 13 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.2338372
2015-10-19 17:49:46,599 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.26868755
2015-10-19 17:49:46,661 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.36323506
2015-10-19 17:49:46,818 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:46,818 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:47,364 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.2781602
2015-10-19 17:49:47,833 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:47,833 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:47,974 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.36319977
2015-10-19 17:49:48,396 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.27772525
2015-10-19 17:49:48,864 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:48,864 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:48,911 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.3624012
2015-10-19 17:49:49,974 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.36323506
2015-10-19 17:49:49,974 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:49,974 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:50,161 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.25044996
2015-10-19 17:49:50,161 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.2783809
2015-10-19 17:49:50,708 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.2781602
2015-10-19 17:49:51,005 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:51,005 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:51,349 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.36319977
2015-10-19 17:49:51,677 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.27772525
2015-10-19 17:49:52,036 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:52,036 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:52,130 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.397029
2015-10-19 17:49:53,052 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:53,052 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:53,333 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.36323506
2015-10-19 17:49:53,599 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.27813601
2015-10-19 17:49:53,740 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.2783809
2015-10-19 17:49:54,021 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.2781602
2015-10-19 17:49:54,083 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:54,083 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:54,724 INFO [IPC Server handler 13 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.36319977
2015-10-19 17:49:55,115 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:55,115 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:55,115 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.27772525
2015-10-19 17:49:55,599 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.44153705
2015-10-19 17:49:56,161 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:56,161 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:56,677 INFO [IPC Server handler 13 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.36323506
2015-10-19 17:49:56,990 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.27813601
2015-10-19 17:49:57,177 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.2783809
2015-10-19 17:49:57,177 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:57,177 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:57,443 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.2781602
2015-10-19 17:49:57,943 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.36319977
2015-10-19 17:49:58,208 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:58,224 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:58,474 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.27772525
2015-10-19 17:49:58,880 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.44789755
2015-10-19 17:49:59,240 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:49:59,240 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:49:59,990 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.36323506
2015-10-19 17:50:00,271 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:00,271 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:00,583 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.27813601
2015-10-19 17:50:00,708 INFO [IPC Server handler 13 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.2781602
2015-10-19 17:50:00,818 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.2783809
2015-10-19 17:50:01,287 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:01,287 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:01,396 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.36319977
2015-10-19 17:50:01,724 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.28101
2015-10-19 17:50:02,208 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.44789755
2015-10-19 17:50:02,318 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:02,318 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:03,349 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:03,349 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:03,380 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.36323506
2015-10-19 17:50:03,583 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.08209034
2015-10-19 17:50:03,880 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.2781602
2015-10-19 17:50:04,193 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.27813601
2015-10-19 17:50:04,365 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:04,365 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:04,771 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.2783809
2015-10-19 17:50:04,818 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.36319977
2015-10-19 17:50:05,021 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.32781887
2015-10-19 17:50:05,396 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:05,396 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:05,740 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.44789755
2015-10-19 17:50:06,412 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:06,412 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:06,755 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.36323506
2015-10-19 17:50:07,240 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.10681946
2015-10-19 17:50:07,396 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.2781602
2015-10-19 17:50:07,443 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:07,443 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:07,662 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.27813601
2015-10-19 17:50:08,084 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.38446784
2015-10-19 17:50:08,224 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.31858623
2015-10-19 17:50:08,459 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:08,459 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:08,505 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.36317363
2015-10-19 17:50:09,084 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.44789755
2015-10-19 17:50:09,490 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:09,490 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:09,880 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.3742101
2015-10-19 17:50:10,537 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.2781602
2015-10-19 17:50:10,537 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:10,537 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:11,177 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.10681946
2015-10-19 17:50:11,287 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.42045513
2015-10-19 17:50:11,459 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.27813601
2015-10-19 17:50:11,584 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:11,584 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:11,662 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.36317363
2015-10-19 17:50:11,724 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.36404583
2015-10-19 17:50:12,505 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.44789755
2015-10-19 17:50:12,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:12,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:13,287 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.41464317
2015-10-19 17:50:13,709 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:13,709 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:13,974 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.2921145
2015-10-19 17:50:14,677 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.448704
2015-10-19 17:50:14,740 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.10681946
2015-10-19 17:50:14,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:14,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:15,068 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.32631406
2015-10-19 17:50:15,162 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.36317363
2015-10-19 17:50:15,365 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.36404583
2015-10-19 17:50:15,881 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:15,881 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:16,021 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.44789755
2015-10-19 17:50:16,615 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.4486067
2015-10-19 17:50:16,927 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:16,927 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:17,287 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.33800578
2015-10-19 17:50:17,974 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:17,974 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:18,084 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.448704
2015-10-19 17:50:18,474 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.10681946
2015-10-19 17:50:18,599 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.36317363
2015-10-19 17:50:18,631 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.36390656
2015-10-19 17:50:18,959 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.36404583
2015-10-19 17:50:19,037 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:19,037 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:19,537 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.44789755
2015-10-19 17:50:20,068 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:20,068 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:20,162 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.4486067
2015-10-19 17:50:20,693 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.36388028
2015-10-19 17:50:21,084 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:21,084 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:21,490 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.448704
2015-10-19 17:50:22,037 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.10681946
2015-10-19 17:50:22,037 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.36317363
2015-10-19 17:50:22,115 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:22,115 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:22,224 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.36390656
2015-10-19 17:50:22,506 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.36404583
2015-10-19 17:50:22,787 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.44789755
2015-10-19 17:50:23,146 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:23,146 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:23,631 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.4486067
2015-10-19 17:50:24,021 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.36388028
2015-10-19 17:50:24,162 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:24,162 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:24,928 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.448704
2015-10-19 17:50:25,193 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:25,193 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:25,334 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.36317363
2015-10-19 17:50:25,646 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.10681946
2015-10-19 17:50:25,678 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.36390656
2015-10-19 17:50:25,943 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.36404583
2015-10-19 17:50:26,084 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.4869866
2015-10-19 17:50:26,209 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:26,209 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:26,959 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.4486067
2015-10-19 17:50:27,240 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:27,240 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:27,381 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.36388028
2015-10-19 17:50:28,271 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:28,271 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:28,396 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.448704
2015-10-19 17:50:28,834 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.36317363
2015-10-19 17:50:29,193 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.14708084
2015-10-19 17:50:29,225 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.36390656
2015-10-19 17:50:29,303 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:29,303 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:29,428 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.36404583
2015-10-19 17:50:29,490 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.5294256
2015-10-19 17:50:30,193 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.4486067
2015-10-19 17:50:30,334 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:30,334 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:30,615 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.36388028
2015-10-19 17:50:31,365 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:31,365 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:31,646 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.448704
2015-10-19 17:50:32,100 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.36317363
2015-10-19 17:50:32,396 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:32,396 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:32,756 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.53341997
2015-10-19 17:50:33,053 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.41394308
2015-10-19 17:50:33,412 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:33,412 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:33,506 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.4486067
2015-10-19 17:50:33,568 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.1527431
2015-10-19 17:50:33,881 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.36390656
2015-10-19 17:50:33,928 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.36388028
2015-10-19 17:50:34,443 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:34,443 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:34,897 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.448704
2015-10-19 17:50:35,506 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.36605552
2015-10-19 17:50:35,506 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:35,506 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:36,131 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.53341997
2015-10-19 17:50:36,131 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.43869466
2015-10-19 17:50:36,522 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:36,522 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:36,662 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.16251178
2015-10-19 17:50:36,850 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.4486067
2015-10-19 17:50:36,990 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.37974644
2015-10-19 17:50:37,350 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.36388028
2015-10-19 17:50:37,537 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:37,537 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:38,225 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.448704
2015-10-19 17:50:38,600 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:38,600 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:38,787 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.4134591
2015-10-19 17:50:39,490 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.53341997
2015-10-19 17:50:39,678 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:39,678 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:39,725 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.44980705
2015-10-19 17:50:40,225 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.4486067
2015-10-19 17:50:40,662 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.42200956
2015-10-19 17:50:40,740 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:40,740 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:40,787 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.36388028
2015-10-19 17:50:41,162 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.1911172
2015-10-19 17:50:41,662 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.4559373
2015-10-19 17:50:41,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:41,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:42,037 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.44859612
2015-10-19 17:50:42,803 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.53341997
2015-10-19 17:50:42,803 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:42,803 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:43,615 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.45751262
2015-10-19 17:50:43,647 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.44980705
2015-10-19 17:50:43,881 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:43,881 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:44,069 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.36388028
2015-10-19 17:50:44,365 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.44950968
2015-10-19 17:50:44,709 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.19255035
2015-10-19 17:50:44,740 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.49438468
2015-10-19 17:50:44,928 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:44,928 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:45,350 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.44859612
2015-10-19 17:50:46,006 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.53341997
2015-10-19 17:50:46,022 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:46,022 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:46,787 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.4947131
2015-10-19 17:50:47,100 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:47,100 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:47,194 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.44980705
2015-10-19 17:50:47,365 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.38984114
2015-10-19 17:50:47,944 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.44950968
2015-10-19 17:50:48,022 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.527932
2015-10-19 17:50:48,147 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:48,147 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:48,147 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.19255035
2015-10-19 17:50:48,756 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.44859612
2015-10-19 17:50:49,162 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:49,162 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:49,412 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.53341997
2015-10-19 17:50:50,209 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:50,209 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:50,241 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.53103787
2015-10-19 17:50:50,678 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.42315674
2015-10-19 17:50:50,772 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.44980705
2015-10-19 17:50:51,303 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:51,303 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:51,459 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.53425497
2015-10-19 17:50:51,475 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.44950968
2015-10-19 17:50:51,491 INFO [IPC Server handler 13 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.19255035
2015-10-19 17:50:52,241 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.44859612
2015-10-19 17:50:52,334 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:52,334 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:52,756 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.53341997
2015-10-19 17:50:53,334 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:53,334 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:53,662 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.5343203
2015-10-19 17:50:53,928 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.44968578
2015-10-19 17:50:54,225 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.44980705
2015-10-19 17:50:54,381 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:54,381 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:54,866 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.53425497
2015-10-19 17:50:54,991 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.19255035
2015-10-19 17:50:55,037 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.44950968
2015-10-19 17:50:55,412 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:55,412 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:55,678 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.44859612
2015-10-19 17:50:56,131 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.53341997
2015-10-19 17:50:56,428 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:56,428 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:57,100 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.5343203
2015-10-19 17:50:57,350 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.44968578
2015-10-19 17:50:57,459 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:57,459 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:57,866 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.5185863
2015-10-19 17:50:58,241 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.53425497
2015-10-19 17:50:58,538 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:58,538 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:50:58,725 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.19255035
2015-10-19 17:50:58,803 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.44950968
2015-10-19 17:50:59,038 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.44859612
2015-10-19 17:50:59,522 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.5764313
2015-10-19 17:50:59,600 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:50:59,600 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:00,491 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.5343203
2015-10-19 17:51:00,663 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:00,663 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:00,725 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.44968578
2015-10-19 17:51:01,397 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.53543663
2015-10-19 17:51:01,569 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.53425497
2015-10-19 17:51:01,709 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:01,709 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:02,147 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.22080891
2015-10-19 17:51:02,147 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.45628184
2015-10-19 17:51:02,553 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.44859612
2015-10-19 17:51:02,756 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:02,756 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:02,959 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.61817545
2015-10-19 17:51:03,772 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:03,772 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:03,897 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.5343203
2015-10-19 17:51:04,038 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.44968578
2015-10-19 17:51:04,569 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.53543663
2015-10-19 17:51:04,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:04,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:04,850 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.53425497
2015-10-19 17:51:05,460 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.2543557
2015-10-19 17:51:05,475 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.50513214
2015-10-19 17:51:05,803 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:05,803 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:05,835 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.44859612
2015-10-19 17:51:06,194 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.61898744
2015-10-19 17:51:06,819 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:06,819 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:07,272 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.5343203
2015-10-19 17:51:07,460 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.44968578
2015-10-19 17:51:07,835 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:07,835 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:08,100 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.53425497
2015-10-19 17:51:08,460 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.53543663
2015-10-19 17:51:08,850 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:08,850 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:09,116 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.5352021
2015-10-19 17:51:09,147 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.46149048
2015-10-19 17:51:09,256 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.27825075
2015-10-19 17:51:09,553 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.61898744
2015-10-19 17:51:09,866 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:09,866 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:10,616 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.5343203
2015-10-19 17:51:10,772 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.44968578
2015-10-19 17:51:10,881 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:10,881 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:11,413 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.53425497
2015-10-19 17:51:11,944 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:11,944 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:11,975 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.53543663
2015-10-19 17:51:12,538 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.5119062
2015-10-19 17:51:12,616 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.5352021
2015-10-19 17:51:12,819 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.27825075
2015-10-19 17:51:12,991 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:12,991 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:12,991 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.61898744
2015-10-19 17:51:13,991 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.5343203
2015-10-19 17:51:14,007 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:14,007 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:14,178 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.44968578
2015-10-19 17:51:14,897 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.53425497
2015-10-19 17:51:15,038 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:15,038 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:15,397 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.53543663
2015-10-19 17:51:15,788 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.5342037
2015-10-19 17:51:16,100 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:16,100 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:16,147 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.5352021
2015-10-19 17:51:16,350 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.61898744
2015-10-19 17:51:16,382 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.27825075
2015-10-19 17:51:17,163 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:17,163 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:17,272 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.54966897
2015-10-19 17:51:17,491 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.44968578
2015-10-19 17:51:18,194 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.58297515
2015-10-19 17:51:18,225 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:18,225 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:18,975 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.5493249
2015-10-19 17:51:19,100 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.5342037
2015-10-19 17:51:19,288 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:19,288 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:19,632 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.5352021
2015-10-19 17:51:19,788 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.61898744
2015-10-19 17:51:20,007 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.27825075
2015-10-19 17:51:20,366 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:20,366 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:20,804 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.59498674
2015-10-19 17:51:21,069 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.4694961
2015-10-19 17:51:21,429 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:21,429 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:21,710 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.6197233
2015-10-19 17:51:22,491 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:22,491 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:22,522 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.60544467
2015-10-19 17:51:22,647 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.5342037
2015-10-19 17:51:23,147 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.61898744
2015-10-19 17:51:23,241 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.5352021
2015-10-19 17:51:23,554 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:23,554 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:23,616 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.27825075
2015-10-19 17:51:24,288 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.6199081
2015-10-19 17:51:24,429 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.5141933
2015-10-19 17:51:24,585 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:24,585 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:25,085 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.6197233
2015-10-19 17:51:25,616 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:25,616 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:26,038 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.5342037
2015-10-19 17:51:26,491 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.61898744
2015-10-19 17:51:26,569 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.6210422
2015-10-19 17:51:26,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:26,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:26,726 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.53656745
2015-10-19 17:51:27,116 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.27825075
2015-10-19 17:51:27,679 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.6199081
2015-10-19 17:51:27,679 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:27,679 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:27,788 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.5352028
2015-10-19 17:51:28,413 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.6197233
2015-10-19 17:51:28,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:28,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:29,382 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.5342037
2015-10-19 17:51:29,647 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.6210422
2015-10-19 17:51:29,710 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.61898744
2015-10-19 17:51:29,710 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:29,710 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:29,944 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.5748315
2015-10-19 17:51:30,179 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.3002774
2015-10-19 17:51:30,757 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:30,757 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:30,960 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.6199081
2015-10-19 17:51:31,038 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.5352028
2015-10-19 17:51:31,710 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.6197233
2015-10-19 17:51:31,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:31,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:32,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:32,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:32,866 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.5342037
2015-10-19 17:51:32,897 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.6210422
2015-10-19 17:51:33,163 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.667
2015-10-19 17:51:33,163 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.667
2015-10-19 17:51:33,444 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.60414076
2015-10-19 17:51:33,819 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:33,819 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:34,023 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.3452217
2015-10-19 17:51:34,288 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.6199081
2015-10-19 17:51:34,460 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.5352028
2015-10-19 17:51:34,835 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:34,835 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:34,991 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.6197233
2015-10-19 17:51:35,866 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:35,866 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:36,179 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.5342037
2015-10-19 17:51:36,601 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.667
2015-10-19 17:51:36,898 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:36,898 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:36,944 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.6210422
2015-10-19 17:51:37,210 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.6209487
2015-10-19 17:51:37,616 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.6199081
2015-10-19 17:51:37,788 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.5352028
2015-10-19 17:51:37,929 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:37,929 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:38,085 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.3638923
2015-10-19 17:51:38,351 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.6197233
2015-10-19 17:51:38,976 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:38,976 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:39,523 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.5342037
2015-10-19 17:51:39,929 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.667
2015-10-19 17:51:39,991 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:39,991 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:40,413 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.6210422
2015-10-19 17:51:40,694 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.6209487
2015-10-19 17:51:41,038 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:41,038 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:41,038 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.6199081
2015-10-19 17:51:41,163 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.5352028
2015-10-19 17:51:41,554 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.3638923
2015-10-19 17:51:41,663 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.6197233
2015-10-19 17:51:42,054 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:42,054 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:42,898 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.55656886
2015-10-19 17:51:43,085 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:43,085 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:43,320 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.667
2015-10-19 17:51:44,179 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:44,179 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:44,398 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.6199081
2015-10-19 17:51:44,554 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.6209487
2015-10-19 17:51:44,554 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.6225072
2015-10-19 17:51:44,585 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.5352028
2015-10-19 17:51:45,038 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.6197233
2015-10-19 17:51:45,320 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.3638923
2015-10-19 17:51:45,320 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:45,320 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:46,195 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.5951485
2015-10-19 17:51:46,601 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.667
2015-10-19 17:51:46,757 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:46,757 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:47,773 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:47,773 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:47,820 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.6199081
2015-10-19 17:51:48,054 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.5352028
2015-10-19 17:51:48,288 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.667
2015-10-19 17:51:48,304 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.667
2015-10-19 17:51:48,304 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.6209487
2015-10-19 17:51:48,351 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.6197233
2015-10-19 17:51:48,742 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.3638923
2015-10-19 17:51:48,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:48,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:49,413 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.6196791
2015-10-19 17:51:49,804 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:49,804 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:49,992 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.667
2015-10-19 17:51:50,851 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:50,851 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:51,163 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.63671136
2015-10-19 17:51:51,367 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.5352028
2015-10-19 17:51:51,601 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.64810735
2015-10-19 17:51:52,460 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.6196791
2015-10-19 17:51:52,898 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.3638923
2015-10-19 17:51:52,929 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.667
2015-10-19 17:51:53,038 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.6209487
2015-10-19 17:51:53,085 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.667
2015-10-19 17:51:53,117 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:53,117 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:54,148 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:54,148 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:54,304 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.6559249
2015-10-19 17:51:54,476 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.5608243
2015-10-19 17:51:54,742 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.6645992
2015-10-19 17:51:55,148 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.6645992
2015-10-19 17:51:55,179 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:55,179 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:55,413 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.6559249
2015-10-19 17:51:55,820 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.6196791
2015-10-19 17:51:56,210 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:56,210 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:56,304 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.667
2015-10-19 17:51:56,382 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.3651891
2015-10-19 17:51:56,507 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.667
2015-10-19 17:51:56,585 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.6497751
2015-10-19 17:51:57,226 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:57,226 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:57,726 INFO [IPC Server handler 13 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.667
2015-10-19 17:51:57,945 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.6042989
2015-10-19 17:51:57,992 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.6497751
2015-10-19 17:51:58,242 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.667
2015-10-19 17:51:58,242 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:58,242 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:59,257 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.6196791
2015-10-19 17:51:59,257 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:51:59,257 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:51:59,804 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.67539066
2015-10-19 17:52:00,007 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.41101098
2015-10-19 17:52:00,242 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.667
2015-10-19 17:52:00,289 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:00,289 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:00,320 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.667
2015-10-19 17:52:01,101 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.667
2015-10-19 17:52:01,304 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:01,304 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:01,398 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.6208445
2015-10-19 17:52:01,585 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.667
2015-10-19 17:52:02,335 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:02,335 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:02,617 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.6196791
2015-10-19 17:52:03,226 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.6899296
2015-10-19 17:52:03,367 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:03,367 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:03,773 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.44964966
2015-10-19 17:52:03,914 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.667
2015-10-19 17:52:04,164 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.667
2015-10-19 17:52:04,351 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.667
2015-10-19 17:52:04,398 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:04,398 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:04,851 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.6208445
2015-10-19 17:52:05,070 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.667
2015-10-19 17:52:05,414 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:05,414 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:05,976 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.6196791
2015-10-19 17:52:06,445 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:06,445 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:06,570 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.7042538
2015-10-19 17:52:07,461 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:07,461 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:07,476 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.44964966
2015-10-19 17:52:07,601 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.667
2015-10-19 17:52:07,773 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.667
2015-10-19 17:52:07,882 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.6740735
2015-10-19 17:52:08,289 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.6208445
2015-10-19 17:52:08,476 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.667
2015-10-19 17:52:08,476 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:08,476 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:09,164 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.6196791
2015-10-19 17:52:09,523 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:09,523 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:09,804 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.7179382
2015-10-19 17:52:10,539 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:10,539 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:11,023 INFO [IPC Server handler 13 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.44964966
2015-10-19 17:52:11,101 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.667
2015-10-19 17:52:11,101 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.667
2015-10-19 17:52:11,398 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.6930778
2015-10-19 17:52:11,570 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:11,570 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:11,601 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.6208445
2015-10-19 17:52:11,773 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.667
2015-10-19 17:52:12,492 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.6196791
2015-10-19 17:52:12,586 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:12,586 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:13,133 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.73131865
2015-10-19 17:52:13,617 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:13,617 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:14,492 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.667
2015-10-19 17:52:14,523 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.44964966
2015-10-19 17:52:14,523 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.667
2015-10-19 17:52:14,679 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:14,679 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:14,820 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.71200526
2015-10-19 17:52:14,976 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.6208445
2015-10-19 17:52:15,101 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.667
2015-10-19 17:52:15,695 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:15,695 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:15,804 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.65839994
2015-10-19 17:52:16,351 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.7437367
2015-10-19 17:52:16,523 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.65839994
2015-10-19 17:52:16,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:16,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:17,648 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.667
2015-10-19 17:52:17,742 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:17,742 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:17,820 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.44964966
2015-10-19 17:52:17,820 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.67948705
2015-10-19 17:52:17,898 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.73058456
2015-10-19 17:52:18,601 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.6208445
2015-10-19 17:52:18,742 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.6683395
2015-10-19 17:52:18,758 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:18,758 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:19,164 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.667
2015-10-19 17:52:19,586 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.75656325
2015-10-19 17:52:19,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:19,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:20,820 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:20,820 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:20,898 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.6779591
2015-10-19 17:52:21,414 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.6996097
2015-10-19 17:52:21,492 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.44964966
2015-10-19 17:52:21,664 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.7536731
2015-10-19 17:52:21,836 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:21,836 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:22,023 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.6208445
2015-10-19 17:52:22,258 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.68301916
2015-10-19 17:52:22,508 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.667
2015-10-19 17:52:22,883 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:22,883 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:22,883 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.7705532
2015-10-19 17:52:23,945 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:23,945 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:24,258 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.6922129
2015-10-19 17:52:24,758 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.46377176
2015-10-19 17:52:24,836 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.72041464
2015-10-19 17:52:24,945 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:24,961 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:25,039 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.77438843
2015-10-19 17:52:25,320 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.6208445
2015-10-19 17:52:25,570 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.6963648
2015-10-19 17:52:25,789 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.667
2015-10-19 17:52:25,992 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:25,992 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:26,148 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.7841368
2015-10-19 17:52:27,008 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:27,008 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:27,508 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.7054196
2015-10-19 17:52:28,039 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:28,039 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:28,820 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.66408277
2015-10-19 17:52:28,883 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.7088234
2015-10-19 17:52:29,055 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:29,055 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:29,086 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.66408277
2015-10-19 17:52:29,180 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.667
2015-10-19 17:52:29,492 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.80117726
2015-10-19 17:52:29,570 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.7968528
2015-10-19 17:52:29,602 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.5280177
2015-10-19 17:52:29,695 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.7501546
2015-10-19 17:52:30,070 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:30,070 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:30,789 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.7177521
2015-10-19 17:52:31,086 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:31,086 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:32,117 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:32,117 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:32,195 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.667
2015-10-19 17:52:32,195 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.721876
2015-10-19 17:52:32,492 INFO [IPC Server handler 13 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.667
2015-10-19 17:52:32,805 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.81005406
2015-10-19 17:52:33,055 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.82024723
2015-10-19 17:52:33,149 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:33,149 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:33,258 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.5352825
2015-10-19 17:52:33,289 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.7692951
2015-10-19 17:52:34,086 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.73053443
2015-10-19 17:52:34,180 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:34,180 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:35,211 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:35,211 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:35,399 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.7350355
2015-10-19 17:52:35,570 INFO [IPC Server handler 13 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.667
2015-10-19 17:52:35,805 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.667
2015-10-19 17:52:36,102 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.8239346
2015-10-19 17:52:36,227 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:36,227 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:36,524 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.8420375
2015-10-19 17:52:36,820 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.5352825
2015-10-19 17:52:36,820 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.78968745
2015-10-19 17:52:37,258 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:37,258 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:37,399 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.7439616
2015-10-19 17:52:38,274 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:38,274 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:38,649 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.7478384
2015-10-19 17:52:38,836 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.667
2015-10-19 17:52:38,961 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.667
2015-10-19 17:52:39,289 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:39,289 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:39,399 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.8372553
2015-10-19 17:52:40,024 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.8623335
2015-10-19 17:52:40,274 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.5352825
2015-10-19 17:52:40,305 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-19 17:52:40,305 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:40,492 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.8116684
2015-10-19 17:52:40,680 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.7566844
2015-10-19 17:52:41,336 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:17408, vCores:-12>
2015-10-19 17:52:41,336 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:42,071 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.7610165
2015-10-19 17:52:42,227 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.667
2015-10-19 17:52:42,258 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.6763808
2015-10-19 17:52:42,336 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-19 17:52:42,352 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0020_01_000011 to attempt_1445182159119_0020_m_000008_0
2015-10-19 17:52:42,352 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:52:42,352 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0020_01_000010 to attempt_1445182159119_0020_m_000009_0
2015-10-19 17:52:42,352 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:52:42,352 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:13312, vCores:-14>
2015-10-19 17:52:42,352 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:52:42,352 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:1 RackLocal:9
2015-10-19 17:52:42,352 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:52:42,352 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:52:42,352 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:52:42,446 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0020_01_000011 taskAttempt attempt_1445182159119_0020_m_000008_0
2015-10-19 17:52:42,446 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0020_m_000008_0
2015-10-19 17:52:42,446 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:52:42,539 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0020_01_000010 taskAttempt attempt_1445182159119_0020_m_000009_0
2015-10-19 17:52:42,539 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0020_m_000009_0
2015-10-19 17:52:42,539 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:52:42,555 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0020_m_000008_0 : 13562
2015-10-19 17:52:42,555 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0020_m_000008_0] using containerId: [container_1445182159119_0020_01_000011 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:52:42,555 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:52:42,555 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0020_m_000008
2015-10-19 17:52:42,555 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:52:42,867 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.85056007
2015-10-19 17:52:42,977 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0020_m_000009_0 : 13562
2015-10-19 17:52:42,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0020_m_000009_0] using containerId: [container_1445182159119_0020_01_000010 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 17:52:42,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:52:42,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0020_m_000009
2015-10-19 17:52:42,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:52:43,367 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:13312, vCores:-14> knownNMs=4
2015-10-19 17:52:43,571 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.88116723
2015-10-19 17:52:43,883 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.76906437
2015-10-19 17:52:43,961 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.5352825
2015-10-19 17:52:44,102 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.8325958
2015-10-19 17:52:45,508 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.667
2015-10-19 17:52:45,508 INFO [IPC Server handler 13 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.7735662
2015-10-19 17:52:45,664 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.68848926
2015-10-19 17:52:46,211 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.86282915
2015-10-19 17:52:46,649 INFO [Socket Reader #1 for port 52529] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0020 (auth:SIMPLE)
2015-10-19 17:52:46,696 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0020_m_000011 asked for a task
2015-10-19 17:52:46,696 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0020_m_000011 given task: attempt_1445182159119_0020_m_000008_0
2015-10-19 17:52:47,008 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.90054715
2015-10-19 17:52:47,305 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.78145766
2015-10-19 17:52:47,383 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.5352825
2015-10-19 17:52:47,711 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.8528074
2015-10-19 17:52:48,961 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.667
2015-10-19 17:52:48,993 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.78529257
2015-10-19 17:52:49,071 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.700124
2015-10-19 17:52:49,586 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.87339133
2015-10-19 17:52:50,539 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.92160046
2015-10-19 17:52:50,789 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.7930737
2015-10-19 17:52:50,852 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.53737575
2015-10-19 17:52:51,164 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.87422293
2015-10-19 17:52:52,680 INFO [IPC Server handler 13 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.7962063
2015-10-19 17:52:52,805 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.667
2015-10-19 17:52:52,852 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.7104523
2015-10-19 17:52:53,149 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.88349867
2015-10-19 17:52:54,180 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.106881365
2015-10-19 17:52:54,305 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.8029223
2015-10-19 17:52:54,477 INFO [Socket Reader #1 for port 52529] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0020 (auth:SIMPLE)
2015-10-19 17:52:54,711 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0020_m_000010 asked for a task
2015-10-19 17:52:54,711 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0020_m_000010 given task: attempt_1445182159119_0020_m_000009_0
2015-10-19 17:52:55,040 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.57893914
2015-10-19 17:52:55,055 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.9517756
2015-10-19 17:52:55,383 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.90311664
2015-10-19 17:52:56,680 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.67118835
2015-10-19 17:52:56,680 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.806447
2015-10-19 17:52:56,883 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.89277303
2015-10-19 17:52:56,899 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.72066176
2015-10-19 17:52:57,211 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.106881365
2015-10-19 17:52:57,696 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.8108071
2015-10-19 17:52:58,571 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.96920705
2015-10-19 17:52:58,680 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.620844
2015-10-19 17:52:59,008 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.923593
2015-10-19 17:53:00,133 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.6823513
2015-10-19 17:53:00,243 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.106881365
2015-10-19 17:53:00,243 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.8178119
2015-10-19 17:53:00,399 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.9046617
2015-10-19 17:53:00,399 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.73236954
2015-10-19 17:53:01,040 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.8223236
2015-10-19 17:53:02,087 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 0.9875252
2015-10-19 17:53:02,196 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.620844
2015-10-19 17:53:02,821 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.9419875
2015-10-19 17:53:03,290 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.106881365
2015-10-19 17:53:03,649 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.69442916
2015-10-19 17:53:03,774 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.8293971
2015-10-19 17:53:03,790 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.7440244
2015-10-19 17:53:03,993 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.9177295
2015-10-19 17:53:04,321 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.83379626
2015-10-19 17:53:05,493 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 1.0
2015-10-19 17:53:05,727 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.620844
2015-10-19 17:53:06,321 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.95737576
2015-10-19 17:53:06,352 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.17144664
2015-10-19 17:53:06,446 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000006_0 is : 1.0
2015-10-19 17:53:06,462 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0020_m_000006_0
2015-10-19 17:53:06,462 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:53:06,462 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0020_01_000008 taskAttempt attempt_1445182159119_0020_m_000006_0
2015-10-19 17:53:06,462 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0020_m_000006_0
2015-10-19 17:53:06,462 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 17:53:06,915 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.7061484
2015-10-19 17:53:07,180 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.7572881
2015-10-19 17:53:07,290 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.8423943
2015-10-19 17:53:07,305 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.9303582
2015-10-19 17:53:07,321 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:53:07,337 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0020_m_000006_0
2015-10-19 17:53:07,337 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:53:07,337 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-19 17:53:07,680 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.84643364
2015-10-19 17:53:08,087 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:1 RackLocal:9
2015-10-19 17:53:08,087 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:13312, vCores:-14>
2015-10-19 17:53:08,087 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-19 17:53:08,087 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-19 17:53:08,087 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:1 RackLocal:9
2015-10-19 17:53:09,071 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.620844
2015-10-19 17:53:09,118 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0020: ask=1 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:14336, vCores:-13> knownNMs=4
2015-10-19 17:53:09,118 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0020_01_000008
2015-10-19 17:53:09,118 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:1 RackLocal:9
2015-10-19 17:53:09,118 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0020_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:53:09,383 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.19258286
2015-10-19 17:53:09,493 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 0.97887045
2015-10-19 17:53:10,134 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:53:10,134 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-19 17:53:10,134 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0020_01_000012 to attempt_1445182159119_0020_r_000000_0
2015-10-19 17:53:10,134 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:1 RackLocal:9
2015-10-19 17:53:10,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:10,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:53:10,149 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0020_01_000012 taskAttempt attempt_1445182159119_0020_r_000000_0
2015-10-19 17:53:10,149 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0020_r_000000_0
2015-10-19 17:53:10,149 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 17:53:10,243 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.717876
2015-10-19 17:53:10,493 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.7696844
2015-10-19 17:53:10,587 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.855221
2015-10-19 17:53:10,665 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.94346225
2015-10-19 17:53:10,899 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.85922086
2015-10-19 17:53:11,165 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0020: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:3072, vCores:-24> knownNMs=4
2015-10-19 17:53:11,274 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0020_r_000000_0 : 13562
2015-10-19 17:53:11,274 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0020_r_000000_0] using containerId: [container_1445182159119_0020_01_000012 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 17:53:11,274 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:53:11,274 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0020_r_000000
2015-10-19 17:53:11,274 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:53:12,180 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.620844
2015-10-19 17:53:12,227 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000005_0 is : 1.0
2015-10-19 17:53:12,227 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0020_m_000005_0
2015-10-19 17:53:12,227 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:53:12,227 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0020_01_000007 taskAttempt attempt_1445182159119_0020_m_000005_0
2015-10-19 17:53:12,227 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0020_m_000005_0
2015-10-19 17:53:12,227 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 17:53:12,446 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.19258286
2015-10-19 17:53:12,634 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:53:12,634 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0020_m_000005_0
2015-10-19 17:53:12,634 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:53:12,634 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-19 17:53:13,196 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:1 RackLocal:9
2015-10-19 17:53:13,540 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.72992194
2015-10-19 17:53:13,821 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.7822583
2015-10-19 17:53:13,993 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.9561502
2015-10-19 17:53:14,024 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.86756015
2015-10-19 17:53:14,274 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.87108153
2015-10-19 17:53:15,243 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0020_01_000007
2015-10-19 17:53:15,243 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:1 RackLocal:9
2015-10-19 17:53:15,243 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0020_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:53:15,477 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.19258286
2015-10-19 17:53:16,087 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.620844
2015-10-19 17:53:16,696 INFO [Socket Reader #1 for port 52529] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0020 (auth:SIMPLE)
2015-10-19 17:53:16,821 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.74229485
2015-10-19 17:53:16,852 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0020_r_000012 asked for a task
2015-10-19 17:53:16,852 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0020_r_000012 given task: attempt_1445182159119_0020_r_000000_0
2015-10-19 17:53:17,165 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.7948398
2015-10-19 17:53:17,384 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.9690681
2015-10-19 17:53:17,431 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.8803751
2015-10-19 17:53:17,587 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.8836931
2015-10-19 17:53:18,384 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0020_m_000009
2015-10-19 17:53:18,384 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0020_m_000009
2015-10-19 17:53:18,384 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 17:53:18,462 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:18,462 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:18,462 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000009_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:18,524 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.19258286
2015-10-19 17:53:19,227 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.62693965
2015-10-19 17:53:19,306 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:1 RackLocal:9
2015-10-19 17:53:19,321 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:4096, vCores:-23> knownNMs=4
2015-10-19 17:53:19,899 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 0 maxEvents 10000
2015-10-19 17:53:20,399 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.753923
2015-10-19 17:53:20,399 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:53:20,399 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:20,399 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0020_01_000013 to attempt_1445182159119_0020_m_000009_1
2015-10-19 17:53:20,399 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:1 RackLocal:10
2015-10-19 17:53:20,399 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:20,399 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000009_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:53:20,399 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0020_01_000013 taskAttempt attempt_1445182159119_0020_m_000009_1
2015-10-19 17:53:20,399 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0020_m_000009_1
2015-10-19 17:53:20,399 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 17:53:20,634 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.80577385
2015-10-19 17:53:20,774 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.980463
2015-10-19 17:53:20,946 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0020_m_000009_1 : 13562
2015-10-19 17:53:20,946 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0020_m_000009_1] using containerId: [container_1445182159119_0020_01_000013 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 17:53:20,946 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000009_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:53:20,946 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0020_m_000009
2015-10-19 17:53:20,977 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.89143646
2015-10-19 17:53:20,993 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 17:53:21,196 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.8946851
2015-10-19 17:53:21,446 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:3072, vCores:-24> knownNMs=4
2015-10-19 17:53:21,571 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.20524552
2015-10-19 17:53:22,024 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 17:53:22,102 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.62693965
2015-10-19 17:53:22,868 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.667
2015-10-19 17:53:23,087 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 17:53:23,790 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.097867966
2015-10-19 17:53:23,821 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.76355594
2015-10-19 17:53:24,118 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.8165839
2015-10-19 17:53:24,181 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 17:53:24,321 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 0.99131024
2015-10-19 17:53:24,524 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.90183806
2015-10-19 17:53:24,524 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.9051126
2015-10-19 17:53:24,618 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.27811313
2015-10-19 17:53:24,806 INFO [Socket Reader #1 for port 52529] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0020 (auth:SIMPLE)
2015-10-19 17:53:24,868 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0020_m_000013 asked for a task
2015-10-19 17:53:24,868 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0020_m_000013 given task: attempt_1445182159119_0020_m_000009_1
2015-10-19 17:53:25,353 INFO [IPC Server handler 13 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 17:53:26,134 INFO [Thread-54] org.apache.hadoop.hdfs.DFSClient: Exception in createBlockOutputStream
java.io.IOException: Bad connect ack with firstBadLink as ************:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.createBlockOutputStream(DFSOutputStream.java:1461)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.nextBlockOutputStream(DFSOutputStream.java:1362)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.run(DFSOutputStream.java:589)
2015-10-19 17:53:26,134 INFO [Thread-54] org.apache.hadoop.hdfs.DFSClient: Abandoning BP-1347369012-**************-1444972147527:blk_1073744056_3281
2015-10-19 17:53:26,290 INFO [Thread-54] org.apache.hadoop.hdfs.DFSClient: Excluding datanode ************:50010
2015-10-19 17:53:26,415 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 17:53:26,524 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.06666667
2015-10-19 17:53:26,571 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.667
2015-10-19 17:53:27,118 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.77376443
2015-10-19 17:53:27,228 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.20716506
2015-10-19 17:53:27,446 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 17:53:27,524 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.82795393
2015-10-19 17:53:27,649 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.27811313
2015-10-19 17:53:27,790 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 1.0
2015-10-19 17:53:27,946 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.91669494
2015-10-19 17:53:28,040 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.9130028
2015-10-19 17:53:28,493 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 17:53:29,556 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 17:53:30,040 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000000_0 is : 1.0
2015-10-19 17:53:30,040 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0020_m_000000_0
2015-10-19 17:53:30,040 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:53:30,040 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0020_01_000002 taskAttempt attempt_1445182159119_0020_m_000000_0
2015-10-19 17:53:30,040 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0020_m_000000_0
2015-10-19 17:53:30,040 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:53:30,134 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.06666667
2015-10-19 17:53:30,399 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.667
2015-10-19 17:53:30,478 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:53:30,478 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0020_m_000000_0
2015-10-19 17:53:30,478 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:53:30,478 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-19 17:53:30,493 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.7848156
2015-10-19 17:53:30,587 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 17:53:30,603 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.295472
2015-10-19 17:53:30,696 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.27811313
2015-10-19 17:53:30,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:1 RackLocal:10
2015-10-19 17:53:30,806 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.83903193
2015-10-19 17:53:31,446 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.92764044
2015-10-19 17:53:31,587 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.92481136
2015-10-19 17:53:31,728 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:32,884 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:32,884 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0020_01_000002
2015-10-19 17:53:32,884 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:1 RackLocal:10
2015-10-19 17:53:32,884 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0020_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:53:33,650 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.06666667
2015-10-19 17:53:33,728 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.27811313
2015-10-19 17:53:33,853 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.667
2015-10-19 17:53:33,978 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:33,978 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.7950223
2015-10-19 17:53:34,071 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.295472
2015-10-19 17:53:34,306 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.8491169
2015-10-19 17:53:34,900 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.9375001
2015-10-19 17:53:35,056 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:35,103 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.9344858
2015-10-19 17:53:36,196 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:36,759 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.3637686
2015-10-19 17:53:37,446 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.805453
2015-10-19 17:53:37,446 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:37,540 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.295472
2015-10-19 17:53:37,790 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.85956883
2015-10-19 17:53:37,790 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.06666667
2015-10-19 17:53:37,900 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.667
2015-10-19 17:53:38,275 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.9477346
2015-10-19 17:53:38,462 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.9446923
2015-10-19 17:53:38,603 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:39,790 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.3637686
2015-10-19 17:53:40,368 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:40,868 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.81524783
2015-10-19 17:53:41,025 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.295472
2015-10-19 17:53:41,275 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.86983776
2015-10-19 17:53:41,493 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.07135515
2015-10-19 17:53:41,493 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:41,759 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.95744973
2015-10-19 17:53:41,900 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.06666667
2015-10-19 17:53:42,009 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.9552187
2015-10-19 17:53:42,181 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.667
2015-10-19 17:53:42,650 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:42,853 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.3637686
2015-10-19 17:53:44,134 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:44,509 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.295472
2015-10-19 17:53:44,509 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.82602364
2015-10-19 17:53:44,900 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.8805084
2015-10-19 17:53:45,040 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.10621303
2015-10-19 17:53:45,243 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.96852434
2015-10-19 17:53:45,243 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:45,572 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.96575046
2015-10-19 17:53:45,868 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.3637686
2015-10-19 17:53:46,431 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:47,040 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.67181367
2015-10-19 17:53:47,087 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.06666667
2015-10-19 17:53:47,947 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.295472
2015-10-19 17:53:48,087 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.8361601
2015-10-19 17:53:48,228 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:48,353 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.89073473
2015-10-19 17:53:48,400 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.13501765
2015-10-19 17:53:48,900 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.44950172
2015-10-19 17:53:48,962 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.9787791
2015-10-19 17:53:48,962 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.97577965
2015-10-19 17:53:49,322 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:50,384 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:50,587 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.06666667
2015-10-19 17:53:51,228 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.6905134
2015-10-19 17:53:51,431 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.295472
2015-10-19 17:53:51,525 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.8458909
2015-10-19 17:53:51,525 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:51,837 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.9012919
2015-10-19 17:53:52,009 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.17192487
2015-10-19 17:53:52,009 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.44950172
2015-10-19 17:53:52,447 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.98835766
2015-10-19 17:53:52,587 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:52,634 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.9865117
2015-10-19 17:53:53,681 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:54,228 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.06666667
2015-10-19 17:53:54,337 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.70565456
2015-10-19 17:53:54,759 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:55,087 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.44950172
2015-10-19 17:53:55,087 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.1800219
2015-10-19 17:53:55,181 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.295472
2015-10-19 17:53:55,556 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.8560148
2015-10-19 17:53:55,697 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.9111408
2015-10-19 17:53:56,291 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 0.9971318
2015-10-19 17:53:56,400 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 0.9953699
2015-10-19 17:53:56,853 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:57,900 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:58,119 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.49267456
2015-10-19 17:53:58,197 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.19622828
2015-10-19 17:53:58,712 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.295472
2015-10-19 17:53:58,900 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.74644077
2015-10-19 17:53:58,900 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.06666667
2015-10-19 17:53:59,009 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:53:59,541 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.86495143
2015-10-19 17:53:59,962 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.92127204
2015-10-19 17:54:00,337 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000003_0 is : 1.0
2015-10-19 17:54:00,369 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0020_m_000003_0
2015-10-19 17:54:00,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:54:00,369 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0020_01_000005 taskAttempt attempt_1445182159119_0020_m_000003_0
2015-10-19 17:54:00,369 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0020_m_000003_0
2015-10-19 17:54:00,369 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:54:00,431 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 1.0
2015-10-19 17:54:00,650 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:54:00,775 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:54:00,791 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0020_m_000003_0
2015-10-19 17:54:00,791 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:54:00,791 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-19 17:54:00,837 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:1 RackLocal:10
2015-10-19 17:54:00,837 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000001_0 is : 1.0
2015-10-19 17:54:00,853 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0020_m_000001_0
2015-10-19 17:54:00,853 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:54:00,853 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0020_01_000003 taskAttempt attempt_1445182159119_0020_m_000001_0
2015-10-19 17:54:00,853 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0020_m_000001_0
2015-10-19 17:54:00,853 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:54:01,150 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.53521925
2015-10-19 17:54:01,212 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:54:01,212 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0020_m_000001_0
2015-10-19 17:54:01,212 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:54:01,212 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-19 17:54:01,275 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.21693178
2015-10-19 17:54:01,759 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 17:54:01,884 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:1 RackLocal:10
2015-10-19 17:54:02,103 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.295472
2015-10-19 17:54:02,869 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 17:54:02,916 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.875085
2015-10-19 17:54:03,009 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.06666667
2015-10-19 17:54:03,166 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.9319112
2015-10-19 17:54:03,900 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0020_01_000003
2015-10-19 17:54:03,900 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0020_01_000005
2015-10-19 17:54:03,900 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0020_m_000001_0: Container killed by the ApplicationMaster.

2015-10-19 17:54:03,900 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:1 RackLocal:10
2015-10-19 17:54:03,900 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0020_m_000003_0: Container killed by the ApplicationMaster.

2015-10-19 17:54:03,963 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 17:54:04,166 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.53521925
2015-10-19 17:54:04,994 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.23853204
2015-10-19 17:54:05,041 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 17:54:05,228 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.7845043
2015-10-19 17:54:05,384 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.295472
2015-10-19 17:54:06,103 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.8885119
2015-10-19 17:54:06,291 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.9451668
2015-10-19 17:54:06,681 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 17:54:06,775 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.06666667
2015-10-19 17:54:07,228 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.53521925
2015-10-19 17:54:07,806 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 17:54:08,744 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.38861483
2015-10-19 17:54:08,885 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 17:54:08,885 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.79901713
2015-10-19 17:54:09,041 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.295472
2015-10-19 17:54:09,525 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.90073824
2015-10-19 17:54:09,760 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.9577042
2015-10-19 17:54:10,260 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.6207798
2015-10-19 17:54:10,353 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.06666667
2015-10-19 17:54:12,103 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.48997167
2015-10-19 17:54:12,275 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 17:54:12,963 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.91168153
2015-10-19 17:54:13,244 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.9684558
2015-10-19 17:54:13,306 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.6207798
2015-10-19 17:54:13,463 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 17:54:14,603 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 17:54:15,510 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.06666667
2015-10-19 17:54:15,603 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.8251953
2015-10-19 17:54:15,650 INFO [IPC Server handler 13 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.5323719
2015-10-19 17:54:15,697 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.295472
2015-10-19 17:54:15,744 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 17:54:16,416 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.6207798
2015-10-19 17:54:16,432 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.92231226
2015-10-19 17:54:16,916 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 17:54:16,994 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.97986937
2015-10-19 17:54:18,807 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 17:54:19,119 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.5323719
2015-10-19 17:54:19,432 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.62954044
2015-10-19 17:54:19,635 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.06666667
2015-10-19 17:54:19,791 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.9326479
2015-10-19 17:54:19,838 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.295472
2015-10-19 17:54:19,885 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 17:54:20,088 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.83850276
2015-10-19 17:54:20,166 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.62954044
2015-10-19 17:54:20,260 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 0.9905238
2015-10-19 17:54:21,463 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 17:54:22,447 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.667
2015-10-19 17:54:22,588 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 17:54:22,791 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.5323719
2015-10-19 17:54:23,244 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.942574
2015-10-19 17:54:23,869 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 1.0
2015-10-19 17:54:24,260 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 17:54:24,322 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.295472
2015-10-19 17:54:24,400 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.06666667
2015-10-19 17:54:24,447 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.8531011
2015-10-19 17:54:25,400 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 17:54:25,510 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.667
2015-10-19 17:54:26,213 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000002_0 is : 1.0
2015-10-19 17:54:26,213 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0020_m_000002_0
2015-10-19 17:54:26,213 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:54:26,213 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0020_01_000004 taskAttempt attempt_1445182159119_0020_m_000002_0
2015-10-19 17:54:26,213 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0020_m_000002_0
2015-10-19 17:54:26,213 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:54:26,432 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.5323719
2015-10-19 17:54:26,447 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 17:54:26,697 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.9500246
2015-10-19 17:54:26,760 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:54:26,760 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0020_m_000002_0
2015-10-19 17:54:26,760 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:54:26,760 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-19 17:54:27,072 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:1 RackLocal:10
2015-10-19 17:54:27,541 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 17:54:28,619 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.8656473
2015-10-19 17:54:28,619 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.68349344
2015-10-19 17:54:28,619 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.295472
2015-10-19 17:54:28,666 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 17:54:28,791 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.10000001
2015-10-19 17:54:29,791 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 17:54:30,494 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.5323719
2015-10-19 17:54:30,635 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.95765096
2015-10-19 17:54:30,885 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 17:54:31,307 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0020_01_000004
2015-10-19 17:54:31,307 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:1 RackLocal:10
2015-10-19 17:54:31,307 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0020_m_000002_0: Container killed by the ApplicationMaster.

2015-10-19 17:54:31,697 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.72477067
2015-10-19 17:54:32,041 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 17:54:32,729 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.879517
2015-10-19 17:54:33,104 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.10000001
2015-10-19 17:54:33,119 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.295472
2015-10-19 17:54:33,135 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 17:54:33,760 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.5323719
2015-10-19 17:54:34,026 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.9677739
2015-10-19 17:54:34,276 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 17:54:34,744 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.7569369
2015-10-19 17:54:35,463 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 17:54:36,557 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 17:54:36,979 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.89259195
2015-10-19 17:54:37,026 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.295472
2015-10-19 17:54:37,291 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.5323719
2015-10-19 17:54:37,479 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.9785102
2015-10-19 17:54:37,526 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.10000001
2015-10-19 17:54:37,697 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 17:54:37,807 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.79283845
2015-10-19 17:54:38,869 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 17:54:39,994 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 17:54:40,807 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.5323719
2015-10-19 17:54:40,885 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.8317286
2015-10-19 17:54:40,916 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.295472
2015-10-19 17:54:40,916 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.98763716
2015-10-19 17:54:41,213 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.9067527
2015-10-19 17:54:41,588 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 17:54:41,776 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.10000001
2015-10-19 17:54:42,776 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 17:54:43,932 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.86973083
2015-10-19 17:54:44,276 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 17:54:44,479 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.5323719
2015-10-19 17:54:44,526 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 0.9951669
2015-10-19 17:54:44,619 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.38885322
2015-10-19 17:54:45,104 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.9228759
2015-10-19 17:54:45,401 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.10000001
2015-10-19 17:54:46,932 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000004_0 is : 1.0
2015-10-19 17:54:46,932 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0020_m_000004_0
2015-10-19 17:54:46,948 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:54:46,948 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0020_01_000006 taskAttempt attempt_1445182159119_0020_m_000004_0
2015-10-19 17:54:46,948 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0020_m_000004_0
2015-10-19 17:54:46,948 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:54:47,026 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:54:47,026 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0020_m_000004_0
2015-10-19 17:54:47,026 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:54:47,026 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-19 17:54:47,041 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.89727306
2015-10-19 17:54:47,276 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 17:54:47,620 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.5323719
2015-10-19 17:54:47,682 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:1 RackLocal:10
2015-10-19 17:54:48,557 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 17:54:48,901 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0020_01_000006
2015-10-19 17:54:48,901 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:1 RackLocal:10
2015-10-19 17:54:48,901 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0020_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:54:49,963 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.94338906
2015-10-19 17:54:50,104 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.92904276
2015-10-19 17:54:50,229 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.454562
2015-10-19 17:54:50,651 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 17:54:50,651 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.10000001
2015-10-19 17:54:50,995 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.5323719
2015-10-19 17:54:51,760 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 17:54:53,026 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 17:54:53,182 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 0.97083515
2015-10-19 17:54:54,276 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.5323719
2015-10-19 17:54:54,276 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 17:54:54,932 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.6330032
2015-10-19 17:54:55,104 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.10000001
2015-10-19 17:54:55,401 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 17:54:55,495 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.9644989
2015-10-19 17:54:55,948 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000008_0 is : 1.0
2015-10-19 17:54:56,073 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0020_m_000008_0
2015-10-19 17:54:56,073 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:54:56,073 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0020_01_000011 taskAttempt attempt_1445182159119_0020_m_000008_0
2015-10-19 17:54:56,073 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0020_m_000008_0
2015-10-19 17:54:56,073 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:54:56,479 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.6330032
2015-10-19 17:54:56,479 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:54:56,479 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0020_m_000008_0
2015-10-19 17:54:56,495 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:54:56,495 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-19 17:54:56,573 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:1 RackLocal:10
2015-10-19 17:54:56,932 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 17:54:58,042 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0020_01_000011
2015-10-19 17:54:58,042 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:1 RackLocal:10
2015-10-19 17:54:58,042 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0020_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:54:58,307 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.5323719
2015-10-19 17:54:58,370 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.667
2015-10-19 17:54:59,432 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.98111796
2015-10-19 17:54:59,792 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.10000001
2015-10-19 17:55:00,167 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 17:55:02,026 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.5323719
2015-10-19 17:55:02,120 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.667
2015-10-19 17:55:03,010 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 0.998139
2015-10-19 17:55:05,714 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 17:55:05,885 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.667
2015-10-19 17:55:05,995 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.5323719
2015-10-19 17:55:06,479 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 1.0
2015-10-19 17:55:08,432 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.10000001
2015-10-19 17:55:09,292 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.667
2015-10-19 17:55:10,370 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000007_0 is : 1.0
2015-10-19 17:55:10,432 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0020_m_000007_0
2015-10-19 17:55:10,432 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:55:10,432 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0020_01_000009 taskAttempt attempt_1445182159119_0020_m_000007_0
2015-10-19 17:55:10,432 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0020_m_000007_0
2015-10-19 17:55:10,448 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 17:55:10,776 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.5323719
2015-10-19 17:55:11,495 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:55:11,495 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0020_m_000007_0
2015-10-19 17:55:11,495 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:55:11,495 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-19 17:55:12,057 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:1 RackLocal:10
2015-10-19 17:55:13,120 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.667
2015-10-19 17:55:14,229 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0020_01_000009
2015-10-19 17:55:14,229 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:1 RackLocal:10
2015-10-19 17:55:14,229 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0020_m_000007_0: Container killed by the ApplicationMaster.

2015-10-19 17:55:14,636 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.5323719
2015-10-19 17:55:16,589 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.667
2015-10-19 17:55:18,823 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.5323719
2015-10-19 17:55:20,011 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.667
2015-10-19 17:55:20,792 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 17:55:22,073 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:23,058 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.5323719
2015-10-19 17:55:23,386 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:23,480 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.667
2015-10-19 17:55:24,011 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.13333334
2015-10-19 17:55:24,558 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:25,745 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:26,730 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.58867985
2015-10-19 17:55:26,948 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:27,058 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.6670339
2015-10-19 17:55:27,667 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.13333334
2015-10-19 17:55:28,573 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:29,698 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:30,402 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.6881311
2015-10-19 17:55:30,667 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.62919205
2015-10-19 17:55:30,823 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:31,120 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.13333334
2015-10-19 17:55:31,917 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:33,292 INFO [IPC Server handler 13 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:33,527 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.62919205
2015-10-19 17:55:33,855 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.7156106
2015-10-19 17:55:34,136 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.667
2015-10-19 17:55:34,402 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:34,558 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.13333334
2015-10-19 17:55:35,573 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:36,683 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:37,089 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.7436006
2015-10-19 17:55:37,761 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:37,777 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.667
2015-10-19 17:55:38,027 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.13333334
2015-10-19 17:55:39,058 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:40,183 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:40,292 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.77410495
2015-10-19 17:55:41,292 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.667
2015-10-19 17:55:41,339 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:41,480 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.13333334
2015-10-19 17:55:42,449 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:43,433 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.80502725
2015-10-19 17:55:44,433 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:44,761 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.667
2015-10-19 17:55:45,605 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:46,589 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.8374014
2015-10-19 17:55:46,714 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.13333334
2015-10-19 17:55:46,792 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:47,933 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:48,246 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.667
2015-10-19 17:55:49,089 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:50,074 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.8676939
2015-10-19 17:55:50,277 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:51,292 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:55:51,480 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:52,621 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:52,777 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.6820075
2015-10-19 17:55:53,386 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.8955376
2015-10-19 17:55:53,761 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:54,886 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:55,058 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:55:55,996 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:56,480 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.9235089
2015-10-19 17:55:57,121 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.7199224
2015-10-19 17:55:57,558 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:58,683 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:55:58,871 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:55:59,605 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.9580773
2015-10-19 17:56:00,949 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:56:00,980 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.76382995
2015-10-19 17:56:02,058 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:56:02,777 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 0.99257255
2015-10-19 17:56:03,183 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:56:03,371 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:56:03,980 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_0 is : 1.0
2015-10-19 17:56:03,996 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0020_m_000009_0
2015-10-19 17:56:03,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:56:03,996 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0020_01_000010 taskAttempt attempt_1445182159119_0020_m_000009_0
2015-10-19 17:56:03,996 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0020_m_000009_0
2015-10-19 17:56:03,996 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:56:04,027 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:56:04,027 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0020_m_000009_0
2015-10-19 17:56:04,027 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0020_m_000009_1
2015-10-19 17:56:04,027 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:56:04,027 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-19 17:56:04,027 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000009_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 17:56:04,027 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0020_01_000013 taskAttempt attempt_1445182159119_0020_m_000009_1
2015-10-19 17:56:04,027 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0020_m_000009_1
2015-10-19 17:56:04,027 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 17:56:04,261 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:1 RackLocal:10
2015-10-19 17:56:04,683 INFO [IPC Server handler 13 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 17:56:04,699 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_m_000009_1 is : 0.8032775
2015-10-19 17:56:04,902 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000009_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 17:56:04,918 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 17:56:05,090 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/_temporary/attempt_1445182159119_0020_m_000009_1
2015-10-19 17:56:05,090 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_m_000009_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 17:56:05,340 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0020_01_000010
2015-10-19 17:56:05,340 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:1 RackLocal:10
2015-10-19 17:56:05,340 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0020_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:56:05,793 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:06,261 INFO [Socket Reader #1 for port 52529] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 52529: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 17:56:06,887 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:07,137 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:56:07,449 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0020_01_000013
2015-10-19 17:56:07,449 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:1 RackLocal:10
2015-10-19 17:56:07,449 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0020_m_000009_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:56:08,012 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:09,137 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:10,230 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:10,746 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:56:11,355 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:12,496 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:13,574 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:14,262 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:56:14,652 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:15,715 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:16,824 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:17,402 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:56:17,902 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:18,996 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:20,590 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:20,621 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:56:21,715 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:22,840 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:23,996 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:24,449 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:56:25,090 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:26,168 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:27,246 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:27,934 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:56:28,340 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:29,434 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:30,543 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:31,590 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:56:31,668 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:32,809 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:33,934 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:35,090 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:35,543 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:56:36,168 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:37,231 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:38,887 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:38,996 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:56:39,981 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:41,075 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:42,168 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:42,684 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:56:43,278 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:44,356 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:45,481 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:46,043 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:56:46,622 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:47,778 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:48,903 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:49,622 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:56:50,715 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:51,794 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:52,872 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:53,169 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:56:53,981 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:55,075 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:56,700 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:56:56,903 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:58,012 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:56:59,091 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:00,137 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:00,325 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:57:02,903 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:03,966 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:05,028 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:05,450 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:57:06,091 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:07,184 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:08,309 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:09,122 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.16666667
2015-10-19 17:57:09,388 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:10,466 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:11,559 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:12,653 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:12,685 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.20000002
2015-10-19 17:57:15,029 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:16,138 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:17,216 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:17,529 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.20000002
2015-10-19 17:57:18,294 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:19,435 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:20,592 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:21,748 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:21,764 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.20000002
2015-10-19 17:57:23,029 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:24,123 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:25,358 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:26,108 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.20000002
2015-10-19 17:57:26,451 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:27,545 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:28,608 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:29,717 INFO [IPC Server handler 13 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:29,858 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.20000002
2015-10-19 17:57:30,811 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:31,952 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:33,014 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:33,561 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.20000002
2015-10-19 17:57:34,093 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:35,155 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:36,218 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:37,062 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.20000002
2015-10-19 17:57:37,296 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:38,390 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:39,468 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:40,578 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:41,062 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.20000002
2015-10-19 17:57:41,734 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:42,859 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:43,969 INFO [IPC Server handler 13 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:44,594 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.20000002
2015-10-19 17:57:45,078 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:46,141 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:47,234 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:48,500 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.20000002
2015-10-19 17:57:48,891 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:51,204 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:52,344 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:52,438 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.20000002
2015-10-19 17:57:53,485 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:54,594 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:55,704 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:56,407 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.20000002
2015-10-19 17:57:56,813 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:57,939 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:57:59,048 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:00,111 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:00,267 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.20000002
2015-10-19 17:58:01,251 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:02,798 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:03,892 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:04,252 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.20000002
2015-10-19 17:58:05,002 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:06,064 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:07,189 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:08,752 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.20000002
2015-10-19 17:58:08,752 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:09,799 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:10,862 INFO [IPC Server handler 13 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:11,955 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:12,549 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.20000002
2015-10-19 17:58:13,034 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:14,159 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:15,253 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:16,331 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:16,440 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.20000002
2015-10-19 17:58:17,487 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:18,597 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:19,659 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:20,519 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.20000002
2015-10-19 17:58:20,722 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:21,800 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:22,863 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:23,925 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:24,113 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.20000002
2015-10-19 17:58:25,113 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:26,207 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:27,316 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:27,457 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.20000002
2015-10-19 17:58:29,019 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:30,113 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:31,223 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:31,848 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.20000002
2015-10-19 17:58:32,317 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:33,457 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:34,973 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:35,567 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.23333333
2015-10-19 17:58:36,036 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:37,114 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:38,161 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:39,255 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:39,739 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.23333333
2015-10-19 17:58:40,333 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:41,427 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:42,599 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:43,646 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.23333333
2015-10-19 17:58:43,693 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:44,833 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:45,896 INFO [IPC Server handler 24 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:47,037 INFO [IPC Server handler 4 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:47,537 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.23333333
2015-10-19 17:58:48,177 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:49,724 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:50,849 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:51,287 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.23333333
2015-10-19 17:58:51,881 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:52,943 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:53,990 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:54,850 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.23333333
2015-10-19 17:58:55,069 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:56,131 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:57,194 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:58,288 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:58:58,506 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.26666668
2015-10-19 17:58:59,366 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:00,444 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:01,538 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:02,054 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.26666668
2015-10-19 17:59:02,632 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:03,741 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:04,819 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:05,319 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.26666668
2015-10-19 17:59:05,882 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:06,945 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:08,085 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:09,179 INFO [IPC Server handler 13 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:09,351 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.26666668
2015-10-19 17:59:10,242 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:11,289 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:12,351 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:13,148 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.26666668
2015-10-19 17:59:13,414 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:14,523 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:15,586 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:16,664 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.26666668
2015-10-19 17:59:16,664 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:17,727 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:18,805 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:19,914 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:20,243 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.26666668
2015-10-19 17:59:21,008 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:22,071 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:23,649 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.3
2015-10-19 17:59:23,743 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:24,790 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:25,868 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:26,931 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:27,259 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.3
2015-10-19 17:59:27,978 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:29,040 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:30,087 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:30,509 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.3
2015-10-19 17:59:31,150 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:32,197 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:33,244 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:33,791 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.3
2015-10-19 17:59:34,322 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:35,416 INFO [IPC Server handler 14 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:36,478 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:36,963 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.3
2015-10-19 17:59:37,541 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:38,619 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:39,682 INFO [IPC Server handler 9 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:40,338 INFO [IPC Server handler 15 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.3
2015-10-19 17:59:40,776 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:41,823 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:42,870 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:43,792 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.3
2015-10-19 17:59:43,917 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:44,995 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:46,557 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:47,182 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.3
2015-10-19 17:59:47,589 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:48,636 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:49,714 INFO [IPC Server handler 7 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:50,276 INFO [IPC Server handler 17 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.3
2015-10-19 17:59:50,745 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:51,792 INFO [IPC Server handler 11 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:52,839 INFO [IPC Server handler 29 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:53,386 INFO [IPC Server handler 6 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.3
2015-10-19 17:59:53,871 INFO [IPC Server handler 18 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:54,933 INFO [IPC Server handler 16 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:56,027 INFO [IPC Server handler 19 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:56,511 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.3
2015-10-19 17:59:57,105 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:58,137 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:59,199 INFO [IPC Server handler 20 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 17:59:59,637 INFO [IPC Server handler 5 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.3
2015-10-19 18:00:00,309 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 18:00:01,356 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 18:00:02,746 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.3
2015-10-19 18:00:02,934 INFO [IPC Server handler 21 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 18:00:03,981 INFO [IPC Server handler 2 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 18:00:05,028 INFO [IPC Server handler 25 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 18:00:05,856 INFO [IPC Server handler 0 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.3
2015-10-19 18:00:07,450 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 18:00:08,481 INFO [IPC Server handler 23 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0020_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 18:00:08,637 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.3
2015-10-19 18:00:08,731 INFO [IPC Server handler 10 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.3
2015-10-19 18:00:09,513 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.6667674
2015-10-19 18:00:12,607 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.6765304
2015-10-19 18:00:15,654 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.6960311
2015-10-19 18:00:18,685 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.71525896
2015-10-19 18:00:21,764 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.7343348
2015-10-19 18:00:24,811 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.7543343
2015-10-19 18:00:27,858 INFO [IPC Server handler 28 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.7738502
2015-10-19 18:00:30,905 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.7936201
2015-10-19 18:00:33,952 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.81336975
2015-10-19 18:00:37,015 INFO [IPC Server handler 12 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.8323589
2015-10-19 18:00:40,109 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.8521129
2015-10-19 18:00:43,172 INFO [IPC Server handler 8 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.8699593
2015-10-19 18:00:46,219 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.8896697
2015-10-19 18:00:49,250 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.90940833
2015-10-19 18:00:52,329 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.9291363
2015-10-19 18:00:55,360 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.9489235
2015-10-19 18:00:58,407 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.9687302
2015-10-19 18:01:01,470 INFO [IPC Server handler 26 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 0.9882213
2015-10-19 18:01:03,799 INFO [IPC Server handler 3 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445182159119_0020_r_000000_0
2015-10-19 18:01:03,799 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_r_000000_0 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-19 18:01:03,799 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445182159119_0020_r_000000_0 given a go for committing the task output.
2015-10-19 18:01:03,799 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445182159119_0020_r_000000_0
2015-10-19 18:01:03,799 INFO [IPC Server handler 27 on 52529] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445182159119_0020_r_000000_0:true
2015-10-19 18:01:03,861 INFO [IPC Server handler 1 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0020_r_000000_0 is : 1.0
2015-10-19 18:01:03,861 INFO [IPC Server handler 22 on 52529] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0020_r_000000_0
2015-10-19 18:01:03,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_r_000000_0 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 18:01:03,877 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0020_01_000012 taskAttempt attempt_1445182159119_0020_r_000000_0
2015-10-19 18:01:03,877 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0020_r_000000_0
2015-10-19 18:01:03,877 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 18:01:03,986 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0020_r_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 18:01:03,986 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0020_r_000000_0
2015-10-19 18:01:03,986 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0020_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 18:01:03,986 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-19 18:01:03,986 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0020Job Transitioned from RUNNING to COMMITTING
2015-10-19 18:01:04,049 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-19 18:01:04,486 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-19 18:01:04,486 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0020Job Transitioned from COMMITTING to SUCCEEDED
2015-10-19 18:01:04,564 INFO [Thread-114] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-19 18:01:04,564 INFO [Thread-114] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-19 18:01:04,564 INFO [Thread-114] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-19 18:01:04,564 INFO [Thread-114] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-19 18:01:04,564 INFO [Thread-114] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-19 18:01:04,564 INFO [Thread-114] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-19 18:01:04,564 INFO [Thread-114] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-19 18:01:04,642 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:12 ContRel:0 HostLocal:1 RackLocal:10
2015-10-19 18:01:05,299 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0020/job_1445182159119_0020_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0020-1445247554890-msrabi-pagerank-1445248864486-10-1-SUCCEEDED-default-1445248046659.jhist_tmp
2015-10-19 18:01:05,658 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0020_01_000012
2015-10-19 18:01:05,658 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:12 ContRel:0 HostLocal:1 RackLocal:10
2015-10-19 18:01:05,658 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0020_r_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 18:01:06,236 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0020-1445247554890-msrabi-pagerank-1445248864486-10-1-SUCCEEDED-default-1445248046659.jhist_tmp
2015-10-19 18:01:06,236 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0020/job_1445182159119_0020_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0020_conf.xml_tmp
2015-10-19 18:01:06,861 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0020_conf.xml_tmp
2015-10-19 18:01:06,861 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0020.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0020.summary
2015-10-19 18:01:06,861 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0020_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0020_conf.xml
2015-10-19 18:01:06,877 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0020-1445247554890-msrabi-pagerank-1445248864486-10-1-SUCCEEDED-default-1445248046659.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0020-1445247554890-msrabi-pagerank-1445248864486-10-1-SUCCEEDED-default-1445248046659.jhist
2015-10-19 18:01:06,877 INFO [Thread-114] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-19 18:01:06,877 INFO [Thread-114] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-19 18:01:06,877 INFO [Thread-114] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://04DN8IQ.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445182159119_0020
2015-10-19 18:01:06,908 INFO [Thread-114] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-19 18:01:07,955 INFO [Thread-114] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:12 ContRel:0 HostLocal:1 RackLocal:10
2015-10-19 18:01:07,955 INFO [Thread-114] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0020
2015-10-19 18:01:07,971 INFO [Thread-114] org.apache.hadoop.ipc.Server: Stopping server on 52529
2015-10-19 18:01:08,002 INFO [IPC Server listener on 52529] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 52529
2015-10-19 18:01:08,002 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
2015-10-19 18:01:08,002 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
