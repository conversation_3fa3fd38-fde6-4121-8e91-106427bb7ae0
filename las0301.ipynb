{"cells": [{"cell_type": "code", "execution_count": 29, "id": "eca62730", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import lasio as ls"]}, {"cell_type": "code", "execution_count": 30, "id": "1e30e55a", "metadata": {}, "outputs": [], "source": ["las = ls.read(\"C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/15-9-19_SR_COMP.LAS\") # type: ignore"]}, {"cell_type": "code", "execution_count": 31, "id": "224be7ef", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['Version', 'Well', 'Curves', 'Parameter', 'Other'])"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["las.sections.keys() # type: ignore"]}, {"cell_type": "code", "execution_count": 32, "id": "3356f920", "metadata": {}, "outputs": [{"data": {"text/plain": ["'15/9-19'"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["las.well.WELL.value"]}, {"cell_type": "code", "execution_count": 33, "id": "5746aea5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DEPT\n", "AC\n", "CALI\n", "DEN\n", "GR\n", "NEU\n", "RDEP\n", "RMED\n"]}], "source": ["for curve in las.curves:\n", "    print(curve.mnemonic)"]}, {"cell_type": "code", "execution_count": 34, "id": "1d662178", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>AC</th>\n", "      <th>CALI</th>\n", "      <th>DEN</th>\n", "      <th>GR</th>\n", "      <th>NEU</th>\n", "      <th>RDEP</th>\n", "      <th>RMED</th>\n", "    </tr>\n", "    <tr>\n", "      <th>DEPT</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3615.4340</th>\n", "      <td>95.7952</td>\n", "      <td>12.7249</td>\n", "      <td>2.3429</td>\n", "      <td>54.8754</td>\n", "      <td>44.5742</td>\n", "      <td>0.8941</td>\n", "      <td>0.8359</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3615.5864</th>\n", "      <td>94.2267</td>\n", "      <td>12.5961</td>\n", "      <td>2.3528</td>\n", "      <td>53.5158</td>\n", "      <td>62.1103</td>\n", "      <td>0.7367</td>\n", "      <td>0.8028</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3615.7388</th>\n", "      <td>101.3254</td>\n", "      <td>13.7632</td>\n", "      <td>2.3170</td>\n", "      <td>48.9803</td>\n", "      <td>71.9087</td>\n", "      <td>0.6423</td>\n", "      <td>0.7958</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3615.8912</th>\n", "      <td>99.5672</td>\n", "      <td>14.3249</td>\n", "      <td>2.2585</td>\n", "      <td>36.6762</td>\n", "      <td>44.8988</td>\n", "      <td>0.6571</td>\n", "      <td>0.7929</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3616.0436</th>\n", "      <td>100.5965</td>\n", "      <td>11.1429</td>\n", "      <td>2.2108</td>\n", "      <td>40.4632</td>\n", "      <td>49.5744</td>\n", "      <td>0.7442</td>\n", "      <td>0.7940</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 AC     CALI     DEN       GR      NEU    RDEP    RMED\n", "DEPT                                                                  \n", "3615.4340   95.7952  12.7249  2.3429  54.8754  44.5742  0.8941  0.8359\n", "3615.5864   94.2267  12.5961  2.3528  53.5158  62.1103  0.7367  0.8028\n", "3615.7388  101.3254  13.7632  2.3170  48.9803  71.9087  0.6423  0.7958\n", "3615.8912   99.5672  14.3249  2.2585  36.6762  44.8988  0.6571  0.7929\n", "3616.0436  100.5965  11.1429  2.2108  40.4632  49.5744  0.7442  0.7940"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["df=las.df()\n", "df.head()"]}, {"cell_type": "code", "execution_count": 35, "id": "db5c3769", "metadata": {}, "outputs": [], "source": ["df.reset_index(inplace=True)"]}, {"cell_type": "code", "execution_count": 36, "id": "71f5da1f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DEPT</th>\n", "      <th>AC</th>\n", "      <th>CALI</th>\n", "      <th>DEN</th>\n", "      <th>GR</th>\n", "      <th>NEU</th>\n", "      <th>RDEP</th>\n", "      <th>RMED</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3615.4340</td>\n", "      <td>95.7952</td>\n", "      <td>12.7249</td>\n", "      <td>2.3429</td>\n", "      <td>54.8754</td>\n", "      <td>44.5742</td>\n", "      <td>0.8941</td>\n", "      <td>0.8359</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3615.5864</td>\n", "      <td>94.2267</td>\n", "      <td>12.5961</td>\n", "      <td>2.3528</td>\n", "      <td>53.5158</td>\n", "      <td>62.1103</td>\n", "      <td>0.7367</td>\n", "      <td>0.8028</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3615.7388</td>\n", "      <td>101.3254</td>\n", "      <td>13.7632</td>\n", "      <td>2.3170</td>\n", "      <td>48.9803</td>\n", "      <td>71.9087</td>\n", "      <td>0.6423</td>\n", "      <td>0.7958</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3615.8912</td>\n", "      <td>99.5672</td>\n", "      <td>14.3249</td>\n", "      <td>2.2585</td>\n", "      <td>36.6762</td>\n", "      <td>44.8988</td>\n", "      <td>0.6571</td>\n", "      <td>0.7929</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3616.0436</td>\n", "      <td>100.5965</td>\n", "      <td>11.1429</td>\n", "      <td>2.2108</td>\n", "      <td>40.4632</td>\n", "      <td>49.5744</td>\n", "      <td>0.7442</td>\n", "      <td>0.7940</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        DEPT        AC     CALI     DEN       GR      NEU    RDEP    RMED\n", "0  3615.4340   95.7952  12.7249  2.3429  54.8754  44.5742  0.8941  0.8359\n", "1  3615.5864   94.2267  12.5961  2.3528  53.5158  62.1103  0.7367  0.8028\n", "2  3615.7388  101.3254  13.7632  2.3170  48.9803  71.9087  0.6423  0.7958\n", "3  3615.8912   99.5672  14.3249  2.2585  36.6762  44.8988  0.6571  0.7929\n", "4  3616.0436  100.5965  11.1429  2.2108  40.4632  49.5744  0.7442  0.7940"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 37, "id": "cf98b773", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DEPTH</th>\n", "      <th>AC</th>\n", "      <th>CALI</th>\n", "      <th>DEN</th>\n", "      <th>GR</th>\n", "      <th>NEU</th>\n", "      <th>RDEP</th>\n", "      <th>RMED</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3615.4340</td>\n", "      <td>95.7952</td>\n", "      <td>12.7249</td>\n", "      <td>2.3429</td>\n", "      <td>54.8754</td>\n", "      <td>44.5742</td>\n", "      <td>0.8941</td>\n", "      <td>0.8359</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3615.5864</td>\n", "      <td>94.2267</td>\n", "      <td>12.5961</td>\n", "      <td>2.3528</td>\n", "      <td>53.5158</td>\n", "      <td>62.1103</td>\n", "      <td>0.7367</td>\n", "      <td>0.8028</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3615.7388</td>\n", "      <td>101.3254</td>\n", "      <td>13.7632</td>\n", "      <td>2.3170</td>\n", "      <td>48.9803</td>\n", "      <td>71.9087</td>\n", "      <td>0.6423</td>\n", "      <td>0.7958</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3615.8912</td>\n", "      <td>99.5672</td>\n", "      <td>14.3249</td>\n", "      <td>2.2585</td>\n", "      <td>36.6762</td>\n", "      <td>44.8988</td>\n", "      <td>0.6571</td>\n", "      <td>0.7929</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3616.0436</td>\n", "      <td>100.5965</td>\n", "      <td>11.1429</td>\n", "      <td>2.2108</td>\n", "      <td>40.4632</td>\n", "      <td>49.5744</td>\n", "      <td>0.7442</td>\n", "      <td>0.7940</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       DEPTH        AC     CALI     DEN       GR      NEU    RDEP    RMED\n", "0  3615.4340   95.7952  12.7249  2.3429  54.8754  44.5742  0.8941  0.8359\n", "1  3615.5864   94.2267  12.5961  2.3528  53.5158  62.1103  0.7367  0.8028\n", "2  3615.7388  101.3254  13.7632  2.3170  48.9803  71.9087  0.6423  0.7958\n", "3  3615.8912   99.5672  14.3249  2.2585  36.6762  44.8988  0.6571  0.7929\n", "4  3616.0436  100.5965  11.1429  2.2108  40.4632  49.5744  0.7442  0.7940"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["df.rename(columns={'DEPT' : 'DEPTH'}, inplace=True)\n", "df.head()"]}, {"cell_type": "code", "execution_count": 38, "id": "26758a97", "metadata": {}, "outputs": [], "source": ["well = las.df()"]}, {"cell_type": "code", "execution_count": 39, "id": "36c655fe", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>AC</th>\n", "      <th>CALI</th>\n", "      <th>DEN</th>\n", "      <th>GR</th>\n", "      <th>NEU</th>\n", "      <th>RDEP</th>\n", "      <th>RMED</th>\n", "    </tr>\n", "    <tr>\n", "      <th>DEPT</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3615.4340</th>\n", "      <td>95.7952</td>\n", "      <td>12.7249</td>\n", "      <td>2.3429</td>\n", "      <td>54.8754</td>\n", "      <td>44.5742</td>\n", "      <td>0.8941</td>\n", "      <td>0.8359</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3615.5864</th>\n", "      <td>94.2267</td>\n", "      <td>12.5961</td>\n", "      <td>2.3528</td>\n", "      <td>53.5158</td>\n", "      <td>62.1103</td>\n", "      <td>0.7367</td>\n", "      <td>0.8028</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3615.7388</th>\n", "      <td>101.3254</td>\n", "      <td>13.7632</td>\n", "      <td>2.3170</td>\n", "      <td>48.9803</td>\n", "      <td>71.9087</td>\n", "      <td>0.6423</td>\n", "      <td>0.7958</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3615.8912</th>\n", "      <td>99.5672</td>\n", "      <td>14.3249</td>\n", "      <td>2.2585</td>\n", "      <td>36.6762</td>\n", "      <td>44.8988</td>\n", "      <td>0.6571</td>\n", "      <td>0.7929</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3616.0436</th>\n", "      <td>100.5965</td>\n", "      <td>11.1429</td>\n", "      <td>2.2108</td>\n", "      <td>40.4632</td>\n", "      <td>49.5744</td>\n", "      <td>0.7442</td>\n", "      <td>0.7940</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 AC     CALI     DEN       GR      NEU    RDEP    RMED\n", "DEPT                                                                  \n", "3615.4340   95.7952  12.7249  2.3429  54.8754  44.5742  0.8941  0.8359\n", "3615.5864   94.2267  12.5961  2.3528  53.5158  62.1103  0.7367  0.8028\n", "3615.7388  101.3254  13.7632  2.3170  48.9803  71.9087  0.6423  0.7958\n", "3615.8912   99.5672  14.3249  2.2585  36.6762  44.8988  0.6571  0.7929\n", "3616.0436  100.5965  11.1429  2.2108  40.4632  49.5744  0.7442  0.7940"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["well.head()"]}, {"cell_type": "code", "execution_count": 40, "id": "839bbf3c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>AC</th>\n", "      <th>CALI</th>\n", "      <th>DEN</th>\n", "      <th>GR</th>\n", "      <th>NEU</th>\n", "      <th>RDEP</th>\n", "      <th>RMED</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>6579.000000</td>\n", "      <td>6579.000000</td>\n", "      <td>6656.000000</td>\n", "      <td>6689.000000</td>\n", "      <td>6668.000000</td>\n", "      <td>6701.000000</td>\n", "      <td>6701.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>78.313607</td>\n", "      <td>9.550656</td>\n", "      <td>2.459291</td>\n", "      <td>36.048563</td>\n", "      <td>17.986061</td>\n", "      <td>3.221012</td>\n", "      <td>3.081261</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>17.009535</td>\n", "      <td>0.906267</td>\n", "      <td>0.162682</td>\n", "      <td>26.705519</td>\n", "      <td>11.694084</td>\n", "      <td>8.860387</td>\n", "      <td>6.306741</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.025100</td>\n", "      <td>6.000000</td>\n", "      <td>2.037700</td>\n", "      <td>2.766100</td>\n", "      <td>2.178300</td>\n", "      <td>0.250300</td>\n", "      <td>0.294700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>66.169300</td>\n", "      <td>9.142900</td>\n", "      <td>2.341250</td>\n", "      <td>14.852300</td>\n", "      <td>10.244475</td>\n", "      <td>0.800600</td>\n", "      <td>0.844800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>74.145900</td>\n", "      <td>9.523800</td>\n", "      <td>2.514200</td>\n", "      <td>32.142600</td>\n", "      <td>15.245100</td>\n", "      <td>1.770000</td>\n", "      <td>1.801400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>89.024250</td>\n", "      <td>9.904800</td>\n", "      <td>2.589800</td>\n", "      <td>50.901700</td>\n", "      <td>22.952525</td>\n", "      <td>3.394000</td>\n", "      <td>3.537300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>149.218700</td>\n", "      <td>20.285700</td>\n", "      <td>3.001300</td>\n", "      <td>304.333700</td>\n", "      <td>146.347400</td>\n", "      <td>198.537100</td>\n", "      <td>115.635000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                AC         CALI          DEN           GR          NEU  \\\n", "count  6579.000000  6579.000000  6656.000000  6689.000000  6668.000000   \n", "mean     78.313607     9.550656     2.459291    36.048563    17.986061   \n", "std      17.009535     0.906267     0.162682    26.705519    11.694084   \n", "min       1.025100     6.000000     2.037700     2.766100     2.178300   \n", "25%      66.169300     9.142900     2.341250    14.852300    10.244475   \n", "50%      74.145900     9.523800     2.514200    32.142600    15.245100   \n", "75%      89.024250     9.904800     2.589800    50.901700    22.952525   \n", "max     149.218700    20.285700     3.001300   304.333700   146.347400   \n", "\n", "              RDEP         RMED  \n", "count  6701.000000  6701.000000  \n", "mean      3.221012     3.081261  \n", "std       8.860387     6.306741  \n", "min       0.250300     0.294700  \n", "25%       0.800600     0.844800  \n", "50%       1.770000     1.801400  \n", "75%       3.394000     3.537300  \n", "max     198.537100   115.635000  "]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["well.describe()"]}, {"cell_type": "code", "execution_count": 41, "id": "9aef0a2e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Index: 6701 entries, 3615.434 to 4636.514\n", "Data columns (total 7 columns):\n", " #   Column  Non-Null Count  Dtype  \n", "---  ------  --------------  -----  \n", " 0   AC      6579 non-null   float64\n", " 1   CALI    6579 non-null   float64\n", " 2   DEN     6656 non-null   float64\n", " 3   GR      6689 non-null   float64\n", " 4   NEU     6668 non-null   float64\n", " 5   RDEP    6701 non-null   float64\n", " 6   RMED    6701 non-null   float64\n", "dtypes: float64(7)\n", "memory usage: 418.8 KB\n"]}], "source": ["well.info()"]}, {"cell_type": "code", "execution_count": 42, "id": "10233a90", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='GR'>"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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***************************************************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df.plot('GR', 'DEPTH')"]}, {"cell_type": "code", "execution_count": 43, "id": "f859a84b", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([<Axes: >, <Axes: >, <Axes: >, <Axes: >, <Axes: >, <Axes: >,\n", "       <Axes: >, <Axes: >], dtype=object)"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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*****************************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", "text/plain": ["<Figure size 1000x1000 with 8 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df.plot(subplots=True, figsize=(10,10))"]}, {"cell_type": "code", "execution_count": 44, "id": "e9612588", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='DEPT'>"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["well.plot(y='GR')"]}, {"cell_type": "code", "execution_count": 45, "id": "9523c834", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'RESD'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3805\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3804\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 3805\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_engine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcasted_key\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3806\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON>Erro<PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32mindex.pyx:167\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mindex.pyx:196\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7081\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7089\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "\u001b[1;31m<PERSON><PERSON>Error\u001b[0m: 'RESD'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[45], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mwell\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mplot\u001b[49m\u001b[43m(\u001b[49m\u001b[43my\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mRESD\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\plotting\\_core.py:1016\u001b[0m, in \u001b[0;36mPlotAccessor.__call__\u001b[1;34m(self, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1013\u001b[0m             \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[0;32m   1015\u001b[0m \u001b[38;5;66;03m# don't overwrite\u001b[39;00m\n\u001b[1;32m-> 1016\u001b[0m data \u001b[38;5;241m=\u001b[39m \u001b[43mdata\u001b[49m\u001b[43m[\u001b[49m\u001b[43my\u001b[49m\u001b[43m]\u001b[49m\u001b[38;5;241m.\u001b[39mcopy()\n\u001b[0;32m   1018\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(data, ABCSeries):\n\u001b[0;32m   1019\u001b[0m     label_name \u001b[38;5;241m=\u001b[39m label_kw \u001b[38;5;129;01mor\u001b[39;00m y\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\frame.py:4102\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   4100\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   4101\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_multilevel(key)\n\u001b[1;32m-> 4102\u001b[0m indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   4103\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[0;32m   4104\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m [indexer]\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3812\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3807\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(casted_key, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m (\n\u001b[0;32m   3808\u001b[0m         \u001b[38;5;28misinstance\u001b[39m(casted_key, abc\u001b[38;5;241m.\u001b[39mIterable)\n\u001b[0;32m   3809\u001b[0m         \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28many\u001b[39m(\u001b[38;5;28misinstance\u001b[39m(x, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m casted_key)\n\u001b[0;32m   3810\u001b[0m     ):\n\u001b[0;32m   3811\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidIndexError(key)\n\u001b[1;32m-> 3812\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01merr\u001b[39;00m\n\u001b[0;32m   3813\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[0;32m   3814\u001b[0m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[0;32m   3815\u001b[0m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[0;32m   3816\u001b[0m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[0;32m   3817\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_error(key)\n", "\u001b[1;31m<PERSON><PERSON>Error\u001b[0m: 'RESD'"]}], "source": ["well.plot(y='RESD')"]}, {"cell_type": "code", "execution_count": null, "id": "af82f406", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='DEPT'>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["well.plot(y='SP')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}