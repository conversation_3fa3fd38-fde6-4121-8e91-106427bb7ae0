"""
Results for test_glm.py.

Hard-coded from R or Stata.  Note that some of the remaining discrepancy vs.
Stata may be because Stata uses ML by default unless you specifically ask for
IRLS.
"""
import os

import numpy as np
import pandas as pd

from statsmodels.api import add_constant
from statsmodels.genmod.tests.results import glm_test_resids

# Test Precisions
DECIMAL_4 = 4
DECIMAL_3 = 3
DECIMAL_2 = 2
DECIMAL_1 = 1
DECIMAL_0 = 0


class Longley:
    """
    Longley used for TestGlmGaussian

    Results are from Stata and R.
    """
    def __init__(self):

        self.resids = np.array([
            [267.34002976,  267.34002976,  267.34002976,
             267.34002976, 267.34002976],
            [-94.0139424,   -94.0139424,   -94.0139424,   -94.0139424,
             -94.0139424],
            [46.28716776,   46.28716776,   46.28716776,   46.28716776,
             46.28716776],
            [-410.11462193, -410.11462193, -410.11462193, -410.11462193,
             -410.11462193],
            [309.71459076,  309.71459076,  309.71459076,  309.71459076,
             309.71459076],
            [-249.31121533, -249.31121533, -249.31121533, -249.31121533,
             -249.31121533],
            [-164.0489564,  -164.0489564,  -164.0489564,  -164.0489564,
             -164.0489564],
            [-13.18035687,  -13.18035687,  -13.18035687,  -13.18035687,
             -13.18035687],
            [14.3047726,    14.3047726,    14.3047726,    14.3047726,
             14.3047726],
            [455.39409455,  455.39409455,  455.39409455,  455.39409455,
             455.39409455],
            [-17.26892711,  -17.26892711,  -17.26892711,  -17.26892711,
             -17.26892711],
            [-39.05504252,  -39.05504252,  -39.05504252,  -39.05504252,
             -39.05504252],
            [-155.5499736,  -155.5499736,  -155.5499736,  -155.5499736,
             -155.5499736],
            [-85.67130804,  -85.67130804,  -85.67130804,  -85.67130804,
             -85.67130804],
            [341.93151396,  341.93151396,  341.93151396,  341.93151396,
             341.93151396],
            [-206.75782519, -206.75782519, -206.75782519, -206.75782519,
             -206.75782519]])
        self.null_deviance = 185008826  # taken from R.
        self.params = np.array([
            1.50618723e+01,  -3.58191793e-02,
            -2.02022980e+00, -1.03322687e+00,  -5.11041057e-02,
            1.82915146e+03, -3.48225863e+06])
        self.bse = np.array([
            8.49149258e+01,   3.34910078e-02, 4.88399682e-01,
            2.14274163e-01,   2.26073200e-01,
            4.55478499e+02, 8.90420384e+05])
        self.aic_R = 235.23486961695903  # R adds 2 for dof to AIC
        self.aic_Stata = 14.57717943930524  # stata divides by nobs
        self.deviance = 836424.0555058046   # from R
        self.scale = 92936.006167311629
        self.llf = -109.61743480847952
        self.null_deviance = 185008826  # taken from R. Rpy bug

        self.bic_Stata = 836399.1760177979  # no bic in R?
        self.df_model = 6
        self.df_resid = 9

        # TODO: taken from Stata; not available in sm yet
        self.chi2 = 1981.711859508729

        self.prsquared = 0.90
        self.prsquared_cox_snell = 1.00

        # self.pearson_chi2 = 836424.1293162981   # from Stata (?)
        self.fittedvalues = np.array([
            60055.659970240202, 61216.013942398131,
            60124.71283224225, 61597.114621930756, 62911.285409240052,
            63888.31121532945, 65153.048956395127, 63774.180356866214,
            66004.695227399934, 67401.605905447621,
            68186.268927114084,  66552.055042522494,
            68810.549973595422, 69649.67130804155, 68989.068486039061,
            70757.757825193927])


class GaussianLog:
    """
    Uses generated data.  These results are from R and Stata.
    """
    def __init__(self):
        self.resids = np.array([
            [3.20800000e-04, 3.20800000e-04,
             8.72100000e-04, 3.20800000e-04,   3.20800000e-04],
            [8.12100000e-04,   8.12100000e-04,   2.16350000e-03,
             8.12100000e-04,   8.12100000e-04],
            [-2.94800000e-04,  -2.94800000e-04,  -7.69700000e-04,
             -2.94800000e-04,  -2.94800000e-04],
            [1.40190000e-03,   1.40190000e-03,   3.58560000e-03,
             1.40190000e-03,   1.40190000e-03],
            [-2.30910000e-03,  -2.30910000e-03,  -5.78490000e-03,
             -2.30910000e-03,  -2.30910000e-03],
            [1.10380000e-03,   1.10380000e-03,   2.70820000e-03,
             1.10380000e-03,   1.10380000e-03],
            [-5.14000000e-06,  -5.14000000e-06,  -1.23000000e-05,
             -5.14000000e-06,  -5.14000000e-06],
            [-1.65500000e-04,  -1.65500000e-04,  -3.89200000e-04,
             -1.65500000e-04,  -1.65500000e-04],
            [-7.55400000e-04,  -7.55400000e-04,  -1.73870000e-03,
             -7.55400000e-04,  -7.55400000e-04],
            [-1.39800000e-04,  -1.39800000e-04,  -3.14800000e-04,
             -1.39800000e-04,  -1.39800000e-04],
            [-7.17000000e-04,  -7.17000000e-04,  -1.58000000e-03,
             -7.17000000e-04,  -7.17000000e-04],
            [-1.12200000e-04,  -1.12200000e-04,  -2.41900000e-04,
             -1.12200000e-04,  -1.12200000e-04],
            [3.22100000e-04,   3.22100000e-04,   6.79000000e-04,
             3.22100000e-04,   3.22100000e-04],
            [-3.78000000e-05,  -3.78000000e-05,  -7.79000000e-05,
             -3.78000000e-05,  -3.78000000e-05],
            [5.54500000e-04,   5.54500000e-04,   1.11730000e-03,
             5.54500000e-04,   5.54500000e-04],
            [3.38400000e-04,   3.38400000e-04,   6.66300000e-04,
             3.38400000e-04,   3.38400000e-04],
            [9.72000000e-05,   9.72000000e-05,   1.87000000e-04,
             9.72000000e-05,   9.72000000e-05],
            [-7.92900000e-04,  -7.92900000e-04,  -1.49070000e-03,
             -7.92900000e-04,  -7.92900000e-04],
            [3.33000000e-04,   3.33000000e-04,   6.11500000e-04,
             3.33000000e-04,   3.33000000e-04],
            [-8.35300000e-04,  -8.35300000e-04,  -1.49790000e-03,
             -8.35300000e-04,  -8.35300000e-04],
            [-3.99700000e-04,  -3.99700000e-04,  -6.99800000e-04,
             -3.99700000e-04,  -3.99700000e-04],
            [1.41300000e-04,   1.41300000e-04,   2.41500000e-04,
             1.41300000e-04,   1.41300000e-04],
            [-8.50700000e-04,  -8.50700000e-04,  -1.41920000e-03,
             -8.50700000e-04,  -8.50700000e-04],
            [1.43000000e-06,   1.43000000e-06,   2.33000000e-06,
             1.43000000e-06,   1.43000000e-06],
            [-9.12000000e-05,  -9.12000000e-05,  -1.44900000e-04,
             -9.12000000e-05,  -9.12000000e-05],
            [6.75500000e-04,   6.75500000e-04,   1.04650000e-03,
             6.75500000e-04,   6.75500000e-04],
            [3.97900000e-04,   3.97900000e-04,   6.01100000e-04,
             3.97900000e-04,   3.97900000e-04],
            [1.07000000e-05,   1.07000000e-05,   1.57000000e-05,
             1.07000000e-05,   1.07000000e-05],
            [-8.15200000e-04,  -8.15200000e-04,  -1.17060000e-03,
             -8.15200000e-04,  -8.15200000e-04],
            [-8.46400000e-04,  -8.46400000e-04,  -1.18460000e-03,
             -8.46400000e-04,  -8.46400000e-04],
            [9.91200000e-04,   9.91200000e-04,   1.35180000e-03,
             9.91200000e-04,   9.91200000e-04],
            [-5.07400000e-04,  -5.07400000e-04,  -6.74200000e-04,
             -5.07400000e-04,  -5.07400000e-04],
            [1.08520000e-03,   1.08520000e-03,   1.40450000e-03,
             1.08520000e-03,   1.08520000e-03],
            [9.56100000e-04,   9.56100000e-04,   1.20500000e-03,
             9.56100000e-04,   9.56100000e-04],
            [1.87500000e-03,   1.87500000e-03,   2.30090000e-03,
             1.87500000e-03,   1.87500000e-03],
            [-1.93920000e-03,  -1.93920000e-03,  -2.31650000e-03,
             -1.93920000e-03,  -1.93920000e-03],
            [8.16000000e-04,   8.16000000e-04,   9.48700000e-04,
             8.16000000e-04,   8.16000000e-04],
            [1.01520000e-03,   1.01520000e-03,   1.14860000e-03,
             1.01520000e-03,   1.01520000e-03],
            [1.04150000e-03,   1.04150000e-03,   1.14640000e-03,
             1.04150000e-03,   1.04150000e-03],
            [-3.88200000e-04,  -3.88200000e-04,  -4.15600000e-04,
             -3.88200000e-04,  -3.88200000e-04],
            [9.95900000e-04,   9.95900000e-04,   1.03690000e-03,
             9.95900000e-04,   9.95900000e-04],
            [-6.82800000e-04,  -6.82800000e-04,  -6.91200000e-04,
             -6.82800000e-04,  -6.82800000e-04],
            [-8.11400000e-04,  -8.11400000e-04,  -7.98500000e-04,
             -8.11400000e-04,  -8.11400000e-04],
            [-1.79050000e-03,  -1.79050000e-03,  -1.71250000e-03,
             -1.79050000e-03,  -1.79050000e-03],
            [6.10000000e-04,   6.10000000e-04,   5.66900000e-04,
             6.10000000e-04,   6.10000000e-04],
            [2.52600000e-04,   2.52600000e-04,   2.28100000e-04,
             2.52600000e-04,   2.52600000e-04],
            [-8.62500000e-04,  -8.62500000e-04,  -7.56400000e-04,
             -8.62500000e-04,  -8.62500000e-04],
            [-3.47300000e-04,  -3.47300000e-04,  -2.95800000e-04,
             -3.47300000e-04,  -3.47300000e-04],
            [-7.79000000e-05,  -7.79000000e-05,  -6.44000000e-05,
             -7.79000000e-05,  -7.79000000e-05],
            [6.72000000e-04,   6.72000000e-04,   5.39400000e-04,
             6.72000000e-04,   6.72000000e-04],
            [-3.72100000e-04,  -3.72100000e-04,  -2.89900000e-04,
             -3.72100000e-04,  -3.72100000e-04],
            [-1.22900000e-04,  -1.22900000e-04,  -9.29000000e-05,
             -1.22900000e-04,  -1.22900000e-04],
            [-1.63470000e-03,  -1.63470000e-03,  -1.19900000e-03,
             -1.63470000e-03,  -1.63470000e-03],
            [2.64400000e-04,   2.64400000e-04,   1.88100000e-04,
             2.64400000e-04,   2.64400000e-04],
            [1.79230000e-03,   1.79230000e-03,   1.23650000e-03,
             1.79230000e-03,   1.79230000e-03],
            [-1.40500000e-04,  -1.40500000e-04,  -9.40000000e-05,
             -1.40500000e-04,  -1.40500000e-04],
            [-2.98500000e-04,  -2.98500000e-04,  -1.93600000e-04,
             -2.98500000e-04,  -2.98500000e-04],
            [-9.33100000e-04,  -9.33100000e-04,  -5.86400000e-04,
             -9.33100000e-04,  -9.33100000e-04],
            [9.11200000e-04,   9.11200000e-04,   5.54900000e-04,
             9.11200000e-04,   9.11200000e-04],
            [-1.31840000e-03,  -1.31840000e-03,  -7.77900000e-04,
             -1.31840000e-03,  -1.31840000e-03],
            [-1.30200000e-04,  -1.30200000e-04,  -7.44000000e-05,
             -1.30200000e-04,  -1.30200000e-04],
            [9.09300000e-04,   9.09300000e-04,   5.03200000e-04,
             9.09300000e-04,   9.09300000e-04],
            [-2.39500000e-04,  -2.39500000e-04,  -1.28300000e-04,
             -2.39500000e-04,  -2.39500000e-04],
            [7.15300000e-04,   7.15300000e-04,   3.71000000e-04,
             7.15300000e-04,   7.15300000e-04],
            [5.45000000e-05,   5.45000000e-05,   2.73000000e-05,
             5.45000000e-05,   5.45000000e-05],
            [2.85310000e-03,   2.85310000e-03,   1.38600000e-03,
             2.85310000e-03,   2.85310000e-03],
            [4.63400000e-04,   4.63400000e-04,   2.17800000e-04,
             4.63400000e-04,   4.63400000e-04],
            [2.80900000e-04,   2.80900000e-04,   1.27700000e-04,
             2.80900000e-04,   2.80900000e-04],
            [5.42000000e-05,   5.42000000e-05,   2.38000000e-05,
             5.42000000e-05,   5.42000000e-05],
            [-3.62300000e-04,  -3.62300000e-04,  -1.54000000e-04,
             -3.62300000e-04,  -3.62300000e-04],
            [-1.11900000e-03,  -1.11900000e-03,  -4.59800000e-04,
             -1.11900000e-03,  -1.11900000e-03],
            [1.28900000e-03,   1.28900000e-03,   5.11900000e-04,
             1.28900000e-03,   1.28900000e-03],
            [-1.40820000e-03,  -1.40820000e-03,  -5.40400000e-04,
             -1.40820000e-03,  -1.40820000e-03],
            [-1.69300000e-04,  -1.69300000e-04,  -6.28000000e-05,
             -1.69300000e-04,  -1.69300000e-04],
            [-1.03620000e-03,  -1.03620000e-03,  -3.71000000e-04,
             -1.03620000e-03,  -1.03620000e-03],
            [1.49150000e-03,   1.49150000e-03,   5.15800000e-04,
             1.49150000e-03,   1.49150000e-03],
            [-7.22000000e-05,  -7.22000000e-05,  -2.41000000e-05,
             -7.22000000e-05,  -7.22000000e-05],
            [5.49000000e-04,   5.49000000e-04,   1.76900000e-04,
             5.49000000e-04,   5.49000000e-04],
            [-2.12320000e-03,  -2.12320000e-03,  -6.60400000e-04,
             -2.12320000e-03,  -2.12320000e-03],
            [7.84000000e-06,   7.84000000e-06,   2.35000000e-06,
             7.84000000e-06,   7.84000000e-06],
            [1.15580000e-03,   1.15580000e-03,   3.34700000e-04,
             1.15580000e-03,   1.15580000e-03],
            [4.83400000e-04,   4.83400000e-04,   1.35000000e-04,
             4.83400000e-04,   4.83400000e-04],
            [-5.26100000e-04,  -5.26100000e-04,  -1.41700000e-04,
             -5.26100000e-04,  -5.26100000e-04],
            [-1.75100000e-04,  -1.75100000e-04,  -4.55000000e-05,
             -1.75100000e-04,  -1.75100000e-04],
            [-1.84600000e-03,  -1.84600000e-03,  -4.62100000e-04,
             -1.84600000e-03,  -1.84600000e-03],
            [2.07200000e-04,   2.07200000e-04,   5.00000000e-05,
             2.07200000e-04,   2.07200000e-04],
            [-8.54700000e-04,  -8.54700000e-04,  -1.98700000e-04,
             -8.54700000e-04,  -8.54700000e-04],
            [-9.20000000e-05,  -9.20000000e-05,  -2.06000000e-05,
             -9.20000000e-05,  -9.20000000e-05],
            [5.35700000e-04,   5.35700000e-04,   1.15600000e-04,
             5.35700000e-04,   5.35700000e-04],
            [-7.67300000e-04,  -7.67300000e-04,  -1.59400000e-04,
             -7.67300000e-04,  -7.67300000e-04],
            [-1.79710000e-03,  -1.79710000e-03,  -3.59500000e-04,
             -1.79710000e-03,  -1.79710000e-03],
            [1.10910000e-03,   1.10910000e-03,   2.13500000e-04,
             1.10910000e-03,   1.10910000e-03],
            [-5.53800000e-04,  -5.53800000e-04,  -1.02600000e-04,
             -5.53800000e-04,  -5.53800000e-04],
            [7.48000000e-04,   7.48000000e-04,   1.33400000e-04,
             7.48000000e-04,   7.48000000e-04],
            [4.23000000e-04,   4.23000000e-04,   7.26000000e-05,
             4.23000000e-04,   4.23000000e-04],
            [-3.16400000e-04,  -3.16400000e-04,  -5.22000000e-05,
             -3.16400000e-04,  -3.16400000e-04],
            [-6.63200000e-04,  -6.63200000e-04,  -1.05200000e-04,
             -6.63200000e-04,  -6.63200000e-04],
            [1.33540000e-03,   1.33540000e-03,   2.03700000e-04,
             1.33540000e-03,   1.33540000e-03],
            [-7.81200000e-04,  -7.81200000e-04,  -1.14600000e-04,
             -7.81200000e-04,  -7.81200000e-04],
            [1.67880000e-03,   1.67880000e-03,   2.36600000e-04,
             1.67880000e-03,   1.67880000e-03]])

        self.null_deviance = 56.691617808182208
        self.params = np.array([
            9.99964386e-01, -1.99896965e-02, -1.00027232e-04])
        self.bse = np.array([1.42119293e-04, 1.20276468e-05, 1.87347682e-07])
        self.aic_R = -1103.8187213072656  # adds 2 for dof for scale

        self.aic_Stata = -11.05818072104212  # divides by nobs for e(aic)
        self.deviance = 8.68876986288542e-05
        self.scale = 8.9574946938163984e-07  # from R but e(phi) in Stata
        self.llf = 555.9093606536328
        self.bic_Stata = -446.7014211525822
        self.df_model = 2
        self.df_resid = 97
        self.chi2 = 33207648.86501769   # from Stata not in sm
        self.fittedvalues = np.array([
            2.7181850213327747,  2.664122305869506,
            2.6106125414084405, 2.5576658143523567, 2.5052916730829535,
            2.4534991313100165, 2.4022966718815781, 2.3516922510411282,
            2.3016933031175575, 2.2523067456332542, 2.2035389848154616,
            2.1553959214958001, 2.107882957382607, 2.0610050016905817,
            2.0147664781120667, 1.969171332114154, 1.9242230385457144,
            1.8799246095383746, 1.8362786026854092, 1.7932871294825108,
            1.7509518640143886, 1.7092740518711942, 1.6682545192788105,
            1.6278936824271399, 1.5881915569806042, 1.5491477677552221,
            1.5107615585467538, 1.4730318020945796, 1.4359570101661721,
            1.3995353437472129, 1.3637646233226499, 1.3286423392342188,
            1.2941656621002184, 1.2603314532836074, 1.2271362753947765,
            1.1945764028156565, 1.162647832232141, 1.1313462931621328,
            1.1006672584668622, 1.0706059548334832, 1.0411573732173065,
            1.0123162792324054, 0.98407722347970683, 0.95643455180206194,
            0.92938241545618494, 0.90291478119174029, 0.87702544122826565,
            0.85170802312101246, 0.82695599950720078, 0.80276269772458597,
            0.77912130929465073, 0.75602489926313921, 0.73346641539106316,
            0.71143869718971686, 0.68993448479364294, 0.66894642766589496,
            0.64846709313034534, 0.62848897472617915, 0.60900450038011367,
            0.5900060403922629, 0.57148591523195513, 0.55343640314018494,
            0.5358497475357491, 0.51871816422248385, 0.50203384839536769,
            0.48578898144361343, 0.46997573754920047, 0.45458629007964013,
            0.4396128177740814, 0.42504751072218311, 0.41088257613548018,
            0.39711024391126759, 0.38372277198930843, 0.37071245150195081,
            0.35807161171849949, 0.34579262478494655, 0.33386791026040569,
            0.32228993945183393, 0.31105123954884056, 0.30014439756060574,
            0.28956206405712448, 0.27929695671718968, 0.26934186368570684,
            0.25968964674310463, 0.25033324428976694, 0.24126567414856051,
            0.23248003618867552, 0.22396951477412205, 0.21572738104035141,
            0.20774699500257574, 0.20002180749946474, 0.19254536197598673,
            0.18531129610924435, 0.17831334328122878, 0.17154533390247831,
            0.16500119659068577, 0.15867495920834204, 0.15256074976354628,
            0.14665279717814039, 0.14094543192735109])


class GaussianInverse:
    """
    This test uses generated data.  Results are from R and Stata.
    """
    def __init__(self):
        self.resids = np.array([
            [-5.15300000e-04, -5.15300000e-04,
             5.14800000e-04, -5.15300000e-04,  -5.15300000e-04],
            [-2.12500000e-04,  -2.12500000e-04,   2.03700000e-04,
             -2.12500000e-04,  -2.12500000e-04],
            [-1.71400000e-04,  -1.71400000e-04,   1.57200000e-04,
             -1.71400000e-04,  -1.71400000e-04],
            [1.94020000e-03,   1.94020000e-03,  -1.69710000e-03,
             1.94020000e-03,   1.94020000e-03],
            [-6.81100000e-04,  -6.81100000e-04,   5.66900000e-04,
             -6.81100000e-04,  -6.81100000e-04],
            [1.21370000e-03,   1.21370000e-03,  -9.58800000e-04,
             1.21370000e-03,   1.21370000e-03],
            [-1.51090000e-03,  -1.51090000e-03,   1.13070000e-03,
             -1.51090000e-03,  -1.51090000e-03],
            [3.21500000e-04,   3.21500000e-04,  -2.27400000e-04,
             3.21500000e-04,   3.21500000e-04],
            [-3.18500000e-04,  -3.18500000e-04,   2.12600000e-04,
             -3.18500000e-04,  -3.18500000e-04],
            [3.75600000e-04,   3.75600000e-04,  -2.36300000e-04,
             3.75600000e-04,   3.75600000e-04],
            [4.82300000e-04,   4.82300000e-04,  -2.85500000e-04,
             4.82300000e-04,   4.82300000e-04],
            [-1.41870000e-03,  -1.41870000e-03,   7.89300000e-04,
             -1.41870000e-03,  -1.41870000e-03],
            [6.75000000e-05,   6.75000000e-05,  -3.52000000e-05,
             6.75000000e-05,   6.75000000e-05],
            [4.06300000e-04,   4.06300000e-04,  -1.99100000e-04,
             4.06300000e-04,   4.06300000e-04],
            [-3.61500000e-04,  -3.61500000e-04,   1.66000000e-04,
             -3.61500000e-04,  -3.61500000e-04],
            [-2.97400000e-04,  -2.97400000e-04,   1.28000000e-04,
             -2.97400000e-04,  -2.97400000e-04],
            [-9.32700000e-04,  -9.32700000e-04,   3.75800000e-04,
             -9.32700000e-04,  -9.32700000e-04],
            [1.16270000e-03,   1.16270000e-03,  -4.38500000e-04,
             1.16270000e-03,   1.16270000e-03],
            [6.77900000e-04,   6.77900000e-04,  -2.39200000e-04,
             6.77900000e-04,   6.77900000e-04],
            [-1.29330000e-03,  -1.29330000e-03,   4.27000000e-04,
             -1.29330000e-03,  -1.29330000e-03],
            [2.24500000e-04,   2.24500000e-04,  -6.94000000e-05,
             2.24500000e-04,   2.24500000e-04],
            [1.05510000e-03,   1.05510000e-03,  -3.04900000e-04,
             1.05510000e-03,   1.05510000e-03],
            [2.50400000e-04,   2.50400000e-04,  -6.77000000e-05,
             2.50400000e-04,   2.50400000e-04],
            [4.08600000e-04,   4.08600000e-04,  -1.03400000e-04,
             4.08600000e-04,   4.08600000e-04],
            [-1.67610000e-03,  -1.67610000e-03,   3.96800000e-04,
             -1.67610000e-03,  -1.67610000e-03],
            [7.47600000e-04,   7.47600000e-04,  -1.65700000e-04,
             7.47600000e-04,   7.47600000e-04],
            [2.08200000e-04,   2.08200000e-04,  -4.32000000e-05,
             2.08200000e-04,   2.08200000e-04],
            [-8.00800000e-04,  -8.00800000e-04,   1.55700000e-04,
             -8.00800000e-04,  -8.00800000e-04],
            [5.81200000e-04,   5.81200000e-04,  -1.05900000e-04,
             5.81200000e-04,   5.81200000e-04],
            [1.00980000e-03,   1.00980000e-03,  -1.72400000e-04,
             1.00980000e-03,   1.00980000e-03],
            [2.77400000e-04,   2.77400000e-04,  -4.44000000e-05,
             2.77400000e-04,   2.77400000e-04],
            [-5.02800000e-04,  -5.02800000e-04,   7.55000000e-05,
             -5.02800000e-04,  -5.02800000e-04],
            [2.69800000e-04,   2.69800000e-04,  -3.80000000e-05,
             2.69800000e-04,   2.69800000e-04],
            [2.01300000e-04,   2.01300000e-04,  -2.67000000e-05,
             2.01300000e-04,   2.01300000e-04],
            [-1.19690000e-03,  -1.19690000e-03,   1.48900000e-04,
             -1.19690000e-03,  -1.19690000e-03],
            [-6.94200000e-04,  -6.94200000e-04,   8.12000000e-05,
             -6.94200000e-04,  -6.94200000e-04],
            [5.65500000e-04,   5.65500000e-04,  -6.22000000e-05,
             5.65500000e-04,   5.65500000e-04],
            [4.93100000e-04,   4.93100000e-04,  -5.10000000e-05,
             4.93100000e-04,   4.93100000e-04],
            [3.25000000e-04,   3.25000000e-04,  -3.17000000e-05,
             3.25000000e-04,   3.25000000e-04],
            [-7.70200000e-04,  -7.70200000e-04,   7.07000000e-05,
             -7.70200000e-04,  -7.70200000e-04],
            [2.58000000e-05,   2.58000000e-05,  -2.23000000e-06,
             2.58000000e-05,   2.58000000e-05],
            [-1.52800000e-04,  -1.52800000e-04,   1.25000000e-05,
             -1.52800000e-04,  -1.52800000e-04],
            [4.52000000e-05,   4.52000000e-05,  -3.48000000e-06,
             4.52000000e-05,   4.52000000e-05],
            [-6.83900000e-04,  -6.83900000e-04,   4.97000000e-05,
             -6.83900000e-04,  -6.83900000e-04],
            [-7.77600000e-04,  -7.77600000e-04,   5.34000000e-05,
             -7.77600000e-04,  -7.77600000e-04],
            [1.03170000e-03,   1.03170000e-03,  -6.70000000e-05,
             1.03170000e-03,   1.03170000e-03],
            [1.20000000e-03,   1.20000000e-03,  -7.37000000e-05,
             1.20000000e-03,   1.20000000e-03],
            [-7.71600000e-04,  -7.71600000e-04,   4.48000000e-05,
             -7.71600000e-04,  -7.71600000e-04],
            [-3.37000000e-04,  -3.37000000e-04,   1.85000000e-05,
             -3.37000000e-04,  -3.37000000e-04],
            [1.19880000e-03,   1.19880000e-03,  -6.25000000e-05,
             1.19880000e-03,   1.19880000e-03],
            [-1.54610000e-03,  -1.54610000e-03,   7.64000000e-05,
             -1.54610000e-03,  -1.54610000e-03],
            [9.11600000e-04,   9.11600000e-04,  -4.27000000e-05,
             9.11600000e-04,   9.11600000e-04],
            [-4.70800000e-04,  -4.70800000e-04,   2.09000000e-05,
             -4.70800000e-04,  -4.70800000e-04],
            [-1.21550000e-03,  -1.21550000e-03,   5.13000000e-05,
             -1.21550000e-03,  -1.21550000e-03],
            [1.09160000e-03,   1.09160000e-03,  -4.37000000e-05,
             1.09160000e-03,   1.09160000e-03],
            [-2.72000000e-04,  -2.72000000e-04,   1.04000000e-05,
             -2.72000000e-04,  -2.72000000e-04],
            [-7.84500000e-04,  -7.84500000e-04,   2.84000000e-05,
             -7.84500000e-04,  -7.84500000e-04],
            [1.53330000e-03,   1.53330000e-03,  -5.28000000e-05,
             1.53330000e-03,   1.53330000e-03],
            [-1.84450000e-03,  -1.84450000e-03,   6.05000000e-05,
             -1.84450000e-03,  -1.84450000e-03],
            [1.68550000e-03,   1.68550000e-03,  -5.26000000e-05,
             1.68550000e-03,   1.68550000e-03],
            [-3.06100000e-04,  -3.06100000e-04,   9.10000000e-06,
             -3.06100000e-04,  -3.06100000e-04],
            [1.00950000e-03,   1.00950000e-03,  -2.86000000e-05,
             1.00950000e-03,   1.00950000e-03],
            [5.22000000e-04,   5.22000000e-04,  -1.41000000e-05,
             5.22000000e-04,   5.22000000e-04],
            [-2.18000000e-05,  -2.18000000e-05,   5.62000000e-07,
             -2.18000000e-05,  -2.18000000e-05],
            [-7.80600000e-04,  -7.80600000e-04,   1.92000000e-05,
             -7.80600000e-04,  -7.80600000e-04],
            [6.81400000e-04,   6.81400000e-04,  -1.60000000e-05,
             6.81400000e-04,   6.81400000e-04],
            [-1.43800000e-04,  -1.43800000e-04,   3.23000000e-06,
             -1.43800000e-04,  -1.43800000e-04],
            [7.76000000e-04,   7.76000000e-04,  -1.66000000e-05,
             7.76000000e-04,   7.76000000e-04],
            [2.54900000e-04,   2.54900000e-04,  -5.22000000e-06,
             2.54900000e-04,   2.54900000e-04],
            [5.77500000e-04,   5.77500000e-04,  -1.13000000e-05,
             5.77500000e-04,   5.77500000e-04],
            [7.58100000e-04,   7.58100000e-04,  -1.42000000e-05,
             7.58100000e-04,   7.58100000e-04],
            [-8.31000000e-04,  -8.31000000e-04,   1.49000000e-05,
             -8.31000000e-04,  -8.31000000e-04],
            [-2.10340000e-03,  -2.10340000e-03,   3.62000000e-05,
             -2.10340000e-03,  -2.10340000e-03],
            [-8.89900000e-04,  -8.89900000e-04,   1.47000000e-05,
             -8.89900000e-04,  -8.89900000e-04],
            [1.08570000e-03,   1.08570000e-03,  -1.71000000e-05,
             1.08570000e-03,   1.08570000e-03],
            [-1.88600000e-04,  -1.88600000e-04,   2.86000000e-06,
             -1.88600000e-04,  -1.88600000e-04],
            [9.10000000e-05,   9.10000000e-05,  -1.32000000e-06,
             9.10000000e-05,   9.10000000e-05],
            [1.07700000e-03,   1.07700000e-03,  -1.50000000e-05,
             1.07700000e-03,   1.07700000e-03],
            [9.04100000e-04,   9.04100000e-04,  -1.21000000e-05,
             9.04100000e-04,   9.04100000e-04],
            [-2.20000000e-04,  -2.20000000e-04,   2.83000000e-06,
             -2.20000000e-04,  -2.20000000e-04],
            [-1.64030000e-03,  -1.64030000e-03,   2.02000000e-05,
             -1.64030000e-03,  -1.64030000e-03],
            [2.20600000e-04,   2.20600000e-04,  -2.62000000e-06,
             2.20600000e-04,   2.20600000e-04],
            [-2.78300000e-04,  -2.78300000e-04,   3.17000000e-06,
             -2.78300000e-04,  -2.78300000e-04],
            [-4.93000000e-04,  -4.93000000e-04,   5.40000000e-06,
             -4.93000000e-04,  -4.93000000e-04],
            [-1.85000000e-04,  -1.85000000e-04,   1.95000000e-06,
             -1.85000000e-04,  -1.85000000e-04],
            [-7.64000000e-04,  -7.64000000e-04,   7.75000000e-06,
             -7.64000000e-04,  -7.64000000e-04],
            [7.79600000e-04,   7.79600000e-04,  -7.61000000e-06,
             7.79600000e-04,   7.79600000e-04],
            [2.88400000e-04,   2.88400000e-04,  -2.71000000e-06,
             2.88400000e-04,   2.88400000e-04],
            [1.09370000e-03,   1.09370000e-03,  -9.91000000e-06,
             1.09370000e-03,   1.09370000e-03],
            [3.07000000e-04,   3.07000000e-04,  -2.68000000e-06,
             3.07000000e-04,   3.07000000e-04],
            [-8.76000000e-04,  -8.76000000e-04,   7.37000000e-06,
             -8.76000000e-04,  -8.76000000e-04],
            [-1.85300000e-04,  -1.85300000e-04,   1.50000000e-06,
             -1.85300000e-04,  -1.85300000e-04],
            [3.24700000e-04,   3.24700000e-04,  -2.54000000e-06,
             3.24700000e-04,   3.24700000e-04],
            [4.59600000e-04,   4.59600000e-04,  -3.47000000e-06,
             4.59600000e-04,   4.59600000e-04],
            [-2.73300000e-04,  -2.73300000e-04,   1.99000000e-06,
             -2.73300000e-04,  -2.73300000e-04],
            [1.32180000e-03,   1.32180000e-03,  -9.29000000e-06,
             1.32180000e-03,   1.32180000e-03],
            [-1.32620000e-03,  -1.32620000e-03,   9.00000000e-06,
             -1.32620000e-03,  -1.32620000e-03],
            [9.62000000e-05,   9.62000000e-05,  -6.31000000e-07,
             9.62000000e-05,   9.62000000e-05],
            [-6.04400000e-04,  -6.04400000e-04,   3.83000000e-06,
             -6.04400000e-04,  -6.04400000e-04],
            [-6.66300000e-04,  -6.66300000e-04,   4.08000000e-06,
             -6.66300000e-04,  -6.66300000e-04]])
        self.null_deviance = 6.8088354977561  # from R, Rpy bug
        self.params = np.array([1.00045997,  0.01991666,  0.00100126])
        self.bse = np.array([4.55214070e-04, 7.00529313e-05, 1.84478509e-06])
        self.aic_R = -1123.1528237643774
        self.aic_Stata = -11.25152876811373
        self.deviance = 7.1612915365488368e-05
        self.scale = 7.3827747608449547e-07
        self.llf = 565.57641188218872
        self.bic_Stata = -446.7014364279675
        self.df_model = 2
        self.df_resid = 97
        self.chi2 = 2704006.698904491
        self.fittedvalues = np.array([
            0.99954024,  0.97906956,  0.95758077, 0.93526008,  0.91228657,
            0.88882978,  0.8650479,   0.84108646,  0.81707757,  0.79313958,
            0.76937709,  0.74588129,  0.72273051,  0.69999099,  0.67771773,
            0.65595543,  0.63473944,  0.61409675,  0.59404691,  0.57460297,
            0.55577231,  0.53755742,  0.51995663,  0.50296478,  0.48657379,
            0.47077316,  0.4555505,   0.44089187,  0.42678213,  0.41320529,
            0.40014475,  0.38758348,  0.37550428,  0.36388987,  0.35272306,
            0.34198684,  0.33166446,  0.32173953,  0.31219604,  0.30301842,
            0.29419156,  0.28570085,  0.27753216,  0.26967189,  0.26210695,
            0.25482476,  0.24781324,  0.2410608,   0.23455636,  0.22828931,
            0.22224947,  0.21642715,  0.21081306,  0.20539835,  0.20017455,
            0.19513359,  0.19026777,  0.18556972,  0.18103243,  0.17664922,
            0.1724137,   0.16831977,  0.16436164,  0.16053377,  0.15683086,
            0.15324789,  0.14978003,  0.1464227,   0.14317153,  0.14002232,
            0.13697109,  0.13401403,  0.1311475,   0.12836802,  0.12567228,
            0.1230571,   0.12051944,  0.11805642,  0.11566526,  0.1133433,
            0.11108802,  0.10889699,  0.10676788,  0.10469847,  0.10268664,
            0.10073034,  0.09882763,  0.09697663,  0.09517555,  0.09342267,
            0.09171634,  0.09005498,  0.08843707,  0.08686116,  0.08532585,
            0.08382979,  0.0823717,   0.08095035,  0.07956453,  0.07821311])


class Star98:
    """
    Star98 class used with TestGlmBinomial
    """
    def __init__(self):
        self.params = (
            -0.0168150366,  0.0099254766, -0.0187242148,
            -0.0142385609, 0.2544871730,  0.2406936644,  0.0804086739,
            -1.9521605027, -0.3340864748, -0.1690221685,  0.0049167021,
            -0.0035799644, -0.0140765648, -0.0040049918, -0.0039063958,
            0.0917143006,  0.0489898381,  0.0080407389,  0.0002220095,
            -0.0022492486, 2.9588779262)
        self.bse = (
            4.339467e-04, 6.013714e-04, 7.435499e-04, 4.338655e-04,
            2.994576e-02, 5.713824e-02, 1.392359e-02, 3.168109e-01,
            6.126411e-02, 3.270139e-02, 1.253877e-03, 2.254633e-04,
            1.904573e-03, 4.739838e-04, 9.623650e-04, 1.450923e-02,
            7.451666e-03, 1.499497e-03, 2.988794e-05, 3.489838e-04,
            1.546712e+00)
        self.null_deviance = 34345.3688931
        self.df_null = 302
        self.deviance = 4078.76541772
        self.df_resid = 282
        self.df_model = 20
        self.aic_R = 6039.22511799
        self.aic_Stata = 19.93143846737438
        self.bic_Stata = 2467.493504191302
        self.llf = -2998.61255899391    # from R
        self.llf_Stata = -2998.612927807218
        self.scale = 1.
        self.pearson_chi2 = 4051.921614
        self.prsquared = 0.8346
        self.prsquared_cox_snell = 1.0000
        self.resids = glm_test_resids.star98_resids
        self.fittedvalues = np.array([
            0.5833118,   0.75144661,  0.50058272, 0.68534524,  0.32251021,
            0.68693601,  0.33299827,  0.65624766,  0.49851481,  0.506736,
            0.23954874,  0.86631452,  0.46432936,  0.44171873,  0.66797935,
            0.73988491,  0.51966014,  0.42442446,  0.5649369,   0.59251634,
            0.34798337,  0.56415024,  0.49974355,  0.3565539,   0.20752309,
            0.18269097,  0.44932642,  0.48025128,  0.59965277,  0.58848671,
            0.36264203,  0.33333196,  0.74253352,  0.5081886,   0.53421878,
            0.56291445,  0.60205239,  0.29174423,  0.2954348,   0.32220414,
            0.47977903,  0.23687535,  0.11776464,  0.1557423,   0.27854799,
            0.22699533,  0.1819439,   0.32554433,  0.22681989,  0.15785389,
            0.15268609,  0.61094772,  0.20743222,  0.51649059,  0.46502006,
            0.41031788,  0.59523288,  0.65733285,  0.27835336,  0.2371213,
            0.25137045,  0.23953942,  0.27854519,  0.39652413,  0.27023163,
            0.61411863,  0.2212025,   0.42005842,  0.55940397,  0.35413774,
            0.45724563,  0.57399437,  0.2168918,   0.58308738,  0.17181104,
            0.49873249,  0.22832683,  0.14846056,  0.5028073,   0.24513863,
            0.48202096,  0.52823155,  0.5086262,   0.46295993,  0.57869402,
            0.78363217,  0.21144435,  0.2298366,   0.17954825,  0.32232586,
            0.8343015,   0.56217006,  0.47367315,  0.52535649,  0.60350746,
            0.43210701,  0.44712008,  0.35858239,  0.2521347,   0.19787004,
            0.63256553,  0.51386532,  0.64997027,  0.13402072,  0.81756174,
            0.74543642,  0.30825852,  0.23988707,  0.17273125,  0.27880599,
            0.17395893,  0.32052828,  0.80467697,  0.18726218,  0.23842081,
            0.19020381,  0.85835388,  0.58703615,  0.72415106,  0.64433695,
            0.68766653,  0.32923663,  0.16352185,  0.38868816,  0.44980444,
            0.74810044,  0.42973792,  0.53762581,  0.72714996,  0.61229484,
            0.30267667,  0.24713253,  0.65086008,  0.48957265,  0.54955545,
            0.5697156,   0.36406211,  0.48906545,  0.45919413,  0.4930565,
            0.39785555,  0.5078719,   0.30159626,  0.28524393,  0.34687707,
            0.22522042,  0.52947159,  0.29277287,  0.8585002,   0.60800389,
            0.75830521,  0.35648175,  0.69508796,  0.45518355,  0.21567675,
            0.39682985,  0.49042948,  0.47615798,  0.60588234,  0.62910299,
            0.46005639,  0.71755165,  0.48852156,  0.47940661,  0.60128813,
            0.16589699,  0.68512861,  0.46305199,  0.68832227,  0.7006721,
            0.56564937,  0.51753941,  0.54261733,  0.56072214,  0.34545715,
            0.30226104,  0.3572956,   0.40996287,  0.33517519,  0.36248407,
            0.33937041,  0.34140691,  0.2627528,   0.29955161,  0.38581683,
            0.24840026,  0.15414272,  0.40415991,  0.53936252,  0.52111887,
            0.28060168,  0.45600958,  0.51110589,  0.43757523,  0.46891953,
            0.39425249,  0.5834369,   0.55817308,  0.32051259,  0.43567448,
            0.34134195,  0.43016545,  0.4885413,   0.28478325,  0.2650776,
            0.46784606,  0.46265983,  0.42655938,  0.18972234,  0.60448491,
            0.211896,    0.37886032,  0.50727577,  0.39782309,  0.50427121,
            0.35882898,  0.39596807,  0.49160806,  0.35618002,  0.6819922,
            0.36871093,  0.43079679,  0.67985516,  0.41270595,  0.68952767,
            0.52587734,  0.32042126,  0.39120123,  0.56870985,  0.32962349,
            0.32168989,  0.54076251,  0.4592907,   0.48480182,  0.4408386,
            0.431178,    0.47078232,  0.55911605,  0.30331618,  0.50310393,
            0.65036038,  0.45078895,  0.62354291,  0.56435463,  0.50034281,
            0.52693538,  0.57217285,  0.49221472,  0.40707122,  0.44226533,
            0.3475959,   0.54746396,  0.86385832,  0.48402233,  0.54313657,
            0.61586824,  0.27097185,  0.69717808,  0.52156974,  0.50401189,
            0.56724181,  0.6577178,   0.42732047,  0.44808396,  0.65435634,
            0.54766225,  0.38160648,  0.49890847,  0.50879037,  0.5875452,
            0.45101593,  0.5709704,   0.3175516,   0.39813159,  0.28305688,
            0.40521062,  0.30120578,  0.26400428,  0.44205496,  0.40545798,
            0.39366599,  0.55288196,  0.14104184,  0.17550155,  0.1949095,
            0.40255144,  0.21016822,  0.09712017,  0.63151487,  0.25885514,
            0.57323748,  0.61836898,  0.43268601,  0.67008878,  0.75801989,
            0.50353406,  0.64222315,  0.29925757,  0.32592036,  0.39634977,
            0.39582747,  0.41037006,  0.34174944])


class Lbw:
    '''
    The LBW data can be found here

    https://www.stata-press.com/data/r9/rmain.html
    '''
    def __init__(self):
        # data set up for data not in datasets
        filename = os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                "stata_lbw_glm.csv")

        data = pd.read_csv(filename)
        dummies = pd.get_dummies(data.race, prefix="race", drop_first=False,
                                 dtype=float)
        data = pd.concat([data, dummies], axis=1)
        self.endog = data.low
        design = data[["age", "lwt", "race_black", "race_other", "smoke",
                       "ptl", "ht", "ui"]]
        self.exog = add_constant(design, prepend=False)
        # Results for Canonical Logit Link
        self.params = (
            -.02710031, -.01515082, 1.26264728,
            .86207916, .92334482, .54183656, 1.83251780,
            .75851348, .46122388)
        self.bse = (
            0.036449917, 0.006925765, 0.526405169,
            0.439146744, 0.400820976, 0.346246857, 0.691623875,
            0.459373871, 1.204574885)
        self.aic_R = 219.447991133
        self.aic_Stata = 1.161100482182551
        self.deviance = 201.4479911325021
        self.scale = 1
        self.llf = -100.7239955662511
        self.chi2 = 25.65329337867037  # from Stata not used by sm
        self.null_deviance = 234.671996193219
        self.bic_Stata = -742.0664715782335
        self.df_resid = 180
        self.df_model = 8
        self.df_null = 188
        self.pearson_chi2 = 182.023342493558
        self.resids = glm_test_resids.lbw_resids
        self.fittedvalues = np.array([
            0.31217507,  0.12793027,  0.32119762, 0.48442686,  0.50853393,
            0.24517662,  0.12755193,  0.33226988,  0.22013309,  0.26268069,
            0.34729955,  0.18782188,  0.75404181,  0.54723527,  0.35016393,
            0.35016393,  0.45824406,  0.25336683,  0.43087357,  0.23284101,
            0.20146616,  0.24315597,  0.02725586,  0.22207692,  0.39800383,
            0.05584178,  0.28403447,  0.06931188,  0.35371946,  0.3896279,
            0.3896279,   0.47812002,  0.60043853,  0.07144772,  0.29995988,
            0.17910031,  0.22773411,  0.22691015,  0.06221253,  0.2384528,
            0.32633864,  0.05131047,  0.2954536,   0.07364416,  0.57241299,
            0.57241299,  0.08272435,  0.23298882,  0.12658158,  0.58967487,
            0.46989562,  0.22455631,  0.2348285,   0.29571887,  0.28212464,
            0.31499013,  0.68340511,  0.14090647,  0.31448425,  0.28082972,
            0.28082972,  0.24918728,  0.27018297,  0.08175784,  0.64808999,
            0.38252574,  0.25550797,  0.09113411,  0.40736693,  0.32644055,
            0.54367425,  0.29606968,  0.47028421,  0.39972155,  0.25079125,
            0.09678472,  0.08807264,  0.27467837,  0.5675742,   0.045619,
            0.10719293,  0.04826292,  0.23934092,  0.24179618,  0.23802197,
            0.49196179,  0.31379451,  0.10605469,  0.04047396,  0.11620849,
            0.09937016,  0.21822964,  0.29770265,  0.83912829,  0.25079125,
            0.08548557,  0.06550308,  0.2046457,   0.2046457,   0.08110349,
            0.13519643,  0.47862055,  0.38891913,  0.1383964,   0.26176764,
            0.31594589,  0.11418612,  0.06324112,  0.28468594,  0.21663702,
            0.03827107,  0.27237604,  0.20246694,  0.19042999,  0.15019447,
            0.18759474,  0.12308435,  0.19700616,  0.11564002,  0.36595033,
            0.07765727,  0.14119063,  0.13584627,  0.11012759,  0.10102472,
            0.10002166,  0.07439288,  0.27919958,  0.12491598,  0.06774594,
            0.72513764,  0.17714986,  0.67373352,  0.80679436,  0.52908941,
            0.15695938,  0.49722003,  0.41970014,  0.62375224,  0.53695622,
            0.25474238,  0.79135707,  0.2503871,   0.25352337,  0.33474211,
            0.19308929,  0.24658944,  0.25495092,  0.30867144,  0.41240259,
            0.59412526,  0.16811226,  0.48282791,  0.36566756,  0.09279325,
            0.75337353,  0.57128885,  0.52974123,  0.44548504,  0.77748843,
            0.3224082,   0.40054277,  0.29522468,  0.19673553,  0.73781774,
            0.57680312,  0.44545573,  0.30242355,  0.38720223,  0.16632904,
            0.30804092,  0.56385194,  0.60012179,  0.48324821,  0.24636345,
            0.26153216,  0.2348285,   0.29023669,  0.41011454,  0.36472083,
            0.65922069,  0.30476903,  0.09986775,  0.70658332,  0.30713075,
            0.36096386,  0.54962701,  0.71996086,  0.6633756])


class Scotvote:
    """
    Scotvot class is used with TestGlmGamma.
    """
    def __init__(self):
        self.params = (
            4.961768e-05, 2.034423e-03, -7.181429e-05, 1.118520e-04,
            -1.467515e-07, -5.186831e-04, -2.42717498e-06, -1.776527e-02)
        self.bse = (
            1.621577e-05, 5.320802e-04, 2.711664e-05, 4.057691e-05,
            1.236569e-07, 2.402534e-04, 7.460253e-07, 1.147922e-02)
        self.null_deviance = 0.536072
        self.df_null = 31
        self.deviance = 0.087388516417
        self.df_resid = 24
        self.df_model = 7
        self.aic_R = 182.947045954721
        self.aic_Stata = 10.72212
        self.bic_Stata = -83.09027
        self.llf = -163.5539382  # from Stata, same as ours with scale = 1
        # self.llf = -82.47352  # Very close to ours as is
        self.scale = 0.003584283
        self.pearson_chi2 = .0860228056
        self.prsquared = 0.429
        self.prsquared_cox_snell = 0.97971
        self.resids = glm_test_resids.scotvote_resids
        self.fittedvalues = np.array([
            57.80431482,  53.2733447, 50.56347993, 58.33003783,
            70.46562169,  56.88801284,  66.81878401,  66.03410393,
            57.92937473,  63.23216907,  53.9914785,   61.28993391,
            64.81036393,  63.47546816,  60.69696114,  74.83508176,
            56.56991106,  72.01804172,  64.35676519,  52.02445881,
            64.24933079,  71.15070332,  45.73479688,  54.93318588,
            66.98031261,  52.02479973,  56.18413736,  58.12267471,
            67.37947398,  60.49162862,  73.82609217,  69.61515621])


class Cancer:
    '''
    The Cancer data can be found here

    https://www.stata-press.com/data/r10/rmain.html
    '''
    def __init__(self):
        filename = os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                "stata_cancer_glm.csv")
        data = pd.read_csv(filename)
        self.endog = data.studytime
        dummies = pd.get_dummies(pd.Series(data.drug, dtype="category"),
                                 drop_first=True)
        design = np.column_stack((data.age, dummies)).astype(float)
        self.exog = add_constant(design, prepend=False)


class CancerLog(Cancer):
    """
    CancerLog is used TestGlmGammaLog
    """
    def __init__(self):
        super().__init__()

        self.resids = np.array([
            [-8.52598100e-01, -1.45739100e+00, -3.92408100e+01,
             -1.41526900e+00,  -5.78417200e+00],
            [-8.23683800e-01,  -1.35040200e+00,  -2.64957500e+01,
             -1.31777000e+00,  -4.67162900e+00],
            [-7.30450400e-01,  -1.07754600e+00,  -4.02136400e+01,
             -1.06208800e+00,  -5.41978500e+00],
            [-7.04471600e-01,  -1.01441500e+00,  -7.25951500e+01,
             -1.00172900e+00,  -7.15130900e+00],
            [-5.28668000e-01,  -6.68617300e-01,  -3.80758100e+01,
             -6.65304600e-01,  -4.48658700e+00],
            [-2.28658500e-01,  -2.48859700e-01,  -6.14913600e+00,
             -2.48707200e-01,  -1.18577100e+00],
            [-1.93939400e-01,  -2.08119900e-01,  -7.46226500e+00,
             -2.08031700e-01,  -1.20300800e+00],
            [-3.55635700e-01,  -4.09525000e-01,  -2.14132500e+01,
             -4.08815100e-01,  -2.75958600e+00],
            [-5.73360000e-02,  -5.84700000e-02,  -4.12946200e+00,
             -5.84681000e-02,  -4.86586900e-01],
            [3.09828000e-02,   3.06685000e-02,   1.86551100e+00,
             3.06682000e-02,   2.40413800e-01],
            [-2.11924300e-01,  -2.29071300e-01,  -2.18386100e+01,
             -2.28953000e-01,  -2.15130900e+00],
            [-3.10989000e-01,  -3.50739300e-01,  -4.19249500e+01,
             -3.50300400e-01,  -3.61084500e+00],
            [-9.22250000e-03,  -9.25100000e-03,  -1.13679700e+00,
             -9.25100000e-03,  -1.02392100e-01],
            [2.39402500e-01,   2.22589700e-01,   1.88577300e+01,
             2.22493500e-01,   2.12475600e+00],
            [3.35166000e-02,   3.31493000e-02,   4.51842400e+00,
             3.31489000e-02,   3.89155400e-01],
            [8.49829400e-01,   6.85180200e-01,   3.57627500e+01,
             6.82689900e-01,   5.51291500e+00],
            [4.12934200e-01,   3.66785200e-01,   4.65392600e+01,
             3.66370400e-01,   4.38379500e+00],
            [4.64148400e-01,   4.07123200e-01,   6.25726500e+01,
             4.06561900e-01,   5.38915500e+00],
            [1.71104600e+00,   1.19474800e+00,   1.12676500e+02,
             1.18311900e+00,   1.38850500e+01],
            [1.26571800e+00,   9.46389000e-01,   1.30431000e+02,
             9.40244600e-01,   1.28486900e+01],
            [-3.48532600e-01,  -3.99988300e-01,  -2.95638100e+01,
             -3.99328600e-01,  -3.20997700e+00],
            [-4.04340300e-01,  -4.76960100e-01,  -4.10254300e+01,
             -4.75818000e-01,  -4.07286500e+00],
            [-4.92057900e-01,  -6.08818300e-01,  -9.34509600e+01,
             -6.06357200e-01,  -6.78109700e+00],
            [-4.02876400e-01,  -4.74878400e-01,  -9.15226200e+01,
             -4.73751900e-01,  -6.07225700e+00],
            [-5.15056700e-01,  -6.46013300e-01,  -2.19014600e+02,
             -6.43043500e-01,  -1.06209700e+01],
            [-8.70423000e-02,  -8.97043000e-02,  -1.26361400e+01,
             -8.96975000e-02,  -1.04875100e+00],
            [1.28362300e-01,   1.23247800e-01,   1.70383300e+01,
             1.23231000e-01,   1.47887800e+00],
            [-2.39271900e-01,  -2.61562100e-01,  -9.30283300e+01,
             -2.61384400e-01,  -4.71795100e+00],
            [7.37246500e-01,   6.08186000e-01,   6.25359600e+01,
             6.06409700e-01,   6.79002300e+00],
            [-3.64110000e-02,  -3.68626000e-02,  -1.41565300e+01,
             -3.68621000e-02,  -7.17951200e-01],
            [2.68833000e-01,   2.47933100e-01,   6.67934100e+01,
             2.47801000e-01,   4.23748400e+00],
            [5.96389600e-01,   5.07237700e-01,   1.13265500e+02,
             5.06180100e-01,   8.21890300e+00],
            [1.98218000e-02,   1.96923000e-02,   1.00820900e+01,
             1.96923000e-02,   4.47040700e-01],
            [7.74936000e-01,   6.34305300e-01,   2.51883900e+02,
             6.32303700e-01,   1.39711800e+01],
            [-7.63925100e-01,  -1.16591700e+00,  -4.93461700e+02,
             -1.14588000e+00,  -1.94156600e+01],
            [-6.23771700e-01,  -8.41174800e-01,  -4.40679600e+02,
             -8.34266300e-01,  -1.65796100e+01],
            [-1.63272900e-01,  -1.73115100e-01,  -6.73975900e+01,
             -1.73064800e-01,  -3.31725800e+00],
            [-4.28562500e-01,  -5.11932900e-01,  -4.73787800e+02,
             -5.10507400e-01,  -1.42494800e+01],
            [8.00693000e-02,   7.80269000e-02,   3.95353400e+01,
             7.80226000e-02,   1.77920500e+00],
            [-2.13674400e-01,  -2.31127400e-01,  -2.15987000e+02,
             -2.31005700e-01,  -6.79344600e+00],
            [-1.63544000e-02,  -1.64444000e-02,  -1.05642100e+01,
             -1.64444000e-02,  -4.15657600e-01],
            [2.04900500e-01,   1.92372100e-01,   1.10651300e+02,
             1.92309400e-01,   4.76156600e+00],
            [-1.94758900e-01,  -2.09067700e-01,  -2.35484100e+02,
             -2.08978200e-01,  -6.77219400e+00],
            [3.16727400e-01,   2.88367800e-01,   1.87065600e+02,
             2.88162100e-01,   7.69732400e+00],
            [6.24234900e-01,   5.27632500e-01,   2.57678500e+02,
             5.26448400e-01,   1.26827400e+01],
            [8.30241100e-01,   6.72002100e-01,   2.86513700e+02,
             6.69644800e-01,   1.54232100e+01],
            [6.55140000e-03,   6.53710000e-03,   7.92130700e+00,
             6.53710000e-03,   2.27805800e-01],
            [3.41595200e-01,   3.08985000e-01,   2.88667600e+02,
             3.08733300e-01,   9.93012900e+00]])
        self.null_deviance = 27.92207137420696  # From R (bug in rpy)
        self.params = np.array([
            -0.04477778, 0.57437126, 1.05210726, 4.64604002])
        self.bse = np.array([0.0147328,   0.19694727,  0.19772507, 0.83534671])

        self.aic_R = 331.89022395372069

        self.aic_Stata = 7.403608467857651
        self.deviance = 16.174635536991005
        self.scale = 0.31805268736385695

        # self.llf = -160.94511197686035  # From R
        self.llf = -173.6866032285836  # from Staa
        self.bic_Stata = -154.1582089453923  # from Stata
        self.df_model = 3
        self.df_resid = 44
        self.chi2 = 36.77821448266359  # from Stata not in sm

        self.fittedvalues = np.array([
            6.78419193, 5.67167253, 7.41979002, 10.15123371,
            8.48656317,   5.18582263,   6.20304079,   7.75958258,
            8.48656317,   7.75958258,  10.15123371,  11.61071755,
            11.10228357,   8.87520908,  11.61071755,   6.48711178,
            10.61611394,  11.61071755,   8.11493609,  10.15123371,
            9.21009116,  10.07296716,  13.78112366,  15.07225103,
            20.62079147,  12.04881666,  11.5211983,   19.71780584,
            9.21009116,  19.71780584,  15.76249142,  13.78112366,
            22.55271436,  18.02872842,  25.41575239,  26.579678,
            20.31745227,  33.24937131,  22.22095589,  31.79337946,
            25.41575239,  23.23857437,  34.77204095,  24.30279515,
            20.31745227,  18.57700761,  34.77204095,  29.06987768])


class CancerIdentity(Cancer):
    """
    CancerIdentity is used with TestGlmGammaIdentity
    """
    def __init__(self):
        super().__init__()

        self.resids = np.array([
            [-8.52598100e-01,  -1.45739100e+00, -3.92408100e+01,
             -1.41526900e+00,  -5.78417200e+00],
            [-8.23683800e-01,  -1.35040200e+00,  -2.64957500e+01,
             -1.31777000e+00,  -4.67162900e+00],
            [-7.30450400e-01,  -1.07754600e+00,  -4.02136400e+01,
             -1.06208800e+00,  -5.41978500e+00],
            [-7.04471600e-01,  -1.01441500e+00,  -7.25951500e+01,
             -1.00172900e+00,  -7.15130900e+00],
            [-5.28668000e-01,  -6.68617300e-01,  -3.80758100e+01,
             -6.65304600e-01,  -4.48658700e+00],
            [-2.28658500e-01,  -2.48859700e-01,  -6.14913600e+00,
             -2.48707200e-01,  -1.18577100e+00],
            [-1.93939400e-01,  -2.08119900e-01,  -7.46226500e+00,
             -2.08031700e-01,  -1.20300800e+00],
            [-3.55635700e-01,  -4.09525000e-01,  -2.14132500e+01,
             -4.08815100e-01,  -2.75958600e+00],
            [-5.73360000e-02,  -5.84700000e-02,  -4.12946200e+00,
             -5.84681000e-02,  -4.86586900e-01],
            [3.09828000e-02,   3.06685000e-02,   1.86551100e+00,
             3.06682000e-02,   2.40413800e-01],
            [-2.11924300e-01,  -2.29071300e-01,  -2.18386100e+01,
             -2.28953000e-01,  -2.15130900e+00],
            [-3.10989000e-01,  -3.50739300e-01,  -4.19249500e+01,
             -3.50300400e-01,  -3.61084500e+00],
            [-9.22250000e-03,  -9.25100000e-03,  -1.13679700e+00,
             -9.25100000e-03,  -1.02392100e-01],
            [2.39402500e-01,   2.22589700e-01,   1.88577300e+01,
             2.22493500e-01,   2.12475600e+00],
            [3.35166000e-02,   3.31493000e-02,   4.51842400e+00,
             3.31489000e-02,   3.89155400e-01],
            [8.49829400e-01,   6.85180200e-01,   3.57627500e+01,
             6.82689900e-01,   5.51291500e+00],
            [4.12934200e-01,   3.66785200e-01,   4.65392600e+01,
             3.66370400e-01,   4.38379500e+00],
            [4.64148400e-01,   4.07123200e-01,   6.25726500e+01,
             4.06561900e-01,   5.38915500e+00],
            [1.71104600e+00,   1.19474800e+00,   1.12676500e+02,
             1.18311900e+00,   1.38850500e+01],
            [1.26571800e+00,   9.46389000e-01,   1.30431000e+02,
             9.40244600e-01,   1.28486900e+01],
            [-3.48532600e-01,  -3.99988300e-01,  -2.95638100e+01,
             -3.99328600e-01,  -3.20997700e+00],
            [-4.04340300e-01,  -4.76960100e-01,  -4.10254300e+01,
             -4.75818000e-01,  -4.07286500e+00],
            [-4.92057900e-01,  -6.08818300e-01,  -9.34509600e+01,
             -6.06357200e-01,  -6.78109700e+00],
            [-4.02876400e-01,  -4.74878400e-01,  -9.15226200e+01,
             -4.73751900e-01,  -6.07225700e+00],
            [-5.15056700e-01,  -6.46013300e-01,  -2.19014600e+02,
             -6.43043500e-01,  -1.06209700e+01],
            [-8.70423000e-02,  -8.97043000e-02,  -1.26361400e+01,
             -8.96975000e-02,  -1.04875100e+00],
            [1.28362300e-01,   1.23247800e-01,   1.70383300e+01,
             1.23231000e-01,   1.47887800e+00],
            [-2.39271900e-01,  -2.61562100e-01,  -9.30283300e+01,
             -2.61384400e-01,  -4.71795100e+00],
            [7.37246500e-01,   6.08186000e-01,   6.25359600e+01,
             6.06409700e-01,   6.79002300e+00],
            [-3.64110000e-02,  -3.68626000e-02,  -1.41565300e+01,
             -3.68621000e-02,  -7.17951200e-01],
            [2.68833000e-01,   2.47933100e-01,   6.67934100e+01,
             2.47801000e-01,   4.23748400e+00],
            [5.96389600e-01,   5.07237700e-01,   1.13265500e+02,
             5.06180100e-01,   8.21890300e+00],
            [1.98218000e-02,   1.96923000e-02,   1.00820900e+01,
             1.96923000e-02,   4.47040700e-01],
            [7.74936000e-01,   6.34305300e-01,   2.51883900e+02,
             6.32303700e-01,   1.39711800e+01],
            [-7.63925100e-01,  -1.16591700e+00,  -4.93461700e+02,
             -1.14588000e+00,  -1.94156600e+01],
            [-6.23771700e-01,  -8.41174800e-01,  -4.40679600e+02,
             -8.34266300e-01,  -1.65796100e+01],
            [-1.63272900e-01,  -1.73115100e-01,  -6.73975900e+01,
             -1.73064800e-01,  -3.31725800e+00],
            [-4.28562500e-01,  -5.11932900e-01,  -4.73787800e+02,
             -5.10507400e-01,  -1.42494800e+01],
            [8.00693000e-02,   7.80269000e-02,   3.95353400e+01,
             7.80226000e-02,   1.77920500e+00],
            [-2.13674400e-01,  -2.31127400e-01,  -2.15987000e+02,
             -2.31005700e-01,  -6.79344600e+00],
            [-1.63544000e-02,  -1.64444000e-02,  -1.05642100e+01,
             -1.64444000e-02,  -4.15657600e-01],
            [2.04900500e-01,   1.92372100e-01,   1.10651300e+02,
             1.92309400e-01,   4.76156600e+00],
            [-1.94758900e-01,  -2.09067700e-01,  -2.35484100e+02,
             -2.08978200e-01,  -6.77219400e+00],
            [3.16727400e-01,   2.88367800e-01,   1.87065600e+02,
             2.88162100e-01,   7.69732400e+00],
            [6.24234900e-01,   5.27632500e-01,   2.57678500e+02,
             5.26448400e-01,   1.26827400e+01],
            [8.30241100e-01,   6.72002100e-01,   2.86513700e+02,
             6.69644800e-01,   1.54232100e+01],
            [6.55140000e-03,   6.53710000e-03,   7.92130700e+00,
             6.53710000e-03,   2.27805800e-01],
            [3.41595200e-01,   3.08985000e-01,   2.88667600e+02,
             3.08733300e-01,   9.93012900e+00]])

        self.params = np.array([
            -0.5369833, 6.47296332, 16.20336802, 38.96617431])
        self.bse = np.array([
            0.13341238,  2.1349966,   3.87411875,  8.19235553])

        self.aic_R = 328.39209118952965

        # TODO: the below will fail
        self.aic_Stata = 7.381090276021671
        self.deviance = 15.093762327607557
        self.scale = 0.29512089119443752
        self.null_deviance = 27.92207137420696  # from R bug in RPy
        # NOTE: our scale is Stata's dispers_p (pearson?)
        # TODO: if scale is analagous to Stata's dispersion, then this might be
        # where the discrepancies come from?
        # self.llf = -159.19604559476483  # From R
        self.llf = -173.1461666245201  # From Stata
        self.bic_Stata = -155.2390821535193
        self.df_model = 3
        self.df_resid = 44
        self.chi2 = 51.56632068622578
        self.fittedvalues = np.array([
            6.21019277,   4.06225956,
            7.28415938,  11.04304251,
            8.89510929,   2.98829295,   5.13622616,   7.82114268,
            8.89510929,   7.82114268,  11.04304251,  12.65399242,
            12.11700911,   9.43209259,  12.65399242,   5.67320947,
            11.58002581,  12.65399242,   8.35812599,  11.04304251,
            9.46125627,  10.53522287,  14.294106,    15.36807261,
            19.12695574,  12.68315609,  12.14617279,  18.58997243,
            9.46125627,  18.58997243,  15.90505591,  14.294106,
            20.20092234,  17.51600582,  25.63546061,  26.17244391,
            22.95054409,  28.85736043,  24.0245107,   28.32037713,
            25.63546061,  24.561494,    29.39434374,  25.09847731,
            22.95054409,  21.87657748,  29.39434374,  27.24641052])


class Cpunish:
    '''
    The following are from the R script in models.datasets.cpunish
    Slightly different than published results, but should be correct
    Probably due to rounding in cleaning?
    '''
    def __init__(self):
        self.params = (
            2.611017e-04, 7.781801e-02, -9.493111e-02, 2.969349e-01,
            2.301183e+00, -1.872207e+01, -6.801480e+00)
        self.bse = (
            5.187132e-05, 7.940193e-02, 2.291926e-02, 4.375164e-01,
            4.283826e-01, 4.283961e+00, 4.146850e+00)
        self.null_deviance = 136.57281747225
        self.df_null = 16
        self.deviance = 18.591641759528944
        self.df_resid = 10
        self.df_model = 6
        self.aic_R = 77.8546573896503   # same as Stata
        self.aic_Stata = 4.579685683305706
        self.bic_Stata = -9.740492454486446
        self.chi2 = 128.8021169250578   # from Stata not in sm
        self.llf = -31.92732869482515
        self.scale = 1
        self.pearson_chi2 = 24.75374835
        self.resids = glm_test_resids.cpunish_resids
        self.fittedvalues = np.array([
            35.2263655,  8.1965744,  1.3118966,
            3.6862982,  2.0823003,  1.0650316,  1.9260424,  2.4171405,
            1.8473219,  2.8643241,  3.1211989,  3.3382067,  2.5269969,
            0.8972542, 0.9793332,  0.5346209,  1.9790936])


class Cpunish_offset(Cpunish):
    '''
    Same model as Cpunish but with offset of 100.  Many things do not change.
    '''
    def __init__(self):
        super().__init__()

        self.params = (
            -1.140665e+01, 2.611017e-04, 7.781801e-02,
            -9.493111e-02, 2.969349e-01, 2.301183e+00,
            -1.872207e+01)
        self.bse = (
            4.147e+00, 5.187e-05, 7.940e-02, 2.292e-02,
            4.375e-01, 4.284e-01, 4.284e+00)


class InvGauss:
    '''
    Usef

    Data was generated by Hardin and Hilbe using Stata.
    Note only the first 5000 observations are used because
    the models code currently uses np.eye.
    '''
    # FIXME: do something with the commented-out code below
    #     np.random.seed(54321)
    #     x1 = np.abs(stats.norm.ppf((np.random.random(5000))))
    #     x2 = np.abs(stats.norm.ppf((np.random.random(5000))))
    #     X = np.column_stack((x1, x2))
    #    X = add_constant(X)
    #    params = np.array([.5, -.25, 1])
    #    eta = np.dot(X, params)
    #    mu = 1/np.sqrt(eta)
    #    sigma = .5
    #   This is not correct.  Errors need to be normally distributed
    #   But Y needs to be Inverse Gaussian, so we could build it up
    #   by throwing out data?
    #   Refs:
    #       * Lai (2009) Generating inverse Gaussian random variates by
    #         approximation
    #       * Atkinson (1982) The simulation of generalized inverse gaussian
    #         and hyperbolic random variables seems to be the canonical ref
    #    Y = np.dot(X, params) + np.random.wald(mu, sigma, 1000)
    #    model = GLM(Y, X, family=models.family.InverseGaussian(link=\
    #        models.family.links.Identity()))

    def __init__(self):
        # set up data #
        filename = os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                "inv_gaussian.csv")
        with open(filename, encoding="utf-8") as fd:
            data = np.genfromtxt(fd, delimiter=",", dtype=float)[1:]
        self.endog = data[:5000, 0]
        self.exog = data[:5000, 1:]
        self.exog = add_constant(self.exog, prepend=False)

        # Results
        # NOTE: loglikelihood difference in R vs. Stata vs. Models
        #  is the same situation as gamma
        self.params = (0.4519770, -0.2508288, 1.0359574)
        self.bse = (0.03148291, 0.02237211, 0.03429943)
        self.null_deviance = 1520.673165475461
        self.df_null = 4999
        self.deviance = 1423.943980407997
        self.df_resid = 4997
        self.df_model = 2
        self.aic_R = 5059.41911646446
        self.aic_Stata = 1.552280060977946
        self.bic_Stata = -41136.47039418921
        self.llf = -3877.700354  # Stata is same as ours with scale set to 1
        # self.llf = -2525.70955823223   # from R, close to ours
        self.scale = 0.2867266359127567
        self.pearson_chi2 = 1432.771536
        self.resids = glm_test_resids.invgauss_resids
        self.fittedvalues = np.array([
            1.0404339,   0.96831526, 0.81265833,  0.9958362,   1.05433442,
            1.09866137,  0.95548191,  1.38082105,  0.98942888,  0.96521958,
            1.02684056,  0.91412576,  0.91492102,  0.92639676,  0.96763425,
            0.80250852,  0.85281816,  0.90962261,  0.95550299,  0.86386815,
            0.94760134,  0.94269533,  0.98960509,  0.84787252,  0.78949111,
            0.76873582,  0.98933453,  0.95105574,  0.8489395,   0.88962971,
            0.84856357,  0.88567313,  0.84505405,  0.84626147,  0.77250421,
            0.90175601,  1.15436378,  0.98375558,  0.83539542,  0.82845381,
            0.90703971,  0.85546165,  0.96707286,  0.84127197,  0.82096543,
            1.1311227,   0.87617029,  0.91194419,  1.05125511,  0.95330314,
            0.75556148,  0.82573228,  0.80424982,  0.83800144,  0.8203644,
            0.84423807,  0.98348433,  0.93165089,  0.83968706,  0.79256287,
            1.0302839,   0.90982028,  0.99471562,  0.70931825,  0.85471721,
            1.02668021,  1.11308301,  0.80497105,  1.02708486,  1.07671424,
            0.821108,    0.86373486,  0.99104964,  1.06840593,  0.94947784,
            0.80982122,  0.95778065,  1.0254212,   1.03480946,  0.83942363,
            1.17194944,  0.91772559,  0.92368795,  1.10410916,  1.12558875,
            1.11290791,  0.87816503,  1.04299294,  0.89631173,  1.02093004,
            0.86331723,  1.13134858,  1.01807861,  0.98441692,  0.72567667,
            1.42760495,  0.78987436,  0.72734482,  0.81750166,  0.86451854,
            0.90564264,  0.81022323,  0.98720325,  0.98263709,  0.99364823,
            0.7264445,   0.81632452,  0.7627845,   1.10726938,  0.79195664,
            0.86836774,  1.01558149,  0.82673675,  0.99529548,  0.97155636,
            0.980696,    0.85460503,  1.00460782,  0.77395244,  0.81229831,
            0.94078297,  1.05910564,  0.95921954,  0.97841172,  0.93093166,
            0.93009865,  0.89888111,  1.18714408,  0.98964763,  1.03388898,
            1.67554215,  0.82998876,  1.34100687,  0.86766346,  0.96392316,
            0.91371033,  0.76589296,  0.92329051,  0.82560326,  0.96758148,
            0.8412995,   1.02550678,  0.74911108,  0.8751611,   1.01389312,
            0.87865556,  1.24095868,  0.90678261,  0.85973204,  1.05617845,
            0.94163038,  0.88087351,  0.95699844,  0.86083491,  0.89669384,
            0.78646825,  1.0014202,   0.82399199,  1.05313139,  1.06458324,
            0.88501766,  1.19043294,  0.8458026,   1.00231535,  0.72464305,
            0.94790753,  0.7829744,   1.1953009,   0.85574035,  0.95433052,
            0.96341484,  0.91362908,  0.94097713,  0.87273804,  0.81126399,
            0.72715262,  0.85526116,  0.76015834,  0.8403826,   0.9831501,
            1.17104665,  0.78862494,  1.01054909,  0.91511601,  1.0990797,
            0.91352124,  1.13671162,  0.98793866,  1.0300545,   1.04490115,
            0.85778231,  0.94824343,  1.14510618,  0.81305136,  0.88085051,
            0.94743792,  0.94875465,  0.96206997,  0.94493612,  0.93547218,
            1.09212018,  0.86934651,  0.90532353,  1.07066001,  1.26197714,
            0.93858662,  0.9685039,   0.7946546,   1.03052031,  0.75395899,
            0.87527062,  0.82156476,  0.949774,    1.01000235,  0.82613526,
            1.0224591,   0.91529149,  0.91608832,  1.09418385,  0.8228272,
            1.06337472,  1.05533176,  0.93513063,  1.00055806,  0.95474743,
            0.91329368,  0.88711836,  0.95584926,  0.9825458,   0.74954073,
            0.96964967,  0.88779583,  0.95321846,  0.95390055,  0.95369029,
            0.94326714,  1.31881201,  0.71512263,  0.84526602,  0.92323824,
            1.01993108,  0.85155992,  0.81416851,  0.98749128,  1.00034192,
            0.98763473,  1.05974138,  1.05912658,  0.89772172,  0.97905626,
            1.1534306,   0.92304181,  1.16450278,  0.7142307,   0.99846981,
            0.79861247,  0.73939835,  0.93776385,  1.0072242,   0.89159707,
            1.05514263,  1.05254569,  0.81005146,  0.95179784,  1.00278795,
            1.04910398,  0.88427798,  0.74394266,  0.92941178,  0.83622845,
            0.84064958,  0.93426956,  1.03619314,  1.22439347,  0.73510451,
            0.82997071,  0.90828036,  0.80866989,  1.34078212,  0.85079169,
            0.88346039,  0.76871666,  0.96763454,  0.66936914,  0.94175741,
            0.97127617,  1.00844382,  0.83449557,  0.88095564,  1.17711652,
            1.0547188,   1.04525593,  0.93817487,  0.77978294,  1.36143199,
            1.16127997,  1.03792952,  1.03151637,  0.83837387,  0.94326066,
            1.0054787,   0.99656841,  1.05575689,  0.97641643,  0.85108163,
            0.82631589,  0.77407305,  0.90566132,  0.91308164,  0.95560906,
            1.04523011,  1.03773723,  0.97378685,  0.83999133,  1.06926871,
            1.01073982,  0.9804959,   1.06473061,  1.25315673,  0.969175,
            0.63443508,  0.84574684,  1.06031239,  0.93834605,  1.01784925,
            0.93488249,  0.80240225,  0.88757274,  0.9224097,   0.99158962,
            0.87412592,  0.76418199,  0.78044069,  1.03117412,  0.82042521,
            1.10272129,  1.09673757,  0.89626935,  1.01678612,  0.84911824,
            0.95821431,  0.99169558,  0.86853864,  0.92172772,  0.94046199,
            0.89750517,  1.09599258,  0.92387291,  1.07770118,  0.98831383,
            0.86352396,  0.83079533,  0.94431185,  1.12424626,  1.02553104,
            0.8357513,   0.97019669,  0.76816092,  1.34011343,  0.86489527,
            0.82156358,  1.25529129,  0.86820218,  0.96970237,  0.85850546,
            0.97429559,  0.84826078,  1.02498396,  0.72478517,  0.993497,
            0.76918521,  0.91079198,  0.80988325,  0.75431095,  1.02918073,
            0.88884197,  0.82625507,  0.78564563,  0.91505355,  0.88896863,
            0.85882361,  0.81538316,  0.67656235,  0.8564822,   0.82473022,
            0.92928331,  0.98068415,  0.82605685,  1.0150412,   1.00631678,
            0.92405101,  0.88909552,  0.94873568,  0.87657342,  0.8280683,
            0.77596382,  0.96598811,  0.78922426,  0.87637606,  0.98698735,
            0.92207026,  0.71487846,  1.03845478,  0.70749745,  1.08603388,
            0.92697779,  0.86470448,  0.70119494,  1.00596847,  0.91426549,
            1.05318838,  0.79621712,  0.96169742,  0.88053405,  0.98963934,
            0.94152997,  0.88413591,  0.75035344,  0.86007123,  0.83713514,
            0.91234911,  0.79562744,  0.84099675,  1.0334279,   1.00272243,
            0.95359383,  0.84292969,  0.94234155,  0.90190899,  0.97302022,
            1.1009829,   1.0148975,   0.99082987,  0.75916515,  0.9204784,
            0.94477378,  1.01108683,  1.00038149,  0.9259798,   1.19400436,
            0.80191877,  0.79565851,  0.81865924,  0.79003506,  0.8995508,
            0.73137983,  0.88336018,  0.7855268,   1.04478073,  0.90857981,
            1.16076951,  0.76096486,  0.90004113,  0.83819665,  0.95295365,
            1.09911441,  0.78498197,  0.95094991,  0.94333419,  0.95131688,
            0.82961049,  1.08001761,  1.06426458,  0.94291798,  1.04381938,
            0.90380364,  0.74060138,  0.98701862,  0.72250236,  0.86125293,
            0.76488061,  0.9858051,   0.98099677,  0.96849209,  0.90053351,
            0.88469597,  0.80688516,  1.06396217,  1.02446023,  0.911863,
            0.98837746,  0.91102987,  0.92810392,  1.13526335,  1.00419541,
            1.00866175,  0.74352261,  0.91051641,  0.81868428,  0.93538014,
            0.87822651,  0.93278572,  1.0356074,   1.25158731,  0.98372647,
            0.81335741,  1.06441863,  0.80305786,  0.95201148,  0.90283451,
            1.17319519,  0.8984894,   0.88911288,  0.91474736,  0.94512294,
            0.92956283,  0.86682085,  1.08937227,  0.94825713,  0.9787145,
            1.16747163,  0.80863682,  0.98314119,  0.91052823,  0.80913225,
            0.78503169,  0.78751737,  1.08932193,  0.86859845,  0.96847458,
            0.93468839,  1.10769915,  1.1769249,   0.84916138,  1.00556408,
            0.84508585,  0.92617942,  0.93985886,  1.17303268,  0.81172495,
            0.93482682,  1.04082486,  1.03209348,  0.97220394,  0.90274672,
            0.93686291,  0.91116431,  1.14814563,  0.83279158,  0.95853283,
            1.0261179,   0.95779432,  0.86995883,  0.78164915,  0.89946906,
            0.9194465,   0.97919367,  0.92719039,  0.89063569,  0.80847805,
            0.81192101,  0.75044535,  0.86819023,  1.03420014,  0.8899434,
            0.94899544,  0.9860773,   1.10047297,  1.00243849,  0.82153972,
            1.14289945,  0.8604684,   0.87187524,  1.00415032,  0.78460709,
            0.86319884,  0.92818335,  1.08892111,  1.06841003,  1.00735918,
            1.20775251,  0.72613554,  1.25768191,  1.08573511,  0.89671127,
            0.91259535,  1.01414208,  0.87422903,  0.82720677,  0.9568079,
            1.00450416,  0.91043845,  0.84095709,  1.08010574,  0.69848293,
            0.90769214,  0.94713501,  1.14808251,  1.0605676,   1.21734482,
            0.78578521,  1.01516235,  0.94330326,  0.98363817,  0.99650084,
            0.74280796,  0.96227123,  0.95741454,  1.00980406,  0.93468092,
            1.10098591,  1.18175828,  0.8553791,   0.81713219,  0.82912143,
            0.87599518,  1.15006511,  1.03151163,  0.8751847,   1.15701331,
            0.73394166,  0.91426368,  0.96953458,  1.13901709,  0.83028721,
            1.15742641,  0.9395442,   0.98118552,  0.89585426,  0.74147117,
            0.8902096,   1.00212097,  0.97665858,  0.92624514,  0.98006601,
            0.9507215,   1.00889825,  1.2406772,   0.88768719,  0.76587533,
            1.0081044,   0.89608494,  1.00083526,  0.85594415,  0.76425576,
            1.0286636,   1.13570272,  0.82020405,  0.81961271,  1.04586579,
            1.26560245,  0.89721521,  1.19324037,  0.948205,    0.79414261,
            0.85157002,  0.95155101,  0.91969239,  0.87699126,  1.03452982,
            0.97093572,  1.14355781,  0.85088592,  0.79032079,  0.84521733,
            0.99547581,  0.87593455,  0.8776799,   1.05531013,  0.94557017,
            0.91538439,  0.79679863,  1.03398557,  0.88379021,  0.98850319,
            1.05833423,  0.90055078,  0.92267584,  0.76273738,  0.98222632,
            0.86392524,  0.78242646,  1.19417739,  0.89159895,  0.97565002,
            0.85818308,  0.85334266,  1.85008011,  0.87199282,  0.77873231,
            0.78036174,  0.96023918,  0.91574121,  0.89217979,  1.16421151,
            1.29817786,  1.18683283,  0.96096225,  0.89964569,  1.00401442,
            0.80758845,  0.89458758,  0.7994919,   0.85889356,  0.73147252,
            0.7777221,   0.9148438,   0.72388117,  0.91134001,  1.0892724,
            1.01736424,  0.86503014,  0.77344917,  1.04515616,  1.06677211,
            0.93421936,  0.8821777,   0.91860774,  0.96381507,  0.70913689,
            0.82354748,  1.12416046,  0.85989778,  0.90588737,  1.22832895,
            0.65955579,  0.93828405,  0.88946418,  0.92152859,  0.83168025,
            0.93346887,  0.96456078,  0.9039245,   1.03598695,  0.78405559,
            1.21739525,  0.79019383,  0.84034646,  1.00273203,  0.96356393,
            0.948103,    0.90279217,  1.0187839,   0.91630508,  1.15965854,
            0.84203423,  0.98803156,  0.91604459,  0.90986512,  0.93384826,
            0.76687038,  0.96251902,  0.80648134,  0.77336547,  0.85720164,
            0.9351947,   0.88004728,  0.91083961,  1.06225829,  0.90230812,
            0.72383932,  0.8343425,   0.8850996,   1.19037918,  0.93595522,
            0.85061223,  0.84330949,  0.82397482,  0.92075047,  0.86129584,
            0.99296756,  0.84912251,  0.8569699,   0.75252201,  0.80591772,
            1.03902954,  1.04379139,  0.87360195,  0.97452318,  0.93240609,
            0.85406409,  1.11717394,  0.95758536,  0.82772817,  0.67947416,
            0.85957788,  0.93731268,  0.90349227,  0.79464185,  0.99148637,
            0.8461071,   0.95399991,  1.04320664,  0.87290871,  0.96780849,
            0.99467159,  0.96421545,  0.80174643,  0.86475812,  0.74421362,
            0.85230296,  0.89891758,  0.77589592,  0.98331957,  0.87387233,
            0.92023388,  1.03037742,  0.83796515,  1.0296667,   0.85891747,
            1.02239978,  0.90958406,  1.09731875,  0.8032638,   0.84482057,
            0.8233118,   0.86184709,  0.93105929,  0.99443502,  0.77442109,
            0.98367982,  0.95786272,  0.81183444,  1.0526009,   0.86993018,
            0.985886,    0.92016756,  1.00847155,  1.2309469,   0.97732206,
            0.83074957,  0.87406987,  0.95268492,  0.94189139,  0.87056443,
            1.0135018,   0.93051004,  1.5170931,   0.80948763,  0.83737473,
            1.05461331,  0.97501633,  1.01449333,  0.79760056,  1.05756482,
            0.97300884,  0.92674035,  0.8933763,   0.91624084,  1.13127607,
            0.88115305,  0.9351562,   0.91430431,  1.11668229,  1.10000526,
            0.88171963,  0.74914744,  0.94610698,  1.13841497,  0.90551414,
            0.89773592,  1.01696097,  0.85096063,  0.80935471,  0.68458106,
            1.2718979,   0.93550219,  0.96071403,  0.75434294,  0.95112257,
            1.16233368,  0.73664915,  1.02195777,  1.07487625,  0.8937445,
            0.78006023,  0.89588994,  1.16354892,  1.02629448,  0.89208642,
            1.02088244,  0.85385355,  0.88586061,  0.94571704,  0.89710576,
            0.95191525,  0.99819848,  0.97117841,  1.13899808,  0.88414949,
            0.90938883,  1.02937917,  0.92936684,  0.87323594,  0.8384819,
            0.87766945,  1.05869911,  0.91028734,  0.969953,    1.11036647,
            0.94996802,  1.01305483,  1.03697568,  0.9750155,   1.04537837,
            0.9314676,   0.86589798,  1.17446667,  1.02564533,  0.82088708,
            0.96481845,  0.86148642,  0.79174298,  1.18029919,  0.82132544,
            0.92193776,  1.03669516,  0.96637464,  0.83725933,  0.88776321,
            1.08395861,  0.91255709,  0.96884738,  0.89840008,  0.91168146,
            0.99652569,  0.95693101,  0.83144932,  0.99886503,  1.02819927,
            0.95273533,  0.95959945,  1.08515986,  0.70269432,  0.79529303,
            0.93355669,  0.92597539,  1.0745695,   0.87949758,  0.86133964,
            0.95653873,  1.09161425,  0.91402143,  1.13895454,  0.89384443,
            1.16281703,  0.8427015,   0.7657266,   0.92724079,  0.95383649,
            0.86820891,  0.78942366,  1.11752711,  0.97902686,  0.87425286,
            0.83944794,  1.12576718,  0.9196059,   0.89844835,  1.10874172,
            1.00396783,  0.9072041,   1.63580253,  0.98327489,  0.68564426,
            1.01007087,  0.92746473,  1.01328833,  0.99584546,  0.86381679,
            1.0082541,   0.85414132,  0.87620981,  1.22461203,  1.03935516,
            0.86457326,  0.95165828,  0.84762138,  0.83080254,  0.84715241,
            0.80323344,  1.09282941,  1.00902453,  1.02834261,  1.09810743,
            0.86560231,  1.31568763,  1.03754782,  0.81298745,  1.14500629,
            0.87364384,  0.89928367,  0.96118471,  0.83321743,  0.90590461,
            0.98739499,  0.79408399,  1.18513754,  1.05619307,  0.99920088,
            1.04347259,  1.07689022,  1.24916765,  0.74246274,  0.90949597,
            0.87077335,  0.81233276,  1.05403934,  0.98333063,  0.77689527,
            0.93181907,  0.98853585,  0.80700332,  0.89570662,  0.97102475,
            0.69178123,  0.72950409,  0.89661719,  0.84821737,  0.8724469,
            0.96453177,  0.9690018,   0.87132764,  0.91711564,  1.79521288,
            0.75894855,  0.90733112,  0.86565687,  0.90433268,  0.83412618,
            1.26779628,  1.06999114,  0.73181364,  0.90334838,  0.86634581,
            0.76999285,  1.55403008,  0.74712547,  0.84702579,  0.72396203,
            0.82292773,  0.73633208,  0.90524618,  0.9954355,   0.85076517,
            0.96097585,  1.21655611,  0.77658146,  0.81026686,  1.07540173,
            0.94219623,  0.97472554,  0.72422803,  0.85055855,  0.85905477,
            1.17391419,  0.87644114,  1.03573284,  1.16647944,  0.87810532,
            0.89134419,  0.83531593,  0.93448128,  1.04967869,  1.00110843,
            0.936784,    1.00143426,  0.79714807,  0.82656251,  0.95057309,
            0.93821813,  0.93469098,  0.99825205,  0.95384714,  1.07063008,
            0.97603699,  0.816668,    0.98286184,  0.86061483,  0.88166732,
            0.93730982,  0.77633837,  0.87671549,  0.99192439,  0.86452825,
            0.95880282,  0.7098419,   1.12717149,  1.16707939,  0.84854333,
            0.87486963,  0.9255293,   1.06534197,  0.9888494,   1.09931069,
            1.21859221,  0.97489537,  0.82508579,  1.14868922,  0.98076133,
            0.85524084,  0.69042079,  0.93012936,  0.96908499,  0.94284892,
            0.80114327,  0.919846,    0.95753354,  1.04536666,  0.77109284,
            0.99942571,  0.79004323,  0.91820045,  0.97665489,  0.64689716,
            0.89444405,  0.96106598,  0.74196857,  0.92905294,  0.70500318,
            0.95074586,  0.98518665,  1.0794044,   1.00364488,  0.96710486,
            0.92429638,  0.94383006,  1.12554253,  0.95199191,  0.87380738,
            0.72183594,  0.94453761,  0.98663804,  0.68247366,  1.02761427,
            0.93255355,  0.85264705,  1.00341417,  1.07765999,  0.97396039,
            0.90770805,  0.82750901,  0.73824542,  1.24491161,  0.83152629,
            0.78656996,  0.99062838,  0.98276905,  0.98291014,  1.12795903,
            0.98742704,  0.9579893,   0.80451701,  0.87198344,  1.24746127,
            0.95839155,  1.11708725,  0.97113877,  0.7721646,   0.95781621,
            0.67069168,  1.05509376,  0.96071852,  0.99768666,  0.83008521,
            0.9156695,   0.86314088,  1.23081412,  1.14723685,  0.8007289,
            0.81590842,  1.31857558,  0.7753396,   1.11091566,  1.03560198,
            1.01837739,  0.94882818,  0.82551111,  0.93188019,  0.99532255,
            0.93848495,  0.77764975,  0.85192319,  0.79913938,  0.99495229,
            0.96122733,  1.13845155,  0.95846389,  0.8891543,   0.97979531,
            0.87167192,  0.88119611,  0.79655111,  0.9298217,   0.96399321,
            1.02005428,  1.06936503,  0.86948022,  1.02560548,  0.9149464,
            0.83797207,  0.86175383,  0.92455994,  0.89218435,  0.81546463,
            0.98488771,  0.92784833,  0.87895608,  0.93366386,  1.17487238,
            0.79088952,  0.9237694,   0.76389869,  0.931953,    0.76272078,
            1.00304977,  0.86612561,  0.87870143,  0.93808276,  1.12489343,
            1.00668791,  0.88027101,  0.88845209,  0.88574216,  0.84284514,
            0.96594357,  0.94363002,  0.78245367,  0.92941326,  0.99622557,
            0.83812683,  0.77901691,  0.9588432,   0.82057415,  0.95178868,
            1.01904651,  0.97598844,  0.99369336,  1.12041918,  1.19432836,
            0.91709572,  0.94645855,  0.93656587,  0.68754669,  0.80869784,
            0.86704186,  0.83033797,  0.71892193,  0.97549489,  1.12150683,
            0.76214802,  1.08564181,  0.84677802,  0.68080207,  1.03577057,
            1.07937239,  0.6773357,   1.0279076,   0.89945816,  0.97765439,
            0.91322633,  0.92490964,  0.92693575,  1.12297137,  0.81825246,
            0.87598377,  1.11873032,  0.83472799,  1.21424495,  1.02318444,
            1.01563195,  1.05663193,  0.82533918,  0.88766496,  0.95906474,
            0.90738779,  0.93509534,  1.06658145,  1.00231797,  1.3131534,
            0.88839464,  1.081006,    0.866936,    0.89030904,  0.91197562,
            0.73449761,  0.95767806,  1.03407868,  0.79812826,  1.10555445,
            0.85610722,  0.87420881,  1.04251375,  1.14286242,  1.00025972,
            0.83742693,  1.11116502,  0.97424809,  0.92059325,  0.93958773,
            0.80386755,  0.6881267,   0.88620708,  1.01715536,  1.12403581,
            0.91078992,  0.81101399,  1.17271429,  1.09980447,  0.86063042,
            0.80805811,  0.87988444,  0.97398188,  0.91808966,  0.90676805,
            0.80042891,  0.84060789,  0.9710147,   1.00012669,  1.04805667,
            0.66912164,  0.96111694,  0.86948596,  0.9056999,   1.01489333,
            1.27876763,  0.873881,    0.98276702,  0.95553234,  0.82877996,
            0.79697623,  0.77015376,  0.8234212,   1.13394959,  0.96244655,
            1.06516156,  0.82743856,  1.02931842,  0.78093489,  1.01322256,
            1.00348929,  0.9408142,   1.06495299,  0.8599522,   0.81640723,
            0.81505589,  1.02506487,  0.91148383,  1.11134309,  0.83992234,
            0.82982074,  0.9721429,   0.98897262,  1.01815004,  0.87838456,
            0.80573592,  1.103707,    0.97326218,  1.08921236,  1.2638062,
            0.83142563,  1.16028769,  0.86701564,  1.15610014,  0.98303722,
            0.87138463,  0.75281511,  1.07715535,  0.91526065,  1.08769832,
            0.83598308,  1.03580956,  0.9390066,   0.78544378,  1.03635836,
            0.7974467,   0.99273331,  0.89639711,  0.9250066,   1.14323824,
            0.9783478,   1.15460639,  0.94265587,  1.09317654,  0.78585439,
            0.99523323,  0.95104776,  0.85582572,  0.96100168,  0.9131529,
            0.86496966,  0.72414589,  1.05142704,  0.85570039,  0.98217968,
            0.99031168,  1.01867086,  0.96781667,  0.98581487,  1.00415938,
            1.0339337,   1.13987579,  1.14205543,  0.83393745,  0.96348647,
            0.91895164,  0.77055293,  1.0053723,   0.93168993,  1.00332386,
            1.04195993,  1.11933891,  0.87439883,  0.87156457,  0.96050419,
            0.72718399,  1.13546762,  0.89614816,  0.85081037,  0.8831463,
            0.76370482,  0.99582951,  1.01844155,  1.08611311,  1.15832217,
            1.17551069,  0.97057262,  0.95163548,  0.98310701,  0.65874788,
            0.9655409,   0.85675853,  1.34637286,  0.93779619,  1.0005791,
            0.88104966,  1.14530829,  0.93687034,  1.01472112,  1.62464726,
            0.84652357,  0.84639676,  0.87513324,  0.94837881,  0.85425129,
            0.89820401,  0.94906277,  0.97796792,  0.98969445,  0.8036801,
            1.03936478,  0.95898918,  0.82919938,  1.29609354,  0.97833841,
            0.86862799,  0.88040491,  0.8741178,   0.80617278,  0.95983882,
            0.9752235,   0.84292828,  0.9327284,   0.93297136,  1.06255543,
            0.88756716,  1.13601403,  0.72311518,  0.95250034,  0.95369843,
            1.02562728,  0.74354691,  0.78463923,  0.88720818,  1.07763289,
            0.94502062,  0.81170329,  0.96516347,  0.76884811,  0.84169312,
            0.83752837,  1.1487847,   1.04311868,  0.78128663,  0.74604211,
            0.96488513,  1.1722513,   0.91661948,  1.06642815,  0.92185781,
            0.93289001,  0.65208625,  0.75734648,  0.99580571,  1.21871511,
            0.96316283,  1.06093093,  0.7914337,   0.90494572,  0.79235327,
            0.90771769,  0.91355145,  0.98754767,  0.88938619,  0.89503537,
            0.82764566,  0.77267065,  0.81520031,  0.90423926,  0.94289609,
            0.88678376,  1.03209085,  0.81319963,  0.91600997,  0.81608666,
            0.72429125,  0.95585073,  1.14039309,  1.00326452,  0.99629944,
            0.95647901,  0.8927127,   0.96558599,  0.86305195,  1.0366906,
            0.90494731,  0.95148458,  1.11229696,  1.17059748,  0.74867876,
            0.99621909,  0.94246499,  0.82403515,  0.92144961,  0.93209989,
            0.9705427,   0.97915309,  0.92431525,  0.7589944,   0.75208652,
            0.89375154,  0.78820016,  1.24061454,  1.08031776,  0.88364539,
            0.86909794,  0.98635253,  0.97620372,  1.24278282,  1.01146474,
            0.93726261,  0.94411536,  1.08344492,  0.75389972,  1.09979822,
            0.84271329,  1.16616317,  0.88177625,  0.8451345,   0.91355741,
            0.99833789,  0.86172172,  0.87076203,  0.83743078,  0.99771528,
            1.0469295,   0.87952668,  1.04362453,  0.96350831,  0.95744466,
            0.84284283,  0.8773066,   0.85984544,  1.00589365,  0.88069101,
            1.02331332,  1.06616241,  0.78475212,  1.02296979,  0.81480926,
            1.09008244,  0.71435844,  0.79655626,  1.09824162,  0.87785428,
            1.18020492,  0.99852432,  0.79028362,  0.80081103,  1.10940685,
            1.08752313,  0.90673214,  0.84978348,  0.69466992,  0.77497046,
            0.83074014,  0.87865947,  0.78890395,  0.7925195,   0.99749611,
            0.91430636,  0.87863864,  0.95392862,  0.91430684,  0.97358575,
            0.87999755,  0.88234274,  0.71682337,  1.09723693,  0.71907671,
            0.97487202,  0.71792963,  0.88374828,  0.73386811,  0.9315647,
            1.05020628,  0.99128682,  0.71831173,  1.07119604,  1.02028122,
            1.04696848,  0.93335813,  1.04275931,  0.72181913,  0.8837163,
            0.90283411,  0.96642474,  0.89851984,  0.8397063,   0.91185676,
            1.00573193,  0.88430729,  0.7738957,   1.07361285,  0.92617819,
            0.64251751,  1.05229257,  0.73378537,  1.08270418,  0.99490809,
            1.13634433,  1.11979997,  1.03383516,  1.00661234,  1.05778729,
            1.05977357,  1.13779694,  0.91237075,  1.04866775,  0.9163203,
            0.93152436,  0.83607634,  1.13426049,  1.26438419,  0.93515536,
            0.92181847,  0.86558905,  1.01985742,  1.44095931,  0.92256398,
            0.83369288,  0.93369164,  0.8243758,   0.98278708,  0.80512458,
            1.02092014,  0.73575074,  1.2214659,   0.85391033,  0.97617313,
            0.82054292,  1.04792993,  0.93961791,  1.01145014,  0.89301558,
            0.93167504,  0.88221321,  1.23543354,  0.97023998,  1.00197517,
            0.85394662,  0.89426495,  0.81344186,  1.08242456,  0.76253284,
            1.00642867,  0.76685541,  1.01487961,  0.84028343,  0.87979545,
            0.92796937,  0.99796437,  1.28844084,  1.02827514,  1.03663144,
            0.83164521,  0.95644234,  0.77797914,  0.96748275,  1.09139879,
            0.84329253,  0.9539873,   0.80094065,  1.13771172,  0.91557533,
            0.93370323,  0.79977904,  1.02721929,  1.16292026,  0.92976802,
            0.85806865,  0.97824974,  1.02721582,  0.82773004,  0.9297126,
            0.93769842,  1.14995068,  1.02895292,  0.90307101,  0.85918303,
            1.14903979,  1.0344768,   0.7502627,   1.27452448,  1.12150928,
            0.87274005,  1.09807041,  0.98634666,  1.03086907,  0.94743667,
            0.91145542,  1.04395791,  0.83396016,  0.94783374,  0.96693806,
            0.88864359,  0.93400675,  1.08563936,  0.78599906,  0.92142347,
            1.15487344,  1.19946426,  0.92729226,  0.83333347,  0.90837637,
            0.89191831,  1.0581614,   0.85162688,  1.10081699,  0.98295351,
            0.86684217,  1.00867408,  0.95966205,  0.73170785,  1.3207658,
            0.87988622,  0.82869937,  0.9620586,   0.71668579,  1.04105616,
            0.71415591,  1.30198958,  0.81934393,  0.86731955,  0.99773712,
            0.99943609,  0.87678188,  1.01650692,  0.73917494,  0.92077402,
            0.98322263,  0.90623212,  0.88261034,  1.12798871,  0.84698889,
            0.85312827,  0.91214965,  0.8778361,   0.99621569,  0.94155734,
            0.66441342,  0.85925635,  0.98064691,  0.97107172,  0.96438785,
            0.95670408,  0.87601389,  0.9388234,   0.91165254,  1.14769638,
            0.99856344,  0.84391431,  0.94850194,  0.93754548,  0.86398937,
            0.95090327,  1.07959765,  1.16684297,  0.82354834,  0.93165852,
            0.91422292,  1.14872038,  0.87050113,  0.92322683,  1.04111597,
            0.87780005,  0.94602618,  1.10071675,  0.88412438,  0.91286998,
            0.9045216,   0.91750005,  0.98647095,  1.10986959,  0.98912028,
            1.01565645,  0.93891294,  0.97696431,  0.91186476,  0.77363533,
            1.00075969,  0.89608139,  0.99828964,  0.87239569,  0.87540604,
            0.76152791,  0.82501538,  0.91656546,  0.74389243,  1.07923575,
            1.00241137,  1.05628365,  1.04407879,  0.90048788,  1.1134027,
            0.89745966,  0.96534,     0.71151925,  0.91798511,  0.7337992,
            0.83636115,  0.75279928,  0.95570185,  0.89073922,  0.90307955,
            0.8030445,   0.84374939,  0.89769981,  0.99002578,  1.01849373,
            0.92436541,  0.79675699,  1.03910383,  1.07487895,  0.8906169,
            0.97729004,  0.97284392,  0.76338988,  0.82756432,  1.12289431,
            0.9582901,   0.97160038,  0.90141331,  0.83271234,  1.16065947,
            0.90605662,  1.13389282,  0.8557889,   0.77149889,  0.9462268,
            0.95908887,  1.03399986,  0.92795031,  0.73529029,  0.93630494,
            0.96730298,  1.05490026,  0.93313995,  0.96980639,  0.9177592,
            0.95483326,  0.85262905,  0.95170479,  0.9601628,   0.94878173,
            0.87627934,  1.00561764,  0.83441231,  0.90890643,  0.97177858,
            1.26394809,  0.80773622,  0.72205262,  0.87692143,  1.01842034,
            0.98128171,  1.10776014,  0.94400422,  0.92697961,  0.79523284,
            0.8609763,   0.96303262,  1.17190075,  1.01259271,  1.04973619,
            0.94837034,  0.86592734,  0.85908444,  1.14914962,  0.98113587,
            1.03070712,  0.89916573,  0.90618114,  0.93223156,  0.96031901,
            0.94162334,  0.98908438,  0.95170104,  0.95056422,  0.81782932,
            0.81770133,  1.32039255,  1.28822384,  0.82916292,  1.01626284,
            0.97537737,  0.83235746,  0.78645733,  0.77916206,  0.93591612,
            0.8469273,   0.74309279,  0.91331015,  1.11240033,  1.41018987,
            0.95320314,  0.95807535,  0.89382722,  0.9259679,   0.92570222,
            0.84567759,  0.82332966,  0.98371126,  1.00248628,  0.72107053,
            1.09687436,  0.78399705,  0.85224803,  0.92151262,  0.85618586,
            0.88485527,  0.954487,    0.86659146,  1.12800711,  0.93019359,
            0.91388385,  0.95298992,  0.96834137,  0.90256791,  1.01222062,
            0.84883116,  1.01234642,  0.91135106,  0.83362478,  0.94928359,
            0.82247066,  0.7671973,   0.85663382,  0.88838144,  0.92491567,
            0.88698604,  0.87485584,  1.08494606,  0.96431031,  1.06243095,
            1.14062212,  1.02081623,  0.72229471,  0.82390737,  0.86599633,
            0.95284398,  0.87238315,  1.02818071,  0.98462575,  0.81992808,
            1.01207538,  1.0081178,   0.88458825,  1.01726135,  0.97708359,
            0.79820777,  1.06081843,  0.97028599,  0.95203124,  1.00482088,
            0.71764193,  0.88115767,  0.90628038,  0.97304174,  0.77015983,
            1.06109546,  0.89575454,  0.94824633,  0.93822134,  0.98048549,
            0.812265,    0.95744328,  0.79087999,  1.0222571,   0.89100453,
            1.03590214,  0.92699983,  0.86840126,  0.99455198,  0.87912973,
            0.93506231,  0.80706147,  0.89931563,  0.7861299,   0.89253527,
            0.90052785,  0.82420191,  0.97042004,  1.03249619,  0.92354267,
            0.80482118,  0.9007601,   0.80123508,  0.82285143,  0.88105118,
            1.03519622,  0.8620259,   0.96447485,  0.80399664,  1.00324939,
            0.96317193,  0.83260244,  0.98561657,  0.88445103,  0.70777743,
            0.81608832,  0.98073402,  1.1206105,   0.69903403,  0.84353026,
            0.9064964,   0.97055276,  0.82747966,  0.85400205,  1.01205886,
            0.85324973,  0.90899616,  0.92797575,  0.94646632,  0.89358892,
            0.7981183,   0.96559671,  0.88352248,  1.09804477,  0.79152196,
            1.1054838,   0.93272283,  0.96165854,  0.8899703,   0.8792494,
            0.74563326,  0.85371604,  0.87760912,  0.87184716,  0.92049887,
            0.99459292,  0.93699011,  0.90492494,  1.12981885,  1.10621082,
            0.91391466,  1.05207781,  1.13395097,  0.87022945,  0.93165871,
            0.89083332,  0.99584874,  0.98626911,  1.13885184,  1.17350384,
            0.93294232,  0.79602714,  0.93670114,  1.09726582,  1.05378961,
            0.9457279,   1.03257053,  1.11349021,  0.80111296,  0.96415105,
            0.99447221,  0.75745769,  0.77537636,  0.83860967,  0.90122484,
            0.78850128,  1.19877642,  0.91190085,  0.80851919,  0.79484738,
            0.93093657,  0.87619908,  1.22781715,  0.89734952,  0.8678127,
            0.76177975,  0.82089769,  0.89288915,  1.01603179,  0.95279916,
            0.84037366,  0.99962719,  0.84298093,  0.77234882,  0.99876963,
            1.01856707,  1.2133211,   0.73822878,  0.83465671,  1.08879938,
            0.8878534,   1.24133317,  0.89264527,  0.83938655,  1.03853109,
            0.9842176,   0.94257497,  0.98282054,  0.90632313,  0.75810741,
            1.02540204,  0.86648513,  0.98430307,  0.84561701,  1.13483974,
            1.12446434,  1.00220923,  1.23248603,  0.98999724,  0.81980761,
            0.91334393,  0.92831557,  1.16798373,  0.8888053,   0.9319632,
            0.89206108,  0.86764558,  0.69337981,  0.9021983,   1.09931186,
            1.15290804,  0.62304114,  1.1205393,   1.27030677,  1.12718725,
            0.93002501,  0.83367301,  0.96589068,  0.86578968,  0.79204086,
            0.85124905,  0.89121046,  0.96406141,  0.99249204,  0.93363878,
            1.11258502,  0.92020983,  1.16020824,  0.99075915,  0.73994574,
            0.9335638,   0.97410789,  1.00029038,  1.43611904,  0.93089581,
            0.94758878,  0.84808364,  0.92192819,  1.0249259,   0.69529827,
            0.94629021,  0.7330735,   1.07902207,  0.93022729,  0.77375973,
            0.95019291,  0.92333668,  0.81483081,  0.78044978,  0.85101115,
            0.88859716,  0.88720344,  0.89291167,  1.10372601,  0.91132273,
            1.04156844,  0.94867703,  0.83546241,  0.84227545,  0.97043199,
            0.73281541,  0.74512501,  0.9128489,   0.99223543,  0.7319106,
            0.93065507,  1.07907995,  0.86895295,  0.84344015,  0.89394039,
            0.88802964,  1.00580322,  1.04286883,  0.82233574,  1.0279258,
            0.97550628,  1.03867605,  1.10231813,  0.9642628,   0.91684874,
            1.11066089,  0.99439688,  0.88595489,  0.88725073,  0.78921585,
            0.80397616,  0.71088468,  0.98316478,  0.72820659,  0.96964036,
            1.03825415,  1.01438989,  1.02763769,  1.29949298,  1.06450406,
            0.86198627,  0.85588074,  0.90445183,  1.01268187,  0.87927487,
            0.9263951,   0.93582126,  0.88738294,  1.20707424,  0.92887657,
            0.97891062,  0.92893689,  0.84846424,  0.96287008,  0.99565057,
            0.93483385,  1.21357183,  0.82369562,  0.65144728,  1.11249654,
            0.7785981,   0.88248898,  0.8953217,   0.95884666,  0.77538093,
            0.82272417,  0.91073072,  1.17185169,  0.99645708,  0.88693463,
            0.90293325,  0.93368474,  0.87575633,  1.01924242,  0.80011545,
            0.99762674,  0.75834671,  0.91952152,  0.86754419,  0.81073894,
            0.8880299,   0.74868718,  0.99979109,  0.90652154,  0.92463566,
            0.93894041,  0.92370595,  0.88766357,  1.04614978,  1.77193759,
            0.85480724,  0.85208602,  0.96154559,  0.95832935,  0.84210613,
            0.9604567,   0.88597666,  1.0010723,   0.91890105,  1.10529207,
            0.91123688,  0.88466788,  1.09759195,  0.8946647,   0.78066485,
            1.04376296,  1.02951755,  0.88455241,  0.99284282,  0.82423576,
            0.80612213,  0.80915541,  0.9482253,   0.8887192,   0.86163309,
            0.891385,    0.84850622,  1.03353375,  1.09248204,  1.05337218,
            0.85927317,  0.89167858,  1.04868715,  0.92933249,  1.1177299,
            0.99846776,  0.82418972,  0.86041965,  0.88015748,  0.89785813,
            0.85997945,  0.97102367,  0.86679181,  1.00848475,  0.9091588,
            0.92565039,  0.84019067,  0.86978485,  1.21977681,  1.14920817,
            1.05177219,  0.84202905,  0.85356083,  1.01379321,  0.93364219,
            1.01999942,  0.85906744,  0.98178266,  0.87218886,  0.93983742,
            0.79713053,  1.01123331,  0.86551625,  0.81983929,  0.86782985,
            0.86735664,  1.43316935,  0.8490094,   0.99909103,  0.85715326,
            0.89452366,  1.08380518,  0.74686847,  1.62233058,  0.81046611,
            0.83563461,  0.96925792,  0.82863186,  0.87147202,  0.92609558,
            0.8879082,   0.93933353,  0.90043906,  0.81677055,  0.78016427,
            0.68871014,  0.83329967,  0.81570171,  0.89780443,  0.81337668,
            1.00772749,  0.96220158,  0.90035459,  1.06031906,  0.85832752,
            0.93636203,  0.96336629,  0.94686138,  0.98499419,  0.87223701,
            0.96079992,  0.81302793,  0.99287479,  0.99369685,  1.21897038,
            0.94547481,  0.80785132,  1.02033902,  0.93270741,  0.90386512,
            1.05290969,  1.08873223,  0.81226537,  0.87185463,  0.96283379,
            0.95065022,  1.07603824,  1.22279786,  0.83749284,  0.93504869,
            0.93554565,  0.95255889,  0.96665227,  0.92370811,  0.76627742,
            1.14267254,  0.98268052,  1.10017739,  0.79569048,  0.86494449,
            1.17939799,  0.80655859,  0.76799971,  1.0018905,   0.83051793,
            1.37419036,  1.10424623,  0.93729691,  0.99655914,  0.94900303,
            1.157402,    0.93397459,  0.8133195,   0.8592273,   1.024661,
            0.83708977,  1.06537435,  0.93561942,  1.00402051,  0.68981047,
            0.92807172,  0.72192097,  1.232419,    0.97080757,  0.90350598,
            0.95122672,  1.04663207,  0.79080723,  0.8421381,   1.01956925,
            0.93307897,  0.88011784,  0.78674974,  0.97537097,  0.7582792,
            0.85704507,  0.97683858,  0.7739793,   0.96245444,  0.99506991,
            0.76853035,  0.90875698,  0.97951121,  0.93350388,  1.16380858,
            0.8154485,   1.16902243,  0.98644779,  0.969998,    0.73120517,
            1.19059456,  0.85953661,  0.99193867,  0.88144929,  0.99254885,
            1.02956121,  0.90689455,  0.89494433,  0.85625065,  0.86227273,
            0.99830845,  0.97635222,  0.83420327,  1.02359646,  0.93694813,
            0.88462353,  0.97040788,  1.02543309,  0.91904348,  1.2527365,
            0.82235812,  0.92026753,  0.93935859,  0.88919482,  1.00405208,
            1.06835782,  1.34738363,  0.97831176,  0.92053317,  1.09692339,
            0.86156677,  1.02455351,  1.25572326,  0.89721167,  0.95787106,
            0.85059479,  0.92044416,  0.99210399,  0.94334232,  0.76604642,
            0.8239008,   0.70790815,  1.06013034,  1.12729012,  0.88584074,
            0.91995677,  0.82002708,  0.91612106,  0.86556894,  0.88014564,
            0.95764757,  0.96559535,  0.97882426,  0.70725389,  0.9273384,
            0.86511581,  0.85436928,  1.26804081,  1.02018914,  0.95359667,
            0.89336753,  0.91851577,  0.78166458,  1.02673106,  1.01340992,
            1.34916703,  0.77389899,  1.12009884,  0.94523179,  0.87991868,
            0.82919239,  0.98198121,  0.83653977,  0.91748611,  1.0642761,
            0.86964263,  0.86304793,  1.11500797,  0.7234409,   1.00464282,
            1.01835251,  0.73389264,  0.88471293,  0.85754755,  1.05383962,
            0.73121546,  0.85445808,  0.768308,    0.81396206,  1.01261272,
            0.76696225,  1.01770784,  0.76742866,  0.98390583,  0.96277488,
            0.87998292,  0.85264282,  1.12704234,  0.79612317,  0.92206712,
            1.09846877,  0.99874997,  0.87707457,  1.03404785,  1.00726392,
            0.91613763,  0.74242708,  0.80247702,  0.90702146,  0.81638055,
            0.78507729,  1.00066404,  0.84687328,  0.76488847,  0.89697089,
            0.82524207,  0.84940145,  1.022041,    0.75856559,  1.15434195,
            1.09781849,  0.93256477,  0.96021119,  1.00796782,  0.88193493,
            0.87902107,  0.82245196,  1.04739362,  1.133521,    0.82969043,
            1.01007529,  1.07135903,  0.981338,    0.86178089,  0.77930618,
            0.82512349,  1.2017057,   1.30452154,  1.12652148,  1.03670177,
            0.90631643,  0.74222362,  0.84452965,  0.86366363,  0.79192948,
            1.10288297,  0.9554774,   1.00912465,  0.95545229,  0.93584303,
            0.91604017,  0.91681165,  0.76792072,  1.66615421,  0.99044246,
            1.05068209,  0.88197497,  0.91153792,  0.82702508,  0.95182748,
            1.05320356,  0.8466656,   1.01676717,  0.65881123,  1.02589358,
            1.03902555,  1.00199915,  1.03022137,  0.93427176,  0.94600332,
            0.94594696,  0.86465228,  0.91241272,  0.72232997,  0.93380167,
            1.1960032,   0.87463367,  0.78428202,  0.88088,     0.97202961,
            0.99425528,  0.89567214,  0.84908979,  0.81004889,  0.85484368,
            0.68478631,  0.96563032,  0.78298607,  0.71894276,  0.88632131,
            0.8885966,   0.99235811,  0.84002222,  0.91265424,  0.91999157,
            0.89786651,  1.18062511,  0.92378385,  0.82501238,  1.09009807,
            0.96787582,  1.12456979,  0.86339677,  0.8786218,   0.89865768,
            1.02943564,  0.98886502,  0.97135566,  0.95914954,  1.05080931,
            0.76554446,  0.80142172,  0.99661393,  1.14749469,  0.93695459,
            0.95769957,  1.00811373,  1.00352699,  0.98747546,  0.99436785,
            1.10256609,  0.84366101,  0.85931876,  0.90745126,  1.04928733,
            0.84499693,  1.14018589,  1.2337188,   0.90516077,  0.84991869,
            0.72984467,  0.9729476,   0.97483938,  0.88626286,  1.02838695,
            0.89750089,  0.80324802,  1.40726294,  0.91149383,  0.86837826,
            1.21798148,  0.96459285,  0.71897535,  0.76230781,  0.88042964,
            0.8205186,   1.0517869,   0.74269565,  0.98278109,  1.1454159,
            1.03806052,  0.75238659,  0.94224089,  0.94931526,  1.24018529,
            0.99048689,  0.88108251,  0.81008694,  0.95443294,  0.99975781,
            0.83336879,  0.74422074,  0.87934792,  0.81994499,  0.98684546,
            0.82176924,  0.91652824,  0.77571479,  0.77039071,  0.9951089,
            0.92896121,  0.96234268,  1.00295341,  1.01455466,  0.75014075,
            0.95568202,  0.80995874,  1.24671334,  0.89480962,  0.81300194,
            0.76967074,  0.92514927,  0.89610963,  0.97441759,  1.19354494,
            0.87041262,  0.97344039,  0.88983828,  0.91614149,  0.85782814,
            0.78403196,  0.96665254,  0.91000054,  0.78641804,  0.96920714,
            0.89670528,  0.79247817,  1.04189638,  0.86777037,  1.18686087,
            0.79506403,  0.92389297,  0.76211023,  0.93617759,  0.91879446,
            0.8207635,   0.78984486,  0.93005953,  0.78743101,  0.9814347,
            0.94882561,  0.9577075,   0.81121566,  1.01025446,  0.90587214,
            0.94842798,  0.8811194,   1.01942816,  0.94698308,  0.92603676,
            0.86119014,  0.97543551,  0.84730649,  0.77552262,  0.97536054,
            0.96944817,  0.8736804,   0.86809673,  0.98134953,  1.16303105,
            0.81534447,  1.35930512,  0.83221293,  0.94136243,  0.76926289,
            1.05844282,  0.87783288,  0.78921971,  0.84360428,  0.78722128,
            1.00022607,  0.96779519,  0.95891975,  0.91900001,  1.07307813,
            1.03713093,  0.96257742,  0.90363152,  0.88729834,  0.91929215,
            1.00508255,  0.80838454,  0.92165553,  0.94513005,  0.95429071,
            0.80829571,  0.79531708,  1.01317347,  0.75337253,  0.85965134,
            0.77014567,  0.77680991,  0.77158741,  0.88882588,  0.91466414,
            0.82815897,  0.80251251,  1.04901425,  1.03386161,  1.3267075,
            1.12457236,  0.8267327,   0.89313417,  0.85992512,  0.93482733,
            0.83456348,  0.87991138,  0.8110149,   0.77913188,  0.89391799,
            0.73646974,  0.87038816,  0.99533506,  0.90744083,  0.98175496,
            1.17458551,  0.86718975,  0.93125366,  0.76131575,  0.90419708,
            0.95122171,  0.97531776,  1.05955142,  0.94714906,  0.79360281,
            1.02765349,  0.85192628,  0.84680852,  0.85470655,  0.94950982,
            0.75868699,  0.89731933,  1.00736877,  1.05171121,  0.73336848,
            0.97323586,  0.9848978,   1.27418684,  0.83954394,  0.73979357,
            1.06785996,  0.97832832,  0.7903268,   0.76600605,  0.94906446,
            0.81383465,  0.83620612,  1.00573379,  0.86359645,  0.9962139,
            0.98779432,  1.13793814,  1.02764992,  0.9070168,   0.81340349,
            0.94807089,  0.90499083,  0.83805736,  0.99623054,  0.91875275,
            0.95603557,  0.93156095,  0.83858677,  1.03667466,  1.01436655,
            0.85551979,  0.76227045,  0.84743986,  0.88487423,  0.93800365,
            0.8984666,   0.92600404,  0.89230381,  1.34625848,  1.10026015,
            0.9314026,   0.82450724,  1.0299575,   0.98494286,  1.07564492,
            0.96565301,  0.89677015,  1.15236174,  0.85476951,  1.00169288,
            0.90520725,  1.06235248,  1.04267637,  0.8311949,   0.82017897,
            0.81635968,  0.97246582,  0.84554172,  0.85409644,  1.18006461,
            0.96488389,  0.69228637,  0.97812108,  0.91764623,  0.86250551,
            0.91067775,  1.04692847,  0.94594707,  1.04351374,  0.9861303,
            0.92192581,  0.835444,    0.84362223,  1.13770705,  0.8075574,
            1.02260109,  1.13786456,  0.80862839,  0.89291687,  0.90278047,
            1.11613951,  1.29900454,  1.5622857,   0.70999772,  0.99692653,
            0.89109939,  0.77506441,  0.86054356,  0.99498141,  0.84222293,
            0.95213508,  0.91438286,  0.89305591,  0.9716793,   0.88609491,
            1.00275797,  0.90086022,  0.75336995,  1.1572679,   0.75952094,
            0.89203313,  0.82115965,  0.81459913,  1.02943406,  0.67063452,
            1.08707079,  0.92139483,  0.89855103,  0.89910955,  1.07169531,
            0.93684641,  0.84893365,  1.08659966,  1.43385982,  0.94788914,
            0.95277539,  0.94709274,  1.08412066,  0.90274516,  0.85147284,
            0.89327944,  0.92176174,  0.83820774,  0.90981839,  0.82303984,
            0.95189716,  0.95154905,  0.73628819,  1.18956148,  1.20224654,
            0.97666968,  1.08057375,  0.90369444,  0.98589538,  0.81426873,
            0.75127684,  0.93200745,  0.833666,    0.79532088,  0.91965037,
            0.99540522,  0.75449668,  0.85698312,  0.79328453,  0.94667443,
            0.7637764,   0.77203985,  0.73841377,  0.98587851,  1.34642268,
            0.78002774,  1.04356217,  1.02266882,  1.08936378,  0.9794388,
            1.07623423,  0.78069571,  1.12194495,  0.8072132,   0.91672662,
            1.36102062,  0.86933509,  1.15282756,  1.06219505,  0.80295502,
            1.00999033,  0.69418333,  0.93678452,  1.13002256,  0.91465628,
            0.73558316,  1.1302073,   0.85856238,  0.89450543,  1.11812369,
            0.75891878,  0.66859534,  0.97445338,  0.82210227,  0.76292085,
            0.79289499,  1.04380135,  0.95586226,  0.87480096,  0.81244036,
            0.86097575,  0.84111811,  0.85369732,  0.99160655,  0.90911501,
            0.81315845,  0.74037745,  1.04369233,  1.03535223,  1.18886682,
            0.87092491,  0.93562683,  0.92555142,  0.95268616,  0.9653025,
            0.93447525,  0.9043932,   1.25701034,  1.10354218,  0.96588129,
            0.94717991,  0.97010307,  0.78264501,  0.80991731,  0.98540974,
            0.83174886,  0.66966351,  1.01747376,  1.21553117,  0.80527296,
            1.06556826,  1.00870321,  1.03316522,  0.88994006,  0.89092714,
            0.94119254,  0.83930854,  1.01500087,  1.03581272,  0.97608081,
            1.11919255,  1.16586474,  0.85064102,  1.06070274,  1.00679658,
            0.75848826,  0.97969353,  0.94834777,  1.64970724,  0.82448941,
            1.02236919,  0.95252025,  0.98638842,  0.89094895,  0.95522527,
            0.91533774,  0.83716951,  0.92612154,  0.8662328,   0.9675949,
            0.96758398,  0.84309291,  0.95071171,  1.0165785,   0.96628063,
            1.00096151,  0.83175371,  0.79063043,  0.97371271,  0.76009001,
            1.02409279,  0.97232166,  0.8480577,   0.8982739,   0.9959743,
            0.96604729,  0.8681602,   0.99850841,  0.96162481,  1.01259965,
            0.98580061,  0.82751273,  0.90469122,  0.98254028,  0.78012425,
            0.87023012,  0.96830515,  0.9415831,   0.8591063,   0.82961507,
            0.89166083,  0.88509907,  0.95987837,  1.12356244,  0.71406404,
            0.99047619,  0.93735587,  0.80540831,  1.0024624,   0.95179491,
            0.83602101,  0.90343297,  0.90510417,  0.96477126,  0.79995299,
            0.93123762,  0.73763362,  1.0619498,   0.80929865,  0.86110233,
            0.84552556,  0.9943,      0.97085623,  0.75751174,  0.9201456,
            1.02268858,  0.9642899,   0.79078558,  1.03160502,  0.85200219,
            1.02246639,  1.08771483,  0.81997868,  0.82499763,  0.92767703,
            1.06700018,  0.7882174,   0.7789828,   0.89096139,  0.73155973,
            1.01717651,  0.91889525,  0.93256065,  0.84716063,  1.00965969,
            0.74505112,  0.80104245,  0.76003901,  0.96662605,  0.96594583,
            1.04571121,  0.97700878,  0.85461917,  0.9150222,   0.89110471,
            1.11183096,  0.98143747,  1.02346975,  0.9059266,   1.00771483,
            0.96336096,  0.93783898,  0.90545613,  1.10404183,  0.75297691,
            0.92548654,  0.79889783,  0.88177552,  0.93896814,  0.87309811,
            0.80691061,  0.89725699,  1.16586955,  0.98948281,  0.94524894,
            0.86085608,  0.76716851,  0.85362573,  1.09936882,  0.9328761,
            0.74819673,  0.94331186,  0.81077304,  0.88610499,  1.01452015,
            0.91513953,  0.92846128,  0.93539081,  0.8946682,   0.9270336,
            0.96673629,  0.9897488,   1.11891899,  0.87551585,  0.85854576,
            1.13458763,  1.11450768,  0.79887951,  1.091154,    1.04180374,
            0.79252573,  0.90484245,  0.94221016,  0.95721137,  0.86776103,
            0.97167404,  0.83404166,  0.94634038,  0.98907413,  0.92321459,
            1.03547804,  0.79660212,  0.94870239,  0.70027204,  0.79841059,
            0.92563393,  1.4385341,   0.8331731,   0.844816,    0.97851389,
            1.24048695,  0.83765698,  0.83600835,  1.13901283,  1.05994936,
            0.84292427,  0.86759056,  0.9272156,   0.77375499,  0.99972839,
            0.95570976,  0.97879539,  0.95528351,  0.84555495,  0.95296134,
            0.87469056,  0.78862024,  0.793795,    0.8516853,   0.92816818,
            1.02492208,  0.8037345,   0.95481283,  0.75138828,  0.72110948,
            1.36815666,  0.9661646,   0.81651816,  0.87764538,  0.97397297,
            0.99845266,  0.77433798,  0.9266279,   1.92493013,  1.07588789,
            0.90412593,  1.03165475,  1.00826548,  0.75500744,  0.87198881,
            0.86871262,  0.97854606,  0.80954477,  0.84130266,  0.89674826,
            1.43926644,  0.74873088,  1.01894282,  0.93606154,  1.08241489,
            0.76626357,  0.97434747,  0.82824599,  1.00267494,  0.97168761,
            1.06433173,  1.22741978,  1.46998419,  0.9521923,   0.98276685,
            0.92422781,  1.14241216,  1.13339577,  1.05586816,  1.04923068,
            0.83364505,  0.98007268,  0.94322393,  0.84310173,  1.03481955,
            1.18281181,  0.79807678,  0.840274,    1.00344058,  1.09442855,
            0.88033836,  0.86189964,  1.1395012,   1.18808865,  0.78667714,
            1.09323293,  0.81511099,  0.95830848,  0.99637275,  0.9146258,
            0.96358155,  0.79048719,  0.80395604,  1.00828722,  0.92872342,
            0.98789363,  0.96720252,  0.80541021,  0.73697557,  0.86692999,
            0.86795696,  1.1516694,   0.95911714,  1.13981603,  1.02002866,
            0.90808456,  0.94208296,  0.93691739,  0.87653118,  0.72824225,
            0.78177906,  1.2139146,   0.83405505,  0.91764545,  0.83318595,
            0.77930256,  0.86499397,  0.95599882,  0.73850016,  0.9630604,
            0.97913407,  1.1790714,   0.94994057,  1.04379512,  0.80815459,
            1.16560205,  0.97486893,  1.02780804,  1.10633754,  0.78679252,
            0.94643528,  1.19999119,  0.98621069,  0.8899674,   0.89235261,
            0.8728921,   0.77089094,  0.8492628,   0.86905159,  0.90741875,
            0.81065291,  0.91208596,  1.04616696,  1.24291958,  0.98628605,
            0.99751975,  0.83249612,  0.96343385,  0.77862866,  0.72381238,
            1.17384381,  1.06013687,  0.73460652,  1.09554763,  0.82015886,
            0.90862905,  0.89037104,  0.7866143,   0.8570287,   0.75061334,
            0.94950855,  0.8091383,   1.04055212,  0.96679573,  0.78338675,
            0.75968533,  1.00495071,  0.6491633,   1.02802735,  1.00725883,
            0.89333988,  0.87539291,  0.99374251,  1.10241119,  1.14935785,
            0.9369769,   0.84772646,  1.05024743,  0.97411124,  0.76972352,
            0.92161017,  0.88689841,  0.78598549,  0.93400036,  1.14699647,
            0.98636563,  0.93051079,  1.00131515,  0.82749213,  0.96665447,
            0.84457933,  0.95172036,  0.86372572,  0.97034285,  0.99877807,
            0.8724721,   0.86281118,  0.96253742,  1.13485439,  1.03410559,
            0.83113167,  1.02644607,  1.0669284,   0.947969,    1.13373538,
            0.85495039,  1.15829218,  0.72662405,  0.81755747,  0.78381403,
            0.84360371,  1.10945791,  0.80215303,  0.8861351,   0.97484684,
            1.02996282,  0.86219328,  0.95675062,  1.10753315,  0.92496918,
            0.79323289,  0.76891191,  0.93106762,  0.94523682,  0.9534338,
            0.8954424,   0.81732651,  1.00443776,  0.96178195,  0.89727229,
            0.88917552,  0.88660003,  0.941933,    1.03900381,  0.75262915,
            0.94265862,  0.84472046,  1.09834757,  0.81516259,  0.90865634,
            0.9582531,   0.99819053,  0.8815072,   0.92425525,  0.79085083,
            0.98173446,  0.95199169,  0.71653726,  1.11863725,  0.97855807,
            0.87873181,  1.37925403,  0.8085008,   1.40027689,  0.79367826,
            0.82070449,  0.87039383,  0.95896081,  0.75617612,  1.3196712,
            0.9335008,   0.9461447,   1.0838461,   0.83347962,  0.69558254,
            0.92358528,  0.99423247,  0.94884494,  0.75094955,  0.90429063,
            1.13740548,  0.89354463,  1.13094104,  1.7373979,   0.87808028,
            0.72820621,  1.02995089,  0.80134468,  0.97511989,  0.93823103,
            0.98097787,  0.73179813,  0.93764192,  1.04399599,  0.95644709,
            0.80476939,  0.87463727,  0.83220517,  0.76978546,  0.97056432,
            1.1693819,   1.0368387,   0.98606478,  1.03538075,  0.88253058,
            0.91105775,  0.93745618,  0.80272442,  0.77045021,  0.8482449,
            1.04505306,  0.90427753,  0.706451,    1.02687396,  0.82931474,
            1.24255717,  0.91343217,  0.8692726,   0.98422894,  0.82142068,
            0.86854354,  0.77715916,  0.94490329,  0.97686366,  1.05198512,
            0.888989,    1.09252847,  0.8034292,   1.04727187,  0.87246831,
            0.89474556,  1.06031526,  0.93056174,  0.7747956,   0.87772054,
            1.1183045,   0.78938083,  0.82019511,  0.82553273,  1.04324276,
            0.7676436,   0.68914756,  0.88400598,  0.79611901,  0.77011016,
            0.76727015,  0.84523666,  1.09972447,  1.03942974,  1.07322466,
            1.01079248,  1.03469338,  0.90450148,  0.87367007,  0.88432601,
            0.85312482,  0.7328442,   1.12256832,  0.8837547,   0.81023384,
            0.87068285,  0.94466637,  1.13236695,  0.95958423,  0.8099625,
            1.07509372,  1.03306035,  0.99385633,  1.06433672,  1.07385915,
            0.92709455,  1.03502217,  0.88961476,  0.8307198,   0.98819038,
            1.09916368,  0.8919766,   0.90349117,  0.97554616,  0.98376763,
            0.89285893,  0.99941071,  1.16078972,  0.66336693,  1.16389515,
            1.10395069,  1.20381952,  0.98928899,  1.17155389,  0.81707565,
            0.82903836,  0.95892646,  0.8437454,   0.79017432,  0.81562954,
            0.65169124,  0.87950793,  0.9017879,   0.82160564,  0.87079127,
            0.88100146,  1.00783979,  0.84102603,  1.16817499,  0.97697533,
            0.89115235,  0.77254376,  0.7679024,   0.97093775,  1.13881665,
            0.90348632,  1.14654277,  1.08625707,  0.98787902,  1.49057495,
            0.99639001,  0.97623973,  0.74807856,  0.76656108,  0.79095998,
            1.04583503,  0.95124469,  0.90228738,  1.03129265,  1.02663212,
            0.67704952,  0.95335397,  1.01726294,  0.78765385,  0.91140255,
            1.04097119,  0.71881619,  1.14572601,  0.79708798,  1.07104057,
            0.95925248,  0.72556831,  0.92256392,  1.08702165,  0.95977251,
            0.99670254,  0.95276505,  1.15268752,  0.68215678,  1.05573208,
            0.89672437,  0.89396611,  1.01814905,  0.81969778,  0.74390457,
            1.20909881,  0.82388701,  1.00574083,  1.01348114,  1.01492015,
            0.94759788,  0.99758684,  1.19912008,  0.92749943,  1.16660441,
            0.97646538,  0.8189475,   0.97464158,  1.01050799,  0.94368665,
            0.70995047,  0.94469581,  1.02534612,  1.3513094,   0.88081968,
            1.00576693,  0.9695495,   1.0549135,   1.29993316,  0.91050559,
            0.95543198,  1.02161725,  0.76895773,  1.03685293,  0.88201449,
            0.90345561,  1.02793048,  1.00267831,  0.84653161,  0.9217411,
            0.94666576,  0.94946561,  0.77482488,  0.94358305,  0.89779666,
            1.01462131,  1.05829923,  1.13217729,  1.12260175,  0.89810828,
            0.96305689,  0.90466377,  0.8091617,   0.93070824,  1.03997521,
            1.04076373,  0.95858477,  0.94382748,  0.7585222,   1.22890096,
            0.97300529,  0.87424719,  0.90435141,  0.91894865,  0.97819677,
            0.80300175,  1.03729016,  1.19305569,  0.81633791,  0.7930351,
            0.8141721,   0.86764479,  0.89207142,  0.89691482,  0.86243171,
            0.91184679,  0.94284352,  1.01357831,  1.03806277,  0.92000143,
            0.91018767,  0.90555137,  0.89089532,  1.3530331,   0.96933587,
            0.82350429,  0.71549154,  1.13399156,  0.87838533,  0.99177078,
            0.93296992,  1.43078263,  0.90278792,  0.85789581,  0.93531789,
            0.84948314,  0.95778101,  0.80962713,  0.88865859,  1.15297165,
            0.85695093,  0.88601982,  0.96665296,  0.9320964,   1.04193558,
            1.006005,    0.78939639,  0.79344784,  0.87012624,  0.8532022,
            0.93351167,  0.91705323,  0.74384626,  0.84219843,  0.78265573,
            1.07759963,  1.0236098,   1.00202257,  1.18687122,  1.00869294,
            0.8809502,   0.76397598,  0.81845324,  0.97439912,  1.10466318,
            1.10678275,  0.96692316,  0.84120323,  1.13151276,  0.72574077,
            0.82457571,  0.8179266,   1.01118196,  0.84303742,  0.86255339,
            1.03927791,  0.82302701,  1.03586066,  0.75785864,  0.9186558,
            0.97139449,  0.92424514,  1.00415659,  1.08544681,  0.80940032,
            0.9073428,   0.83621672,  1.04027879,  0.79447936,  0.94829305,
            1.16176292,  1.11185195,  0.88652664,  0.98676451,  0.89310091,
            0.72272527,  0.79963233,  0.94651986,  0.91540761,  1.0498236,
            0.84938647,  1.15539602,  1.03118991,  0.86565049,  0.77764016,
            0.77866522,  0.78008955,  0.89062575,  0.81285464,  0.92554114,
            1.08747324,  0.84338687,  0.76746516,  0.99205474,  0.86649541,
            0.97586166,  0.9721711,   1.14895298,  1.04659345,  1.0605085,
            1.06392238,  1.08286448,  0.93612266,  0.82545354,  0.84305431,
            0.83650404,  1.11073704,  0.91760695,  0.83281572,  0.84244131,
            1.05843708,  0.94695861,  0.95469608,  0.96038612,  0.81373042,
            0.94943303,  1.00824522,  0.86416102,  0.87121008,  1.04208739,
            0.81171276,  1.12798927,  0.99122576,  0.80626996,  1.07103151,
            0.99809277,  1.08490135,  0.9441509,   0.98766371,  1.33205139,
            0.92145678,  0.88112784,  0.9297591,   1.17549838,  0.8481953,
            0.96359948,  0.98478935,  0.77028684,  0.86408555,  0.92863805,
            0.94593549,  0.78705212,  1.1923026,   0.9983487,   0.99152533,
            0.95313678,  1.01847515,  1.05728959,  0.88009142,  1.00351951,
            1.00549552,  0.81671365,  0.90545602,  0.77895202,  0.82217088,
            0.94838645,  0.85928327,  0.90729044,  0.92975916,  0.91946285,
            0.80537364,  1.11885357,  0.84691232,  0.85356231,  0.85102988,
            1.06499659,  1.0242127,   0.91245632,  0.83131215,  0.72151085,
            0.9295769,   0.89549018,  0.87914839,  0.93541175,  0.97319188,
            0.791944,    1.08008186,  0.79549907,  0.90967683,  0.80506028,
            1.1206821,   0.91258859,  1.24855319,  0.96112955,  1.14305514,
            0.79327927,  0.84209204,  0.94494251,  0.89573237,  1.0571304,
            0.94504292,  0.84446547,  0.92060829,  0.82347072,  0.86280426,
            0.85516098,  0.78649432,  0.89522516,  0.94529795,  0.90322825,
            0.9616288,   0.77439126,  1.0130917,   0.84021262,  0.97337238,
            0.93206526,  0.93809914,  0.87626441,  0.92706652,  0.86819358,
            0.74060652,  0.84046045,  0.94130171,  0.92537388,  0.80485074,
            0.81633347,  0.76401825,  0.81300784,  0.8052467,   1.27234895,
            0.92674704,  1.12106762,  0.91743016,  0.94694287,  0.87309918,
            0.99163895,  0.83777703,  0.89713459,  0.88208343,  0.90205904,
            0.9708827,   0.94965009,  0.81446019,  0.89512677,  0.97025135,
            1.02314481,  0.88399736,  1.01059963,  0.86193889,  0.94621507,
            0.97334837,  0.90122433,  0.71015398,  1.17491792,  1.13869784,
            1.03908735,  0.85480742,  0.98971408,  1.04147459,  0.85170846,
            0.94861439,  0.7778831,   0.73445723,  0.89587488,  0.88627975,
            0.98253057,  0.86159356,  1.06559385,  0.90852704,  0.86562284,
            0.92122779,  0.98233847,  0.94989946,  0.97171474,  0.92428639,
            1.03712828,  0.88170861,  0.86802004,  0.79670394,  0.85606075,
            1.09636421,  0.85048902,  0.99393971,  1.10510884,  0.80515088,
            0.95559246,  0.96803475,  0.98115871,  0.94603995,  0.8654312,
            0.90759845,  0.9010954,   0.77979965,  0.83322032,  0.8485444,
            0.89217626,  0.78817966,  1.03815705,  0.84076982,  0.93362471,
            1.06173045,  0.82612852,  0.8336989,   0.93943901,  0.91775212,
            1.00501856,  1.04269442,  0.93195426,  0.78377288,  1.03372915,
            0.8415154,   1.02888978,  0.93202174,  0.78683383,  0.85106996,
            0.9724203,   0.93409182,  0.97876305,  1.17153649,  0.9434591,
            0.81361398,  1.09554602,  1.48193137,  0.96349931,  0.93586569,
            1.0210303,   0.88980694,  0.88890459,  1.05330284,  1.09511186,
            0.91202441,  0.78753378,  0.98074421,  1.04268892,  1.14265114,
            0.86482628,  0.87233851,  1.18915875,  0.82556032,  0.87461473,
            1.08396187,  0.69206719,  0.88113605,  0.96951674,  0.89248729,
            0.909926,    0.82966779,  0.8261611,   0.9551228,   0.79879533,
            1.09416042,  1.01020839,  1.04133795,  1.09654304,  0.84060693,
            1.02612223,  1.00177693,  0.90510435,  1.2091018,   1.03290288,
            0.80529305,  0.74332311,  1.04728164,  1.04647891,  0.83707027,
            0.81648396,  1.07180239,  0.7926372,   0.99855278,  1.16851397,
            0.94566149,  0.75612408,  0.94975744,  0.92924923,  1.03215206,
            0.82394984,  0.84142091,  0.88028348,  1.11036047,  0.82451341,
            0.83694112,  0.84207459,  0.94095384,  1.00173733,  1.10241786,
            0.86609134,  0.86859604,  1.1211537,   0.84188088,  0.89023025,
            0.99062899,  0.96828743,  0.80106184,  0.86745454,  0.99013196,
            0.91838615,  0.86400837,  0.95679525,  0.78893711,  1.03753175,
            0.97177648,  0.88685941,  0.9441012,   0.69289996,  0.84219432,
            1.01050959,  0.83578317,  0.79907595,  1.21281139,  0.91613925,
            1.00202544,  0.95293036,  0.84583258,  0.84574886,  0.76470341,
            1.23606485,  1.10063291,  0.93852084,  0.97201415,  0.68523403,
            0.94560108,  0.81903039,  1.14332074,  0.80914367,  1.46398921,
            0.85155227,  1.41106313,  0.85740937,  0.91107708,  0.9003576,
            0.94132363,  0.85710825,  0.74805485,  1.2521402,   0.95307547,
            0.94274593,  0.86732331,  0.83850172,  0.96835288,  1.09443821,
            0.68532627,  0.84736457,  1.06989165,  0.81424504,  1.02942437,
            0.80255995,  0.89258275,  0.93560962,  1.04192911,  1.13498644,
            1.24409985,  0.93295415,  1.08360355,  1.16468059,  0.81482388,
            0.92387137,  1.07508578,  0.86564567,  1.0142773,   0.86143907,
            0.91214944,  0.9757589,   0.90588817,  0.74168224,  0.91222552,
            0.96119617,  0.95431519,  0.78080736,  1.0327991,   1.05112022,
            0.92761155,  1.0183631,   0.73188757,  0.85617225,  0.93341155,
            0.95106173,  0.9481304,   0.92996766,  1.08092599,  0.96485228,
            0.97964284,  0.94224551,  1.00654477,  1.01367565,  0.89785325,
            0.80725703,  0.7495798,   0.78240339,  1.04479122,  0.88200252,
            1.0664992,   1.05951775,  0.82508097,  0.81201381,  0.81860218,
            1.07561763,  1.02830358,  0.87348993,  1.0081337,   0.87470565,
            1.45597242,  0.77540871,  0.8036279,   0.80514427,  0.92688461,
            0.88152328,  1.56288788,  0.87251203,  0.92808414,  1.03548911,
            0.65226699,  0.81243827,  1.03103554,  1.11995602,  0.78956176,
            0.96734427,  0.91600861,  0.8246106,   1.09390498,  0.98187349,
            0.8919928,   0.98746862,  0.96298125,  0.93854424,  0.83060031,
            0.74692856,  0.99757209,  0.78888849,  1.17517182,  1.06657933,
            1.1244446,   0.93608433,  0.88898472,  0.96823218,  0.87496056,
            0.81776683,  0.98863687,  0.82962648,  1.02395766,  0.99622674,
            1.07138771,  0.86669915,  0.98172208,  0.8787271,   0.86125353,
            0.79554881,  0.93382729,  1.00706175,  1.08386454,  0.69664542,
            0.77316657,  0.79978147,  0.80764736,  0.9969375,   0.83554928,
            0.91017317,  0.95323454,  1.29872357,  1.08851275,  1.01673108,
            0.79536208,  0.84878371,  0.95165619,  0.87733936,  0.86319684,
            0.96758495,  0.87763237,  0.95094713,  1.00143077,  1.0596993,
            1.27278299,  0.82281481,  0.89765404,  0.94538181,  0.88161857,
            0.77679456,  0.84274277,  0.89864342,  0.98705162,  0.95456512,
            0.92712401,  0.77427128,  1.03292269,  0.87034158,  1.24316113,
            0.98278702,  1.17325118,  1.18863971,  0.88678137,  0.90389731,
            1.01740421,  0.80228624,  0.97742223,  0.82741518,  0.8359407,
            0.7177401,   1.02297899,  0.81896048,  0.77127181,  0.83328601,
            0.96939523,  0.94073198,  0.90356023,  1.12355064,  1.12811114,
            0.92403138,  1.05423548,  0.70827734,  0.95891358,  0.89898027,
            1.02318421,  0.93775375,  0.8245529,   0.80604304,  0.77555283,
            0.92112699,  0.85662169,  0.92725859,  0.93599147,  0.78971931,
            0.8337306,   0.93775212,  0.91025099,  0.75308822,  0.95391173,
            0.96840576,  0.8394416,   0.89087015,  0.73703219,  0.97812386,
            0.8787356,   0.93985266,  0.96406021,  0.88666152,  0.89242745,
            0.97900374,  0.85697634,  0.8795755,   0.78581812,  0.87138735,
            0.74602994,  0.96158936,  0.84529806,  0.85333232,  1.06116542,
            1.05929382,  1.09720986,  1.28959453,  0.91541148,  0.87657407,
            1.06514793,  0.8668096,   1.07325125,  0.85009534,  0.95542191,
            0.86977409,  0.96249874,  0.97715908,  0.89360331,  0.98859647,
            0.67560717,  0.90213348,  1.12051182,  0.99684949,  0.9863559,
            1.32246221,  0.84632664,  0.89707447,  1.00486846,  0.90843649,
            1.02399424,  0.97899017,  0.95693977,  0.8384806,   0.93927435,
            0.79153251,  1.08694094,  1.01785553,  0.99674552,  0.898566,
            0.94116882,  0.95224977,  0.99859129,  0.81125029,  0.85985586,
            1.14418875,  0.96306241,  1.31398561,  0.77961419,  1.01958366,
            0.9575668,   0.771084,    1.04473363,  1.01569517,  1.04560744,
            0.9648178,   0.93466398,  1.09313672,  0.90349389,  1.00193114,
            0.79991514,  0.91102351,  0.9795356,   0.89285193,  1.04898573,
            0.93031782,  0.95087069,  1.15644699,  0.91155375,  0.93005986,
            0.70098757,  0.82751625,  0.85462106,  1.34969332,  0.93382692,
            1.05558387,  1.25417819,  1.0546501,   1.05217032,  0.86031346,
            1.00864463,  0.73592482,  1.01899722,  1.00462831,  0.96882832,
            0.81334751,  1.05102745,  0.82288113,  1.05798623,  0.77971966,
            1.38584414,  1.0248193,   0.78951056,  0.76171823,  0.78407227,
            1.14808104,  0.97890501,  0.99870905,  0.96006489,  0.78442704,
            0.99315422,  0.83653213,  0.95210661,  0.97233777,  0.78140495,
            0.95996216,  0.76318841,  0.82333311,  0.87123204,  0.79531258,
            0.82681452,  1.00492217,  0.93549261,  1.00240153,  1.02086339,
            1.00424549,  0.87437775,  0.84675564,  0.98014462,  0.77262117,
            1.02620976,  0.91162462,  1.0275041,   1.1475431,   0.78167746,
            0.86273856,  0.84499552,  0.99712362,  0.9694771,   0.94523806,
            0.8450763,   0.93068519,  1.29362523,  1.0249628,   1.05522183,
            1.13433408,  1.06981137,  0.85666419,  0.98203234,  0.75867592,
            0.8844762,   0.89708521,  0.75482121,  0.80137918,  0.90412883,
            0.88815714,  1.11497471,  0.77441965,  0.93853353,  0.8962444,
            0.83055142,  0.99776183,  0.92581583,  0.78783745,  0.90934299,
            0.81136457,  0.99000726,  0.9669203,   1.2890399,   1.01923088,
            1.11076459,  1.01331706,  1.02470946,  0.92950448,  1.10298478,
            1.03723287,  1.09129035,  0.95138186,  0.85764624,  0.86606803,
            0.8141785,   1.0129293,   0.93267714,  0.95663734,  1.01940702,
            0.8072268,   1.0707215,   0.90482063,  1.01546955,  0.84018308,
            0.95938216,  0.96454054,  0.93114659,  1.09705112,  0.88720628,
            0.81067916,  0.82667413,  0.89494027,  0.9173495,   0.73326273,
            1.00209461,  0.9560545,   1.09126364,  0.95709908,  0.81314274,
            0.8274943,   1.37605062,  0.99097917,  1.02221806,  0.90277482,
            1.01611791,  0.79663017,  1.16686882,  1.19669266,  0.88366356,
            0.77661102,  0.73467145,  1.15438391,  0.91439204,  0.78280849,
            1.07238853,  1.03588797,  1.0438292,   0.75935005,  0.76200114,
            0.81603429,  0.74402367,  1.1171573,   0.90227791,  0.94762351,
            0.92462278,  0.8847803,   1.1343863,   0.8662186,   1.00410699,
            1.05008842,  0.94783969,  0.89555844,  0.98278045,  0.80396855,
            1.00483139,  0.82540491,  0.83284354,  0.93132265,  0.91191039,
            0.95753995,  1.18260689,  0.84124197,  0.87429189,  0.67617592,
            0.89495946,  0.92898357,  1.10528183,  1.06994417,  0.82259834,
            0.74746328,  0.99070832,  1.07386274,  0.84007203,  0.89720099,
            0.9670094,   1.02728082,  0.78001838,  0.97709347,  0.90602469,
            1.49985196,  0.80256976,  1.05905677,  0.98298874,  0.94679703,
            0.94305923,  0.98720786,  0.82091251,  0.91644161,  0.79576881,
            0.98942172,  0.92974761,  0.99307545,  0.86959859,  0.88549807,
            1.09246144,  0.87265047,  1.01449921,  0.74353851,  0.95029192,
            0.94385304,  0.84779449,  1.00690543,  0.79727923,  0.92285822,
            0.83164749,  1.06508941,  1.09757529,  0.9059649,   0.9146043,
            0.74474669,  0.71306438,  0.77989422,  0.84965464,  0.9424323,
            0.82492634,  0.85076686,  1.01110574,  1.01445751,  0.87929754,
            0.8773275,   0.72314196,  0.92285502,  1.18173931,  0.86460799,
            0.91795108,  1.16580482,  0.79880497,  0.72734786,  0.97579653,
            0.76967834,  0.97543732,  1.04996964,  1.16439594,  1.08656546,
            1.15644902,  0.98333436,  1.24374723,  0.95810117,  0.8488915,
            1.06288523,  0.99055893,  0.75517736,  0.95856183,  0.85574796,
            1.00426506,  1.25275675,  0.92735225,  0.83351314,  0.90216604,
            0.87996386,  1.13312875,  1.00891523,  0.76513657,  0.85659621,
            0.91142459,  1.05893495,  0.92253051,  0.87153684,  1.03190013,
            0.92160845,  1.01768282,  0.80590054,  1.05172907,  0.92758177,
            0.86902046,  0.93927127,  0.80389584,  0.96016014,  0.9720314,
            0.93255573,  0.85792534,  0.97826842,  0.80506149,  0.97170364,
            1.08397772,  1.01866333,  1.18898045,  1.02855427,  0.94848891,
            0.94336541,  0.93119013,  0.92907817,  1.11806635,  0.88409637,
            0.88809707,  1.06735612,  0.98447974,  0.88816438,  1.00099784,
            0.92443453,  1.00325146,  0.86977836,  0.84621801,  0.92361073,
            0.85573903,  0.77309241,  0.86717528,  1.19892035,  1.07497019,
            1.02178857,  0.8718756,   0.90646803,  0.92912096,  1.04538692,
            0.95245707,  0.99698525,  0.94583199,  0.92537599,  0.86720487,
            0.89927054,  0.86111792,  0.94401208,  1.01130191,  1.03759681,
            0.8177749,   1.07784373,  0.79823294,  1.00839713,  1.39409602,
            0.87146241,  1.21218822,  0.84895926,  1.01742432,  0.8044077,
            0.78632084,  1.07751744,  1.13147508,  0.90268302,  0.90024653,
            0.92072578,  0.87763264,  1.00736787,  0.90978808,  0.90895492,
            0.90766826,  0.98956566,  0.92075658,  0.77613105,  0.93815569,
            0.95455546,  1.00607757,  0.82187828,  0.94197599,  0.867015,
            0.90709762,  0.75604815,  0.91312261,  0.9286002,   0.74623204,
            0.87368702,  0.83879278,  0.92224793,  0.81676402,  0.90355168,
            0.92762955,  0.91784037,  0.82273304,  0.75947806,  0.92687078,
            0.87971276,  1.15037445,  0.86707445,  0.8611453,   0.91921763,
            1.07088129,  1.05150864,  1.02162325,  0.90305964,  0.99912687,
            0.87693204,  0.6186911,   0.95526533,  1.15975655,  1.00061222,
            0.74608861,  0.954568,    0.84965574,  0.79177899,  0.9741051,
            1.0119514,   0.79147502,  0.81367071,  0.87757421,  1.01270813,
            0.86044808,  0.9689615,   0.9577413,   0.79480242,  0.76073002,
            0.83131288,  0.96379259,  0.84679732,  0.82508685,  0.89977283,
            0.86766439,  1.12231836,  0.93058445,  1.04584181,  0.88838751,
            0.96615893,  0.98731619,  1.05517799,  1.02860493,  0.98881473,
            0.85210319,  0.91497438,  0.9275787,   0.97456134,  0.9011687,
            0.69417417,  0.89661214,  0.79038577,  1.08118303,  1.0509366,
            0.97813138,  0.85714945,  0.97330329,  0.83611871,  0.99772489,
            0.83591193,  0.75592677,  0.85392601,  1.02734573,  0.72404609,
            0.83534547,  0.91630472,  0.88463459,  1.12044562,  1.10991104,
            0.96047701,  1.12342573,  0.72046647,  0.96852239,  0.89605698,
            0.98310243,  0.92300659,  0.87794646,  0.83109321,  1.43297752,
            0.80609029,  0.8692251,   0.90254649,  0.81647796,  1.07521371,
            1.03942973,  0.96156488,  1.25225334,  1.0265727,   0.9518054,
            0.87765718,  1.15552582,  0.79577766,  0.66849239,  0.87236017,
            1.03437641,  0.98567811,  0.78463682,  1.09573491,  0.89858959,
            0.94056747,  1.16075317,  1.06296054,  0.85844006,  0.95475376,
            0.67038747,  0.7924646,   0.94009167,  0.88282093,  0.97711174,
            0.9209607,   1.03230176,  0.99981312,  1.12345314,  1.11705968,
            1.02453864,  0.91724212,  0.98337942,  0.89195196,  0.83800177,
            0.95044243,  0.76543521,  0.8613025,   0.83907753,  0.69333275,
            0.84411739,  0.68621941,  0.9847701,   1.13328481,  1.1432074,
            0.97156328,  0.86464461,  0.74258211,  0.97319505,  1.11453917,
            0.87344741,  0.91382664,  1.01635943,  1.38708812,  0.81377942,
            1.3828856,   0.74476285,  0.86657537,  1.1216954,   0.91008346,
            0.800862,    0.98356936,  0.92409916,  1.13970543,  0.97547004,
            0.99385865,  1.16476579,  0.78678084,  1.003947,    0.81491463,
            1.19724322,  0.9173622,   0.93274116,  0.80047839,  0.86798029,
            0.9433708,   0.82376832,  1.01726905,  0.81914971,  0.73290844])


class Medpar1:
    '''
    The medpar1 data can be found here.

    https://www.stata-press.com/data/hh2/medpar1
    '''
    def __init__(self):
        filename = os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                "stata_medpar1_glm.csv")
        data = pd.read_csv(filename).to_records()
        self.endog = data.los
        dummies = pd.get_dummies(data.admitype, prefix="race", drop_first=True,
                                 dtype=float)
        design = np.column_stack((data.codes, dummies)).astype(float)
        self.exog = add_constant(design, prepend=False)


class InvGaussLog(Medpar1):
    """
    InvGaussLog is used with TestGlmInvgaussLog
    """
    def __init__(self):
        super().__init__()
        filename = os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                "medparlogresids.csv")
        self.resids = pd.read_csv(filename, sep=',', header=None).values
        self.null_deviance = 335.1539777981053  # from R, Rpy bug
        self.params = np.array([0.09927544, -0.19161722,  1.05712336])
        self.bse = np.array([0.00600728,  0.02632126,  0.04915765])
        self.aic_R = 18545.836421595981
        self.aic_Stata = 6.619000588187141
        self.deviance = 304.27188306012789
        self.scale = 0.10240599519220173
        # self.llf = -9268.9182107979905  # from R
        self.llf = -12162.72308108797  # from Stata, big rounding diff with R
        self.bic_Stata = -29849.51723280784
        self.chi2 = 398.5465213008323   # from Stata not in sm
        self.df_model = 2
        self.df_resid = 3673
        self.fittedvalues = np.array([
            7.03292237,  7.03292237,  7.03292237, 7.03292237,  5.76642001,
            7.03292237,  7.03292237,  6.36826384,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  5.76642001,  7.03292237,
            5.22145448,  7.03292237,  5.22145448,  4.72799187,  4.72799187,
            7.03292237,  7.03292237,  6.36826384,  7.03292237,  5.76642001,
            7.03292237,  4.28116479,  7.03292237,  7.03292237,  7.03292237,
            5.76642001,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  3.87656588,  7.03292237,  7.03292237,  4.28116479,
            7.03292237,  7.03292237,  4.72799187,  7.03292237,  7.03292237,
            7.03292237,  5.22145448,  6.36826384,  6.36826384,  4.28116479,
            4.72799187,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            5.22145448,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  5.76642001,  6.36826384,
            6.36826384,  5.22145448,  7.03292237,  7.03292237,  7.03292237,
            5.76642001,  7.03292237,  7.03292237,  3.87656588,  5.76642001,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            5.22145448,  5.22145448,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  7.03292237,  4.72799187,  7.03292237,  6.36826384,
            7.03292237,  6.36826384,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  5.22145448,  6.36826384,  5.22145448,
            7.03292237,  7.03292237,  4.72799187,  5.76642001,  7.03292237,
            4.72799187,  6.36826384,  3.87656588,  7.03292237,  7.03292237,
            5.22145448,  5.22145448,  6.36826384,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  4.28116479,
            7.03292237,  6.36826384,  6.36826384,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            6.36826384,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  5.76642001,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  5.76642001,
            7.03292237,  7.03292237,  6.36826384,  3.87656588,  7.03292237,
            7.03292237,  5.22145448,  7.03292237,  5.76642001,  4.28116479,
            5.76642001,  6.36826384,  6.36826384,  7.03292237,  7.03292237,
            5.76642001,  7.03292237,  7.03292237,  4.28116479,  7.03292237,
            6.36826384,  7.03292237,  6.36826384,  7.03292237,  5.22145448,
            7.03292237,  4.28116479,  4.72799187,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  6.36826384,
            7.03292237,  4.28116479,  5.22145448,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  4.28116479,  7.03292237,  7.03292237,
            7.03292237,  6.36826384,  5.76642001,  7.03292237,  7.03292237,
            7.03292237,  4.72799187,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            6.36826384,  7.03292237,  6.36826384,  4.28116479,  5.76642001,
            5.22145448,  6.36826384,  6.36826384,  7.03292237,  7.03292237,
            7.03292237,  6.36826384,  7.03292237,  7.03292237,  6.36826384,
            5.76642001,  7.03292237,  5.22145448,  5.76642001,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            4.28116479,  7.03292237,  5.22145448,  7.03292237,  6.36826384,
            5.76642001,  4.28116479,  4.28116479,  7.03292237,  6.36826384,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  4.28116479,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  5.22145448,
            7.03292237,  7.03292237,  7.03292237,  5.22145448,  7.03292237,
            5.76642001,  7.03292237,  4.72799187,  4.28116479,  6.36826384,
            5.76642001,  7.03292237,  7.03292237,  7.03292237,  5.22145448,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            3.87656588,  4.72799187,  7.03292237,  7.03292237,  7.03292237,
            4.72799187,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  6.36826384,  3.87656588,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  4.28116479,  7.03292237,  6.36826384,
            7.03292237,  5.22145448,  5.22145448,  6.36826384,  7.03292237,
            6.36826384,  6.36826384,  7.03292237,  4.28116479,  7.03292237,
            7.03292237,  7.03292237,  5.22145448,  6.36826384,  7.03292237,
            3.87656588,  6.36826384,  5.22145448,  5.76642001,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  4.28116479,  7.03292237,
            5.22145448,  7.03292237,  6.36826384,  5.22145448,  4.72799187,
            7.03292237,  7.03292237,  7.03292237,  4.72799187,  6.36826384,
            7.03292237,  6.36826384,  5.76642001,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  5.22145448,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  3.87656588,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  4.72799187,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  6.36826384,  7.03292237,  7.03292237,
            6.36826384,  7.03292237,  6.36826384,  7.03292237,  5.22145448,
            6.36826384,  7.03292237,  6.36826384,  7.03292237,  6.36826384,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  3.87656588,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  6.36826384,  7.03292237,  7.03292237,  6.36826384,
            5.76642001,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            5.76642001,  7.03292237,  6.36826384,  7.03292237,  7.03292237,
            7.03292237,  3.87656588,  7.03292237,  6.36826384,  6.36826384,
            4.72799187,  5.76642001,  6.36826384,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  3.87656588,  5.22145448,  4.72799187,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  4.72799187,  6.36826384,
            7.03292237,  7.03292237,  5.76642001,  7.03292237,  6.36826384,
            7.03292237,  7.03292237,  7.03292237,  5.76642001,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  6.36826384,
            7.03292237,  5.22145448,  5.76642001,  7.03292237,  5.76642001,
            6.36826384,  5.76642001,  5.76642001,  7.03292237,  5.76642001,
            7.03292237,  7.03292237,  7.03292237,  4.72799187,  6.36826384,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            6.36826384,  7.03292237,  6.36826384,  7.03292237,  4.72799187,
            7.03292237,  7.03292237,  4.28116479,  6.36826384,  3.87656588,
            7.03292237,  3.5102043,   7.03292237,  7.03292237,  5.76642001,
            5.22145448,  7.03292237,  5.76642001,  4.28116479,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  5.76642001,  4.72799187,
            7.03292237,  6.36826384,  7.03292237,  5.22145448,  7.03292237,
            4.72799187,  7.03292237,  7.03292237,  7.03292237,  5.22145448,
            5.22145448,  4.72799187,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            4.28116479,  7.03292237,  7.03292237,  7.03292237,  5.76642001,
            6.36826384,  7.03292237,  7.03292237,  5.76642001,  7.03292237,
            7.03292237,  6.36826384,  4.72799187,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  4.72799187,  5.76642001,  7.03292237,  5.76642001,
            6.36826384,  7.03292237,  7.03292237,  7.03292237,  4.72799187,
            7.03292237,  7.03292237,  7.03292237,  5.76642001,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            6.36826384,  7.03292237,  7.03292237,  5.76642001,  6.36826384,
            4.72799187,  7.03292237,  7.03292237,  7.03292237,  5.76642001,
            7.03292237,  6.36826384,  5.22145448,  5.76642001,  4.72799187,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  5.22145448,  7.03292237,  7.03292237,
            6.36826384,  7.03292237,  7.03292237,  5.76642001,  7.03292237,
            7.03292237,  7.03292237,  6.36826384,  7.03292237,  5.22145448,
            7.03292237,  7.03292237,  7.03292237,  5.76642001,  7.03292237,
            6.36826384,  6.36826384,  7.03292237,  5.76642001,  7.03292237,
            6.36826384,  7.03292237,  7.03292237,  7.03292237,  4.72799187,
            5.22145448,  7.03292237,  3.87656588,  5.76642001,  6.36826384,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  6.36826384,  7.03292237,  7.03292237,
            4.72799187,  7.03292237,  6.36826384,  7.03292237,  4.28116479,
            7.03292237,  7.03292237,  5.76642001,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            4.72799187,  6.36826384,  3.87656588,  7.03292237,  7.03292237,
            6.36826384,  4.72799187,  4.28116479,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  3.87656588,  7.03292237,  7.03292237,  7.03292237,
            3.87656588,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  6.36826384,  7.03292237,  7.03292237,  3.87656588,
            7.03292237,  4.72799187,  5.22145448,  5.22145448,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  6.36826384,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  5.22145448,  5.22145448,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  4.72799187,  6.36826384,  5.76642001,
            5.76642001,  6.36826384,  7.03292237,  7.03292237,  7.03292237,
            6.36826384,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  4.72799187,  7.03292237,  5.76642001,  7.03292237,
            7.03292237,  7.03292237,  4.72799187,  4.28116479,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  5.76642001,
            7.03292237,  5.76642001,  7.03292237,  7.03292237,  7.03292237,
            5.22145448,  7.03292237,  7.03292237,  7.03292237,  5.22145448,
            6.36826384,  7.03292237,  7.03292237,  6.36826384,  6.36826384,
            7.03292237,  7.03292237,  5.76642001,  7.03292237,  5.22145448,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  4.72799187,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  4.28116479,
            7.03292237,  6.36826384,  7.03292237,  5.76642001,  4.28116479,
            5.76642001,  7.03292237,  3.87656588,  7.03292237,  7.03292237,
            7.03292237,  3.5102043,   7.03292237,  7.03292237,  7.03292237,
            7.03292237,  5.76642001,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  5.76642001,  5.76642001,  5.76642001,  7.03292237,
            7.03292237,  6.36826384,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  5.76642001,  7.03292237,  4.28116479,  6.36826384,
            5.76642001,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  5.22145448,
            7.03292237,  7.03292237,  7.03292237,  5.76642001,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  5.76642001,  7.03292237,  7.03292237,  7.03292237,
            6.36826384,  6.36826384,  7.03292237,  7.03292237,  6.36826384,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  7.03292237,  5.22145448,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  3.5102043,   7.03292237,  7.03292237,
            7.03292237,  3.87656588,  6.36826384,  5.76642001,  7.03292237,
            7.03292237,  6.36826384,  4.72799187,  7.03292237,  7.03292237,
            5.76642001,  7.03292237,  3.87656588,  5.22145448,  6.36826384,
            4.28116479,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  6.36826384,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  6.36826384,
            7.03292237,  5.22145448,  6.36826384,  6.36826384,  7.03292237,
            6.36826384,  7.03292237,  7.03292237,  7.03292237,  5.22145448,
            7.03292237,  6.36826384,  7.03292237,  7.03292237,  4.72799187,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  6.36826384,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            6.36826384,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  3.5102043,   7.03292237,  5.22145448,
            5.22145448,  7.03292237,  6.36826384,  7.03292237,  4.72799187,
            7.03292237,  7.03292237,  7.03292237,  4.72799187,  7.03292237,
            5.76642001,  7.03292237,  3.87656588,  7.03292237,  5.22145448,
            3.87656588,  4.72799187,  6.36826384,  5.76642001,  7.03292237,
            6.36826384,  7.03292237,  4.28116479,  5.76642001,  7.03292237,
            7.03292237,  7.03292237,  5.22145448,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  4.28116479,  6.36826384,  7.03292237,  7.03292237,
            7.03292237,  5.76642001,  6.36826384,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            3.5102043,   4.72799187,  7.03292237,  4.28116479,  7.03292237,
            4.72799187,  7.03292237,  5.22145448,  5.76642001,  5.76642001,
            3.87656588,  5.76642001,  5.22145448,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  5.22145448,  6.36826384,
            7.03292237,  7.03292237,  7.03292237,  5.22145448,  7.03292237,
            7.03292237,  7.03292237,  5.22145448,  7.03292237,  7.03292237,
            6.36826384,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  4.28116479,
            4.72799187,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  6.36826384,  7.03292237,  7.03292237,  6.36826384,
            6.36826384,  5.76642001,  7.03292237,  5.76642001,  6.36826384,
            7.03292237,  7.03292237,  7.03292237,  5.76642001,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            5.22145448,  7.03292237,  7.03292237,  5.76642001,  6.36826384,
            5.76642001,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            4.72799187,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  6.36826384,  5.76642001,  6.36826384,  4.72799187,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  4.72799187,
            7.03292237,  6.36826384,  7.03292237,  5.22145448,  7.03292237,
            7.03292237,  6.36826384,  7.03292237,  5.76642001,  6.36826384,
            5.76642001,  7.03292237,  7.03292237,  7.03292237,  6.36826384,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            4.72799187,  7.03292237,  5.22145448,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  4.72799187,  6.36826384,  7.03292237,
            7.03292237,  6.36826384,  7.03292237,  5.76642001,  5.22145448,
            7.03292237,  7.03292237,  7.03292237,  5.22145448,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  4.28116479,
            5.76642001,  7.03292237,  4.28116479,  7.03292237,  6.36826384,
            7.03292237,  7.03292237,  4.28116479,  7.03292237,  7.03292237,
            6.36826384,  3.87656588,  3.5102043,   6.36826384,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  5.76642001,
            7.03292237,  4.72799187,  5.76642001,  7.03292237,  7.03292237,
            3.87656588,  7.03292237,  7.03292237,  7.03292237,  4.28116479,
            7.03292237,  7.03292237,  7.03292237,  5.76642001,  5.76642001,
            7.03292237,  6.36826384,  5.76642001,  7.03292237,  6.36826384,
            5.76642001,  7.03292237,  5.76642001,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            4.28116479,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  4.72799187,  5.76642001,  6.36826384,  6.36826384,
            7.03292237,  7.03292237,  7.03292237,  5.76642001,  4.28116479,
            7.03292237,  5.76642001,  4.72799187,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  5.76642001,
            6.36826384,  6.36826384,  7.03292237,  7.03292237,  6.36826384,
            3.87656588,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            3.5102043,   7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  6.36826384,  7.03292237,  7.03292237,
            7.03292237,  4.72799187,  7.03292237,  6.36826384,  4.72799187,
            4.72799187,  7.03292237,  5.76642001,  7.03292237,  7.03292237,
            7.03292237,  6.36826384,  4.28116479,  7.03292237,  7.03292237,
            7.03292237,  5.76642001,  7.03292237,  7.03292237,  7.03292237,
            4.72799187,  7.03292237,  7.03292237,  6.36826384,  5.22145448,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  5.22145448,  7.03292237,  7.03292237,  6.36826384,
            7.03292237,  7.03292237,  5.22145448,  7.03292237,  6.36826384,
            6.36826384,  7.03292237,  6.36826384,  7.03292237,  7.03292237,
            7.03292237,  6.36826384,  6.36826384,  7.03292237,  4.72799187,
            4.28116479,  4.72799187,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  4.28116479,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            6.36826384,  4.28116479,  4.28116479,  7.03292237,  5.22145448,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  6.36826384,  7.03292237,  7.03292237,  7.03292237,
            5.76642001,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  4.72799187,  7.03292237,
            3.87656588,  7.03292237,  4.72799187,  7.03292237,  7.03292237,
            7.03292237,  5.22145448,  7.03292237,  4.28116479,  7.03292237,
            7.03292237,  4.72799187,  5.22145448,  7.03292237,  7.03292237,
            7.03292237,  6.36826384,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  6.36826384,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  5.76642001,  5.22145448,
            7.03292237,  7.03292237,  3.87656588,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  4.72799187,  6.36826384,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            4.28116479,  7.03292237,  7.03292237,  7.03292237,  5.76642001,
            7.03292237,  5.22145448,  4.72799187,  7.03292237,  7.03292237,
            7.03292237,  6.36826384,  5.76642001,  7.03292237,  5.76642001,
            7.03292237,  4.28116479,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  5.76642001,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  5.76642001,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  5.76642001,  7.03292237,  3.87656588,
            6.36826384,  5.76642001,  7.03292237,  4.28116479,  7.03292237,
            5.76642001,  5.22145448,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  6.36826384,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            5.76642001,  7.03292237,  7.03292237,  7.03292237,  3.5102043,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  4.28116479,  4.72799187,  6.36826384,  7.03292237,
            7.03292237,  4.28116479,  5.76642001,  7.03292237,  7.03292237,
            7.03292237,  4.28116479,  7.03292237,  7.03292237,  5.22145448,
            6.36826384,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  4.72799187,
            7.03292237,  5.22145448,  6.36826384,  7.03292237,  6.36826384,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  5.22145448,
            7.03292237,  7.03292237,  5.22145448,  7.03292237,  6.36826384,
            7.03292237,  7.03292237,  5.76642001,  7.03292237,  7.03292237,
            3.5102043,   7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  5.76642001,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  6.36826384,
            4.72799187,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            6.36826384,  7.03292237,  6.36826384,  4.72799187,  5.22145448,
            5.76642001,  7.03292237,  6.36826384,  6.36826384,  7.03292237,
            6.36826384,  7.03292237,  5.22145448,  4.72799187,  5.76642001,
            6.36826384,  7.03292237,  7.03292237,  5.76642001,  5.22145448,
            7.03292237,  6.36826384,  3.87656588,  6.36826384,  7.03292237,
            5.76642001,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  3.5102043,   7.03292237,  7.03292237,  7.03292237,
            5.22145448,  7.03292237,  6.36826384,  7.03292237,  6.36826384,
            7.03292237,  6.36826384,  5.22145448,  7.03292237,  7.03292237,
            7.03292237,  6.36826384,  6.36826384,  7.03292237,  7.03292237,
            6.36826384,  4.72799187,  7.03292237,  5.22145448,  7.03292237,
            4.72799187,  7.03292237,  4.28116479,  7.03292237,  7.03292237,
            6.36826384,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            4.28116479,  6.36826384,  7.03292237,  3.87656588,  7.03292237,
            7.03292237,  7.03292237,  6.36826384,  7.03292237,  7.03292237,
            7.03292237,  6.36826384,  7.03292237,  5.22145448,  7.03292237,
            7.03292237,  5.76642001,  6.36826384,  7.03292237,  4.72799187,
            7.03292237,  7.03292237,  5.22145448,  7.03292237,  3.5102043,
            6.36826384,  6.36826384,  7.03292237,  6.36826384,  7.03292237,
            5.22145448,  6.36826384,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  5.76642001,  4.28116479,  7.03292237,  7.03292237,
            4.72799187,  4.72799187,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  5.76642001,  7.03292237,  5.76642001,
            4.28116479,  7.03292237,  4.28116479,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  3.5102043,   7.03292237,  5.22145448,
            7.03292237,  6.36826384,  7.03292237,  6.36826384,  7.03292237,
            4.72799187,  7.03292237,  7.03292237,  4.72799187,  3.5102043,
            3.17846635,  3.87656588,  5.22145448,  6.36826384,  7.03292237,
            4.28116479,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            6.36826384,  7.03292237,  7.03292237,  5.76642001,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  4.72799187,  7.03292237,
            7.03292237,  7.03292237,  5.76642001,  7.03292237,  3.5102043,
            7.03292237,  7.03292237,  5.22145448,  6.36826384,  3.87656588,
            4.72799187,  7.03292237,  7.03292237,  3.87656588,  7.03292237,
            6.36826384,  7.03292237,  6.36826384,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  4.72799187,  7.03292237,
            7.03292237,  6.36826384,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  5.76642001,  7.03292237,  4.28116479,  7.03292237,
            7.03292237,  7.03292237,  4.72799187,  6.36826384,  6.36826384,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            6.36826384,  4.72799187,  6.36826384,  7.03292237,  7.03292237,
            5.22145448,  7.03292237,  5.76642001,  7.03292237,  7.03292237,
            7.03292237,  5.76642001,  7.03292237,  6.36826384,  6.36826384,
            7.03292237,  7.03292237,  6.36826384,  7.03292237,  5.22145448,
            7.03292237,  5.22145448,  5.22145448,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            4.72799187,  4.28116479,  7.03292237,  6.36826384,  7.03292237,
            5.76642001,  7.03292237,  7.03292237,  7.03292237,  4.72799187,
            7.03292237,  5.76642001,  7.03292237,  4.72799187,  7.03292237,
            7.03292237,  4.72799187,  5.76642001,  6.36826384,  7.03292237,
            4.28116479,  6.36826384,  7.03292237,  6.36826384,  5.76642001,
            7.03292237,  4.28116479,  5.22145448,  4.72799187,  7.03292237,
            7.03292237,  6.36826384,  5.22145448,  7.03292237,  5.76642001,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  4.28116479,  7.03292237,
            6.36826384,  5.22145448,  5.76642001,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  6.36826384,  7.03292237,  7.03292237,
            5.22145448,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  4.28116479,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  6.36826384,  7.03292237,  7.03292237,  7.03292237,
            6.36826384,  7.03292237,  4.72799187,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  5.22145448,  6.36826384,  7.03292237,
            5.76642001,  5.76642001,  7.03292237,  7.03292237,  7.03292237,
            4.28116479,  7.03292237,  5.76642001,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  7.03292237,  3.87656588,  6.36826384,  6.36826384,
            5.22145448,  7.03292237,  5.22145448,  7.03292237,  7.03292237,
            7.03292237,  4.28116479,  7.03292237,  3.87656588,  7.03292237,
            7.03292237,  5.22145448,  6.36826384,  4.72799187,  7.03292237,
            7.03292237,  7.03292237,  6.36826384,  7.03292237,  5.76642001,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  5.22145448,
            4.28116479,  7.03292237,  6.36826384,  7.03292237,  7.03292237,
            5.76642001,  5.22145448,  5.76642001,  7.03292237,  4.28116479,
            7.03292237,  7.03292237,  4.72799187,  6.36826384,  7.03292237,
            4.72799187,  5.76642001,  7.03292237,  7.03292237,  6.36826384,
            6.36826384,  5.76642001,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  4.72799187,  7.03292237,  6.36826384,
            7.03292237,  4.72799187,  4.72799187,  5.76642001,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  4.72799187,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            5.76642001,  7.03292237,  4.72799187,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            6.36826384,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            3.5102043,   6.36826384,  5.22145448,  7.03292237,  5.22145448,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            4.72799187,  7.03292237,  6.36826384,  7.03292237,  7.03292237,
            7.03292237,  6.36826384,  7.03292237,  7.03292237,  7.03292237,
            6.36826384,  4.72799187,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  6.36826384,  7.03292237,  5.76642001,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  5.22145448,  4.72799187,
            7.03292237,  7.03292237,  7.03292237,  4.28116479,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  3.87656588,  7.03292237,
            5.22145448,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            3.5102043,   7.03292237,  6.36826384,  7.03292237,  7.03292237,
            7.03292237,  4.72799187,  7.03292237,  7.03292237,  4.28116479,
            6.36826384,  7.03292237,  5.22145448,  7.03292237,  7.03292237,
            5.76642001,  7.03292237,  7.03292237,  7.03292237,  5.76642001,
            4.72799187,  7.03292237,  4.72799187,  6.36826384,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  6.36826384,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  3.87656588,  5.22145448,  7.03292237,  7.03292237,
            6.36826384,  4.28116479,  7.03292237,  5.76642001,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  3.87656588,  6.36826384,  7.03292237,
            7.03292237,  5.76642001,  7.03292237,  5.22145448,  7.03292237,
            5.76642001,  4.72799187,  7.03292237,  7.03292237,  6.36826384,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  5.76642001,
            5.22145448,  7.03292237,  5.76642001,  6.36826384,  4.28116479,
            7.03292237,  4.72799187,  3.87656588,  5.22145448,  7.03292237,
            6.36826384,  5.76642001,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  6.36826384,  5.76642001,  6.36826384,  7.03292237,
            5.76642001,  7.03292237,  5.76642001,  5.22145448,  3.87656588,
            5.76642001,  6.36826384,  7.03292237,  5.22145448,  6.36826384,
            5.22145448,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            6.36826384,  7.03292237,  5.76642001,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  4.72799187,  5.76642001,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  4.72799187,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  5.22145448,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  3.5102043,
            3.87656588,  7.03292237,  4.72799187,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  3.87656588,
            5.22145448,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  5.76642001,  7.03292237,  7.03292237,  7.03292237,
            4.28116479,  7.03292237,  4.72799187,  4.72799187,  7.03292237,
            6.36826384,  5.76642001,  7.03292237,  4.28116479,  7.03292237,
            7.03292237,  7.03292237,  5.76642001,  7.03292237,  7.03292237,
            5.76642001,  5.22145448,  7.03292237,  4.72799187,  7.03292237,
            4.28116479,  5.76642001,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  4.28116479,  7.03292237,
            7.03292237,  7.03292237,  5.22145448,  5.22145448,  7.03292237,
            7.03292237,  7.03292237,  5.76642001,  6.36826384,  7.03292237,
            7.03292237,  5.22145448,  7.03292237,  7.03292237,  5.76642001,
            5.22145448,  7.03292237,  7.03292237,  7.03292237,  3.87656588,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  5.76642001,  7.03292237,  6.36826384,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  6.36826384,
            7.03292237,  7.03292237,  7.03292237,  4.28116479,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  3.5102043,
            7.03292237,  7.03292237,  7.03292237,  5.76642001,  4.28116479,
            5.22145448,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  5.76642001,  6.36826384,  7.03292237,
            5.22145448,  5.76642001,  5.76642001,  7.03292237,  7.03292237,
            5.22145448,  7.03292237,  7.03292237,  5.22145448,  6.36826384,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  5.22145448,
            6.36826384,  5.22145448,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  5.22145448,  7.03292237,  5.76642001,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  4.72799187,  7.03292237,
            7.03292237,  7.03292237,  6.36826384,  4.72799187,  5.22145448,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            6.36826384,  7.03292237,  7.03292237,  5.76642001,  6.36826384,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  7.03292237,
            4.72799187,  3.87656588,  7.03292237,  7.03292237,  4.72799187,
            7.03292237,  7.03292237,  6.36826384,  7.03292237,  5.22145448,
            7.03292237,  7.03292237,  7.03292237,  3.87656588,  5.76642001,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  5.76642001,
            5.22145448,  7.03292237,  6.36826384,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  5.76642001,
            5.76642001,  7.03292237,  5.76642001,  3.87656588,  6.36826384,
            7.03292237,  7.03292237,  7.03292237,  6.36826384,  5.76642001,
            5.22145448,  7.03292237,  5.22145448,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  5.22145448,  4.72799187,
            7.03292237,  6.36826384,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  7.03292237,  7.03292237,  5.22145448,  6.36826384,
            7.03292237,  7.03292237,  3.17846635,  5.76642001,  7.03292237,
            3.5102043,   7.03292237,  7.03292237,  7.03292237,  3.87656588,
            7.03292237,  6.36826384,  6.36826384,  7.03292237,  5.22145448,
            7.03292237,  7.03292237,  7.03292237,  7.03292237,  7.03292237,
            7.03292237,  4.28116479,  6.36826384,  7.03292237,  6.36826384,
            4.72799187,  7.03292237,  7.03292237,  5.22145448,  4.28116479,
            7.03292237,  6.36826384,  7.03292237,  4.72799187,  5.76642001,
            6.36826384,  5.22145448,  7.03292237,  7.03292237,  7.03292237,
            6.36826384,  7.03292237,  7.03292237,  3.87656588,  7.03292237,
            4.72799187,  7.03292237,  3.53462742,  4.76088805,  5.25778406,
            4.31095206,  5.80654132,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  5.25778406,  5.25778406,  5.80654132,  5.80654132,
            3.90353806,  5.25778406,  4.31095206,  5.80654132,  5.25778406,
            3.53462742,  2.89810483,  5.80654132,  5.25778406,  5.80654132,
            2.89810483,  5.80654132,  5.25778406,  3.53462742,  4.76088805,
            5.80654132,  3.20058132,  5.80654132,  5.80654132,  4.76088805,
            5.80654132,  3.53462742,  3.53462742,  5.80654132,  5.80654132,
            5.80654132,  4.76088805,  5.80654132,  4.76088805,  3.90353806,
            5.80654132,  3.53462742,  5.80654132,  2.6242144,   3.20058132,
            5.80654132,  5.80654132,  3.90353806,  3.20058132,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  5.80654132,
            2.89810483,  5.80654132,  5.80654132,  3.90353806,  3.53462742,
            4.31095206,  5.80654132,  5.80654132,  4.76088805,  5.80654132,
            3.53462742,  5.80654132,  4.76088805,  2.89810483,  5.25778406,
            4.31095206,  5.80654132,  4.31095206,  5.80654132,  5.80654132,
            4.76088805,  4.31095206,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  4.76088805,  5.80654132,  5.25778406,
            5.25778406,  5.80654132,  5.80654132,  3.53462742,  5.80654132,
            3.53462742,  5.80654132,  4.31095206,  5.80654132,  5.80654132,
            5.25778406,  5.80654132,  3.20058132,  5.80654132,  5.80654132,
            3.20058132,  3.90353806,  5.80654132,  5.80654132,  5.25778406,
            3.53462742,  3.20058132,  5.80654132,  4.31095206,  5.80654132,
            5.80654132,  5.80654132,  3.20058132,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  4.31095206,  5.80654132,  3.90353806,
            5.80654132,  4.31095206,  4.31095206,  5.80654132,  4.76088805,
            3.90353806,  3.90353806,  4.76088805,  3.90353806,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  5.25778406,  3.53462742,  5.80654132,  3.53462742,
            5.80654132,  5.80654132,  5.80654132,  2.89810483,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  4.76088805,  4.76088805,
            5.80654132,  2.89810483,  5.80654132,  4.76088805,  5.80654132,
            5.80654132,  4.31095206,  3.20058132,  5.80654132,  4.76088805,
            5.80654132,  2.89810483,  2.89810483,  5.25778406,  3.90353806,
            5.80654132,  5.80654132,  5.25778406,  5.80654132,  5.80654132,
            3.90353806,  5.80654132,  5.25778406,  4.76088805,  5.80654132,
            2.89810483,  5.25778406,  5.80654132,  5.80654132,  4.31095206,
            5.25778406,  5.80654132,  5.80654132,  5.80654132,  5.80654132,
            2.89810483,  5.80654132,  3.53462742,  3.90353806,  5.25778406,
            5.80654132,  3.20058132,  2.89810483,  5.80654132,  4.31095206,
            5.80654132,  3.53462742,  5.25778406,  4.76088805,  5.80654132,
            3.53462742,  3.90353806,  5.80654132,  3.20058132,  5.80654132,
            5.80654132,  3.53462742,  5.25778406,  4.76088805,  4.76088805,
            5.80654132,  5.80654132,  2.89810483,  3.20058132,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  5.25778406,  5.25778406,
            5.80654132,  5.80654132,  4.76088805,  5.80654132,  4.31095206,
            5.25778406,  5.80654132,  4.31095206,  4.31095206,  5.80654132,
            5.80654132,  3.53462742,  4.76088805,  3.53462742,  4.76088805,
            4.31095206,  5.80654132,  3.90353806,  5.80654132,  4.76088805,
            5.80654132,  5.80654132,  5.80654132,  4.31095206,  3.90353806,
            5.80654132,  4.76088805,  4.76088805,  3.53462742,  5.80654132,
            5.80654132,  5.25778406,  3.53462742,  3.20058132,  3.53462742,
            3.90353806,  5.80654132,  4.31095206,  4.76088805,  5.80654132,
            5.80654132,  5.80654132,  3.90353806,  4.76088805,  2.89810483,
            5.80654132,  5.80654132,  5.80654132,  4.76088805,  5.25778406,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  3.90353806,  5.25778406,  4.76088805,
            5.80654132,  4.76088805,  3.90353806,  5.80654132,  5.80654132,
            4.76088805,  5.80654132,  5.25778406,  5.80654132,  2.89810483,
            5.80654132,  5.25778406,  3.90353806,  3.90353806,  5.80654132,
            5.25778406,  3.53462742,  5.80654132,  4.76088805,  5.25778406,
            5.80654132,  3.90353806,  4.31095206,  5.80654132,  5.25778406,
            3.90353806,  3.53462742,  5.25778406,  2.89810483,  5.80654132,
            3.53462742,  4.76088805,  4.31095206,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  3.90353806,  5.80654132,
            4.31095206,  5.80654132,  5.80654132,  5.25778406,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  5.25778406,  5.25778406,
            5.80654132,  5.25778406,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  5.25778406,  4.31095206,  5.80654132,  5.25778406,
            5.80654132,  5.25778406,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  4.31095206,  5.25778406,  3.53462742,  2.89810483,
            5.80654132,  5.80654132,  3.20058132,  5.80654132,  4.31095206,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  3.90353806,
            3.90353806,  3.90353806,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  4.76088805,  3.20058132,  4.31095206,  5.80654132,
            3.90353806,  5.80654132,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  3.90353806,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  3.90353806,  5.80654132,  3.90353806,  3.53462742,
            5.80654132,  4.76088805,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  5.80654132,
            4.76088805,  5.25778406,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  5.25778406,
            3.53462742,  5.25778406,  5.80654132,  3.53462742,  5.80654132,
            3.90353806,  5.80654132,  5.80654132,  5.80654132,  3.90353806,
            3.20058132,  5.80654132,  5.80654132,  3.90353806,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  3.53462742,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  3.53462742,  5.25778406,  3.90353806,
            5.80654132,  4.76088805,  4.76088805,  3.90353806,  5.80654132,
            5.80654132,  4.31095206,  2.89810483,  5.80654132,  5.80654132,
            3.90353806,  5.80654132,  3.53462742,  3.90353806,  5.80654132,
            5.80654132,  4.76088805,  5.80654132,  4.31095206,  5.25778406,
            5.25778406,  3.20058132,  3.53462742,  5.80654132,  4.31095206,
            5.80654132,  4.76088805,  3.90353806,  4.76088805,  4.76088805,
            5.80654132,  5.80654132,  5.25778406,  3.90353806,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  3.53462742,  4.31095206,  3.90353806,  4.76088805,
            4.31095206,  3.53462742,  3.90353806,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  3.20058132,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  3.90353806,  4.76088805,
            5.25778406,  3.53462742,  3.20058132,  5.80654132,  3.90353806,
            5.80654132,  3.53462742,  5.80654132,  5.80654132,  3.90353806,
            5.80654132,  3.90353806,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  4.76088805,  3.90353806,  4.76088805,  5.25778406,
            2.89810483,  5.80654132,  4.31095206,  5.80654132,  4.76088805,
            5.80654132,  5.25778406,  5.80654132,  5.80654132,  5.80654132,
            3.53462742,  2.89810483,  5.80654132,  5.80654132,  5.80654132,
            3.90353806,  4.76088805,  5.80654132,  5.25778406,  4.76088805,
            5.25778406,  5.80654132,  5.80654132,  5.25778406,  5.80654132,
            5.80654132,  5.80654132,  2.89810483,  5.25778406,  5.80654132,
            5.80654132,  4.76088805,  4.76088805,  5.25778406,  5.80654132,
            5.80654132,  4.31095206,  3.20058132,  3.53462742,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  5.25778406,
            5.80654132,  5.80654132,  3.90353806,  4.76088805,  5.80654132,
            3.53462742,  5.80654132,  5.25778406,  2.89810483,  5.80654132,
            5.25778406,  5.80654132,  5.80654132,  5.80654132,  5.25778406,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  4.31095206,  5.80654132,  3.20058132,  5.80654132,
            5.25778406,  4.76088805,  5.25778406,  5.80654132,  4.76088805,
            5.80654132,  3.90353806,  4.31095206,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  5.25778406,  5.80654132,  3.90353806,
            4.76088805,  3.90353806,  5.80654132,  3.53462742,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  3.53462742,  5.80654132,
            4.76088805,  5.80654132,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  3.90353806,
            2.6242144,   5.80654132,  5.80654132,  5.80654132,  5.80654132,
            4.76088805,  5.80654132,  3.53462742,  5.80654132,  5.80654132,
            3.90353806,  5.80654132,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  3.20058132,  3.20058132,  5.80654132,
            5.80654132,  5.80654132,  3.90353806,  5.80654132,  5.25778406,
            4.31095206,  5.25778406,  4.31095206,  4.31095206,  4.76088805,
            5.80654132,  4.76088805,  5.80654132,  3.53462742,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  3.20058132,
            5.80654132,  3.90353806,  5.80654132,  4.76088805,  5.80654132,
            3.90353806,  5.80654132,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  5.25778406,  5.80654132,  4.31095206,  5.25778406,
            4.31095206,  5.80654132,  3.90353806,  5.80654132,  3.53462742,
            5.25778406,  5.80654132,  5.80654132,  4.31095206,  3.90353806,
            3.53462742,  5.80654132,  5.80654132,  5.80654132,  4.31095206,
            5.80654132,  5.80654132,  5.25778406,  4.76088805,  4.31095206,
            3.20058132,  5.80654132,  3.53462742,  3.20058132,  5.80654132,
            5.80654132,  3.20058132,  3.20058132,  5.80654132,  4.31095206,
            4.31095206,  5.80654132,  5.80654132,  3.90353806,  3.90353806,
            3.53462742,  5.80654132,  3.90353806,  3.53462742,  5.80654132,
            3.90353806,  5.25778406,  5.80654132,  3.53462742,  5.80654132,
            5.25778406,  5.80654132,  4.31095206,  3.90353806,  5.80654132,
            5.80654132,  4.31095206,  5.25778406,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  5.25778406,
            3.20058132,  5.25778406,  2.89810483,  3.90353806,  5.80654132,
            3.53462742,  5.80654132,  5.25778406,  5.80654132,  2.89810483,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  3.20058132,
            5.80654132,  5.25778406,  3.53462742,  4.31095206,  4.76088805,
            3.90353806,  5.80654132,  5.80654132,  5.25778406,  3.90353806,
            4.76088805,  4.31095206,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  3.90353806,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  5.25778406,
            3.53462742,  5.80654132,  5.80654132,  5.25778406,  5.80654132,
            3.20058132,  5.80654132,  4.76088805,  5.80654132,  4.76088805,
            5.80654132,  5.25778406,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  5.25778406,  2.89810483,  5.80654132,  5.80654132,
            2.89810483,  3.53462742,  5.80654132,  5.80654132,  2.89810483,
            4.31095206,  3.53462742,  4.31095206,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  4.31095206,
            4.76088805,  5.25778406,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  5.25778406,  3.90353806,  5.80654132,  5.25778406,
            5.80654132,  2.89810483,  2.89810483,  5.80654132,  3.53462742,
            5.80654132,  3.53462742,  5.80654132,  4.31095206,  2.89810483,
            5.80654132,  5.80654132,  2.89810483,  4.76088805,  5.80654132,
            5.80654132,  3.20058132,  5.80654132,  3.90353806,  5.80654132,
            5.80654132,  3.20058132,  3.90353806,  4.76088805,  4.76088805,
            5.80654132,  3.90353806,  4.31095206,  5.80654132,  4.31095206,
            5.80654132,  3.20058132,  4.31095206,  4.76088805,  3.53462742,
            5.80654132,  5.80654132,  3.53462742,  3.53462742,  3.53462742,
            5.80654132,  5.80654132,  3.90353806,  3.90353806,  3.20058132,
            5.80654132,  5.80654132,  2.89810483,  3.90353806,  5.80654132,
            2.89810483,  3.53462742,  3.53462742,  4.31095206,  5.80654132,
            3.53462742,  5.80654132,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  5.25778406,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  4.76088805,  5.80654132,  5.80654132,  4.76088805,
            5.80654132,  5.80654132,  4.76088805,  4.76088805,  5.80654132,
            5.25778406,  4.31095206,  5.80654132,  4.76088805,  3.90353806,
            4.31095206,  5.80654132,  2.89810483,  4.31095206,  5.25778406,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  3.20058132,
            5.25778406,  5.80654132,  4.76088805,  5.80654132,  4.31095206,
            5.80654132,  5.80654132,  4.76088805,  4.31095206,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  4.31095206,
            4.31095206,  3.20058132,  4.76088805,  5.80654132,  3.20058132,
            3.20058132,  5.80654132,  3.90353806,  5.25778406,  3.20058132,
            4.76088805,  3.20058132,  3.53462742,  4.76088805,  5.80654132,
            5.80654132,  4.31095206,  4.76088805,  5.80654132,  4.31095206,
            5.80654132,  4.76088805,  4.31095206,  2.89810483,  5.80654132,
            5.80654132,  5.80654132,  4.76088805,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  4.76088805,  5.25778406,  4.31095206,
            5.80654132,  3.90353806,  3.53462742,  4.76088805,  5.80654132,
            4.31095206,  5.80654132,  5.80654132,  3.20058132,  5.80654132,
            5.25778406,  5.80654132,  5.80654132,  5.80654132,  3.53462742,
            2.6242144,   5.80654132,  5.80654132,  3.53462742,  5.25778406,
            3.90353806,  5.80654132,  2.89810483,  5.80654132,  3.90353806,
            5.80654132,  5.80654132,  3.90353806,  2.89810483,  5.80654132,
            4.76088805,  4.31095206,  5.80654132,  5.25778406,  5.80654132,
            5.80654132,  4.31095206,  5.80654132,  5.80654132,  5.80654132,
            3.90353806,  4.76088805,  5.80654132,  4.76088805,  5.80654132,
            4.76088805,  3.53462742,  3.90353806,  5.80654132,  5.80654132,
            5.80654132,  5.25778406,  5.80654132,  5.80654132,  5.25778406,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  5.80654132,
            3.53462742,  3.53462742,  3.90353806,  5.80654132,  4.31095206,
            3.53462742,  5.80654132,  4.76088805,  4.76088805,  3.20058132,
            3.90353806,  5.80654132,  5.25778406,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  4.31095206,  5.25778406,  4.31095206,
            5.80654132,  3.20058132,  5.80654132,  4.31095206,  4.31095206,
            4.76088805,  5.80654132,  4.76088805,  4.31095206,  5.80654132,
            5.25778406,  3.53462742,  3.53462742,  5.25778406,  5.80654132,
            3.90353806,  5.25778406,  4.31095206,  4.31095206,  3.53462742,
            5.80654132,  3.90353806,  5.80654132,  5.80654132,  4.76088805,
            5.25778406,  3.20058132,  3.90353806,  5.80654132,  5.25778406,
            5.80654132,  5.80654132,  5.25778406,  5.80654132,  4.31095206,
            5.25778406,  4.76088805,  5.80654132,  5.80654132,  5.25778406,
            3.53462742,  5.80654132,  5.80654132,  5.80654132,  5.25778406,
            5.25778406,  5.80654132,  3.20058132,  5.80654132,  5.80654132,
            3.53462742,  5.80654132,  5.80654132,  5.80654132,  4.31095206,
            5.80654132,  4.76088805,  5.80654132,  5.80654132,  5.80654132,
            3.90353806,  4.31095206,  5.25778406,  5.80654132,  3.53462742,
            3.90353806,  5.25778406,  4.31095206,  5.80654132,  5.25778406,
            5.25778406,  2.89810483,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  5.25778406,  5.80654132,  4.76088805,
            5.80654132,  5.80654132,  5.80654132,  4.31095206,  5.80654132,
            3.20058132,  3.90353806,  5.80654132,  5.80654132,  5.25778406,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  5.80654132,  2.6242144,   5.80654132,  3.90353806,
            5.25778406,  4.76088805,  5.80654132,  5.80654132,  3.90353806,
            5.80654132,  3.53462742,  2.89810483,  5.80654132,  3.53462742,
            2.89810483,  4.76088805,  5.80654132,  5.80654132,  5.80654132,
            4.31095206,  5.80654132,  4.76088805,  3.90353806,  2.89810483,
            4.76088805,  5.80654132,  2.6242144,   3.53462742,  4.31095206,
            5.25778406,  5.25778406,  3.20058132,  4.31095206,  4.31095206,
            3.20058132,  4.31095206,  5.25778406,  4.31095206,  5.25778406,
            3.90353806,  4.31095206,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  3.90353806,  5.80654132,  5.80654132,  5.80654132,
            4.31095206,  5.80654132,  5.80654132,  5.80654132,  3.90353806,
            5.25778406,  3.90353806,  4.31095206,  4.76088805,  3.90353806,
            5.80654132,  5.80654132,  5.80654132,  2.89810483,  5.80654132,
            5.80654132,  5.80654132,  5.80654132,  5.80654132,  5.80654132,
            5.80654132,  3.90353806,  3.20058132,  5.25778406,  4.76088805,
            5.25778406])


class InvGaussIdentity(Medpar1):
    """
    Accuracy is different for R vs Stata ML vs Stata IRLS, we are close.
    """
    def __init__(self):
        super().__init__()
        self.params = np.array([0.44538838, -1.05872706,  2.83947966])
        self.bse = np.array([0.02586783,  0.13830023,  0.20834864])
        filename = os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                "igaussident_resids.csv")
        self.resids = pd.read_csv(filename, sep=',', header=None).values
        self.null_deviance = 335.1539777981053  # from R, Rpy bug
        self.df_null = 3675
        self.deviance = 305.33661191013988
        self.df_resid = 3673
        self.df_model = 2
        self.aic_R = 18558.677276882016
        self.aic_Stata = 6.619290231464371
        self.bic_Stata = -29848.45250412075
        self.llf_stata = -12163.25544543151
        self.chi2 = 567.1229375785638  # in Stata not sm
        # self.llf = -9275.3386384410078  # from R
        self.llf = -12163.25545    # from Stata, big diff with R
        self.scale = 0.10115387793455666
        self.pearson_chi2 = 371.5346609292967  # deviance_p in Stata
        self.fittedvalues = np.array([
            6.84797506,  6.84797506,  6.84797506, 6.84797506,  5.9571983,
            6.84797506,  6.84797506,  6.40258668,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  5.9571983,   6.84797506,
            5.51180993,  6.84797506,  5.51180993,  5.06642155,  5.06642155,
            6.84797506,  6.84797506,  6.40258668,  6.84797506,  5.9571983,
            6.84797506,  4.62103317,  6.84797506,  6.84797506,  6.84797506,
            5.9571983,   6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  4.17564479,  6.84797506,  6.84797506,  4.62103317,
            6.84797506,  6.84797506,  5.06642155,  6.84797506,  6.84797506,
            6.84797506,  5.51180993,  6.40258668,  6.40258668,  4.62103317,
            5.06642155,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            5.51180993,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  5.9571983,   6.40258668,
            6.40258668,  5.51180993,  6.84797506,  6.84797506,  6.84797506,
            5.9571983,   6.84797506,  6.84797506,  4.17564479,  5.9571983,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            5.51180993,  5.51180993,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  6.84797506,  5.06642155,  6.84797506,  6.40258668,
            6.84797506,  6.40258668,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  5.51180993,  6.40258668,  5.51180993,
            6.84797506,  6.84797506,  5.06642155,  5.9571983,   6.84797506,
            5.06642155,  6.40258668,  4.17564479,  6.84797506,  6.84797506,
            5.51180993,  5.51180993,  6.40258668,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  4.62103317,
            6.84797506,  6.40258668,  6.40258668,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.40258668,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  5.9571983,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  5.9571983,
            6.84797506,  6.84797506,  6.40258668,  4.17564479,  6.84797506,
            6.84797506,  5.51180993,  6.84797506,  5.9571983,   4.62103317,
            5.9571983,   6.40258668,  6.40258668,  6.84797506,  6.84797506,
            5.9571983,   6.84797506,  6.84797506,  4.62103317,  6.84797506,
            6.40258668,  6.84797506,  6.40258668,  6.84797506,  5.51180993,
            6.84797506,  4.62103317,  5.06642155,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.40258668,
            6.84797506,  4.62103317,  5.51180993,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  4.62103317,  6.84797506,  6.84797506,
            6.84797506,  6.40258668,  5.9571983,   6.84797506,  6.84797506,
            6.84797506,  5.06642155,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.40258668,  6.84797506,  6.40258668,  4.62103317,  5.9571983,
            5.51180993,  6.40258668,  6.40258668,  6.84797506,  6.84797506,
            6.84797506,  6.40258668,  6.84797506,  6.84797506,  6.40258668,
            5.9571983,   6.84797506,  5.51180993,  5.9571983,   6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            4.62103317,  6.84797506,  5.51180993,  6.84797506,  6.40258668,
            5.9571983,   4.62103317,  4.62103317,  6.84797506,  6.40258668,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  4.62103317,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  5.51180993,
            6.84797506,  6.84797506,  6.84797506,  5.51180993,  6.84797506,
            5.9571983,   6.84797506,  5.06642155,  4.62103317,  6.40258668,
            5.9571983,   6.84797506,  6.84797506,  6.84797506,  5.51180993,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            4.17564479,  5.06642155,  6.84797506,  6.84797506,  6.84797506,
            5.06642155,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  6.40258668,  4.17564479,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  4.62103317,  6.84797506,  6.40258668,
            6.84797506,  5.51180993,  5.51180993,  6.40258668,  6.84797506,
            6.40258668,  6.40258668,  6.84797506,  4.62103317,  6.84797506,
            6.84797506,  6.84797506,  5.51180993,  6.40258668,  6.84797506,
            4.17564479,  6.40258668,  5.51180993,  5.9571983,   6.84797506,
            6.84797506,  6.84797506,  6.84797506,  4.62103317,  6.84797506,
            5.51180993,  6.84797506,  6.40258668,  5.51180993,  5.06642155,
            6.84797506,  6.84797506,  6.84797506,  5.06642155,  6.40258668,
            6.84797506,  6.40258668,  5.9571983,   6.84797506,  6.84797506,
            6.84797506,  6.84797506,  5.51180993,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  4.17564479,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  5.06642155,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.40258668,  6.84797506,  6.84797506,
            6.40258668,  6.84797506,  6.40258668,  6.84797506,  5.51180993,
            6.40258668,  6.84797506,  6.40258668,  6.84797506,  6.40258668,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  4.17564479,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  6.40258668,  6.84797506,  6.84797506,  6.40258668,
            5.9571983,   6.84797506,  6.84797506,  6.84797506,  6.84797506,
            5.9571983,   6.84797506,  6.40258668,  6.84797506,  6.84797506,
            6.84797506,  4.17564479,  6.84797506,  6.40258668,  6.40258668,
            5.06642155,  5.9571983,   6.40258668,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  4.17564479,  5.51180993,  5.06642155,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  5.06642155,  6.40258668,
            6.84797506,  6.84797506,  5.9571983,   6.84797506,  6.40258668,
            6.84797506,  6.84797506,  6.84797506,  5.9571983,   6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.40258668,
            6.84797506,  5.51180993,  5.9571983,   6.84797506,  5.9571983,
            6.40258668,  5.9571983,   5.9571983,   6.84797506,  5.9571983,
            6.84797506,  6.84797506,  6.84797506,  5.06642155,  6.40258668,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.40258668,  6.84797506,  6.40258668,  6.84797506,  5.06642155,
            6.84797506,  6.84797506,  4.62103317,  6.40258668,  4.17564479,
            6.84797506,  3.73025641,  6.84797506,  6.84797506,  5.9571983,
            5.51180993,  6.84797506,  5.9571983,   4.62103317,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  5.9571983,   5.06642155,
            6.84797506,  6.40258668,  6.84797506,  5.51180993,  6.84797506,
            5.06642155,  6.84797506,  6.84797506,  6.84797506,  5.51180993,
            5.51180993,  5.06642155,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            4.62103317,  6.84797506,  6.84797506,  6.84797506,  5.9571983,
            6.40258668,  6.84797506,  6.84797506,  5.9571983,   6.84797506,
            6.84797506,  6.40258668,  5.06642155,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  5.06642155,  5.9571983,   6.84797506,  5.9571983,
            6.40258668,  6.84797506,  6.84797506,  6.84797506,  5.06642155,
            6.84797506,  6.84797506,  6.84797506,  5.9571983,   6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.40258668,  6.84797506,  6.84797506,  5.9571983,   6.40258668,
            5.06642155,  6.84797506,  6.84797506,  6.84797506,  5.9571983,
            6.84797506,  6.40258668,  5.51180993,  5.9571983,   5.06642155,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  5.51180993,  6.84797506,  6.84797506,
            6.40258668,  6.84797506,  6.84797506,  5.9571983,   6.84797506,
            6.84797506,  6.84797506,  6.40258668,  6.84797506,  5.51180993,
            6.84797506,  6.84797506,  6.84797506,  5.9571983,   6.84797506,
            6.40258668,  6.40258668,  6.84797506,  5.9571983,   6.84797506,
            6.40258668,  6.84797506,  6.84797506,  6.84797506,  5.06642155,
            5.51180993,  6.84797506,  4.17564479,  5.9571983,   6.40258668,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.40258668,  6.84797506,  6.84797506,
            5.06642155,  6.84797506,  6.40258668,  6.84797506,  4.62103317,
            6.84797506,  6.84797506,  5.9571983,   6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            5.06642155,  6.40258668,  4.17564479,  6.84797506,  6.84797506,
            6.40258668,  5.06642155,  4.62103317,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  4.17564479,  6.84797506,  6.84797506,  6.84797506,
            4.17564479,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  6.40258668,  6.84797506,  6.84797506,  4.17564479,
            6.84797506,  5.06642155,  5.51180993,  5.51180993,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  6.40258668,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  5.51180993,  5.51180993,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  5.06642155,  6.40258668,  5.9571983,
            5.9571983,   6.40258668,  6.84797506,  6.84797506,  6.84797506,
            6.40258668,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  5.06642155,  6.84797506,  5.9571983,   6.84797506,
            6.84797506,  6.84797506,  5.06642155,  4.62103317,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  5.9571983,
            6.84797506,  5.9571983,   6.84797506,  6.84797506,  6.84797506,
            5.51180993,  6.84797506,  6.84797506,  6.84797506,  5.51180993,
            6.40258668,  6.84797506,  6.84797506,  6.40258668,  6.40258668,
            6.84797506,  6.84797506,  5.9571983,   6.84797506,  5.51180993,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  5.06642155,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  4.62103317,
            6.84797506,  6.40258668,  6.84797506,  5.9571983,   4.62103317,
            5.9571983,   6.84797506,  4.17564479,  6.84797506,  6.84797506,
            6.84797506,  3.73025641,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  5.9571983,   6.84797506,  6.84797506,  6.84797506,
            6.84797506,  5.9571983,   5.9571983,   5.9571983,   6.84797506,
            6.84797506,  6.40258668,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  5.9571983,   6.84797506,  4.62103317,  6.40258668,
            5.9571983,   6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  5.51180993,
            6.84797506,  6.84797506,  6.84797506,  5.9571983,   6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  5.9571983,   6.84797506,  6.84797506,  6.84797506,
            6.40258668,  6.40258668,  6.84797506,  6.84797506,  6.40258668,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  6.84797506,  5.51180993,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  3.73025641,  6.84797506,  6.84797506,
            6.84797506,  4.17564479,  6.40258668,  5.9571983,   6.84797506,
            6.84797506,  6.40258668,  5.06642155,  6.84797506,  6.84797506,
            5.9571983,   6.84797506,  4.17564479,  5.51180993,  6.40258668,
            4.62103317,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.40258668,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.40258668,
            6.84797506,  5.51180993,  6.40258668,  6.40258668,  6.84797506,
            6.40258668,  6.84797506,  6.84797506,  6.84797506,  5.51180993,
            6.84797506,  6.40258668,  6.84797506,  6.84797506,  5.06642155,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  6.40258668,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.40258668,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  3.73025641,  6.84797506,  5.51180993,
            5.51180993,  6.84797506,  6.40258668,  6.84797506,  5.06642155,
            6.84797506,  6.84797506,  6.84797506,  5.06642155,  6.84797506,
            5.9571983,   6.84797506,  4.17564479,  6.84797506,  5.51180993,
            4.17564479,  5.06642155,  6.40258668,  5.9571983,   6.84797506,
            6.40258668,  6.84797506,  4.62103317,  5.9571983,   6.84797506,
            6.84797506,  6.84797506,  5.51180993,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  4.62103317,  6.40258668,  6.84797506,  6.84797506,
            6.84797506,  5.9571983,   6.40258668,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            3.73025641,  5.06642155,  6.84797506,  4.62103317,  6.84797506,
            5.06642155,  6.84797506,  5.51180993,  5.9571983,   5.9571983,
            4.17564479,  5.9571983,   5.51180993,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  5.51180993,  6.40258668,
            6.84797506,  6.84797506,  6.84797506,  5.51180993,  6.84797506,
            6.84797506,  6.84797506,  5.51180993,  6.84797506,  6.84797506,
            6.40258668,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  4.62103317,
            5.06642155,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.40258668,  6.84797506,  6.84797506,  6.40258668,
            6.40258668,  5.9571983,   6.84797506,  5.9571983,   6.40258668,
            6.84797506,  6.84797506,  6.84797506,  5.9571983,   6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            5.51180993,  6.84797506,  6.84797506,  5.9571983,   6.40258668,
            5.9571983,   6.84797506,  6.84797506,  6.40258668,  6.84797506,
            5.06642155,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.40258668,  5.9571983,   6.40258668,  5.06642155,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  5.06642155,
            6.84797506,  6.40258668,  6.84797506,  5.51180993,  6.84797506,
            6.84797506,  6.40258668,  6.84797506,  5.9571983,   6.40258668,
            5.9571983,   6.84797506,  6.84797506,  6.84797506,  6.40258668,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            5.06642155,  6.84797506,  5.51180993,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  5.06642155,  6.40258668,  6.84797506,
            6.84797506,  6.40258668,  6.84797506,  5.9571983,   5.51180993,
            6.84797506,  6.84797506,  6.84797506,  5.51180993,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  4.62103317,
            5.9571983,   6.84797506,  4.62103317,  6.84797506,  6.40258668,
            6.84797506,  6.84797506,  4.62103317,  6.84797506,  6.84797506,
            6.40258668,  4.17564479,  3.73025641,  6.40258668,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  5.9571983,
            6.84797506,  5.06642155,  5.9571983,   6.84797506,  6.84797506,
            4.17564479,  6.84797506,  6.84797506,  6.84797506,  4.62103317,
            6.84797506,  6.84797506,  6.84797506,  5.9571983,   5.9571983,
            6.84797506,  6.40258668,  5.9571983,   6.84797506,  6.40258668,
            5.9571983,   6.84797506,  5.9571983,   6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            4.62103317,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  5.06642155,  5.9571983,   6.40258668,  6.40258668,
            6.84797506,  6.84797506,  6.84797506,  5.9571983,   4.62103317,
            6.84797506,  5.9571983,   5.06642155,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  5.9571983,
            6.40258668,  6.40258668,  6.84797506,  6.84797506,  6.40258668,
            4.17564479,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            3.73025641,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.40258668,  6.84797506,  6.84797506,
            6.84797506,  5.06642155,  6.84797506,  6.40258668,  5.06642155,
            5.06642155,  6.84797506,  5.9571983,   6.84797506,  6.84797506,
            6.84797506,  6.40258668,  4.62103317,  6.84797506,  6.84797506,
            6.84797506,  5.9571983,   6.84797506,  6.84797506,  6.84797506,
            5.06642155,  6.84797506,  6.84797506,  6.40258668,  5.51180993,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  5.51180993,  6.84797506,  6.84797506,  6.40258668,
            6.84797506,  6.84797506,  5.51180993,  6.84797506,  6.40258668,
            6.40258668,  6.84797506,  6.40258668,  6.84797506,  6.84797506,
            6.84797506,  6.40258668,  6.40258668,  6.84797506,  5.06642155,
            4.62103317,  5.06642155,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  4.62103317,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.40258668,  4.62103317,  4.62103317,  6.84797506,  5.51180993,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.40258668,  6.84797506,  6.84797506,  6.84797506,
            5.9571983,   6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  5.06642155,  6.84797506,
            4.17564479,  6.84797506,  5.06642155,  6.84797506,  6.84797506,
            6.84797506,  5.51180993,  6.84797506,  4.62103317,  6.84797506,
            6.84797506,  5.06642155,  5.51180993,  6.84797506,  6.84797506,
            6.84797506,  6.40258668,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.40258668,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  5.9571983,   5.51180993,
            6.84797506,  6.84797506,  4.17564479,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  5.06642155,  6.40258668,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            4.62103317,  6.84797506,  6.84797506,  6.84797506,  5.9571983,
            6.84797506,  5.51180993,  5.06642155,  6.84797506,  6.84797506,
            6.84797506,  6.40258668,  5.9571983,   6.84797506,  5.9571983,
            6.84797506,  4.62103317,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  5.9571983,   6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  5.9571983,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  5.9571983,   6.84797506,  4.17564479,
            6.40258668,  5.9571983,   6.84797506,  4.62103317,  6.84797506,
            5.9571983,   5.51180993,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.40258668,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            5.9571983,   6.84797506,  6.84797506,  6.84797506,  3.73025641,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  4.62103317,  5.06642155,  6.40258668,  6.84797506,
            6.84797506,  4.62103317,  5.9571983,   6.84797506,  6.84797506,
            6.84797506,  4.62103317,  6.84797506,  6.84797506,  5.51180993,
            6.40258668,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  5.06642155,
            6.84797506,  5.51180993,  6.40258668,  6.84797506,  6.40258668,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  5.51180993,
            6.84797506,  6.84797506,  5.51180993,  6.84797506,  6.40258668,
            6.84797506,  6.84797506,  5.9571983,   6.84797506,  6.84797506,
            3.73025641,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  5.9571983,   6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.40258668,
            5.06642155,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.40258668,  6.84797506,  6.40258668,  5.06642155,  5.51180993,
            5.9571983,   6.84797506,  6.40258668,  6.40258668,  6.84797506,
            6.40258668,  6.84797506,  5.51180993,  5.06642155,  5.9571983,
            6.40258668,  6.84797506,  6.84797506,  5.9571983,   5.51180993,
            6.84797506,  6.40258668,  4.17564479,  6.40258668,  6.84797506,
            5.9571983,   6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  3.73025641,  6.84797506,  6.84797506,  6.84797506,
            5.51180993,  6.84797506,  6.40258668,  6.84797506,  6.40258668,
            6.84797506,  6.40258668,  5.51180993,  6.84797506,  6.84797506,
            6.84797506,  6.40258668,  6.40258668,  6.84797506,  6.84797506,
            6.40258668,  5.06642155,  6.84797506,  5.51180993,  6.84797506,
            5.06642155,  6.84797506,  4.62103317,  6.84797506,  6.84797506,
            6.40258668,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            4.62103317,  6.40258668,  6.84797506,  4.17564479,  6.84797506,
            6.84797506,  6.84797506,  6.40258668,  6.84797506,  6.84797506,
            6.84797506,  6.40258668,  6.84797506,  5.51180993,  6.84797506,
            6.84797506,  5.9571983,   6.40258668,  6.84797506,  5.06642155,
            6.84797506,  6.84797506,  5.51180993,  6.84797506,  3.73025641,
            6.40258668,  6.40258668,  6.84797506,  6.40258668,  6.84797506,
            5.51180993,  6.40258668,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  5.9571983,   4.62103317,  6.84797506,  6.84797506,
            5.06642155,  5.06642155,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  5.9571983,   6.84797506,  5.9571983,
            4.62103317,  6.84797506,  4.62103317,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  3.73025641,  6.84797506,  5.51180993,
            6.84797506,  6.40258668,  6.84797506,  6.40258668,  6.84797506,
            5.06642155,  6.84797506,  6.84797506,  5.06642155,  3.73025641,
            3.28486804,  4.17564479,  5.51180993,  6.40258668,  6.84797506,
            4.62103317,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.40258668,  6.84797506,  6.84797506,  5.9571983,   6.84797506,
            6.84797506,  6.84797506,  6.84797506,  5.06642155,  6.84797506,
            6.84797506,  6.84797506,  5.9571983,   6.84797506,  3.73025641,
            6.84797506,  6.84797506,  5.51180993,  6.40258668,  4.17564479,
            5.06642155,  6.84797506,  6.84797506,  4.17564479,  6.84797506,
            6.40258668,  6.84797506,  6.40258668,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  5.06642155,  6.84797506,
            6.84797506,  6.40258668,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  5.9571983,   6.84797506,  4.62103317,  6.84797506,
            6.84797506,  6.84797506,  5.06642155,  6.40258668,  6.40258668,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.40258668,  5.06642155,  6.40258668,  6.84797506,  6.84797506,
            5.51180993,  6.84797506,  5.9571983,   6.84797506,  6.84797506,
            6.84797506,  5.9571983,   6.84797506,  6.40258668,  6.40258668,
            6.84797506,  6.84797506,  6.40258668,  6.84797506,  5.51180993,
            6.84797506,  5.51180993,  5.51180993,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            5.06642155,  4.62103317,  6.84797506,  6.40258668,  6.84797506,
            5.9571983,   6.84797506,  6.84797506,  6.84797506,  5.06642155,
            6.84797506,  5.9571983,   6.84797506,  5.06642155,  6.84797506,
            6.84797506,  5.06642155,  5.9571983,   6.40258668,  6.84797506,
            4.62103317,  6.40258668,  6.84797506,  6.40258668,  5.9571983,
            6.84797506,  4.62103317,  5.51180993,  5.06642155,  6.84797506,
            6.84797506,  6.40258668,  5.51180993,  6.84797506,  5.9571983,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  4.62103317,  6.84797506,
            6.40258668,  5.51180993,  5.9571983,   6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.40258668,  6.84797506,  6.84797506,
            5.51180993,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  4.62103317,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.40258668,  6.84797506,  6.84797506,  6.84797506,
            6.40258668,  6.84797506,  5.06642155,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  5.51180993,  6.40258668,  6.84797506,
            5.9571983,   5.9571983,   6.84797506,  6.84797506,  6.84797506,
            4.62103317,  6.84797506,  5.9571983,   6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  6.84797506,  4.17564479,  6.40258668,  6.40258668,
            5.51180993,  6.84797506,  5.51180993,  6.84797506,  6.84797506,
            6.84797506,  4.62103317,  6.84797506,  4.17564479,  6.84797506,
            6.84797506,  5.51180993,  6.40258668,  5.06642155,  6.84797506,
            6.84797506,  6.84797506,  6.40258668,  6.84797506,  5.9571983,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  5.51180993,
            4.62103317,  6.84797506,  6.40258668,  6.84797506,  6.84797506,
            5.9571983,   5.51180993,  5.9571983,   6.84797506,  4.62103317,
            6.84797506,  6.84797506,  5.06642155,  6.40258668,  6.84797506,
            5.06642155,  5.9571983,   6.84797506,  6.84797506,  6.40258668,
            6.40258668,  5.9571983,   6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  5.06642155,  6.84797506,  6.40258668,
            6.84797506,  5.06642155,  5.06642155,  5.9571983,   6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  5.06642155,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            5.9571983,   6.84797506,  5.06642155,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.40258668,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            3.73025641,  6.40258668,  5.51180993,  6.84797506,  5.51180993,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            5.06642155,  6.84797506,  6.40258668,  6.84797506,  6.84797506,
            6.84797506,  6.40258668,  6.84797506,  6.84797506,  6.84797506,
            6.40258668,  5.06642155,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.40258668,  6.84797506,  5.9571983,   6.84797506,
            6.84797506,  6.84797506,  6.84797506,  5.51180993,  5.06642155,
            6.84797506,  6.84797506,  6.84797506,  4.62103317,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  4.17564479,  6.84797506,
            5.51180993,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            3.73025641,  6.84797506,  6.40258668,  6.84797506,  6.84797506,
            6.84797506,  5.06642155,  6.84797506,  6.84797506,  4.62103317,
            6.40258668,  6.84797506,  5.51180993,  6.84797506,  6.84797506,
            5.9571983,   6.84797506,  6.84797506,  6.84797506,  5.9571983,
            5.06642155,  6.84797506,  5.06642155,  6.40258668,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.40258668,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  4.17564479,  5.51180993,  6.84797506,  6.84797506,
            6.40258668,  4.62103317,  6.84797506,  5.9571983,   6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  4.17564479,  6.40258668,  6.84797506,
            6.84797506,  5.9571983,   6.84797506,  5.51180993,  6.84797506,
            5.9571983,   5.06642155,  6.84797506,  6.84797506,  6.40258668,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  5.9571983,
            5.51180993,  6.84797506,  5.9571983,   6.40258668,  4.62103317,
            6.84797506,  5.06642155,  4.17564479,  5.51180993,  6.84797506,
            6.40258668,  5.9571983,   6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.40258668,  5.9571983,   6.40258668,  6.84797506,
            5.9571983,   6.84797506,  5.9571983,   5.51180993,  4.17564479,
            5.9571983,   6.40258668,  6.84797506,  5.51180993,  6.40258668,
            5.51180993,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.40258668,  6.84797506,  5.9571983,   6.84797506,  6.84797506,
            6.84797506,  6.84797506,  5.06642155,  5.9571983,   6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  5.06642155,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  5.51180993,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  3.73025641,
            4.17564479,  6.84797506,  5.06642155,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  4.17564479,
            5.51180993,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  5.9571983,   6.84797506,  6.84797506,  6.84797506,
            4.62103317,  6.84797506,  5.06642155,  5.06642155,  6.84797506,
            6.40258668,  5.9571983,   6.84797506,  4.62103317,  6.84797506,
            6.84797506,  6.84797506,  5.9571983,   6.84797506,  6.84797506,
            5.9571983,   5.51180993,  6.84797506,  5.06642155,  6.84797506,
            4.62103317,  5.9571983,   6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  4.62103317,  6.84797506,
            6.84797506,  6.84797506,  5.51180993,  5.51180993,  6.84797506,
            6.84797506,  6.84797506,  5.9571983,   6.40258668,  6.84797506,
            6.84797506,  5.51180993,  6.84797506,  6.84797506,  5.9571983,
            5.51180993,  6.84797506,  6.84797506,  6.84797506,  4.17564479,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  5.9571983,   6.84797506,  6.40258668,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  6.40258668,
            6.84797506,  6.84797506,  6.84797506,  4.62103317,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  3.73025641,
            6.84797506,  6.84797506,  6.84797506,  5.9571983,   4.62103317,
            5.51180993,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  5.9571983,   6.40258668,  6.84797506,
            5.51180993,  5.9571983,   5.9571983,   6.84797506,  6.84797506,
            5.51180993,  6.84797506,  6.84797506,  5.51180993,  6.40258668,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  5.51180993,
            6.40258668,  5.51180993,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  5.51180993,  6.84797506,  5.9571983,   6.84797506,
            6.84797506,  6.84797506,  6.84797506,  5.06642155,  6.84797506,
            6.84797506,  6.84797506,  6.40258668,  5.06642155,  5.51180993,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.40258668,  6.84797506,  6.84797506,  5.9571983,   6.40258668,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  6.84797506,
            5.06642155,  4.17564479,  6.84797506,  6.84797506,  5.06642155,
            6.84797506,  6.84797506,  6.40258668,  6.84797506,  5.51180993,
            6.84797506,  6.84797506,  6.84797506,  4.17564479,  5.9571983,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  5.9571983,
            5.51180993,  6.84797506,  6.40258668,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  5.9571983,
            5.9571983,   6.84797506,  5.9571983,   4.17564479,  6.40258668,
            6.84797506,  6.84797506,  6.84797506,  6.40258668,  5.9571983,
            5.51180993,  6.84797506,  5.51180993,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  5.51180993,  5.06642155,
            6.84797506,  6.40258668,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  6.84797506,  6.84797506,  5.51180993,  6.40258668,
            6.84797506,  6.84797506,  3.28486804,  5.9571983,   6.84797506,
            3.73025641,  6.84797506,  6.84797506,  6.84797506,  4.17564479,
            6.84797506,  6.40258668,  6.40258668,  6.84797506,  5.51180993,
            6.84797506,  6.84797506,  6.84797506,  6.84797506,  6.84797506,
            6.84797506,  4.62103317,  6.40258668,  6.84797506,  6.40258668,
            5.06642155,  6.84797506,  6.84797506,  5.51180993,  4.62103317,
            6.84797506,  6.40258668,  6.84797506,  5.06642155,  5.9571983,
            6.40258668,  5.51180993,  6.84797506,  6.84797506,  6.84797506,
            6.40258668,  6.84797506,  6.84797506,  4.17564479,  6.84797506,
            5.06642155,  6.84797506,  3.56230611,  4.89847125,  5.34385962,
            4.45308287,  5.789248,    5.789248,    5.789248,    5.789248,
            5.789248,    5.34385962,  5.34385962,  5.789248,    5.789248,
            4.00769449,  5.34385962,  4.45308287,  5.789248,    5.34385962,
            3.56230611,  2.67152936,  5.789248,    5.34385962,  5.789248,
            2.67152936,  5.789248,    5.34385962,  3.56230611,  4.89847125,
            5.789248,    3.11691773,  5.789248,    5.789248,    4.89847125,
            5.789248,    3.56230611,  3.56230611,  5.789248,    5.789248,
            5.789248,    4.89847125,  5.789248,    4.89847125,  4.00769449,
            5.789248,    3.56230611,  5.789248,    2.22614098,  3.11691773,
            5.789248,    5.789248,    4.00769449,  3.11691773,  5.789248,
            5.789248,    5.789248,    5.789248,    5.789248,    5.789248,
            2.67152936,  5.789248,    5.789248,    4.00769449,  3.56230611,
            4.45308287,  5.789248,    5.789248,    4.89847125,  5.789248,
            3.56230611,  5.789248,    4.89847125,  2.67152936,  5.34385962,
            4.45308287,  5.789248,    4.45308287,  5.789248,    5.789248,
            4.89847125,  4.45308287,  5.789248,    5.789248,    5.789248,
            5.789248,    5.789248,    4.89847125,  5.789248,    5.34385962,
            5.34385962,  5.789248,    5.789248,    3.56230611,  5.789248,
            3.56230611,  5.789248,    4.45308287,  5.789248,    5.789248,
            5.34385962,  5.789248,    3.11691773,  5.789248,    5.789248,
            3.11691773,  4.00769449,  5.789248,    5.789248,    5.34385962,
            3.56230611,  3.11691773,  5.789248,    4.45308287,  5.789248,
            5.789248,    5.789248,    3.11691773,  5.789248,    5.789248,
            5.789248,    5.789248,    4.45308287,  5.789248,    4.00769449,
            5.789248,    4.45308287,  4.45308287,  5.789248,    4.89847125,
            4.00769449,  4.00769449,  4.89847125,  4.00769449,  5.789248,
            5.789248,    5.789248,    5.789248,    5.789248,    5.789248,
            5.789248,    5.34385962,  3.56230611,  5.789248,    3.56230611,
            5.789248,    5.789248,    5.789248,    2.67152936,  5.789248,
            5.789248,    5.789248,    5.789248,    4.89847125,  4.89847125,
            5.789248,    2.67152936,  5.789248,    4.89847125,  5.789248,
            5.789248,    4.45308287,  3.11691773,  5.789248,    4.89847125,
            5.789248,    2.67152936,  2.67152936,  5.34385962,  4.00769449,
            5.789248,    5.789248,    5.34385962,  5.789248,    5.789248,
            4.00769449,  5.789248,    5.34385962,  4.89847125,  5.789248,
            2.67152936,  5.34385962,  5.789248,    5.789248,    4.45308287,
            5.34385962,  5.789248,    5.789248,    5.789248,    5.789248,
            2.67152936,  5.789248,    3.56230611,  4.00769449,  5.34385962,
            5.789248,    3.11691773,  2.67152936,  5.789248,    4.45308287,
            5.789248,    3.56230611,  5.34385962,  4.89847125,  5.789248,
            3.56230611,  4.00769449,  5.789248,    3.11691773,  5.789248,
            5.789248,    3.56230611,  5.34385962,  4.89847125,  4.89847125,
            5.789248,    5.789248,    2.67152936,  3.11691773,  5.789248,
            5.789248,    5.789248,    5.789248,    5.34385962,  5.34385962,
            5.789248,    5.789248,    4.89847125,  5.789248,    4.45308287,
            5.34385962,  5.789248,    4.45308287,  4.45308287,  5.789248,
            5.789248,    3.56230611,  4.89847125,  3.56230611,  4.89847125,
            4.45308287,  5.789248,    4.00769449,  5.789248,    4.89847125,
            5.789248,    5.789248,    5.789248,    4.45308287,  4.00769449,
            5.789248,    4.89847125,  4.89847125,  3.56230611,  5.789248,
            5.789248,    5.34385962,  3.56230611,  3.11691773,  3.56230611,
            4.00769449,  5.789248,    4.45308287,  4.89847125,  5.789248,
            5.789248,    5.789248,    4.00769449,  4.89847125,  2.67152936,
            5.789248,    5.789248,    5.789248,    4.89847125,  5.34385962,
            5.789248,    5.789248,    5.789248,    5.789248,    5.789248,
            5.789248,    5.789248,    4.00769449,  5.34385962,  4.89847125,
            5.789248,    4.89847125,  4.00769449,  5.789248,    5.789248,
            4.89847125,  5.789248,    5.34385962,  5.789248,    2.67152936,
            5.789248,    5.34385962,  4.00769449,  4.00769449,  5.789248,
            5.34385962,  3.56230611,  5.789248,    4.89847125,  5.34385962,
            5.789248,    4.00769449,  4.45308287,  5.789248,    5.34385962,
            4.00769449,  3.56230611,  5.34385962,  2.67152936,  5.789248,
            3.56230611,  4.89847125,  4.45308287,  5.789248,    5.789248,
            5.789248,    5.789248,    5.789248,    4.00769449,  5.789248,
            4.45308287,  5.789248,    5.789248,    5.34385962,  5.789248,
            5.789248,    5.789248,    5.789248,    5.34385962,  5.34385962,
            5.789248,    5.34385962,  5.789248,    5.789248,    5.789248,
            5.789248,    5.34385962,  4.45308287,  5.789248,    5.34385962,
            5.789248,    5.34385962,  5.789248,    5.789248,    5.789248,
            5.789248,    4.45308287,  5.34385962,  3.56230611,  2.67152936,
            5.789248,    5.789248,    3.11691773,  5.789248,    4.45308287,
            5.789248,    5.789248,    5.789248,    5.789248,    4.00769449,
            4.00769449,  4.00769449,  5.789248,    5.789248,    5.789248,
            5.789248,    4.89847125,  3.11691773,  4.45308287,  5.789248,
            4.00769449,  5.789248,    5.789248,    5.789248,    5.789248,
            5.789248,    4.00769449,  5.789248,    5.789248,    5.789248,
            5.789248,    4.00769449,  5.789248,    4.00769449,  3.56230611,
            5.789248,    4.89847125,  5.789248,    5.789248,    5.789248,
            5.789248,    5.789248,    5.789248,    5.789248,    5.789248,
            4.89847125,  5.34385962,  5.789248,    5.789248,    5.789248,
            5.789248,    5.789248,    5.789248,    5.789248,    5.34385962,
            3.56230611,  5.34385962,  5.789248,    3.56230611,  5.789248,
            4.00769449,  5.789248,    5.789248,    5.789248,    4.00769449,
            3.11691773,  5.789248,    5.789248,    4.00769449,  5.789248,
            5.789248,    5.789248,    5.789248,    5.789248,    5.789248,
            5.789248,    5.789248,    5.789248,    5.789248,    5.789248,
            5.789248,    5.789248,    3.56230611,  5.789248,    5.789248,
            5.789248,    5.789248,    3.56230611,  5.34385962,  4.00769449,
            5.789248,    4.89847125,  4.89847125,  4.00769449,  5.789248,
            5.789248,    4.45308287,  2.67152936,  5.789248,    5.789248,
            4.00769449,  5.789248,    3.56230611,  4.00769449,  5.789248,
            5.789248,    4.89847125,  5.789248,    4.45308287,  5.34385962,
            5.34385962,  3.11691773,  3.56230611,  5.789248,    4.45308287,
            5.789248,    4.89847125,  4.00769449,  4.89847125,  4.89847125,
            5.789248,    5.789248,    5.34385962,  4.00769449,  5.789248,
            5.789248,    5.789248,    5.789248,    5.789248,    5.789248,
            5.789248,    3.56230611,  4.45308287,  4.00769449,  4.89847125,
            4.45308287,  3.56230611,  4.00769449,  5.789248,    5.789248,
            5.789248,    5.789248,    3.11691773,  5.789248,    5.789248,
            5.789248,    5.789248,    5.789248,    4.00769449,  4.89847125,
            5.34385962,  3.56230611,  3.11691773,  5.789248,    4.00769449,
            5.789248,    3.56230611,  5.789248,    5.789248,    4.00769449,
            5.789248,    4.00769449,  5.789248,    5.789248,    5.789248,
            5.789248,    4.89847125,  4.00769449,  4.89847125,  5.34385962,
            2.67152936,  5.789248,    4.45308287,  5.789248,    4.89847125,
            5.789248,    5.34385962,  5.789248,    5.789248,    5.789248,
            3.56230611,  2.67152936,  5.789248,    5.789248,    5.789248,
            4.00769449,  4.89847125,  5.789248,    5.34385962,  4.89847125,
            5.34385962,  5.789248,    5.789248,    5.34385962,  5.789248,
            5.789248,    5.789248,    2.67152936,  5.34385962,  5.789248,
            5.789248,    4.89847125,  4.89847125,  5.34385962,  5.789248,
            5.789248,    4.45308287,  3.11691773,  3.56230611,  5.789248,
            5.789248,    5.789248,    5.789248,    5.789248,    5.34385962,
            5.789248,    5.789248,    4.00769449,  4.89847125,  5.789248,
            3.56230611,  5.789248,    5.34385962,  2.67152936,  5.789248,
            5.34385962,  5.789248,    5.789248,    5.789248,    5.34385962,
            5.789248,    5.789248,    5.789248,    5.789248,    5.789248,
            5.789248,    4.45308287,  5.789248,    3.11691773,  5.789248,
            5.34385962,  4.89847125,  5.34385962,  5.789248,    4.89847125,
            5.789248,    4.00769449,  4.45308287,  5.789248,    5.789248,
            5.789248,    5.789248,    5.34385962,  5.789248,    4.00769449,
            4.89847125,  4.00769449,  5.789248,    3.56230611,  5.789248,
            5.789248,    5.789248,    5.789248,    3.56230611,  5.789248,
            4.89847125,  5.789248,    5.789248,    5.789248,    5.789248,
            5.789248,    5.789248,    5.789248,    5.789248,    4.00769449,
            2.22614098,  5.789248,    5.789248,    5.789248,    5.789248,
            4.89847125,  5.789248,    3.56230611,  5.789248,    5.789248,
            4.00769449,  5.789248,    5.789248,    5.789248,    5.789248,
            5.789248,    5.789248,    3.11691773,  3.11691773,  5.789248,
            5.789248,    5.789248,    4.00769449,  5.789248,    5.34385962,
            4.45308287,  5.34385962,  4.45308287,  4.45308287,  4.89847125,
            5.789248,    4.89847125,  5.789248,    3.56230611,  5.789248,
            5.789248,    5.789248,    5.789248,    5.789248,    3.11691773,
            5.789248,    4.00769449,  5.789248,    4.89847125,  5.789248,
            4.00769449,  5.789248,    5.789248,    5.789248,    5.789248,
            5.789248,    5.34385962,  5.789248,    4.45308287,  5.34385962,
            4.45308287,  5.789248,    4.00769449,  5.789248,    3.56230611,
            5.34385962,  5.789248,    5.789248,    4.45308287,  4.00769449,
            3.56230611,  5.789248,    5.789248,    5.789248,    4.45308287,
            5.789248,    5.789248,    5.34385962,  4.89847125,  4.45308287,
            3.11691773,  5.789248,    3.56230611,  3.11691773,  5.789248,
            5.789248,    3.11691773,  3.11691773,  5.789248,    4.45308287,
            4.45308287,  5.789248,    5.789248,    4.00769449,  4.00769449,
            3.56230611,  5.789248,    4.00769449,  3.56230611,  5.789248,
            4.00769449,  5.34385962,  5.789248,    3.56230611,  5.789248,
            5.34385962,  5.789248,    4.45308287,  4.00769449,  5.789248,
            5.789248,    4.45308287,  5.34385962,  5.789248,    5.789248,
            5.789248,    5.789248,    5.789248,    5.789248,    5.34385962,
            3.11691773,  5.34385962,  2.67152936,  4.00769449,  5.789248,
            3.56230611,  5.789248,    5.34385962,  5.789248,    2.67152936,
            5.789248,    5.789248,    5.789248,    5.789248,    3.11691773,
            5.789248,    5.34385962,  3.56230611,  4.45308287,  4.89847125,
            4.00769449,  5.789248,    5.789248,    5.34385962,  4.00769449,
            4.89847125,  4.45308287,  5.789248,    5.789248,    5.789248,
            5.789248,    5.789248,    4.00769449,  5.789248,    5.789248,
            5.789248,    5.789248,    5.789248,    5.789248,    5.34385962,
            3.56230611,  5.789248,    5.789248,    5.34385962,  5.789248,
            3.11691773,  5.789248,    4.89847125,  5.789248,    4.89847125,
            5.789248,    5.34385962,  5.789248,    5.789248,    5.789248,
            5.789248,    5.34385962,  2.67152936,  5.789248,    5.789248,
            2.67152936,  3.56230611,  5.789248,    5.789248,    2.67152936,
            4.45308287,  3.56230611,  4.45308287,  5.789248,    5.789248,
            5.789248,    5.789248,    5.789248,    5.789248,    4.45308287,
            4.89847125,  5.34385962,  5.789248,    5.789248,    5.789248,
            5.789248,    5.34385962,  4.00769449,  5.789248,    5.34385962,
            5.789248,    2.67152936,  2.67152936,  5.789248,    3.56230611,
            5.789248,    3.56230611,  5.789248,    4.45308287,  2.67152936,
            5.789248,    5.789248,    2.67152936,  4.89847125,  5.789248,
            5.789248,    3.11691773,  5.789248,    4.00769449,  5.789248,
            5.789248,    3.11691773,  4.00769449,  4.89847125,  4.89847125,
            5.789248,    4.00769449,  4.45308287,  5.789248,    4.45308287,
            5.789248,    3.11691773,  4.45308287,  4.89847125,  3.56230611,
            5.789248,    5.789248,    3.56230611,  3.56230611,  3.56230611,
            5.789248,    5.789248,    4.00769449,  4.00769449,  3.11691773,
            5.789248,    5.789248,    2.67152936,  4.00769449,  5.789248,
            2.67152936,  3.56230611,  3.56230611,  4.45308287,  5.789248,
            3.56230611,  5.789248,    5.789248,    5.789248,    5.789248,
            5.789248,    5.789248,    5.34385962,  5.789248,    5.789248,
            5.789248,    5.789248,    5.789248,    5.789248,    5.789248,
            5.789248,    4.89847125,  5.789248,    5.789248,    4.89847125,
            5.789248,    5.789248,    4.89847125,  4.89847125,  5.789248,
            5.34385962,  4.45308287,  5.789248,    4.89847125,  4.00769449,
            4.45308287,  5.789248,    2.67152936,  4.45308287,  5.34385962,
            5.789248,    5.789248,    5.789248,    5.789248,    3.11691773,
            5.34385962,  5.789248,    4.89847125,  5.789248,    4.45308287,
            5.789248,    5.789248,    4.89847125,  4.45308287,  5.789248,
            5.789248,    5.789248,    5.789248,    5.789248,    4.45308287,
            4.45308287,  3.11691773,  4.89847125,  5.789248,    3.11691773,
            3.11691773,  5.789248,    4.00769449,  5.34385962,  3.11691773,
            4.89847125,  3.11691773,  3.56230611,  4.89847125,  5.789248,
            5.789248,    4.45308287,  4.89847125,  5.789248,    4.45308287,
            5.789248,    4.89847125,  4.45308287,  2.67152936,  5.789248,
            5.789248,    5.789248,    4.89847125,  5.789248,    5.789248,
            5.789248,    5.789248,    4.89847125,  5.34385962,  4.45308287,
            5.789248,    4.00769449,  3.56230611,  4.89847125,  5.789248,
            4.45308287,  5.789248,    5.789248,    3.11691773,  5.789248,
            5.34385962,  5.789248,    5.789248,    5.789248,    3.56230611,
            2.22614098,  5.789248,    5.789248,    3.56230611,  5.34385962,
            4.00769449,  5.789248,    2.67152936,  5.789248,    4.00769449,
            5.789248,    5.789248,    4.00769449,  2.67152936,  5.789248,
            4.89847125,  4.45308287,  5.789248,    5.34385962,  5.789248,
            5.789248,    4.45308287,  5.789248,    5.789248,    5.789248,
            4.00769449,  4.89847125,  5.789248,    4.89847125,  5.789248,
            4.89847125,  3.56230611,  4.00769449,  5.789248,    5.789248,
            5.789248,    5.34385962,  5.789248,    5.789248,    5.34385962,
            5.789248,    5.789248,    5.789248,    5.789248,    5.789248,
            5.789248,    5.789248,    5.789248,    5.789248,    5.789248,
            3.56230611,  3.56230611,  4.00769449,  5.789248,    4.45308287,
            3.56230611,  5.789248,    4.89847125,  4.89847125,  3.11691773,
            4.00769449,  5.789248,    5.34385962,  5.789248,    5.789248,
            5.789248,    5.789248,    4.45308287,  5.34385962,  4.45308287,
            5.789248,    3.11691773,  5.789248,    4.45308287,  4.45308287,
            4.89847125,  5.789248,    4.89847125,  4.45308287,  5.789248,
            5.34385962,  3.56230611,  3.56230611,  5.34385962,  5.789248,
            4.00769449,  5.34385962,  4.45308287,  4.45308287,  3.56230611,
            5.789248,    4.00769449,  5.789248,    5.789248,    4.89847125,
            5.34385962,  3.11691773,  4.00769449,  5.789248,    5.34385962,
            5.789248,    5.789248,    5.34385962,  5.789248,    4.45308287,
            5.34385962,  4.89847125,  5.789248,    5.789248,    5.34385962,
            3.56230611,  5.789248,    5.789248,    5.789248,    5.34385962,
            5.34385962,  5.789248,    3.11691773,  5.789248,    5.789248,
            3.56230611,  5.789248,    5.789248,    5.789248,    4.45308287,
            5.789248,    4.89847125,  5.789248,    5.789248,    5.789248,
            4.00769449,  4.45308287,  5.34385962,  5.789248,    3.56230611,
            4.00769449,  5.34385962,  4.45308287,  5.789248,    5.34385962,
            5.34385962,  2.67152936,  5.789248,    5.789248,    5.789248,
            5.789248,    5.789248,    5.34385962,  5.789248,    4.89847125,
            5.789248,    5.789248,    5.789248,    4.45308287,  5.789248,
            3.11691773,  4.00769449,  5.789248,    5.789248,    5.34385962,
            5.789248,    5.789248,    5.789248,    5.789248,    5.789248,
            5.789248,    5.789248,    2.22614098,  5.789248,    4.00769449,
            5.34385962,  4.89847125,  5.789248,    5.789248,    4.00769449,
            5.789248,    3.56230611,  2.67152936,  5.789248,    3.56230611,
            2.67152936,  4.89847125,  5.789248,    5.789248,    5.789248,
            4.45308287,  5.789248,    4.89847125,  4.00769449,  2.67152936,
            4.89847125,  5.789248,    2.22614098,  3.56230611,  4.45308287,
            5.34385962,  5.34385962,  3.11691773,  4.45308287,  4.45308287,
            3.11691773,  4.45308287,  5.34385962,  4.45308287,  5.34385962,
            4.00769449,  4.45308287,  5.789248,    5.789248,    5.789248,
            5.789248,    4.00769449,  5.789248,    5.789248,    5.789248,
            4.45308287,  5.789248,    5.789248,    5.789248,    4.00769449,
            5.34385962,  4.00769449,  4.45308287,  4.89847125,  4.00769449,
            5.789248,    5.789248,    5.789248,    2.67152936,  5.789248,
            5.789248,    5.789248,    5.789248,    5.789248,    5.789248,
            5.789248,    4.00769449,  3.11691773,  5.34385962,  4.89847125,
            5.34385962])


class Committee:
    def __init__(self):
        self.resids = np.array([
            [-5.04950800e-01,  -6.29721800e-01, -8.35499100e+01,
             -1.30628500e+00,  -6.62028600e+00],
            [-2.34152200e-01,  -2.55423500e-01,  -2.16830700e+02,
             -7.58866000e-01,  -7.18370200e+00],
            [1.02423700e+00,   7.98775800e-01,   4.83736300e+02,
             2.50351500e+00,   2.25135300e+01],
            [-2.85061700e-01,  -3.17796600e-01,  -7.04115100e+04,
             -2.37991800e+00,  -1.41745600e+02],
            [2.09902500e-01,   1.96787700e-01,   2.24751400e+03,
             9.51945500e-01,   2.17724200e+01],
            [-4.03483500e-01,  -4.75741500e-01,  -1.95633600e+04,
             -2.63502600e+00,  -8.89461400e+01],
            [-1.64413400e-01,  -1.74401100e-01,  -1.73310300e+04,
             -1.16235500e+00,  -5.34213500e+01],
            [-4.29607700e-01,  -5.13466700e-01,  -5.30037000e+03,
             -2.24496200e+00,  -4.78260300e+01],
            [3.23713000e-01,   2.94184600e-01,   4.11079400e+03,
             1.48684400e+00,   3.65598400e+01],
            [1.50367200e-01,   1.43429400e-01,   7.28532100e+03,
             8.85542900e-01,   3.31355000e+01],
            [4.21288600e-01,   3.73428000e-01,   1.37315700e+03,
             1.52133200e+00,   2.41570200e+01],
            [4.50658700e-01,   3.96586700e-01,   1.70146900e+03,
             1.66177900e+00,   2.78032600e+01],
            [2.43537500e-01,   2.26174000e-01,   3.18402300e+03,
             1.13656200e+00,   2.79073400e+01],
            [1.05182900e+00,   8.16205400e-01,   6.00135200e+03,
             3.89079700e+00,   7.97131300e+01],
            [-5.54450300e-01,  -7.12749000e-01,  -2.09485200e+03,
             -2.45496500e+00,  -3.42189900e+01],
            [-6.05750600e-01,  -8.06411100e-01,  -2.74738200e+02,
             -1.90774400e+00,  -1.30510500e+01],
            [-3.41215700e-01,  -3.90244600e-01,  -6.31138000e+02,
             -1.27022900e+00,  -1.47600100e+01],
            [2.21898500e-01,   2.07328700e-01,   6.91135800e+02,
             8.16876400e-01,   1.24392900e+01],
            [2.45592500e-01,   2.26639200e-01,   1.99250600e-01,
             2.57948300e-01,   2.74723700e-01],
            [-7.58952600e-01,  -1.15300800e+00,  -2.56739000e+02,
             -2.40716600e+00,  -1.41474200e+01]])
        self.null_deviance = 27.81104693643434  # from R, Rpy bug
        self.params = np.array([
            -0.0268147,   1.25103364,  2.91070663,
            -0.34799563,  0.00659808, -0.31303026, -6.44847076])
        self.bse = np.array([
            1.99956263e-02, 4.76820254e-01,
            6.48362654e-01, 4.17956107e-01, 1.41512690e-03, 1.07770186e-01,
            1.99557656e+00])
        self.aic_R = 216.66573352377935
        self.aic_Stata = 10.83328660860436
        self.deviance = 5.615520158267981
        self.scale = 0.38528595746569905
        self.llf = -101.33286676188968  # from R
        self.llf_Stata = -101.3328660860436  # same as R
        self.bic_Stata = -33.32900074962649
        self.chi2 = 5.008550263545408
        self.df_model = 6
        self.df_resid = 13
        self.fittedvalues = np.array([
            12.62019383,  30.18289514, 21.48377849, 496.74068604,
            103.23024673,  219.94693494,  324.4301163,   110.82526477,
            112.44244488,  219.86056381,   56.84399998,   61.19840382,
            114.09290269,   75.29071944,   61.21994387,   21.05130889,
            42.75939828,   55.56133536,    0.72532053,   18.14664665])


class Wfs:
    """
    Wfs used for TestGlmPoissonOffset

    Results are from Stata and R.
    """
    def __init__(self):

        self.resids = glm_test_resids.wfs_resids
        self.null_deviance = 3731.85161919  # from R
        self.params = [
            .9969348, 1.3693953, 1.6137574, 1.7849111, 1.9764051,
            .11241858, .15166023, .02297282, -.10127377, -.31014953,
            -.11709716]
        self.bse = [
            .0527437, .0510688, .0511949, .0512138, .0500341,
            .0324963, .0283292, .0226563, .0309871, .0552107, .0549118]
        self.aic_R = 522.14215776  # R adds 2 for dof to AIC
        self.aic_Stata = 7.459173652869477  # stata divides by nobs
        # self.deviance = 70.6652992116034   # from Stata
        self.deviance = 70.665301270867  # from R
        self.scale = 1.0
        self.llf = -250.0710778504317  # from Stata, ours with scale=1
        self.bic_Stata = -179.9959200693088  # no bic in R?
        self.df_model = 10
        self.df_resid = 59

        # TODO: taken from Stata; not available in sm yet
        self.chi2 = 2699.138063147485

        self.fittedvalues = [
            7.11599, 19.11356, 33.76075, 33.26743, 11.94399,
            27.49849, 35.07923, 37.22563, 64.18037, 108.0408,
            100.0948, 35.67896, 24.10508, 73.99577, 52.2802,
            38.88975, 35.06507, 102.1198, 107.251, 41.53885,
            196.3685, 335.8434, 205.3413, 43.20131, 41.98048,
            96.65113, 63.2286, 30.78585, 70.46306, 172.2402,
            102.5898, 43.06099, 358.273, 549.8983, 183.958,
            26.87062, 62.53445, 141.687, 52.47494, 13.10253,
            114.9587, 214.803, 90.33611, 18.32685, 592.5995,
            457.4376, 140.9273, 3.812064, 111.3119, 97.62744,
            57.48056, 19.43552, 130.4872, 151.7268, 69.67963,
            13.04879, 721.728, 429.2136, 128.2132, 9.04735,
            301.7067, 177.3487, 46.40818, 4.707507, 330.4211,
            330.7497, 84.38604, 1456.757, 451.005, 67.51025]


class CpunishTweediePower15:
    """
    # From R
    setwd('c:/workspace')
    data <- read.csv('cpunish.csv', sep=",")

    library(statmod)
    library(tweedie)

    summary(glm(EXECUTIONS ~ INCOME + SOUTH - 1,
            family=tweedie(var.power=1.5, link.power=1),
            data=data))
    """
    def __init__(self):

        resid_resp = [
            28.90498242, 0.5714367394, 4.3135711827, -3.7417822942,
            -4.9544111888, 0.4666602184, 0.0747051827, -6.114236142,
            -1.0048540116, -6.9747602544, -0.7626907093,
            -0.5688093336, -6.9845579527, -1.1594503855,
            -0.6365453438, -0.3994222036, -0.732355528]
        resid_dev = [
            3.83881147757395, 0.113622743768915, 2.01981988071128,
            -0.938107751845672, -1.29607304923555, 0.316205676540778,
            0.045273675744568, -1.69968893354602, -0.699080227540624,
            -2.1707839733642, -0.568738719015137, -0.451266938413727,
            -2.17218106358745, -0.774613533242944, -0.493831656345955,
            -0.336453094366771, -0.551210030548659]
        resid_pear = [
            6.02294407053171, 0.115516970886608, 2.9148208139849,
            -0.806210703943481, -1.04601155367613, 0.338668788938945,
            0.045708693925888, -1.27176471794657, -0.5964031365026,
            -1.46974255264233, -0.498557360800493,
            -0.405777068096011, -1.47045242302365, -0.65086941662954,
            -0.439928270112046, -0.310433407220704,
            -0.485001313250992]
        resid_work = [
            28.9049727916181, 0.571427719513967, 4.31357425907762,
            -3.74179256698823, -4.9544210736226, 0.466663015515745,
            0.0747086948013966, -6.114245735344, -1.00485035431368,
            -6.97477010217068, -0.76268749374494, -0.568806471745149,
            -6.98456778258272, -1.15944644619981, -0.636542358439925,
            -0.399419650775458, -0.732352367853816]
        self.resid_response = resid_resp
        self.resid_deviance = resid_dev
        self.resid_pearson = resid_pear
        self.resid_working = resid_work
        # self.null_deviance = 3731.85161919 # N/A
        self.params = [0.0000471043, 6.4721324886]
        self.bse = [0.0000246888, 3.5288126173]
        # self.aic_R = 522.14215776 # R adds 2 for dof to AIC
        # self.aic_Stata = 7.459173652869477  # stata divides by nobs
        # self.deviance = 70.6652992116034   # from Stata
        self.deviance = 36.087307138233  # from R
        # self.scale = 1.0
        # self.llf = -250.0710778504317  # from Stata, ours with scale=1
        # self.bic_Stata = -179.9959200693088  # no bic in R?
        self.df_model = 1
        self.df_resid = 15

        # TODO: taken from Stata; not available in sm yet
        # self.chi2 = 2699.138063147485

        self.fittedvalues = [
            8.09501758000751, 8.42856326056927,
            1.68642881732415, 7.74178229423817,
            7.95441118875248, 1.53333978161934,
            1.92529481734232, 8.11423614202829,
            2.00485401159015, 7.97476025442155,
            1.76269070926448, 1.56880933358418,
            7.98455795270665, 2.15945038549266,
            1.63654534384372, 1.39942220361664,
            1.73235552803559]


class CpunishTweediePower2:
    """
    # From R
    setwd('c:/workspace')
    data <- read.csv('cpunish.csv', sep=",")

    library(statmod)
    library(tweedie)

    summary(glm(EXECUTIONS ~ INCOME + SOUTH - 1,
            family=tweedie(var.power=2, link.power=1),
            data=data))
    """
    def __init__(self):
        resid_resp = [
            28.9397568116168, 0.605199215492085, 4.30845487128123,
            -3.7059362524505, -4.91921022348665, 0.46200835064931,
            0.068864196242604, -6.07952005594693, -1.01093636580438,
            -6.9396210244365, -0.768038385056284, -0.573568809339664,
            -6.94944844711606, -1.16600175635393, -0.641510318056987,
            -0.403667790321936, -0.737611172529194]
        resid_dev = [
            2.03295746713119, 0.0704291140028282, 1.60058476017728,
            -0.591230836989137, -0.836067997150736, 0.274690511542166,
            0.0352446721149477, -1.13465831620614, -0.625909330466303,
            -1.5477830210949, -0.520517540529698, -0.421531194473357,
            -1.54848147513823, -0.684927882583903, -0.45784673829438,
            -0.320960880764019, -0.505992145923248]
        resid_pear = [
            3.59043221590711, 0.0720921473930558, 2.54705286789752,
            -0.480919661289957, -0.621174344999372,
            0.300397177607798, 0.0356599448410699,
            -0.752460543924524, -0.502719222246499,
            -0.874049404005278, -0.434401419984914,
            -0.364501892726482, -0.874205109115113,
            -0.538319857282425, -0.390804925805356,
            -0.287580717535275, -0.424497254731367]
        resid_work = [
            28.9397568116168, 0.605199215492085, 4.30845487128123,
            -3.7059362524505, -4.91921022348665, 0.46200835064931,
            0.068864196242604, -6.07952005594693, -1.01093636580438,
            -6.9396210244365, -0.768038385056284, -0.573568809339664,
            -6.94944844711606, -1.16600175635393, -0.641510318056987,
            -0.403667790321936, -0.737611172529194]
        self.resid_response = resid_resp
        self.resid_deviance = resid_dev
        self.resid_pearson = resid_pear
        self.resid_working = resid_work
        # self.null_deviance = 3731.85161919 # N/A
        self.params = [4.72472244209477e-05, 6.43243456540827]
        self.bse = [1.86839521185429e-05, 3.83231672422612]
        # self.aic_R = 522.14215776 # R adds 2 for dof to AIC
        # self.aic_Stata = 7.459173652869477  # stata divides by nobs
        # self.deviance = 70.6652992116034   # from Stata
        self.deviance = 15.7840685407599  # from R
        # self.scale = 1.0
        # self.llf = -250.0710778504317  # from Stata, ours with scale=1
        # self.bic_Stata = -179.9959200693088 # no bic in R?
        self.df_model = 1
        self.df_resid = 15

        # TODO: taken from Stata; not available in sm yet
        # self.chi2 = 2699.138063147485

        self.fittedvalues = [
            8.06024318838318, 8.39480078450791,
            1.69154512871877, 7.7059362524505,
            7.91921022348665, 1.53799164935069,
            1.9311358037574, 8.07952005594693,
            2.01093636580438, 7.9396210244365,
            1.76803838505628, 1.57356880933966,
            7.94944844711606, 2.16600175635393,
            1.64151031805699, 1.40366779032194,
            1.73761117252919]


class CpunishTweedieLog1:
    """
    # From R
    setwd('c:/workspace')
    data <- read.csv('cpunish.csv', sep=",")

    library(statmod)
    library(tweedie)

    summary(glm(EXECUTIONS ~ INCOME + SOUTH - 1,
            family=tweedie(var.power=1, link.power=0),
            data=data))
    """
    def __init__(self):
        resid_resp = [
            28.7231009386298, -0.307318358456484, 4.19015460156576,
            -3.30975297068573, -4.87746969906705, 0.285041779927669,
            0.0315071085472043, -6.33304532673002, -1.02436294926752,
            -6.9340610414309, -0.859055122126197, -0.736490247380883,
            -6.96145354225969, -1.13750232106315, -0.778363801217565,
            -0.636042191521576, -0.839322392162821]
        resid_dev = [
            7.30513948467594, -0.101296157943519, 2.44987904003561,
            -1.34021826264378, -1.99062116973315, 0.212014827300475,
            0.0223969676885324, -2.63775728156667, -0.798884085657077,
            -3.11862021596631, -0.691356293575324, -0.607658243497501,
            -3.12628915913493, -0.869326536299756, -0.636663290048755,
            -0.536212950673418, -0.67812263418512]
        resid_pear = [
            9.98383729954486, -0.100734032611758, 3.11465040934513,
            -1.22417704160631, -1.73780566805242, 0.217661565866984,
            0.0224564769560215, -2.19386916576256,
            -0.719962160947025, -2.46172701579962,
            -0.630049829146329, -0.558895774299477,
            -2.4671965358931, -0.778034748813176,
            -0.583676657782738, -0.497265896656757,
            -0.61887064145702]
        resid_work = [
            3.47027319357873, -0.0330190014589175, 2.31520029566659,
            -0.452785885372436, -0.619167053050639,
            0.166209168591668, 0.0160057009522403,
            -0.759991705123147, -0.506017436072008,
            -0.873961141113221, -0.46209233491888,
            -0.424125760851072, -0.874394795536774,
            -0.532164250702372, -0.437685360377137,
            -0.388768819543728, -0.456321521305397]
        self.resid_response = resid_resp
        self.resid_deviance = resid_dev
        self.resid_working = resid_work
        self.resid_pearson = resid_pear
        # self.null_deviance = 3731.85161919 # N/A
        self.params = [1.65700638623525e-05, 1.54257997850499]
        self.bse = [1.81044999017907e-05, 0.725739640176733]
        # self.aic_R = 522.14215776 # R adds 2 for dof to AIC
        # self.aic_Stata = 7.459173652869477  # stata divides by nobs
        # self.deviance = 70.6652992116034   # from Stata
        self.deviance = 95.0325613464258  # from R
        # self.scale = 1.0
        # self.llf = -250.0710778504317  # from Stata, ours with scale=1
        # self.bic_Stata = -179.9959200693088  # no bic in R?
        self.df_model = 1
        self.df_resid = 15

        # TODO: taken from Stata; not available in sm yet
        # self.chi2 = 2699.138063147485

        self.fittedvalues = [
            8.27689906137016, 9.30731835845648,
            1.80984539843424, 7.30975297068573,
            7.87746969906705, 1.71495822007233,
            1.9684928914528, 8.33304532673002,
            2.02436294926752, 7.9340610414309,
            1.8590551221262, 1.73649024738088,
            7.96145354225969, 2.13750232106315,
            1.77836380121756, 1.63604219152158,
            1.83932239216282]


class FairTweedieLog15:
    """
    # From R
    setwd('c:/workspace')
    data <- read.csv('fair.csv', sep=",")

    library(statmod)
    library(tweedie)

    model <- glm(affairs ~ rate_marriage + age + yrs_married -1, data=data,
             family=tweedie(var.power=1.5, link.power = 0))
    r <- resid(model, type='response')
    paste(as.character(r[1:17]), collapse=",")
    r <- resid(model, type='deviance')
    paste(as.character(r[1:17]), collapse=",")
    r <- resid(model, type='pearson')
    paste(as.character(r[1:17]), collapse=",")
    r <- resid(model, type='working')
    paste(as.character(r[1:17]), collapse=",")
    paste(as.character(model$coefficients[1:17]), collapse=",")
    s <- summary(model)
    paste(as.character(sqrt(diag(s$cov.scaled))), collapse=",")
    s$deviance
    paste(as.character(model$fitted.values[1:17]), collapse=",")
    """
    def __init__(self):
        resid_resp = [
            -0.997868449815039, 2.69283106662728, 0.677397439981157,
            0.220024942629269, 4.30244966465517, 4.12917275616972,
            0.669303122309246, 1.64321562230925, 3.73361710426128,
            0.271937359562684, 1.70030700747884, 1.55430573164611,
            -0.263723852468304, 1.51263973164611, 2.75223392654071,
            0.310487741565721, 1.28077676333896,  -0.722602160018842]
        resid_dev = [
            -1.40274708439925, 2.48476334070913, 0.722690630291423,
            0.333179337353702, 4.00781035212304, 3.33344591331998,
            1.51543361886727, 2.82502498800952, 2.2795411865605,
            0.245239170945663, 0.993721205729013, 1.74920359743562,
            -0.363141475997386, 1.71412357710318, 2.57445879456298,
            0.279858474280908, 1.22953362433333, -1.84397406923697]
        resid_pear = [
            -0.923380371255914, 4.28706294677515, 0.864309147553743,
            0.366063826152319, 9.17690493704408, 6.57783985712941,
            2.39340023647571, 5.87607098775551, 3.55791152198837,
            0.260052421285998, 1.21439278430259, 2.66470328868695,
            -0.327698246542009, 2.59327105694137, 4.53096038849505,
            0.299198418236691, 1.6399313081981, -0.921987034618483]
        resid_work = [
            -0.899807800767353, 5.00583784559752, 0.937441759049674,
            0.433762277766879, 11.8128959278604, 7.6822784352496,
            3.65998654763585, 8.98568506862295, 3.50120010377224,
            0.256207345500911, 1.08551656668241, 3.18923357641756,
            -0.352302468597673, 3.10374035363038, 5.35005901385941,
            0.29552727652976, 1.78077778644209, -1]
        self.resid_response = resid_resp
        self.resid_deviance = resid_dev
        self.resid_working = resid_work
        self.resid_pearson = resid_pear
        # self.null_deviance = 3731.85161919 # N/A
        self.params = [
            -0.389168171340452, 0.0670222370664611, -0.0970852004566712]
        self.bse = [
            0.0323435784513691, 0.0063805300018014, 0.00893580175352525]
        # self.aic_R = 522.14215776 # R adds 2 for dof to AIC
        # self.aic_Stata = 7.459173652869477  # stata divides by nobs
        # self.deviance = 70.6652992116034   # from Stata
        self.deviance = 20741.82  # from R
        # self.scale = 1.0
        # self.llf = -250.0710778504317  # from Stata, ours with scale=1
        # self.bic_Stata = -179.9959200693088  # no bic in R?
        self.df_model = 2
        self.df_resid = 6363

        # TODO: taken from Stata; not available in sm yet
        # self.chi2 = 2699.138063147485

        self.fittedvalues = [
            1.10897954981504, 0.537938133372725,
            0.722602160018842, 0.507247757370731,
            0.364216335344828, 0.537493243830281,
            0.182870377690754, 0.182870377690754,
            1.06638209573872, 1.06139564043732,
            1.56635749252116, 0.487360268353893,
            0.748572252468304, 0.487360268353893,
            0.514430573459285, 1.05062295843428,
            0.71922323666104, 0.722602160018842]
