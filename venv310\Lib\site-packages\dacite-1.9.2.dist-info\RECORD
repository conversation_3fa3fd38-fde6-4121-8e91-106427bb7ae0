dacite-1.9.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
dacite-1.9.2.dist-info/LICENSE,sha256=4XQc7hH8grgVIQ9TaNMByUDn4974mvcY2oFVAVC6Mnc,1069
dacite-1.9.2.dist-info/METADATA,sha256=avqHpzKZp8gTkzg1sUk51cpzBKTVFZN61dipgVz6dFc,17427
dacite-1.9.2.dist-info/RECORD,,
dacite-1.9.2.dist-info/WHEEL,sha256=In9FTNxeP60KnTkGw7wk6mJPYd_dQSjEZmXdBdMCI-8,91
dacite-1.9.2.dist-info/top_level.txt,sha256=FBtiau9le0LN4KVuREUl4faJJ5ZRe70wytUXbYqX8UE,7
dacite/__init__.py,sha256=dbi2vAWGqoq48V9TUHRwefM7X97gfJmUSBXdB5ffKXs,659
dacite/__pycache__/__init__.cpython-310.pyc,,
dacite/__pycache__/cache.cpython-310.pyc,,
dacite/__pycache__/config.cpython-310.pyc,,
dacite/__pycache__/core.cpython-310.pyc,,
dacite/__pycache__/data.cpython-310.pyc,,
dacite/__pycache__/dataclasses.cpython-310.pyc,,
dacite/__pycache__/exceptions.cpython-310.pyc,,
dacite/__pycache__/frozen_dict.cpython-310.pyc,,
dacite/__pycache__/generics.cpython-310.pyc,,
dacite/__pycache__/types.cpython-310.pyc,,
dacite/cache.py,sha256=1y0ZMynciy2vf0P4o3e9oDUBZJw2O9DE-kSb6Mjq_cA,605
dacite/config.py,sha256=o1SlzreG24GwWi92xJgUETXiqyhUlO2bl9dWUvaSScs,982
dacite/core.py,sha256=Z0V3VlOnhXiij_Gq_KpDvzeLemR8F9YM8zgb8lU77ZE,6014
dacite/data.py,sha256=T0YOBDA2rR3-zHA24EXgizWlsHRDE6YrArMqJi9QEBc,330
dacite/dataclasses.py,sha256=Y0gTVZYJSQuSi-6r-5WiGV2W901Payh5uAhKmkLUF3Y,925
dacite/exceptions.py,sha256=9Iy6Yy2Ykim-wpr_yFP7u2EJRhgYMluEKpRPCjt1Kqc,2645
dacite/frozen_dict.py,sha256=c3yDFG1k-VuP5Xt9lAjee3Ose2XJU19b3XxkAgRB-gQ,839
dacite/generics.py,sha256=bVCzYpvmhuEyE7SEIovkWMh1HW4fwpbf6COf3EWeORs,4197
dacite/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dacite/types.py,sha256=JMBuhnDJ72nsBj7VdqbbIYzo-BFfLkyPPr-ZJ5GYafE,5207
