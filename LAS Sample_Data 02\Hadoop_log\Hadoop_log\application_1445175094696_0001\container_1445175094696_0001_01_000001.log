2015-10-18 21:33:04,047 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445175094696_0001_000001
2015-10-18 21:33:04,672 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-18 21:33:04,672 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 1 cluster_timestamp: 1445175094696 } attemptId: 1 } keyId: -2054027300)
2015-10-18 21:33:04,984 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-18 21:33:05,843 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-18 21:33:05,922 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-18 21:33:05,968 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-18 21:33:05,968 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-18 21:33:05,968 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-18 21:33:05,968 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-18 21:33:05,968 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-18 21:33:05,968 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-18 21:33:05,968 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-18 21:33:05,968 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-18 21:33:06,015 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 21:33:06,047 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 21:33:06,078 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 21:33:06,093 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-18 21:33:06,140 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-18 21:33:06,468 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-18 21:33:06,531 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-18 21:33:06,531 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-18 21:33:06,531 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445175094696_0001 to jobTokenSecretManager
2015-10-18 21:33:06,734 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445175094696_0001 because: not enabled; too many maps; too much input;
2015-10-18 21:33:06,750 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445175094696_0001 = 1313861632. Number of splits = 10
2015-10-18 21:33:06,750 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445175094696_0001 = 1
2015-10-18 21:33:06,750 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445175094696_0001Job Transitioned from NEW to INITED
2015-10-18 21:33:06,750 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445175094696_0001.
2015-10-18 21:33:06,812 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-18 21:33:06,828 INFO [Socket Reader #1 for port 60143] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 60143
2015-10-18 21:33:06,922 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-18 21:33:06,922 INFO [IPC Server listener on 60143] org.apache.hadoop.ipc.Server: IPC Server listener on 60143: starting
2015-10-18 21:33:06,922 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-18 21:33:06,922 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at 04DN8IQ.fareast.corp.microsoft.com/************:60143
2015-10-18 21:33:07,015 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-18 21:33:07,031 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-18 21:33:07,047 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-18 21:33:07,047 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-18 21:33:07,047 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-18 21:33:07,047 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-18 21:33:07,047 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-18 21:33:07,062 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 60150
2015-10-18 21:33:07,062 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-18 21:33:07,109 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\Jetty_0_0_0_0_60150_mapreduce____.bdalqx\webapp
2015-10-18 21:33:07,343 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:60150
2015-10-18 21:33:07,343 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 60150
2015-10-18 21:33:07,765 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-18 21:33:07,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445175094696_0001
2015-10-18 21:33:07,781 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-18 21:33:07,781 INFO [Socket Reader #1 for port 60153] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 60153
2015-10-18 21:33:07,781 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-18 21:33:07,781 INFO [IPC Server listener on 60153] org.apache.hadoop.ipc.Server: IPC Server listener on 60153: starting
2015-10-18 21:33:07,812 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-18 21:33:07,812 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-18 21:33:07,812 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-18 21:33:07,859 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-18 21:33:07,984 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-18 21:33:07,984 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-18 21:33:07,984 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-18 21:33:07,984 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-18 21:33:08,000 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445175094696_0001Job Transitioned from INITED to SETUP
2015-10-18 21:33:08,000 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-18 21:33:08,015 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445175094696_0001Job Transitioned from SETUP to RUNNING
2015-10-18 21:33:08,047 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:08,062 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:08,062 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:08,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:33:08,094 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-18 21:33:08,094 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-18 21:33:08,125 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445175094696_0001, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445175094696_0001/job_1445175094696_0001_1.jhist
2015-10-18 21:33:08,984 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-18 21:33:09,031 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0001: ask=6 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:11264, vCores:-16> knownNMs=4
2015-10-18 21:33:09,047 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:11264, vCores:-16>
2015-10-18 21:33:09,047 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:10,094 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 6
2015-10-18 21:33:10,094 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0001_01_000002 to attempt_1445175094696_0001_m_000001_0
2015-10-18 21:33:10,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0001_01_000003 to attempt_1445175094696_0001_m_000003_0
2015-10-18 21:33:10,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0001_01_000004 to attempt_1445175094696_0001_m_000005_0
2015-10-18 21:33:10,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0001_01_000005 to attempt_1445175094696_0001_m_000007_0
2015-10-18 21:33:10,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0001_01_000006 to attempt_1445175094696_0001_m_000008_0
2015-10-18 21:33:10,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0001_01_000007 to attempt_1445175094696_0001_m_000000_0
2015-10-18 21:33:10,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-23>
2015-10-18 21:33:10,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:10,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:6 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:6 RackLocal:0
2015-10-18 21:33:10,297 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:10,375 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445175094696_0001/job.jar
2015-10-18 21:33:10,390 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445175094696_0001/job.xml
2015-10-18 21:33:10,390 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-18 21:33:10,390 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-18 21:33:10,390 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-18 21:33:10,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:33:10,547 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:10,547 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:33:10,547 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:10,562 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:33:10,562 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:10,562 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:33:10,562 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:10,562 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:33:10,562 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:10,578 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:33:10,594 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0001_01_000002 taskAttempt attempt_1445175094696_0001_m_000001_0
2015-10-18 21:33:10,609 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0001_m_000001_0
2015-10-18 21:33:10,609 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 21:33:10,672 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0001_01_000006 taskAttempt attempt_1445175094696_0001_m_000008_0
2015-10-18 21:33:10,672 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0001_01_000005 taskAttempt attempt_1445175094696_0001_m_000007_0
2015-10-18 21:33:10,672 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0001_m_000008_0
2015-10-18 21:33:10,672 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0001_m_000007_0
2015-10-18 21:33:10,672 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0001_01_000007 taskAttempt attempt_1445175094696_0001_m_000000_0
2015-10-18 21:33:10,672 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0001_m_000000_0
2015-10-18 21:33:10,672 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0001_01_000004 taskAttempt attempt_1445175094696_0001_m_000005_0
2015-10-18 21:33:10,672 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0001_01_000003 taskAttempt attempt_1445175094696_0001_m_000003_0
2015-10-18 21:33:10,672 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0001_m_000005_0
2015-10-18 21:33:10,672 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0001_m_000003_0
2015-10-18 21:33:10,703 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 21:33:10,703 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 21:33:10,719 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-18 21:33:10,719 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 21:33:10,719 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 21:33:11,094 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0001_m_000003_0 : 13562
2015-10-18 21:33:11,094 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0001_m_000001_0 : 13562
2015-10-18 21:33:11,094 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0001_m_000007_0 : 13562
2015-10-18 21:33:11,094 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0001_m_000008_0 : 13562
2015-10-18 21:33:11,094 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0001_m_000005_0 : 13562
2015-10-18 21:33:11,094 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0001_m_000003_0] using containerId: [container_1445175094696_0001_01_000003 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 21:33:11,094 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:33:11,094 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0001_m_000007_0] using containerId: [container_1445175094696_0001_01_000005 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 21:33:11,094 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:33:11,094 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0001_m_000000_0 : 13562
2015-10-18 21:33:11,094 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0001_m_000008_0] using containerId: [container_1445175094696_0001_01_000006 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 21:33:11,094 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:33:11,094 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0001_m_000005_0] using containerId: [container_1445175094696_0001_01_000004 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 21:33:11,094 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:33:11,094 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0001_m_000001_0] using containerId: [container_1445175094696_0001_01_000002 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 21:33:11,094 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:33:11,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0001_m_000003
2015-10-18 21:33:11,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:33:11,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0001_m_000007
2015-10-18 21:33:11,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:33:11,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0001_m_000000_0] using containerId: [container_1445175094696_0001_01_000007 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:52368]
2015-10-18 21:33:11,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:33:11,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0001_m_000008
2015-10-18 21:33:11,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:33:11,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0001_m_000005
2015-10-18 21:33:11,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:33:11,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0001_m_000001
2015-10-18 21:33:11,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:33:11,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0001_m_000000
2015-10-18 21:33:11,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:33:11,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0001: ask=5 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:4096, vCores:-23> knownNMs=4
2015-10-18 21:33:11,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-23>
2015-10-18 21:33:11,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:12,187 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-23>
2015-10-18 21:33:12,187 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:13,406 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 21:33:13,406 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:13,406 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0001_01_000008 to attempt_1445175094696_0001_m_000002_0
2015-10-18 21:33:13,406 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-24>
2015-10-18 21:33:13,406 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:13,406 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:6 RackLocal:1
2015-10-18 21:33:13,406 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:13,406 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:33:13,562 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0001_01_000008 taskAttempt attempt_1445175094696_0001_m_000002_0
2015-10-18 21:33:13,562 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0001_m_000002_0
2015-10-18 21:33:13,562 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-18 21:33:13,750 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0001_m_000002_0 : 13562
2015-10-18 21:33:13,750 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0001_m_000002_0] using containerId: [container_1445175094696_0001_01_000008 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:52368]
2015-10-18 21:33:13,750 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:33:13,750 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0001_m_000002
2015-10-18 21:33:13,750 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:33:14,422 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-25> knownNMs=4
2015-10-18 21:33:14,422 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-18 21:33:14,422 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:14,515 INFO [Socket Reader #1 for port 60153] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0001 (auth:SIMPLE)
2015-10-18 21:33:14,531 INFO [Socket Reader #1 for port 60153] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0001 (auth:SIMPLE)
2015-10-18 21:33:14,547 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0001_m_000002 asked for a task
2015-10-18 21:33:14,547 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0001_m_000003 asked for a task
2015-10-18 21:33:14,547 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0001_m_000003 given task: attempt_1445175094696_0001_m_000003_0
2015-10-18 21:33:14,547 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0001_m_000002 given task: attempt_1445175094696_0001_m_000001_0
2015-10-18 21:33:15,344 INFO [Socket Reader #1 for port 60153] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0001 (auth:SIMPLE)
2015-10-18 21:33:15,344 INFO [Socket Reader #1 for port 60153] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0001 (auth:SIMPLE)
2015-10-18 21:33:15,359 INFO [Socket Reader #1 for port 60153] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0001 (auth:SIMPLE)
2015-10-18 21:33:15,375 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0001_m_000005 asked for a task
2015-10-18 21:33:15,375 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0001_m_000005 given task: attempt_1445175094696_0001_m_000007_0
2015-10-18 21:33:15,422 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0001_m_000006 asked for a task
2015-10-18 21:33:15,422 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0001_m_000006 given task: attempt_1445175094696_0001_m_000008_0
2015-10-18 21:33:15,422 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0001_m_000004 asked for a task
2015-10-18 21:33:15,422 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0001_m_000004 given task: attempt_1445175094696_0001_m_000005_0
2015-10-18 21:33:15,531 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 21:33:15,531 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:15,531 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0001_01_000009 to attempt_1445175094696_0001_m_000004_0
2015-10-18 21:33:15,531 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-18 21:33:15,531 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:15,531 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:8 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:8 ContRel:0 HostLocal:6 RackLocal:2
2015-10-18 21:33:15,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:15,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:33:15,875 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0001_01_000009 taskAttempt attempt_1445175094696_0001_m_000004_0
2015-10-18 21:33:15,875 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0001_m_000004_0
2015-10-18 21:33:15,875 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-18 21:33:16,594 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0001: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:33:16,594 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 21:33:16,625 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:16,625 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0001_01_000010 to attempt_1445175094696_0001_m_000006_0
2015-10-18 21:33:16,625 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:16,625 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:16,625 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:6 RackLocal:3
2015-10-18 21:33:16,625 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:33:16,625 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:33:16,891 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0001_m_000004_0 : 13562
2015-10-18 21:33:16,891 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0001_m_000004_0] using containerId: [container_1445175094696_0001_01_000009 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:52368]
2015-10-18 21:33:16,891 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:33:16,891 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0001_m_000004
2015-10-18 21:33:16,891 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:33:16,937 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0001_01_000010 taskAttempt attempt_1445175094696_0001_m_000006_0
2015-10-18 21:33:16,937 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0001_m_000006_0
2015-10-18 21:33:16,937 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-18 21:33:17,531 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0001_m_000006_0 : 13562
2015-10-18 21:33:17,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0001_m_000006_0] using containerId: [container_1445175094696_0001_01_000010 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:52368]
2015-10-18 21:33:17,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:33:17,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0001_m_000006
2015-10-18 21:33:17,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:33:17,687 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:33:17,687 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:17,687 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:17,828 INFO [Socket Reader #1 for port 60153] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0001 (auth:SIMPLE)
2015-10-18 21:33:18,047 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0001_m_000007 asked for a task
2015-10-18 21:33:18,047 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0001_m_000007 given task: attempt_1445175094696_0001_m_000000_0
2015-10-18 21:33:18,734 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:18,734 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:19,797 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:19,797 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:20,875 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:20,875 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:22,078 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:22,078 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:22,188 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.13102192
2015-10-18 21:33:22,203 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.13102706
2015-10-18 21:33:22,969 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.13103712
2015-10-18 21:33:23,016 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.13104132
2015-10-18 21:33:23,031 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.13102318
2015-10-18 21:33:23,125 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:23,125 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:24,328 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:24,328 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:24,453 INFO [Socket Reader #1 for port 60153] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0001 (auth:SIMPLE)
2015-10-18 21:33:24,672 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0001_m_000008 asked for a task
2015-10-18 21:33:24,672 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0001_m_000008 given task: attempt_1445175094696_0001_m_000002_0
2015-10-18 21:33:25,219 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.13102192
2015-10-18 21:33:25,234 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.13102706
2015-10-18 21:33:25,344 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:25,344 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:25,984 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.13103712
2015-10-18 21:33:26,031 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.13104132
2015-10-18 21:33:26,047 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.13102318
2015-10-18 21:33:26,375 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:26,375 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:27,391 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:27,391 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:27,625 INFO [Socket Reader #1 for port 60153] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0001 (auth:SIMPLE)
2015-10-18 21:33:27,719 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0001_m_000010 asked for a task
2015-10-18 21:33:27,719 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0001_m_000010 given task: attempt_1445175094696_0001_m_000006_0
2015-10-18 21:33:28,250 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.13102192
2015-10-18 21:33:28,266 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.13102706
2015-10-18 21:33:28,438 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:28,438 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:29,016 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.13103712
2015-10-18 21:33:29,094 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.13102318
2015-10-18 21:33:29,094 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.13104132
2015-10-18 21:33:29,547 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:29,547 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:30,641 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:30,641 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:31,313 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.18271543
2015-10-18 21:33:31,313 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.18734875
2015-10-18 21:33:31,672 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:31,672 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:32,031 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.21869554
2015-10-18 21:33:32,172 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.14710897
2015-10-18 21:33:32,172 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.19012474
2015-10-18 21:33:32,235 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.056993112
2015-10-18 21:33:32,735 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:32,735 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:33,797 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:33,797 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:34,375 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.23922287
2015-10-18 21:33:34,375 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.23921879
2015-10-18 21:33:34,922 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:34,922 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:35,094 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.23924637
2015-10-18 21:33:35,250 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.23922269
2015-10-18 21:33:35,250 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.23921506
2015-10-18 21:33:35,735 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.12349423
2015-10-18 21:33:36,000 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:36,000 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:36,391 INFO [Socket Reader #1 for port 60153] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0001 (auth:SIMPLE)
2015-10-18 21:33:36,563 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0001_m_000009 asked for a task
2015-10-18 21:33:36,563 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0001_m_000009 given task: attempt_1445175094696_0001_m_000004_0
2015-10-18 21:33:37,141 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:37,141 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:37,422 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.23922287
2015-10-18 21:33:37,422 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.23921879
2015-10-18 21:33:38,110 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.23924637
2015-10-18 21:33:38,203 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:38,203 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:38,313 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.23921506
2015-10-18 21:33:38,313 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.23922269
2015-10-18 21:33:39,016 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.13101934
2015-10-18 21:33:39,250 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:39,250 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:40,297 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:40,297 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:40,453 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.23921879
2015-10-18 21:33:40,469 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.23922287
2015-10-18 21:33:41,125 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.26161054
2015-10-18 21:33:41,328 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.23921506
2015-10-18 21:33:41,328 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.24963492
2015-10-18 21:33:41,328 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:41,328 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:42,344 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:42,344 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:42,453 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.13101934
2015-10-18 21:33:43,360 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:43,360 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:43,485 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.32146728
2015-10-18 21:33:43,485 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.33425605
2015-10-18 21:33:44,141 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.34743145
2015-10-18 21:33:44,344 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.3474054
2015-10-18 21:33:44,344 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.347063
2015-10-18 21:33:44,407 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:44,407 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:45,454 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:45,454 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:45,750 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.13101934
2015-10-18 21:33:46,485 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:46,485 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:46,547 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.3473985
2015-10-18 21:33:46,547 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.34743196
2015-10-18 21:33:47,157 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.34743145
2015-10-18 21:33:47,360 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.3474145
2015-10-18 21:33:47,360 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.3474054
2015-10-18 21:33:47,516 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:47,516 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:47,532 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.03875154
2015-10-18 21:33:48,547 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:48,547 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:49,219 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.13101934
2015-10-18 21:33:49,563 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.3473985
2015-10-18 21:33:49,563 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.34743196
2015-10-18 21:33:49,563 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:49,563 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:50,172 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.34743145
2015-10-18 21:33:50,375 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.3474054
2015-10-18 21:33:50,375 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.3474145
2015-10-18 21:33:50,594 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:50,594 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:51,219 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.08792806
2015-10-18 21:33:51,610 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:51,610 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:52,579 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.3473985
2015-10-18 21:33:52,579 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.34743196
2015-10-18 21:33:52,641 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:52,641 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:52,876 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.13101934
2015-10-18 21:33:53,188 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.41016906
2015-10-18 21:33:53,422 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.38722077
2015-10-18 21:33:53,422 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.36639354
2015-10-18 21:33:53,657 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:53,657 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:54,079 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.022142902
2015-10-18 21:33:54,688 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:54,688 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:54,813 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.111703545
2015-10-18 21:33:55,501 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.06675848
2015-10-18 21:33:55,594 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.455629
2015-10-18 21:33:55,594 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.45559394
2015-10-18 21:33:55,766 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:55,766 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:56,204 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.4556257
2015-10-18 21:33:56,438 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.45562187
2015-10-18 21:33:56,438 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.45560944
2015-10-18 21:33:56,657 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.13101934
2015-10-18 21:33:56,797 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:56,797 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:57,672 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.036798228
2015-10-18 21:33:57,829 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:57,829 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:58,579 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.13059291
2015-10-18 21:33:58,610 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.45559394
2015-10-18 21:33:58,610 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.455629
2015-10-18 21:33:58,876 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:58,876 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:33:59,235 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.4556257
2015-10-18 21:33:59,454 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.45562187
2015-10-18 21:33:59,454 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.45560944
2015-10-18 21:33:59,657 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.1267911
2015-10-18 21:33:59,923 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:33:59,923 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:00,610 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.13101934
2015-10-18 21:34:00,969 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:00,969 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:01,548 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.08919638
2015-10-18 21:34:01,641 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.45559394
2015-10-18 21:34:01,641 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.455629
2015-10-18 21:34:02,001 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:02,001 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:02,251 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.4556257
2015-10-18 21:34:02,469 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.45562187
2015-10-18 21:34:02,469 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.45560944
2015-10-18 21:34:02,673 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.131014
2015-10-18 21:34:03,048 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:03,048 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:04,001 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.13104042
2015-10-18 21:34:04,063 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:04,063 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:04,594 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.13101934
2015-10-18 21:34:04,657 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.49710312
2015-10-18 21:34:04,657 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.47812068
2015-10-18 21:34:05,094 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:05,094 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:05,485 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.54659325
2015-10-18 21:34:05,485 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.13101135
2015-10-18 21:34:05,485 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.5354482
2015-10-18 21:34:05,485 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.5423038
2015-10-18 21:34:06,391 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:06,391 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:06,720 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.131014
2015-10-18 21:34:07,641 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:07,641 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:07,876 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.5637838
2015-10-18 21:34:07,876 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.5638294
2015-10-18 21:34:08,141 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.13104042
2015-10-18 21:34:08,532 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.17944741
2015-10-18 21:34:08,548 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.56384325
2015-10-18 21:34:08,548 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.56380385
2015-10-18 21:34:08,548 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.56381226
2015-10-18 21:34:08,720 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:08,720 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:09,360 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.13101135
2015-10-18 21:34:09,735 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:09,735 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:10,641 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.131014
2015-10-18 21:34:10,782 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:10,782 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:10,907 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.5637838
2015-10-18 21:34:10,907 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.5638294
2015-10-18 21:34:11,626 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.56380385
2015-10-18 21:34:11,626 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.56381226
2015-10-18 21:34:11,626 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.56384325
2015-10-18 21:34:11,829 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:11,829 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:12,079 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.13104042
2015-10-18 21:34:12,298 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.23921585
2015-10-18 21:34:12,891 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:12,891 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:13,407 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.13101135
2015-10-18 21:34:13,954 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.5638294
2015-10-18 21:34:13,985 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.5774687
2015-10-18 21:34:13,985 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:13,985 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:14,485 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.131014
2015-10-18 21:34:14,720 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.6136139
2015-10-18 21:34:14,720 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.57112455
2015-10-18 21:34:14,720 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.5968743
2015-10-18 21:34:15,017 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:15,017 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:15,470 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.5774687
2015-10-18 21:34:15,876 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.13104042
2015-10-18 21:34:16,032 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.6136139
2015-10-18 21:34:16,063 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:16,063 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:16,157 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.23921585
2015-10-18 21:34:16,673 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.5968743
2015-10-18 21:34:16,970 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.6665732
2015-10-18 21:34:17,001 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.667
2015-10-18 21:34:17,017 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.57112455
2015-10-18 21:34:17,095 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:17,095 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:17,267 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.6665732
2015-10-18 21:34:17,345 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.13101135
2015-10-18 21:34:17,751 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.667
2015-10-18 21:34:17,751 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.667
2015-10-18 21:34:17,751 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.667
2015-10-18 21:34:18,142 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:18,142 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:18,173 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.131014
2015-10-18 21:34:19,157 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:19,157 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:19,673 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.13104042
2015-10-18 21:34:20,001 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.667
2015-10-18 21:34:20,001 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.23921585
2015-10-18 21:34:20,017 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.667
2015-10-18 21:34:20,188 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:20,188 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:20,782 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.667
2015-10-18 21:34:20,782 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.667
2015-10-18 21:34:20,782 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.667
2015-10-18 21:34:21,157 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.13101135
2015-10-18 21:34:21,204 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:21,204 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:21,798 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.131014
2015-10-18 21:34:22,235 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:22,235 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:23,017 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.667
2015-10-18 21:34:23,017 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.667
2015-10-18 21:34:23,235 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:23,235 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:23,407 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.13104042
2015-10-18 21:34:23,720 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.23921585
2015-10-18 21:34:23,782 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.667
2015-10-18 21:34:23,782 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.667
2015-10-18 21:34:23,798 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.667
2015-10-18 21:34:24,251 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:24,251 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:25,095 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.13101135
2015-10-18 21:34:25,267 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:25,267 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:25,689 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.131014
2015-10-18 21:34:26,048 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.67946714
2015-10-18 21:34:26,048 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.667
2015-10-18 21:34:26,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:26,282 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:26,798 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.667
2015-10-18 21:34:26,798 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.667
2015-10-18 21:34:26,814 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.6689715
2015-10-18 21:34:27,095 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.13104042
2015-10-18 21:34:27,329 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:27,329 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:27,642 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.23921585
2015-10-18 21:34:28,345 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:28,345 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:28,907 INFO [IPC Server handler 8 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.13101135
2015-10-18 21:34:29,048 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.7152163
2015-10-18 21:34:29,079 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.69352
2015-10-18 21:34:29,392 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:29,392 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:29,579 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.131014
2015-10-18 21:34:29,814 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.7057723
2015-10-18 21:34:29,814 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.7018783
2015-10-18 21:34:29,814 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.6953506
2015-10-18 21:34:30,439 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:30,439 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:30,861 INFO [IPC Server handler 8 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.13104042
2015-10-18 21:34:31,361 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.23921585
2015-10-18 21:34:31,470 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:31,470 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:32,064 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.7698906
2015-10-18 21:34:32,095 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.74739856
2015-10-18 21:34:32,501 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:32,501 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:32,829 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.7428194
2015-10-18 21:34:32,829 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.73303574
2015-10-18 21:34:32,829 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.7399259
2015-10-18 21:34:33,126 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.13101135
2015-10-18 21:34:33,548 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 21:34:33,548 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0001_01_000011 to attempt_1445175094696_0001_m_000009_0
2015-10-18 21:34:33,548 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:33,548 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:34:33,548 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-18 21:34:33,548 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:34:33,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:34:33,720 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0001_01_000011 taskAttempt attempt_1445175094696_0001_m_000009_0
2015-10-18 21:34:33,720 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0001_m_000009_0
2015-10-18 21:34:33,720 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:34:33,861 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0001_m_000009_0 : 13562
2015-10-18 21:34:33,861 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0001_m_000009_0] using containerId: [container_1445175094696_0001_01_000011 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:25649]
2015-10-18 21:34:33,861 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:34:33,861 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0001_m_000009
2015-10-18 21:34:33,861 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:34:33,861 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.131014
2015-10-18 21:34:34,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:34:34,751 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.13104042
2015-10-18 21:34:35,064 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.82155967
2015-10-18 21:34:35,095 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.23921585
2015-10-18 21:34:35,111 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.795543
2015-10-18 21:34:35,845 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.782469
2015-10-18 21:34:35,845 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.7738089
2015-10-18 21:34:35,845 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.78231955
2015-10-18 21:34:36,986 INFO [IPC Server handler 8 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.13101135
2015-10-18 21:34:37,533 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.131014
2015-10-18 21:34:37,626 INFO [Socket Reader #1 for port 60153] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0001 (auth:SIMPLE)
2015-10-18 21:34:37,642 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0001_m_000011 asked for a task
2015-10-18 21:34:37,642 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0001_m_000011 given task: attempt_1445175094696_0001_m_000009_0
2015-10-18 21:34:38,079 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.8683236
2015-10-18 21:34:38,111 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.84080493
2015-10-18 21:34:38,423 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.15274993
2015-10-18 21:34:38,736 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.23921585
2015-10-18 21:34:38,861 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.82484484
2015-10-18 21:34:38,861 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.82377374
2015-10-18 21:34:38,861 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.81504875
2015-10-18 21:34:40,376 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.13101135
2015-10-18 21:34:40,923 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.15534748
2015-10-18 21:34:41,111 INFO [IPC Server handler 8 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.9058733
2015-10-18 21:34:41,126 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.8758801
2015-10-18 21:34:41,876 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.8694682
2015-10-18 21:34:41,876 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.8596029
2015-10-18 21:34:41,876 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.867276
2015-10-18 21:34:42,204 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.22569326
2015-10-18 21:34:42,814 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.23921585
2015-10-18 21:34:44,173 INFO [IPC Server handler 8 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.9333354
2015-10-18 21:34:44,236 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.90179586
2015-10-18 21:34:44,298 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.13463335
2015-10-18 21:34:44,923 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.8988889
2015-10-18 21:34:44,923 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.17358303
2015-10-18 21:34:44,939 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.9018339
2015-10-18 21:34:44,939 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.8917837
2015-10-18 21:34:45,033 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000009_0 is : 0.16604526
2015-10-18 21:34:45,892 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.23924798
2015-10-18 21:34:46,611 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.23921585
2015-10-18 21:34:47,220 INFO [IPC Server handler 8 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 0.96462274
2015-10-18 21:34:47,267 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.9379275
2015-10-18 21:34:47,923 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.21104981
2015-10-18 21:34:48,048 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.9371443
2015-10-18 21:34:48,064 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.93117994
2015-10-18 21:34:48,064 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.94109833
2015-10-18 21:34:48,064 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000009_0 is : 0.16604526
2015-10-18 21:34:48,486 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.19182336
2015-10-18 21:34:49,705 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.23924798
2015-10-18 21:34:50,267 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 1.0
2015-10-18 21:34:50,330 INFO [IPC Server handler 8 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 0.97735846
2015-10-18 21:34:50,580 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.25139976
2015-10-18 21:34:51,080 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 0.98000383
2015-10-18 21:34:51,095 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 0.9905039
2015-10-18 21:34:51,095 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 0.98529786
2015-10-18 21:34:51,095 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000009_0 is : 0.16604526
2015-10-18 21:34:52,095 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.23923388
2015-10-18 21:34:52,517 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.23919508
2015-10-18 21:34:53,267 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 1.0
2015-10-18 21:34:53,345 INFO [IPC Server handler 8 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 1.0
2015-10-18 21:34:53,486 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.23924798
2015-10-18 21:34:54,111 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 1.0
2015-10-18 21:34:54,111 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 1.0
2015-10-18 21:34:54,111 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 1.0
2015-10-18 21:34:54,111 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000009_0 is : 0.3031575
2015-10-18 21:34:54,330 INFO [IPC Server handler 8 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.3129791
2015-10-18 21:34:54,923 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000001_0 is : 1.0
2015-10-18 21:34:54,923 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000003_0 is : 1.0
2015-10-18 21:34:54,923 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0001_m_000001_0
2015-10-18 21:34:54,923 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:34:54,923 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0001_01_000002 taskAttempt attempt_1445175094696_0001_m_000001_0
2015-10-18 21:34:54,923 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0001_m_000001_0
2015-10-18 21:34:54,923 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0001_m_000003_0
2015-10-18 21:34:54,923 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 21:34:54,923 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:34:54,923 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0001_01_000003 taskAttempt attempt_1445175094696_0001_m_000003_0
2015-10-18 21:34:54,923 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0001_m_000003_0
2015-10-18 21:34:54,923 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 21:34:55,236 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:34:55,236 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:34:55,252 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0001_m_000003_0
2015-10-18 21:34:55,252 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:34:55,252 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0001_m_000001_0
2015-10-18 21:34:55,252 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:34:55,267 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-18 21:34:55,267 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-18 21:34:55,517 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-18 21:34:55,517 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 21:34:55,517 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-18 21:34:55,517 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-18 21:34:55,517 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-18 21:34:56,017 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.23923388
2015-10-18 21:34:56,142 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445175094696_0001_m_000006
2015-10-18 21:34:56,142 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 21:34:56,142 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445175094696_0001_m_000006
2015-10-18 21:34:56,158 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:34:56,158 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:34:56,158 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000006_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:34:56,548 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.23919508
2015-10-18 21:34:56,548 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-18 21:34:56,548 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0001: ask=5 release= 0 newContainers=0 finishedContainers=2 resourcelimit=<memory:1024, vCores:-26> knownNMs=4
2015-10-18 21:34:56,548 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0001_01_000002
2015-10-18 21:34:56,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0001_01_000003
2015-10-18 21:34:56,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0001_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:34:56,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:8 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-18 21:34:56,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0001_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:34:57,127 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 1.0
2015-10-18 21:34:57,127 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 1.0
2015-10-18 21:34:57,127 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 1.0
2015-10-18 21:34:57,142 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000009_0 is : 0.3031575
2015-10-18 21:34:57,298 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.23924798
2015-10-18 21:34:57,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 21:34:57,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-18 21:34:57,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0001_01_000012 to attempt_1445175094696_0001_r_000000_0
2015-10-18 21:34:57,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:7 RackLocal:3
2015-10-18 21:34:57,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:34:57,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:34:57,611 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0001_01_000012 taskAttempt attempt_1445175094696_0001_r_000000_0
2015-10-18 21:34:57,611 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0001_r_000000_0
2015-10-18 21:34:57,611 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 21:34:57,767 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0001_r_000000_0 : 13562
2015-10-18 21:34:57,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0001_r_000000_0] using containerId: [container_1445175094696_0001_01_000012 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 21:34:57,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:34:57,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0001_r_000000
2015-10-18 21:34:57,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:34:58,080 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.3474062
2015-10-18 21:34:58,642 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0001: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:34:59,674 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.23923388
2015-10-18 21:35:00,174 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000009_0 is : 0.34338555
2015-10-18 21:35:00,345 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.23919508
2015-10-18 21:35:01,002 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.23924798
2015-10-18 21:35:01,486 INFO [Socket Reader #1 for port 60153] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0001 (auth:SIMPLE)
2015-10-18 21:35:01,752 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.3474062
2015-10-18 21:35:01,845 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0001_r_000012 asked for a task
2015-10-18 21:35:01,845 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0001_r_000012 given task: attempt_1445175094696_0001_r_000000_0
2015-10-18 21:35:03,205 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000009_0 is : 0.4402952
2015-10-18 21:35:03,392 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 0 maxEvents 10000
2015-10-18 21:35:03,595 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.23923388
2015-10-18 21:35:04,095 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.23919508
2015-10-18 21:35:04,424 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 2 maxEvents 10000
2015-10-18 21:35:04,799 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.23924798
2015-10-18 21:35:05,455 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 2 maxEvents 10000
2015-10-18 21:35:05,642 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.3474062
2015-10-18 21:35:06,252 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000009_0 is : 0.4402952
2015-10-18 21:35:06,471 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 2 maxEvents 10000
2015-10-18 21:35:07,299 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.23923388
2015-10-18 21:35:07,486 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 2 maxEvents 10000
2015-10-18 21:35:07,830 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.23919508
2015-10-18 21:35:08,392 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.23924798
2015-10-18 21:35:08,517 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 2 maxEvents 10000
2015-10-18 21:35:09,283 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000009_0 is : 0.54922926
2015-10-18 21:35:09,346 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.3474062
2015-10-18 21:35:09,533 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 2 maxEvents 10000
2015-10-18 21:35:10,580 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 2 maxEvents 10000
2015-10-18 21:35:11,189 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.23923388
2015-10-18 21:35:11,627 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 2 maxEvents 10000
2015-10-18 21:35:11,721 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.23919508
2015-10-18 21:35:12,143 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.23924798
2015-10-18 21:35:12,440 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000009_0 is : 0.5773621
2015-10-18 21:35:12,752 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 2 maxEvents 10000
2015-10-18 21:35:13,362 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.3474062
2015-10-18 21:35:14,299 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 2 maxEvents 10000
2015-10-18 21:35:14,768 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.23923388
2015-10-18 21:35:15,315 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 2 maxEvents 10000
2015-10-18 21:35:15,456 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.23919508
2015-10-18 21:35:15,456 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000009_0 is : 0.5773621
2015-10-18 21:35:15,909 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.23924798
2015-10-18 21:35:16,315 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 2 maxEvents 10000
2015-10-18 21:35:17,003 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.3474062
2015-10-18 21:35:17,362 INFO [IPC Server handler 9 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 2 maxEvents 10000
2015-10-18 21:35:17,675 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000009_0 is : 0.5773621
2015-10-18 21:35:18,409 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 2 maxEvents 10000
2015-10-18 21:35:18,518 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000009_0 is : 0.667
2015-10-18 21:35:18,643 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.23923388
2015-10-18 21:35:19,440 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 2 maxEvents 10000
2015-10-18 21:35:19,550 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.23919508
2015-10-18 21:35:20,144 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.0
2015-10-18 21:35:20,175 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000005_0 is : 1.0
2015-10-18 21:35:20,269 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0001_m_000005_0
2015-10-18 21:35:20,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:35:20,269 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0001_01_000004 taskAttempt attempt_1445175094696_0001_m_000005_0
2015-10-18 21:35:20,269 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0001_m_000005_0
2015-10-18 21:35:20,269 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 21:35:20,284 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.25078782
2015-10-18 21:35:20,597 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 2 maxEvents 10000
2015-10-18 21:35:20,644 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:35:20,644 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0001_m_000005_0
2015-10-18 21:35:20,644 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:35:20,644 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-18 21:35:20,862 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:7 RackLocal:3
2015-10-18 21:35:20,925 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.3474062
2015-10-18 21:35:21,566 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000009_0 is : 0.667
2015-10-18 21:35:21,659 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 2 maxEvents 10000
2015-10-18 21:35:22,363 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.23923388
2015-10-18 21:35:22,706 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 21:35:23,144 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.23919508
2015-10-18 21:35:23,691 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 21:35:24,019 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.32416594
2015-10-18 21:35:24,597 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000009_0 is : 0.6774287
2015-10-18 21:35:24,738 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 21:35:24,863 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.3474062
2015-10-18 21:35:25,722 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 21:35:26,035 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.23923388
2015-10-18 21:35:26,754 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 21:35:26,754 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.23919508
2015-10-18 21:35:27,613 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000009_0 is : 0.74125445
2015-10-18 21:35:27,676 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.34742972
2015-10-18 21:35:27,738 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 21:35:28,457 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.3474062
2015-10-18 21:35:28,770 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 21:35:29,692 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.28171226
2015-10-18 21:35:29,754 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 21:35:30,317 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.23919508
2015-10-18 21:35:30,645 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000009_0 is : 0.80483437
2015-10-18 21:35:30,785 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 21:35:31,192 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.0
2015-10-18 21:35:31,192 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.34742972
2015-10-18 21:35:31,207 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000007_0 is : 1.0
2015-10-18 21:35:31,207 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0001_m_000007_0
2015-10-18 21:35:31,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:35:31,207 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0001_01_000005 taskAttempt attempt_1445175094696_0001_m_000007_0
2015-10-18 21:35:31,207 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0001_m_000007_0
2015-10-18 21:35:31,207 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 21:35:31,332 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:35:31,332 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0001_m_000007_0
2015-10-18 21:35:31,332 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:35:31,332 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-18 21:35:31,770 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 3 maxEvents 10000
2015-10-18 21:35:31,911 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000008_0 is : 1.0
2015-10-18 21:35:31,911 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0001_m_000008_0
2015-10-18 21:35:31,911 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:35:31,911 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0001_01_000006 taskAttempt attempt_1445175094696_0001_m_000008_0
2015-10-18 21:35:31,911 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0001_m_000008_0
2015-10-18 21:35:31,911 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 21:35:32,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:35:32,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0001_m_000008_0
2015-10-18 21:35:32,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:35:32,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-18 21:35:32,317 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.3474062
2015-10-18 21:35:32,348 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:7 RackLocal:3
2015-10-18 21:35:32,348 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0001_01_000004
2015-10-18 21:35:32,348 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0001_01_000005
2015-10-18 21:35:32,348 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 21:35:32,348 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0001_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:35:32,348 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:35:32,348 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0001_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:35:32,348 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0001_01_000013 to attempt_1445175094696_0001_m_000006_1
2015-10-18 21:35:32,348 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:7 RackLocal:4
2015-10-18 21:35:32,348 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:35:32,348 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000006_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:35:32,348 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0001_01_000013 taskAttempt attempt_1445175094696_0001_m_000006_1
2015-10-18 21:35:32,348 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0001_m_000006_1
2015-10-18 21:35:32,348 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 21:35:32,489 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0001_m_000006_1 : 13562
2015-10-18 21:35:32,489 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0001_m_000006_1] using containerId: [container_1445175094696_0001_01_000013 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 21:35:32,489 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000006_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:35:32,489 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0001_m_000006
2015-10-18 21:35:32,848 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445175094696_0001_m_000002
2015-10-18 21:35:32,848 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 21:35:32,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445175094696_0001_m_000002
2015-10-18 21:35:32,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:35:32,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:35:32,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000002_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:35:33,379 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:7 RackLocal:4
2015-10-18 21:35:33,379 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0001: ask=4 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:35:33,379 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0001_01_000006
2015-10-18 21:35:33,379 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:7 RackLocal:4
2015-10-18 21:35:33,379 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0001_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:35:33,473 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.34743717
2015-10-18 21:35:33,676 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000009_0 is : 0.8692819
2015-10-18 21:35:34,208 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.0
2015-10-18 21:35:34,286 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.291151
2015-10-18 21:35:35,161 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.34742972
2015-10-18 21:35:36,052 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.40286878
2015-10-18 21:35:36,708 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000009_0 is : 0.9325457
2015-10-18 21:35:37,145 INFO [Socket Reader #1 for port 60153] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0001 (auth:SIMPLE)
2015-10-18 21:35:37,177 INFO [IPC Server handler 9 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0001_m_000013 asked for a task
2015-10-18 21:35:37,177 INFO [IPC Server handler 9 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0001_m_000013 given task: attempt_1445175094696_0001_m_000006_1
2015-10-18 21:35:37,427 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.34743717
2015-10-18 21:35:38,161 INFO [IPC Server handler 9 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.34703106
2015-10-18 21:35:39,411 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.34742972
2015-10-18 21:35:39,740 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000009_0 is : 0.9958359
2015-10-18 21:35:40,021 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000009_0 is : 1.0
2015-10-18 21:35:40,099 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0001_m_000009_0
2015-10-18 21:35:40,099 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:35:40,099 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0001_01_000011 taskAttempt attempt_1445175094696_0001_m_000009_0
2015-10-18 21:35:40,099 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0001_m_000009_0
2015-10-18 21:35:40,099 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:35:40,146 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.45462772
2015-10-18 21:35:40,536 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:35:40,536 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0001_m_000009_0
2015-10-18 21:35:40,536 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:35:40,536 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-18 21:35:40,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:7 RackLocal:4
2015-10-18 21:35:40,724 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0001_01_000011
2015-10-18 21:35:40,724 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 21:35:40,724 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0001_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:35:40,724 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0001_01_000014 to attempt_1445175094696_0001_m_000002_1
2015-10-18 21:35:40,724 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:8 RackLocal:4
2015-10-18 21:35:40,724 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:35:40,724 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000002_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:35:40,724 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0001_01_000014 taskAttempt attempt_1445175094696_0001_m_000002_1
2015-10-18 21:35:40,724 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0001_m_000002_1
2015-10-18 21:35:40,724 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:35:41,193 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.34743717
2015-10-18 21:35:41,255 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0001_m_000002_1 : 13562
2015-10-18 21:35:41,255 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0001_m_000002_1] using containerId: [container_1445175094696_0001_01_000014 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:25649]
2015-10-18 21:35:41,255 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000002_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:35:41,255 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0001_m_000002
2015-10-18 21:35:41,849 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:35:41,927 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.3474061
2015-10-18 21:35:43,099 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.34742972
2015-10-18 21:35:43,240 INFO [Socket Reader #1 for port 60153] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0001 (auth:SIMPLE)
2015-10-18 21:35:43,271 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0001_m_000014 asked for a task
2015-10-18 21:35:43,271 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0001_m_000014 given task: attempt_1445175094696_0001_m_000002_1
2015-10-18 21:35:43,818 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.45563135
2015-10-18 21:35:44,834 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.13101135
2015-10-18 21:35:44,834 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.34743717
2015-10-18 21:35:45,553 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.3474061
2015-10-18 21:35:46,818 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.34742972
2015-10-18 21:35:47,475 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.45563135
2015-10-18 21:35:47,850 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.13101135
2015-10-18 21:35:47,865 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445175094696_0001_m_000004
2015-10-18 21:35:47,865 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445175094696_0001_m_000004
2015-10-18 21:35:47,865 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:35:47,865 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:35:47,865 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000004_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:35:47,865 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 21:35:48,069 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:8 RackLocal:4
2015-10-18 21:35:48,069 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:35:48,600 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.34743717
2015-10-18 21:35:49,381 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.3474061
2015-10-18 21:35:50,334 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.131014
2015-10-18 21:35:50,600 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.34742972
2015-10-18 21:35:50,897 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.13101135
2015-10-18 21:35:51,241 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.45563135
2015-10-18 21:35:52,303 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.34743717
2015-10-18 21:35:53,241 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.3474061
2015-10-18 21:35:53,429 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.131014
2015-10-18 21:35:54,397 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.34742972
2015-10-18 21:35:54,772 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 21:35:54,976 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.17912397
2015-10-18 21:35:55,132 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.45563135
2015-10-18 21:35:55,835 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:35:56,085 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.34743717
2015-10-18 21:35:56,491 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.23919508
2015-10-18 21:35:56,866 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:35:56,976 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.3474061
2015-10-18 21:35:57,898 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:35:58,023 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.23923388
2015-10-18 21:35:58,210 INFO [IPC Server handler 8 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.34742972
2015-10-18 21:35:58,476 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.13333334
2015-10-18 21:35:58,679 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 21:35:58,679 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0001_01_000015 to attempt_1445175094696_0001_m_000004_1
2015-10-18 21:35:58,679 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-18 21:35:58,679 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:35:58,679 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000004_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:35:58,679 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0001_01_000015 taskAttempt attempt_1445175094696_0001_m_000004_1
2015-10-18 21:35:58,679 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0001_m_000004_1
2015-10-18 21:35:58,679 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:35:58,867 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0001_m_000004_1 : 13562
2015-10-18 21:35:58,867 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0001_m_000004_1] using containerId: [container_1445175094696_0001_01_000015 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:25649]
2015-10-18 21:35:58,867 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000004_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:35:58,867 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0001_m_000004
2015-10-18 21:35:58,867 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.45563135
2015-10-18 21:35:58,882 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:35:59,539 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.23919508
2015-10-18 21:35:59,695 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:35:59,742 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.34743717
2015-10-18 21:35:59,882 INFO [IPC Server handler 9 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:00,804 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.3474061
2015-10-18 21:36:00,929 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:01,023 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.23923388
2015-10-18 21:36:01,023 INFO [Socket Reader #1 for port 60153] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0001 (auth:SIMPLE)
2015-10-18 21:36:01,070 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0001_m_000015 asked for a task
2015-10-18 21:36:01,070 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0001_m_000015 given task: attempt_1445175094696_0001_m_000004_1
2015-10-18 21:36:01,508 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:36:01,914 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:02,336 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.34742972
2015-10-18 21:36:02,586 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.23919508
2015-10-18 21:36:02,914 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445175094696_0001_m_000000
2015-10-18 21:36:02,914 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 21:36:02,914 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445175094696_0001_m_000000
2015-10-18 21:36:02,914 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:36:02,914 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:36:02,914 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000000_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:36:02,930 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:02,992 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.45563135
2015-10-18 21:36:03,773 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.34743717
2015-10-18 21:36:03,836 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-18 21:36:03,836 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:36:03,930 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:04,039 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.23923388
2015-10-18 21:36:04,399 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.3474061
2015-10-18 21:36:04,508 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:36:04,914 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:05,602 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.3474061
2015-10-18 21:36:05,899 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.36769423
2015-10-18 21:36:05,914 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:06,696 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.45563135
2015-10-18 21:36:06,930 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:07,039 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.34743717
2015-10-18 21:36:07,540 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:36:07,540 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.34743717
2015-10-18 21:36:07,961 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:08,290 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.13104042
2015-10-18 21:36:08,633 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.3474061
2015-10-18 21:36:08,993 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:09,618 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.3474061
2015-10-18 21:36:09,759 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.45047268
2015-10-18 21:36:10,009 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:10,071 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.34743717
2015-10-18 21:36:10,587 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:36:10,743 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.45563135
2015-10-18 21:36:10,993 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:11,337 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.35857326
2015-10-18 21:36:11,337 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.13104042
2015-10-18 21:36:11,665 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.3474061
2015-10-18 21:36:12,024 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:13,009 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:13,071 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.34743717
2015-10-18 21:36:13,337 INFO [IPC Server handler 9 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.45565325
2015-10-18 21:36:13,431 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.3474061
2015-10-18 21:36:13,618 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:36:14,056 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:14,243 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.45563135
2015-10-18 21:36:14,368 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.23924798
2015-10-18 21:36:14,697 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.45561612
2015-10-18 21:36:14,978 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.4051463
2015-10-18 21:36:15,040 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:16,056 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:16,072 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.4315687
2015-10-18 21:36:16,650 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:36:16,978 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.45565325
2015-10-18 21:36:17,041 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:17,087 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.37499714
2015-10-18 21:36:17,384 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.23924798
2015-10-18 21:36:17,744 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.45561612
2015-10-18 21:36:18,056 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:18,244 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.45943055
2015-10-18 21:36:18,963 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.455643
2015-10-18 21:36:19,041 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:19,088 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.455643
2015-10-18 21:36:19,697 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:36:20,072 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:20,463 INFO [IPC Server handler 9 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.23924798
2015-10-18 21:36:20,744 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.45565325
2015-10-18 21:36:20,791 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.45561612
2015-10-18 21:36:20,963 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.4402078
2015-10-18 21:36:21,104 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:22,010 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.52043986
2015-10-18 21:36:22,166 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:22,166 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.455643
2015-10-18 21:36:22,572 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.455643
2015-10-18 21:36:22,744 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:36:23,182 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:23,526 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.34742972
2015-10-18 21:36:23,901 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.56381613
2015-10-18 21:36:24,198 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:24,760 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.45565325
2015-10-18 21:36:24,948 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.45561612
2015-10-18 21:36:25,245 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.455643
2015-10-18 21:36:25,245 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:25,807 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:36:25,823 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.56380075
2015-10-18 21:36:26,167 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.455643
2015-10-18 21:36:26,276 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:26,557 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.34742972
2015-10-18 21:36:26,932 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.56381613
2015-10-18 21:36:27,307 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:28,292 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.5638263
2015-10-18 21:36:28,386 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:28,448 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.45565325
2015-10-18 21:36:28,729 INFO [IPC Server handler 9 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.45561612
2015-10-18 21:36:28,839 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:36:29,370 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:29,542 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.56380075
2015-10-18 21:36:29,620 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.3688237
2015-10-18 21:36:29,886 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.455643
2015-10-18 21:36:29,964 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.56381613
2015-10-18 21:36:30,417 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:31,308 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.5638263
2015-10-18 21:36:31,433 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:31,745 INFO [IPC Server handler 9 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.56381613
2015-10-18 21:36:31,886 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:36:32,058 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.45565325
2015-10-18 21:36:32,480 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.45561612
2015-10-18 21:36:32,480 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:32,652 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.45565325
2015-10-18 21:36:33,042 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.667
2015-10-18 21:36:33,121 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.56380075
2015-10-18 21:36:33,527 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:33,808 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.455643
2015-10-18 21:36:34,324 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.5638263
2015-10-18 21:36:34,511 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:34,902 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:36:35,543 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:35,683 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.45565325
2015-10-18 21:36:35,683 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.45565325
2015-10-18 21:36:36,090 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.667
2015-10-18 21:36:36,105 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.45561612
2015-10-18 21:36:36,590 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:36,793 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.56380075
2015-10-18 21:36:37,340 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.6407466
2015-10-18 21:36:37,574 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:37,684 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.455643
2015-10-18 21:36:37,824 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.6407466
2015-10-18 21:36:37,918 INFO [IPC Server handler 9 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:36:38,606 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:38,715 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.5531162
2015-10-18 21:36:39,121 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.667
2015-10-18 21:36:39,340 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.45565325
2015-10-18 21:36:39,637 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:40,090 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.45561612
2015-10-18 21:36:40,340 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.667
2015-10-18 21:36:40,481 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.56380075
2015-10-18 21:36:40,637 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:40,934 INFO [IPC Server handler 9 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:36:41,293 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.455643
2015-10-18 21:36:41,622 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:41,731 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.5638328
2015-10-18 21:36:42,137 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.6929846
2015-10-18 21:36:42,669 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:43,059 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.45565325
2015-10-18 21:36:43,356 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.667
2015-10-18 21:36:43,715 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:43,872 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.45561612
2015-10-18 21:36:43,934 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:36:44,184 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.56380075
2015-10-18 21:36:44,747 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:44,794 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.5638328
2015-10-18 21:36:44,919 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.455643
2015-10-18 21:36:45,169 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.74144036
2015-10-18 21:36:45,778 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:46,372 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.667
2015-10-18 21:36:46,794 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:46,950 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:36:47,028 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.45881757
2015-10-18 21:36:47,357 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.5638328
2015-10-18 21:36:47,763 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.45561612
2015-10-18 21:36:47,810 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:47,810 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.667
2015-10-18 21:36:47,997 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.56380075
2015-10-18 21:36:48,200 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.78497446
2015-10-18 21:36:48,607 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.455643
2015-10-18 21:36:48,857 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:49,388 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.667
2015-10-18 21:36:49,841 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:49,951 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:36:50,763 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.54273546
2015-10-18 21:36:50,888 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:50,888 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.667
2015-10-18 21:36:51,248 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.8334095
2015-10-18 21:36:51,701 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.45561612
2015-10-18 21:36:51,873 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.56380075
2015-10-18 21:36:51,873 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:52,435 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.667
2015-10-18 21:36:52,435 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.455643
2015-10-18 21:36:52,920 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:52,951 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:36:53,904 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:53,904 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.667
2015-10-18 21:36:54,264 INFO [IPC Server handler 9 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.8739724
2015-10-18 21:36:54,420 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.5638328
2015-10-18 21:36:54,936 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:55,514 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.45561612
2015-10-18 21:36:55,607 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.56380075
2015-10-18 21:36:55,936 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:55,998 INFO [IPC Server handler 8 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:36:56,451 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.48917285
2015-10-18 21:36:56,967 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.67430884
2015-10-18 21:36:56,967 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:57,295 INFO [IPC Server handler 9 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.92091775
2015-10-18 21:36:57,951 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:57,998 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.5638328
2015-10-18 21:36:58,952 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:59,030 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:36:59,702 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.45561612
2015-10-18 21:36:59,717 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.56380075
2015-10-18 21:36:59,999 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:36:59,999 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.7239394
2015-10-18 21:37:00,327 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 0.96984553
2015-10-18 21:37:00,686 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.56003565
2015-10-18 21:37:00,983 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:37:01,905 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.5638328
2015-10-18 21:37:02,061 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:37:02,061 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:37:02,296 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_1 is : 1.0
2015-10-18 21:37:02,311 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0001_m_000002_1
2015-10-18 21:37:02,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000002_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:37:02,311 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0001_01_000014 taskAttempt attempt_1445175094696_0001_m_000002_1
2015-10-18 21:37:02,311 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0001_m_000002_1
2015-10-18 21:37:02,311 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:37:02,421 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000002_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:37:02,421 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0001_m_000002_1
2015-10-18 21:37:02,421 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445175094696_0001_m_000002_0
2015-10-18 21:37:02,421 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:37:02,421 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-18 21:37:02,421 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000002_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 21:37:02,421 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0001_01_000008 taskAttempt attempt_1445175094696_0001_m_000002_0
2015-10-18 21:37:02,421 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0001_m_000002_0
2015-10-18 21:37:02,421 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-18 21:37:02,874 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-18 21:37:03,030 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.77432305
2015-10-18 21:37:03,124 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:37:03,249 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000002_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 21:37:03,405 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 21:37:03,561 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.5738508
2015-10-18 21:37:03,561 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000002_0 is : 0.52501327
2015-10-18 21:37:03,577 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out1/_temporary/1/_temporary/attempt_1445175094696_0001_m_000002_0
2015-10-18 21:37:03,577 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000002_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 21:37:03,921 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0001_01_000014
2015-10-18 21:37:03,921 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 21:37:03,921 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0001_m_000002_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:37:03,921 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0001_01_000016 to attempt_1445175094696_0001_m_000000_1
2015-10-18 21:37:03,921 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:10 RackLocal:4
2015-10-18 21:37:03,921 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:37:03,937 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000000_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:37:03,937 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0001_01_000016 taskAttempt attempt_1445175094696_0001_m_000000_1
2015-10-18 21:37:03,937 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0001_m_000000_1
2015-10-18 21:37:03,937 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:37:04,077 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0001_m_000000_1 : 13562
2015-10-18 21:37:04,077 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0001_m_000000_1] using containerId: [container_1445175094696_0001_01_000016 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:25649]
2015-10-18 21:37:04,077 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000000_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:37:04,077 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0001_m_000000
2015-10-18 21:37:04,140 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:04,358 INFO [Socket Reader #1 for port 60153] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 60153: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 21:37:04,905 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.5638263
2015-10-18 21:37:04,952 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 21:37:05,124 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.20000002
2015-10-18 21:37:05,171 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:05,671 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.5638328
2015-10-18 21:37:05,984 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0001_01_000008
2015-10-18 21:37:05,984 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:10 RackLocal:4
2015-10-18 21:37:05,984 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0001_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:37:05,984 INFO [Socket Reader #1 for port 60153] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0001 (auth:SIMPLE)
2015-10-18 21:37:05,999 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0001_m_000016 asked for a task
2015-10-18 21:37:05,999 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0001_m_000016 given task: attempt_1445175094696_0001_m_000000_1
2015-10-18 21:37:06,046 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.81307507
2015-10-18 21:37:06,156 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:07,187 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:07,218 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.65224063
2015-10-18 21:37:08,125 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.23333333
2015-10-18 21:37:08,171 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:08,671 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.65224063
2015-10-18 21:37:08,828 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.5638263
2015-10-18 21:37:09,078 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.8606008
2015-10-18 21:37:09,203 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:09,453 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.5638328
2015-10-18 21:37:10,187 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:11,062 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.667
2015-10-18 21:37:11,140 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.23333333
2015-10-18 21:37:11,203 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:12,125 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.90666175
2015-10-18 21:37:12,187 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:12,266 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.5638263
2015-10-18 21:37:13,094 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.13101934
2015-10-18 21:37:13,156 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.5638328
2015-10-18 21:37:13,203 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:14,188 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.23333333
2015-10-18 21:37:14,188 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:14,672 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.667
2015-10-18 21:37:15,157 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 0.957229
2015-10-18 21:37:15,203 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:16,047 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.5638263
2015-10-18 21:37:16,125 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.13101934
2015-10-18 21:37:16,219 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:16,469 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.7285315
2015-10-18 21:37:16,797 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.5638328
2015-10-18 21:37:17,204 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.23333333
2015-10-18 21:37:17,204 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:17,876 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_1 is : 1.0
2015-10-18 21:37:17,876 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0001_m_000004_1
2015-10-18 21:37:17,876 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000004_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:37:17,876 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0001_01_000015 taskAttempt attempt_1445175094696_0001_m_000004_1
2015-10-18 21:37:17,876 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0001_m_000004_1
2015-10-18 21:37:17,876 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:37:17,985 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000004_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:37:17,985 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0001_m_000004_1
2015-10-18 21:37:17,985 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445175094696_0001_m_000004_0
2015-10-18 21:37:17,985 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:37:18,001 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-18 21:37:18,001 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000004_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 21:37:18,001 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0001_01_000009 taskAttempt attempt_1445175094696_0001_m_000004_0
2015-10-18 21:37:18,001 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0001_m_000004_0
2015-10-18 21:37:18,001 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-18 21:37:18,407 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:10 RackLocal:4
2015-10-18 21:37:18,407 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:37:18,423 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.667
2015-10-18 21:37:19,016 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445175094696_0001_m_000004
2015-10-18 21:37:19,016 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 21:37:19,157 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.23921585
2015-10-18 21:37:19,157 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000004_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 21:37:19,345 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 21:37:19,345 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out1/_temporary/1/_temporary/attempt_1445175094696_0001_m_000004_0
2015-10-18 21:37:19,345 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000004_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 21:37:19,438 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:19,470 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.5638263
2015-10-18 21:37:19,501 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0001_01_000015
2015-10-18 21:37:19,501 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:10 RackLocal:4
2015-10-18 21:37:19,501 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0001_m_000004_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:37:20,220 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.23333333
2015-10-18 21:37:20,454 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:20,485 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000004_0 is : 0.5638328
2015-10-18 21:37:21,235 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.7788236
2015-10-18 21:37:21,485 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:21,720 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.667
2015-10-18 21:37:22,126 INFO [Socket Reader #1 for port 60153] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 60153: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 21:37:22,173 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.23921585
2015-10-18 21:37:22,470 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:22,720 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.5638263
2015-10-18 21:37:23,392 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.23333333
2015-10-18 21:37:23,642 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:24,251 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.8028742
2015-10-18 21:37:24,845 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:25,173 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0001_01_000009
2015-10-18 21:37:25,173 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:10 RackLocal:4
2015-10-18 21:37:25,173 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0001_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:37:25,205 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.23921585
2015-10-18 21:37:25,220 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.667
2015-10-18 21:37:25,861 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:26,470 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.5638263
2015-10-18 21:37:26,642 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.23333333
2015-10-18 21:37:26,845 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:27,267 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.8240807
2015-10-18 21:37:27,861 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:28,221 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.3474062
2015-10-18 21:37:28,877 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:29,330 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.667
2015-10-18 21:37:29,658 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.23333333
2015-10-18 21:37:29,830 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.60764307
2015-10-18 21:37:29,861 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:30,330 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.8542602
2015-10-18 21:37:30,877 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:31,252 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.3474062
2015-10-18 21:37:31,862 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:32,674 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.23333333
2015-10-18 21:37:32,893 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:33,190 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.667
2015-10-18 21:37:33,330 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.8924125
2015-10-18 21:37:33,502 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.667
2015-10-18 21:37:33,534 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.667
2015-10-18 21:37:33,877 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:34,284 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.3474062
2015-10-18 21:37:34,909 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:35,690 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.23333333
2015-10-18 21:37:35,893 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:36,362 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.9185283
2015-10-18 21:37:36,581 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.667
2015-10-18 21:37:36,925 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:36,925 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.667
2015-10-18 21:37:37,315 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.45563135
2015-10-18 21:37:37,909 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:38,690 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.23333333
2015-10-18 21:37:38,909 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:39,378 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.95001626
2015-10-18 21:37:39,941 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:40,112 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.667
2015-10-18 21:37:40,331 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.45563135
2015-10-18 21:37:40,441 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.667
2015-10-18 21:37:40,925 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:41,956 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:42,425 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 0.98732066
2015-10-18 21:37:42,988 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:43,394 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.45563135
2015-10-18 21:37:43,910 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.6690611
2015-10-18 21:37:44,019 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:44,238 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.667
2015-10-18 21:37:45,066 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:45,504 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 1.0
2015-10-18 21:37:46,113 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:46,488 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.56380075
2015-10-18 21:37:46,535 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_1 is : 1.0
2015-10-18 21:37:46,582 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0001_m_000006_1
2015-10-18 21:37:46,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000006_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:37:46,582 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0001_01_000013 taskAttempt attempt_1445175094696_0001_m_000006_1
2015-10-18 21:37:46,598 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0001_m_000006_1
2015-10-18 21:37:46,598 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 21:37:46,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000006_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:37:46,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0001_m_000006_1
2015-10-18 21:37:46,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445175094696_0001_m_000006_0
2015-10-18 21:37:46,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:37:46,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-18 21:37:46,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000006_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 21:37:46,754 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0001_01_000010 taskAttempt attempt_1445175094696_0001_m_000006_0
2015-10-18 21:37:46,754 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0001_m_000006_0
2015-10-18 21:37:46,754 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-18 21:37:47,129 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:37:47,223 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:10 RackLocal:4
2015-10-18 21:37:47,410 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445175094696_0001_m_000006
2015-10-18 21:37:47,410 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 21:37:47,801 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.68128943
2015-10-18 21:37:48,254 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000006_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 21:37:48,332 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0001_01_000013
2015-10-18 21:37:48,332 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:10 RackLocal:4
2015-10-18 21:37:48,332 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0001_m_000006_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:37:48,379 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 21:37:48,395 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out1/_temporary/1/_temporary/attempt_1445175094696_0001_m_000006_0
2015-10-18 21:37:48,395 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000006_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 21:37:48,442 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000006_0 is : 0.667
2015-10-18 21:37:49,207 INFO [Socket Reader #1 for port 60153] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 60153: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 21:37:49,223 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:37:49,520 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.56380075
2015-10-18 21:37:50,223 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:37:50,426 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0001_01_000010
2015-10-18 21:37:50,426 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:10 RackLocal:4
2015-10-18 21:37:50,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0001_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:37:51,082 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.699751
2015-10-18 21:37:51,254 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:37:52,301 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:37:52,598 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.56380075
2015-10-18 21:37:53,348 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:37:53,770 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.26666668
2015-10-18 21:37:54,317 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.73098195
2015-10-18 21:37:54,380 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:37:55,036 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.56380075
2015-10-18 21:37:55,442 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:37:55,677 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.667
2015-10-18 21:37:56,474 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:37:56,818 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.26666668
2015-10-18 21:37:57,489 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.7573587
2015-10-18 21:37:57,489 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:37:58,536 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:37:58,755 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.667
2015-10-18 21:37:59,568 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:37:59,833 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.3
2015-10-18 21:38:00,615 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:00,834 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.78043
2015-10-18 21:38:01,662 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:01,865 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.667
2015-10-18 21:38:02,678 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:02,834 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.3
2015-10-18 21:38:03,693 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:04,568 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.79452866
2015-10-18 21:38:04,725 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:04,943 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.6671598
2015-10-18 21:38:05,756 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:05,865 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.3
2015-10-18 21:38:06,803 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:07,866 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:07,991 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.71632916
2015-10-18 21:38:07,991 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.8075797
2015-10-18 21:38:08,897 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:08,897 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.3
2015-10-18 21:38:09,928 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:10,944 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:11,007 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.75135016
2015-10-18 21:38:11,647 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.8209292
2015-10-18 21:38:11,913 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.3
2015-10-18 21:38:11,960 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:12,976 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:13,991 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:14,038 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.7970705
2015-10-18 21:38:14,913 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.3
2015-10-18 21:38:15,007 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:15,241 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.8300672
2015-10-18 21:38:16,007 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:17,039 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:17,070 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.84828156
2015-10-18 21:38:17,929 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.3
2015-10-18 21:38:18,039 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:18,804 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.84154224
2015-10-18 21:38:19,039 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:20,039 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:20,101 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.8939634
2015-10-18 21:38:20,945 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.3
2015-10-18 21:38:21,070 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:22,055 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:22,305 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.8563279
2015-10-18 21:38:23,070 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:23,133 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.9395075
2015-10-18 21:38:23,961 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.3
2015-10-18 21:38:24,071 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:25,055 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:25,883 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_0 is : 0.871447
2015-10-18 21:38:26,071 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:26,180 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 0.9838744
2015-10-18 21:38:27,040 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.3
2015-10-18 21:38:27,087 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:27,196 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_m_000000_1 is : 1.0
2015-10-18 21:38:27,212 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0001_m_000000_1
2015-10-18 21:38:27,212 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000000_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:38:27,212 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0001_01_000016 taskAttempt attempt_1445175094696_0001_m_000000_1
2015-10-18 21:38:27,212 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0001_m_000000_1
2015-10-18 21:38:27,212 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:38:27,540 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000000_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:38:27,540 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0001_m_000000_1
2015-10-18 21:38:27,555 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445175094696_0001_m_000000_0
2015-10-18 21:38:27,555 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:38:27,555 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-18 21:38:27,555 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000000_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 21:38:27,555 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0001_01_000007 taskAttempt attempt_1445175094696_0001_m_000000_0
2015-10-18 21:38:27,555 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0001_m_000000_0
2015-10-18 21:38:27,555 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-18 21:38:27,946 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000000_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 21:38:28,009 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:10 RackLocal:4
2015-10-18 21:38:28,055 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 21:38:28,196 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:38:28,259 WARN [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out1/_temporary/1/_temporary/attempt_1445175094696_0001_m_000000_0
2015-10-18 21:38:28,259 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_m_000000_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 21:38:29,056 INFO [Socket Reader #1 for port 60153] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 60153: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 21:38:29,149 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0001_01_000016
2015-10-18 21:38:29,149 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:10 RackLocal:4
2015-10-18 21:38:29,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0001_m_000000_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:38:29,259 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:30,071 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.3
2015-10-18 21:38:30,228 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0001_01_000007
2015-10-18 21:38:30,228 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:10 RackLocal:4
2015-10-18 21:38:30,228 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0001_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:38:30,290 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:31,337 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:32,384 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:33,181 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.3
2015-10-18 21:38:33,462 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:34,619 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0001_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:38:34,806 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.3
2015-10-18 21:38:36,291 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.33333334
2015-10-18 21:38:44,901 INFO [IPC Server handler 9 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.33333334
2015-10-18 21:38:49,464 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.6733228
2015-10-18 21:38:52,793 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.67907834
2015-10-18 21:38:56,168 INFO [IPC Server handler 8 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.6835589
2015-10-18 21:38:59,418 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.6886257
2015-10-18 21:39:02,466 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.693805
2015-10-18 21:39:05,607 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.6988112
2015-10-18 21:39:08,716 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.70111877
2015-10-18 21:39:11,763 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.7018286
2015-10-18 21:39:14,826 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.70233333
2015-10-18 21:39:17,889 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.7032014
2015-10-18 21:39:20,952 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.7038162
2015-10-18 21:39:23,999 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.70503205
2015-10-18 21:39:27,062 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.7066357
2015-10-18 21:39:30,109 INFO [IPC Server handler 9 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.7081547
2015-10-18 21:39:33,172 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.70916384
2015-10-18 21:39:36,313 INFO [IPC Server handler 8 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.7099367
2015-10-18 21:39:39,376 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.71111405
2015-10-18 21:39:42,408 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.7129647
2015-10-18 21:39:45,439 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.7150527
2015-10-18 21:39:48,486 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.71773803
2015-10-18 21:39:51,534 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.72055244
2015-10-18 21:39:54,612 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.7229215
2015-10-18 21:39:56,956 WARN [ResponseProcessor for block BP-1347369012-**************-1444972147527:blk_1073743654_2877] org.apache.hadoop.hdfs.DFSClient: Slow ReadProcessor read fields took 58504ms (threshold=30000ms); ack: seqno: -2 status: SUCCESS status: ERROR downstreamAckTimeNanos: 0, targets: [************:50010, *************:50010]
2015-10-18 21:39:56,956 WARN [ResponseProcessor for block BP-1347369012-**************-1444972147527:blk_1073743654_2877] org.apache.hadoop.hdfs.DFSClient: DFSOutputStream ResponseProcessor exception  for block BP-1347369012-**************-1444972147527:blk_1073743654_2877
java.io.IOException: Bad response ERROR for block BP-1347369012-**************-1444972147527:blk_1073743654_2877 from datanode *************:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer$ResponseProcessor.run(DFSOutputStream.java:898)
2015-10-18 21:39:56,972 WARN [DataStreamer for file /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445175094696_0001/job_1445175094696_0001_1.jhist block BP-1347369012-**************-1444972147527:blk_1073743654_2877] org.apache.hadoop.hdfs.DFSClient: Error Recovery for block BP-1347369012-**************-1444972147527:blk_1073743654_2877 in pipeline ************:50010, *************:50010: bad datanode *************:50010
2015-10-18 21:39:57,659 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.7246576
2015-10-18 21:40:00,691 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.7266214
2015-10-18 21:40:03,738 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.73021144
2015-10-18 21:40:06,754 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.73379314
2015-10-18 21:40:09,786 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.73705614
2015-10-18 21:40:12,833 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.7403482
2015-10-18 21:40:15,864 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.74341124
2015-10-18 21:40:18,896 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.74685216
2015-10-18 21:40:21,911 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.7512287
2015-10-18 21:40:24,974 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.75504905
2015-10-18 21:40:27,990 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.75793755
2015-10-18 21:40:31,021 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.7630632
2015-10-18 21:40:34,021 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.7673512
2015-10-18 21:40:37,053 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.772029
2015-10-18 21:40:40,068 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.7757995
2015-10-18 21:40:43,084 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.77956414
2015-10-18 21:40:46,100 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.78345203
2015-10-18 21:40:49,147 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.7876533
2015-10-18 21:40:52,163 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.7911389
2015-10-18 21:40:55,194 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.79425716
2015-10-18 21:40:58,241 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.7969761
2015-10-18 21:41:01,273 INFO [IPC Server handler 9 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.7995682
2015-10-18 21:41:04,335 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8023983
2015-10-18 21:41:07,398 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8039333
2015-10-18 21:41:10,523 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.80409086
2015-10-18 21:41:13,601 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8042954
2015-10-18 21:41:16,695 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8044063
2015-10-18 21:41:19,820 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8045326
2015-10-18 21:41:22,945 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.80470586
2015-10-18 21:41:26,336 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8048949
2015-10-18 21:41:29,461 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8050058
2015-10-18 21:41:32,555 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8076522
2015-10-18 21:41:35,618 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8105826
2015-10-18 21:41:38,649 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8134484
2015-10-18 21:41:41,681 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8164158
2015-10-18 21:41:44,728 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8193321
2015-10-18 21:41:47,759 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.82223284
2015-10-18 21:41:50,791 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8252424
2015-10-18 21:41:53,838 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.82808006
2015-10-18 21:41:56,869 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8307326
2015-10-18 21:41:59,885 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8307326
2015-10-18 21:42:20,948 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8342062
2015-10-18 21:42:23,964 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8394259
2015-10-18 21:42:26,996 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8444314
2015-10-18 21:42:30,011 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.84782016
2015-10-18 21:42:33,058 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.85239166
2015-10-18 21:42:36,121 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8555591
2015-10-18 21:42:39,184 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.857729
2015-10-18 21:42:42,246 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8595499
2015-10-18 21:42:45,293 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.86152774
2015-10-18 21:42:48,325 INFO [IPC Server handler 8 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.86339885
2015-10-18 21:42:51,372 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8668615
2015-10-18 21:42:54,403 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.87000453
2015-10-18 21:42:57,450 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8729582
2015-10-18 21:43:00,497 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.87606704
2015-10-18 21:43:03,544 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8790171
2015-10-18 21:43:06,576 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.881962
2015-10-18 21:43:09,607 INFO [IPC Server handler 9 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.88496315
2015-10-18 21:43:12,654 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.88839495
2015-10-18 21:43:15,701 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8918775
2015-10-18 21:43:18,732 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.895071
2015-10-18 21:43:21,764 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.8978838
2015-10-18 21:43:24,827 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.89970374
2015-10-18 21:43:27,874 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.901495
2015-10-18 21:43:30,936 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.9031664
2015-10-18 21:43:33,999 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.9045185
2015-10-18 21:43:37,061 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.9059273
2015-10-18 21:43:40,108 INFO [IPC Server handler 8 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.90721804
2015-10-18 21:43:43,171 INFO [IPC Server handler 1 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.9083564
2015-10-18 21:43:46,234 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.9098567
2015-10-18 21:43:49,296 INFO [IPC Server handler 2 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.91135466
2015-10-18 21:43:52,343 INFO [IPC Server handler 3 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.91278976
2015-10-18 21:43:55,422 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.91416144
2015-10-18 21:43:58,484 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.91531277
2015-10-18 21:44:01,547 INFO [IPC Server handler 10 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.91683465
2015-10-18 21:44:04,610 INFO [IPC Server handler 12 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.91835785
2015-10-18 21:44:07,688 INFO [IPC Server handler 15 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.92001444
2015-10-18 21:44:10,751 INFO [IPC Server handler 0 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.92165625
2015-10-18 21:44:13,829 INFO [IPC Server handler 16 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.9232619
2015-10-18 21:44:16,892 INFO [IPC Server handler 5 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.9244463
2015-10-18 21:44:19,954 INFO [IPC Server handler 22 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.92604136
2015-10-18 21:44:23,001 INFO [IPC Server handler 17 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.9278889
2015-10-18 21:44:26,033 INFO [IPC Server handler 6 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.93047047
2015-10-18 21:44:29,064 INFO [IPC Server handler 21 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.9320843
2015-10-18 21:44:32,095 INFO [IPC Server handler 23 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.9348923
2015-10-18 21:44:35,142 INFO [IPC Server handler 20 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.937525
2015-10-18 21:44:38,189 INFO [IPC Server handler 9 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.940097
2015-10-18 21:44:41,236 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.9427008
2015-10-18 21:44:44,268 INFO [IPC Server handler 4 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.9452903
2015-10-18 21:44:47,299 INFO [IPC Server handler 11 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.94771564
2015-10-18 21:44:50,377 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.9502695
2015-10-18 21:44:53,409 INFO [IPC Server handler 7 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.95297956
2015-10-18 21:44:56,440 INFO [IPC Server handler 26 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.955726
2015-10-18 21:44:59,456 INFO [IPC Server handler 19 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.9584242
2015-10-18 21:45:02,487 INFO [IPC Server handler 25 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.96098524
2015-10-18 21:45:05,503 INFO [IPC Server handler 18 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.96433085
2015-10-18 21:45:08,519 INFO [IPC Server handler 28 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.9683516
2015-10-18 21:45:11,550 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.97353345
2015-10-18 21:45:14,582 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.9777605
2015-10-18 21:45:17,582 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.9823156
2015-10-18 21:45:20,598 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.98744524
2015-10-18 21:45:23,629 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.991481
2015-10-18 21:45:26,645 INFO [IPC Server handler 27 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 0.99550235
2015-10-18 21:45:29,645 INFO [IPC Server handler 14 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 1.0
2015-10-18 21:45:29,770 INFO [IPC Server handler 24 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445175094696_0001_r_000000_0
2015-10-18 21:45:29,770 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_r_000000_0 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-18 21:45:29,770 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445175094696_0001_r_000000_0 given a go for committing the task output.
2015-10-18 21:45:29,801 INFO [IPC Server handler 8 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445175094696_0001_r_000000_0
2015-10-18 21:45:29,801 INFO [IPC Server handler 8 on 60153] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445175094696_0001_r_000000_0:true
2015-10-18 21:45:29,848 INFO [IPC Server handler 29 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0001_r_000000_0 is : 1.0
2015-10-18 21:45:29,863 INFO [IPC Server handler 13 on 60153] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0001_r_000000_0
2015-10-18 21:45:29,863 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_r_000000_0 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:45:29,863 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0001_01_000012 taskAttempt attempt_1445175094696_0001_r_000000_0
2015-10-18 21:45:29,863 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0001_r_000000_0
2015-10-18 21:45:29,879 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 21:45:29,942 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0001_r_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:45:29,942 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0001_r_000000_0
2015-10-18 21:45:29,942 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0001_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:45:29,942 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-18 21:45:29,942 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445175094696_0001Job Transitioned from RUNNING to COMMITTING
2015-10-18 21:45:29,957 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-18 21:45:30,238 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-18 21:45:30,238 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445175094696_0001Job Transitioned from COMMITTING to SUCCEEDED
2015-10-18 21:45:30,254 INFO [Thread-123] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-18 21:45:30,254 INFO [Thread-123] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-18 21:45:30,254 INFO [Thread-123] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-18 21:45:30,254 INFO [Thread-123] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-18 21:45:30,254 INFO [Thread-123] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-18 21:45:30,254 INFO [Thread-123] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-18 21:45:30,254 INFO [Thread-123] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-18 21:45:30,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:15 ContRel:0 HostLocal:10 RackLocal:4
2015-10-18 21:45:30,504 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445175094696_0001/job_1445175094696_0001_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0001-1445175178663-msrabi-word+count-1445175930238-10-1-SUCCEEDED-default-1445175188000.jhist_tmp
2015-10-18 21:45:30,848 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0001-1445175178663-msrabi-word+count-1445175930238-10-1-SUCCEEDED-default-1445175188000.jhist_tmp
2015-10-18 21:45:30,895 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445175094696_0001/job_1445175094696_0001_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0001_conf.xml_tmp
2015-10-18 21:45:31,457 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0001_01_000012
2015-10-18 21:45:31,457 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:15 ContRel:0 HostLocal:10 RackLocal:4
2015-10-18 21:45:31,457 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0001_r_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:45:52,036 INFO [Thread-128] org.apache.hadoop.hdfs.DFSClient: Exception in createBlockOutputStream
java.io.IOException: Bad connect ack with firstBadLink as *************:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.createBlockOutputStream(DFSOutputStream.java:1461)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.nextBlockOutputStream(DFSOutputStream.java:1362)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.run(DFSOutputStream.java:589)
2015-10-18 21:45:52,036 INFO [Thread-128] org.apache.hadoop.hdfs.DFSClient: Abandoning BP-1347369012-**************-1444972147527:blk_1073743703_2928
2015-10-18 21:45:52,052 INFO [Thread-128] org.apache.hadoop.hdfs.DFSClient: Excluding datanode *************:50010
2015-10-18 21:45:52,161 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0001_conf.xml_tmp
2015-10-18 21:45:52,177 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0001.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0001.summary
2015-10-18 21:45:52,177 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0001_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0001_conf.xml
2015-10-18 21:45:52,192 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0001-1445175178663-msrabi-word+count-1445175930238-10-1-SUCCEEDED-default-1445175188000.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0001-1445175178663-msrabi-word+count-1445175930238-10-1-SUCCEEDED-default-1445175188000.jhist
2015-10-18 21:45:52,192 INFO [Thread-123] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-18 21:45:52,192 INFO [Thread-123] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-18 21:45:52,208 INFO [Thread-123] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://04DN8IQ.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445175094696_0001
2015-10-18 21:45:52,208 INFO [Thread-123] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-18 21:45:53,208 INFO [Thread-123] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:15 ContRel:0 HostLocal:10 RackLocal:4
2015-10-18 21:45:53,208 INFO [Thread-123] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445175094696_0001
2015-10-18 21:45:53,224 INFO [Thread-123] org.apache.hadoop.ipc.Server: Stopping server on 60153
2015-10-18 21:45:53,224 INFO [IPC Server listener on 60153] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 60153
2015-10-18 21:45:53,224 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-18 21:45:53,224 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
