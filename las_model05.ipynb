{"cells": [{"cell_type": "code", "execution_count": 1, "id": "04288df6-6f7a-4343-b829-272cd50c09f0", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "from sklearn.neighbors import KNeighborsClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.metrics import classification_report, confusion_matrix, accuracy_score\n", "\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "id": "0fff5b54-5687-4cf1-a4d4-c8241430fc3d", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(\"C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/CSV Data/Xeek_train_subset_clean.csv\")"]}, {"cell_type": "code", "execution_count": 3, "id": "a03fbde5-1eb8-40ac-a101-54c66d2f6bdd", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DEPTH_MD</th>\n", "      <th>CALI</th>\n", "      <th>RDEP</th>\n", "      <th>RHOB</th>\n", "      <th>GR</th>\n", "      <th>NPHI</th>\n", "      <th>PEF</th>\n", "      <th>DTC</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>133198.000000</td>\n", "      <td>133006.000000</td>\n", "      <td>125805.000000</td>\n", "      <td>108053.000000</td>\n", "      <td>133198.000000</td>\n", "      <td>91725.000000</td>\n", "      <td>100840.000000</td>\n", "      <td>132635.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1792.944663</td>\n", "      <td>13.199399</td>\n", "      <td>1.743774</td>\n", "      <td>2.199556</td>\n", "      <td>69.553872</td>\n", "      <td>0.355602</td>\n", "      <td>4.511845</td>\n", "      <td>122.700286</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>739.441515</td>\n", "      <td>3.561386</td>\n", "      <td>13.233330</td>\n", "      <td>0.236902</td>\n", "      <td>39.328728</td>\n", "      <td>0.143857</td>\n", "      <td>5.092807</td>\n", "      <td>34.234879</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>415.261599</td>\n", "      <td>5.946157</td>\n", "      <td>0.123068</td>\n", "      <td>1.366913</td>\n", "      <td>0.109284</td>\n", "      <td>-0.023143</td>\n", "      <td>1.010027</td>\n", "      <td>7.415132</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1182.822400</td>\n", "      <td>11.381848</td>\n", "      <td>0.777323</td>\n", "      <td>2.029237</td>\n", "      <td>43.244637</td>\n", "      <td>0.234927</td>\n", "      <td>2.857540</td>\n", "      <td>87.899776</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>1747.524495</td>\n", "      <td>12.698571</td>\n", "      <td>1.130621</td>\n", "      <td>2.162121</td>\n", "      <td>66.266132</td>\n", "      <td>0.382014</td>\n", "      <td>3.780121</td>\n", "      <td>135.968094</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>2413.874901</td>\n", "      <td>14.944049</td>\n", "      <td>1.644573</td>\n", "      <td>2.404375</td>\n", "      <td>93.808681</td>\n", "      <td>0.480985</td>\n", "      <td>5.098502</td>\n", "      <td>147.388626</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>3272.024000</td>\n", "      <td>25.717396</td>\n", "      <td>1856.935059</td>\n", "      <td>3.115611</td>\n", "      <td>804.298950</td>\n", "      <td>0.817310</td>\n", "      <td>365.575592</td>\n", "      <td>230.432953</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            DEPTH_MD           CALI           RDEP           RHOB  \\\n", "count  133198.000000  133006.000000  125805.000000  108053.000000   \n", "mean     1792.944663      13.199399       1.743774       2.199556   \n", "std       739.441515       3.561386      13.233330       0.236902   \n", "min       415.261599       5.946157       0.123068       1.366913   \n", "25%      1182.822400      11.381848       0.777323       2.029237   \n", "50%      1747.524495      12.698571       1.130621       2.162121   \n", "75%      2413.874901      14.944049       1.644573       2.404375   \n", "max      3272.024000      25.717396    1856.935059       3.115611   \n", "\n", "                  GR          NPHI            PEF            DTC  \n", "count  133198.000000  91725.000000  100840.000000  132635.000000  \n", "mean       69.553872      0.355602       4.511845     122.700286  \n", "std        39.328728      0.143857       5.092807      34.234879  \n", "min         0.109284     -0.023143       1.010027       7.415132  \n", "25%        43.244637      0.234927       2.857540      87.899776  \n", "50%        66.266132      0.382014       3.780121     135.968094  \n", "75%        93.808681      0.480985       5.098502     147.388626  \n", "max       804.298950      0.817310     365.575592     230.432953  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe()"]}, {"cell_type": "code", "execution_count": 4, "id": "e69f3f37-43dd-43b8-b877-6c90123234f3", "metadata": {}, "outputs": [], "source": ["df = df.dropna()"]}, {"cell_type": "code", "execution_count": 5, "id": "ce9b4438-a5af-49e3-aace-34da84b6b2e4", "metadata": {}, "outputs": [], "source": ["# Select inputs and target\n", "X = df[['RDEP', 'RHOB', 'GR', 'NPHI', 'PEF', 'DTC']]\n", "y = df['LITH']"]}, {"cell_type": "code", "execution_count": 6, "id": "a19840ef-0970-4359-a872-b57700193f73", "metadata": {}, "outputs": [], "source": ["X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3)"]}, {"cell_type": "code", "execution_count": 7, "id": "95752691-fba1-4f5f-bae5-8e221abfbadc", "metadata": {}, "outputs": [], "source": ["scaler = StandardScaler()\n", "\n", "#Fit the StandardScaler to the training data\n", "X_train = scaler.fit_transform(X_train)\n", "\n", "# Apply the StandardScaler, but not fit, to the validation data\n", "X_test = scaler.transform(X_test)"]}, {"cell_type": "code", "execution_count": 8, "id": "934cbff6-e6fe-48ab-bbb4-905c9f140dfa", "metadata": {}, "outputs": [], "source": ["clf = KNeighborsClassifier()"]}, {"cell_type": "code", "execution_count": 9, "id": "7889243e-0cfc-4748-8c15-0fe0db75621e", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-1 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-1 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-1 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-1 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-1 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-1 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-1 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-1 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-1 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-1 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-1 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-1 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-1 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-1 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-1 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-1 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-1 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>KNeighborsClassifier()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;KNeighborsClassifier<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.neighbors.KNeighborsClassifier.html\">?<span>Documentation for KNeighborsClassifier</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>KNeighborsClassifier()</pre></div> </div></div></div></div>"], "text/plain": ["KNeighborsClassifier()"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["clf.fit(X_train, y_train)"]}, {"cell_type": "code", "execution_count": 10, "id": "0e6efdb7-3c6b-489e-86a7-737cbba3bf3e", "metadata": {}, "outputs": [], "source": ["y_pred = clf.predict(X_test)"]}, {"cell_type": "code", "execution_count": 11, "id": "032a2beb-8f9d-455e-b219-c2c54f001e1d", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.8914038241542735"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["accuracy_score(y_test, y_pred)"]}, {"cell_type": "code", "execution_count": 12, "id": "7daf6a3b-bffa-49d7-9163-633132<PERSON>ea4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                 precision    recall  f1-score   support\n", "\n", "      Anhydrite       1.00      0.79      0.88        19\n", "          Chalk       0.77      0.77      0.77       674\n", "           Coal       0.85      0.61      0.71        18\n", "       Dolomite       0.75      0.66      0.70        32\n", "         Halite       0.83      0.71      0.77         7\n", "      Limestone       0.87      0.85      0.86      3389\n", "           Marl       0.77      0.77      0.77      1097\n", "      Sandstone       0.83      0.84      0.83      2974\n", "Sandstone/Shale       0.70      0.59      0.64      1366\n", "          Shale       0.94      0.96      0.95     14575\n", "           <PERSON>ff       0.68      0.52      0.59       325\n", "\n", "       accuracy                           0.89     24476\n", "      macro avg       0.82      0.73      0.77     24476\n", "   weighted avg       0.89      0.89      0.89     24476\n", "\n"]}], "source": ["print(classification_report(y_test, y_pred))"]}, {"cell_type": "code", "execution_count": 13, "id": "d5feff35-53df-46de-8aa7-303413d4dc4d", "metadata": {}, "outputs": [], "source": ["test_data = X_test.copy()\n", "# test_data['ACT_LITH'] = y_test"]}, {"cell_type": "code", "execution_count": 14, "id": "ad8ac3f9-07c3-4728-a7af-f8816dff28dd", "metadata": {}, "outputs": [{"ename": "IndexError", "evalue": "only integers, slices (`:`), ellipsis (`...`), numpy.newaxis (`None`) and integer or boolean arrays are valid indices", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mIndexError\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[14], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mtest_data\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mPRED_LITH\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m \u001b[38;5;241m=\u001b[39m clf\u001b[38;5;241m.\u001b[39mpredict(X_test)\n", "\u001b[1;31mIndexError\u001b[0m: only integers, slices (`:`), ellipsis (`...`), numpy.newaxis (`None`) and integer or boolean arrays are valid indices"]}], "source": ["test_data['PRED_LITH'] = clf.predict(X_test)"]}, {"cell_type": "code", "execution_count": 15, "id": "0ccf23c0-075f-4de8-9019-084efb9dcd52", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[-0.05871768,  0.30973969, -1.13279975, -1.29907993, -0.24681289,\n", "        -1.14471487],\n", "       [-0.03703632, -0.70376032,  0.84586085,  0.70560414, -0.63040496,\n", "         1.1089803 ],\n", "       [-0.075617  ,  0.76765964,  0.08035415, -0.84623435, -0.33165189,\n", "        -1.03373589],\n", "       ...,\n", "       [-0.07773867, -0.74247946,  0.31606726,  0.50109416,  0.12928106,\n", "         0.95839689],\n", "       [ 0.00972825,  0.55396822,  0.15384215, -1.25425019,  0.32511116,\n", "        -1.10157927],\n", "       [-0.03836467, -0.81360835,  0.8809989 ,  1.14846083, -0.26257804,\n", "         0.7397207 ]])"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["test_data"]}, {"cell_type": "code", "execution_count": 16, "id": "b7e44b3d-f303-4693-9faf-25b279e81fa2", "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "Data source must be a DataFrame or Mapping, not <class 'numpy.ndarray'>.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTypeError\u001b[0m                                 Trace<PERSON> (most recent call last)", "Cell \u001b[1;32mIn[16], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m g \u001b[38;5;241m=\u001b[39m \u001b[43msns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mFacetGrid\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtest_data\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcol\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mACT_LITH\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcol_wrap\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m4\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[0;32m      2\u001b[0m \u001b[43m                  \u001b[49m\u001b[43mcol_order\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m \u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mShale\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mSand<PERSON>\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[0;32m      3\u001b[0m \u001b[43m                              \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mSandstone/Shale\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[0;32m      4\u001b[0m \u001b[43m                              \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mLimestone\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mTuff\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[0;32m      5\u001b[0m \u001b[43m                              \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mMarl\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mAnhydrite\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[0;32m      6\u001b[0m \u001b[43m                              \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mDolomite\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mChalk\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m      7\u001b[0m \u001b[43m                              \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mCoal\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mHalite\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m      8\u001b[0m g\u001b[38;5;241m.\u001b[39mmap(sns\u001b[38;5;241m.\u001b[39mscatterplot, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mNPHI\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mRHOB\u001b[39m\u001b[38;5;124m'\u001b[39m, alpha\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0.5\u001b[39m)\n\u001b[0;32m      9\u001b[0m g\u001b[38;5;241m.\u001b[39mset(xlim\u001b[38;5;241m=\u001b[39m(\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m0.15\u001b[39m, \u001b[38;5;241m1\u001b[39m))\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\seaborn\\axisgrid.py:382\u001b[0m, in \u001b[0;36mFacetGrid.__init__\u001b[1;34m(self, data, row, col, hue, col_wrap, sharex, sharey, height, aspect, palette, row_order, col_order, hue_order, hue_kws, dropna, legend_out, despine, margin_titles, xlim, ylim, subplot_kws, gridspec_kws)\u001b[0m\n\u001b[0;32m    371\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__init__\u001b[39m(\n\u001b[0;32m    372\u001b[0m     \u001b[38;5;28mself\u001b[39m, data, \u001b[38;5;241m*\u001b[39m,\n\u001b[0;32m    373\u001b[0m     row\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, col\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m, hue\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, col_wrap\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    378\u001b[0m     gridspec_kws\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[0;32m    379\u001b[0m ):\n\u001b[0;32m    381\u001b[0m     \u001b[38;5;28msuper\u001b[39m()\u001b[38;5;241m.\u001b[39m\u001b[38;5;21m__init__\u001b[39m()\n\u001b[1;32m--> 382\u001b[0m     data \u001b[38;5;241m=\u001b[39m \u001b[43mhandle_data_source\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    384\u001b[0m     \u001b[38;5;66;03m# Determine the hue facet layer information\u001b[39;00m\n\u001b[0;32m    385\u001b[0m     hue_var \u001b[38;5;241m=\u001b[39m hue\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\seaborn\\_core\\data.py:278\u001b[0m, in \u001b[0;36mhandle_data_source\u001b[1;34m(data)\u001b[0m\n\u001b[0;32m    276\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m data \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(data, Mapping):\n\u001b[0;32m    277\u001b[0m     err \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mData source must be a DataFrame or Mapping, not \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mtype\u001b[39m(data)\u001b[38;5;132;01m!r}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m--> 278\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(err)\n\u001b[0;32m    280\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m data\n", "\u001b[1;31mTypeError\u001b[0m: Data source must be a DataFrame or Mapping, not <class 'numpy.ndarray'>."]}], "source": ["g = sns.FacetGrid(test_data, col='ACT_LITH', col_wrap=4, \n", "                  col_order= ['Shale', 'Sandstone', \n", "                              'Sandstone/Shale', \n", "                              'Limestone', 'Tuff', \n", "                              'Marl', 'Anhydrite', \n", "                              'Dolomite', 'Chalk',\n", "                              'Coal', 'Halite'])\n", "g.map(sns.scatterplot, 'NPHI', 'RHOB', alpha=0.5)\n", "g.set(xlim=(-0.15, 1))\n", "g.set(ylim=(3, 1))"]}, {"cell_type": "code", "execution_count": 17, "id": "0997121b-9da1-40fb-9664-5b691aaa6300", "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "Data source must be a DataFrame or Mapping, not <class 'numpy.ndarray'>.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTypeError\u001b[0m                                 Trace<PERSON> (most recent call last)", "Cell \u001b[1;32mIn[17], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m g \u001b[38;5;241m=\u001b[39m \u001b[43msns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mFacetGrid\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtest_data\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcol\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mPRED_LITH\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcol_wrap\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m4\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[0;32m      2\u001b[0m \u001b[43m                 \u001b[49m\u001b[43mcol_order\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m \u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mShale\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mSand<PERSON>\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[0;32m      3\u001b[0m \u001b[43m                              \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mSandstone/Shale\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[0;32m      4\u001b[0m \u001b[43m                              \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mLimestone\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mTuff\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[0;32m      5\u001b[0m \u001b[43m                              \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mMarl\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mAnhydrite\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[0;32m      6\u001b[0m \u001b[43m                              \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mDolomite\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mChalk\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m      7\u001b[0m \u001b[43m                              \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mCoal\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mHalite\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m      8\u001b[0m g\u001b[38;5;241m.\u001b[39mmap(sns\u001b[38;5;241m.\u001b[39mscatterplot, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mNPHI\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mRHOB\u001b[39m\u001b[38;5;124m'\u001b[39m, alpha\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0.5\u001b[39m)\n\u001b[0;32m      9\u001b[0m g\u001b[38;5;241m.\u001b[39mset(xlim\u001b[38;5;241m=\u001b[39m(\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m0.15\u001b[39m, \u001b[38;5;241m1\u001b[39m))\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\seaborn\\axisgrid.py:382\u001b[0m, in \u001b[0;36mFacetGrid.__init__\u001b[1;34m(self, data, row, col, hue, col_wrap, sharex, sharey, height, aspect, palette, row_order, col_order, hue_order, hue_kws, dropna, legend_out, despine, margin_titles, xlim, ylim, subplot_kws, gridspec_kws)\u001b[0m\n\u001b[0;32m    371\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__init__\u001b[39m(\n\u001b[0;32m    372\u001b[0m     \u001b[38;5;28mself\u001b[39m, data, \u001b[38;5;241m*\u001b[39m,\n\u001b[0;32m    373\u001b[0m     row\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, col\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m, hue\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, col_wrap\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    378\u001b[0m     gridspec_kws\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[0;32m    379\u001b[0m ):\n\u001b[0;32m    381\u001b[0m     \u001b[38;5;28msuper\u001b[39m()\u001b[38;5;241m.\u001b[39m\u001b[38;5;21m__init__\u001b[39m()\n\u001b[1;32m--> 382\u001b[0m     data \u001b[38;5;241m=\u001b[39m \u001b[43mhandle_data_source\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    384\u001b[0m     \u001b[38;5;66;03m# Determine the hue facet layer information\u001b[39;00m\n\u001b[0;32m    385\u001b[0m     hue_var \u001b[38;5;241m=\u001b[39m hue\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\seaborn\\_core\\data.py:278\u001b[0m, in \u001b[0;36mhandle_data_source\u001b[1;34m(data)\u001b[0m\n\u001b[0;32m    276\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m data \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(data, Mapping):\n\u001b[0;32m    277\u001b[0m     err \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mData source must be a DataFrame or Mapping, not \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mtype\u001b[39m(data)\u001b[38;5;132;01m!r}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m--> 278\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(err)\n\u001b[0;32m    280\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m data\n", "\u001b[1;31mTypeError\u001b[0m: Data source must be a DataFrame or Mapping, not <class 'numpy.ndarray'>."]}], "source": ["g = sns.FacetGrid(test_data, col='PRED_LITH', col_wrap=4, \n", "                 col_order= ['Shale', 'Sandstone', \n", "                              'Sandstone/Shale', \n", "                              'Limestone', 'Tuff', \n", "                              'Marl', 'Anhydrite', \n", "                              'Dolomite', 'Chalk',\n", "                              'Coal', 'Halite'])\n", "g.map(sns.scatterplot, 'NPHI', 'RHOB', alpha=0.5)\n", "g.set(xlim=(-0.15, 1))\n", "g.set(ylim=(3, 1))"]}, {"cell_type": "code", "execution_count": null, "id": "507f0347-a397-4f39-ab32-f44f35a03e85", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[   14     1     0     1     0     0     0     2     0     0     0]\n", " [    0   449     0     0     0   195     4    16     8     0     0]\n", " [    0     0     9     0     0     0     2     3     0     0     0]\n", " [    0     0     0     2     0    12     0    11     1     3     0]\n", " [    0     0     0     0     1     2     0     0     0     3     0]\n", " [    3   185     0     5     0  2613   135   118    47   197     8]\n", " [    0    22     0     0     0   215   610    52    36   192    12]\n", " [    2    41     2     8     1   166    71  2144   155   413    37]\n", " [    0    20     1     2     0    60    52   235   619   358     6]\n", " [    0     1     1     0     2    74   157   340   215 13734    59]\n", " [    0     0     0     0     0     4    19    49     3   160    76]]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 720x720 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Confusion Matrix\n", "cf_matrix = confusion_matrix(y_test, y_pred)\n", "print(cf_matrix)\n", "\n", "labels = ['Shale', 'Sandstone', 'Sandstone/Shale', 'Limestone', 'Tuff',\n", "       'Marl', 'Anhydrite', 'Dolomite', 'Chalk', 'Coal', 'Halite']\n", "labels.sort()\n", "\n", "fig = plt.figure(figsize=(10,10))\n", "ax = sns.heatmap(cf_matrix, annot=True, cmap='Reds', fmt='.0f',\n", "                xticklabels=labels, \n", "                yticklabels = labels)\n", "\n", "ax.set_title('Seaborn Confusion Matrix with labels\\n\\n');\n", "ax.set_xlabel('\\nPredicted Values')\n", "ax.set_ylabel('Actual Values ');\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "85a20d7e-3c22-43ed-ae99-3a95e5c003c3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e1915ebc-ef8b-4579-8b37-922631e68518", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9771a1ed-6237-4bce-be55-dfe89465b975", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}